"""
get pc format
"""
import sys
import shapely.wkt


def main():
    """
    :return: void
    """
    file = open(sys.argv[1])
    out1 = open(sys.argv[2], "w")
    out2 = open(sys.argv[3], "w")
    out1.write(
        "uid" + "\t" + "name" + "\t" + "city" + "\t" + "address" + "\t" + "point_x" + "\t" + "point_y" + "\t" + "phone"
        + "\t" + "std_tag" + "\t" + "uid_src" + "\n")

    cnt = 1
    for line in file:
        items = line.strip("\n").split("\t")
        faceId = items[0]
        shape = shapely.wkt.loads(items[1])
        x = shape.centroid.x
        y = shape.centroid.y
        name = items[3]
        city = items[10]
        addr = city
        phone = ""
        std_tag = ""
        src_type = "TJ"

        out1.write(str(cnt) + "\t" + str(name) + "\t" + str(city) + "\t" + str(addr) + "\t" + str(x) + "\t" + str(y)
                   + "\t" + phone + "\t" + std_tag + "\t" + src_type + "\n")
        out2.write(str(cnt) + "\t" + str(faceId) + "\n")
        cnt += 1


main()
