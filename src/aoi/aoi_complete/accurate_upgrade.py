# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准AOI2.0例行更新
"""
import sys
import os
from pathlib import Path
root_dir = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_dir.as_posix())
sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext


def progress(ctx: AoiCompleteContext):
    """
    精准AOI2.0例行更新
    :return:
    """
    print("开始执行精准2.0转换")
    # 查询大门语义化BID
    gate_semantics_list = ctx.pg_query_dao.get_gate_semantics_bid_list()
    if gate_semantics_list is None or len(gate_semantics_list) == 0:
        print("查询大门语义化BID失败")
        return
    bid_list = [gate_semantics[0] for gate_semantics in gate_semantics_list]
    # 更新精准AOI2.0例行
    ctx.pg_rw_dao.update_accurate_aoi_complete(bid_list)
    return


if __name__ == '__main__':
    with AoiCompleteContext(init=True) as ctx:
        progress(ctx)
        print("精准2.0转换完成")
