#-*- coding: utf-8 -*-
"""
2023年02月09日15:40:25
交通设施;加油加气站
交通设施;服务区
数量
"""
import time
import psycopg2
import requests
import json


curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}
 
# poiDevConf = {"db": "poi_online",
#             "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
#             "host": "**************", "port": 9432}

poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}


#连接poi测试库
# poiDevDbConn = psycopg2.connect(database=poiDevConf["db"],
#                               user=poiDevConf["user"],
#                               password=poiDevConf["pwd"],
#                               host=poiDevConf["host"],
#                               port=poiDevConf["port"])
# poiDevDao = poiDevDbConn.cursor()

#连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()



def checkSpecialAoi(bid):
    """
    特殊情形判断aoi
    """
    flag = False
    # 学校
    sql = "select std_tag from poi where bid = '{}'".format(bid)
    poiDao.execute(sql)
    data = poiDao.fetchone()
    if data:
        if data[0] == '交通设施;加油加气站':
            return 1
        if data[0] == '交通设施;服务区':
            return 2
    return 0



def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    # 初始化数据
    #获取待处理的数据列表
    oil_num = 0
    service_num = 0
    aoi_gate_list_sql = "select distinct(bid)  \
        from aoi_gate_match_batch_result where status = 1 and level = 2 and valid = 1 limit 2"
    # aoi_gate_list_sql = "select bid,face_id,success_node_id,id from \
        # aoi_gate_match_batch_result where bid in ('10000009795415658129','10000050805904863321','10000053154131216308')"
    poiDao.execute(aoi_gate_list_sql)
    aoi_gate_list = poiDao.fetchall()
    num = len(aoi_gate_list)
    if aoi_gate_list:
        for v in aoi_gate_list:
            bid = v[0]
            status = checkSpecialAoi(bid)
            if status == 2:
                service_num = service_num + 1
            elif status == 1:
                oil_num = oil_num + 1
    print('all:' + str(num))
    print('加油加气站:' + str(oil_num))
    print('服务区:' + str(service_num))

    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

if __name__ == "__main__":
    process()