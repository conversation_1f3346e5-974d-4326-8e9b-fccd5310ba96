#!/bin/bash
DATE=$(date +%Y%m%d)
END_NAME=$(date +%m%d)
#shell执行路径
BASEDIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$BASEDIR/../../../conf/globalConf.sh"
WORKPATH=$TMP_PATH"/aoi_yuanqu_online"
mkdir -p "$WORKPATH"

DATA_SOURCE="ftp://map_data_poi_product:<EMAIL>:8100/xingzhengquhua/edz/edz_AOI/edz_AOI.txt"

#--------------------正式环境
SCRIPT_PATH="${LIB_PATH}/relyscript"
#PSQL="$PG_CONN -d postgres "

#清理进程方法
function killprocess(){
    echo "[INFO] 开始清理${1}进程"
    PROCESS=`ps -ef|grep $1|grep -v grep|grep -v PPID|awk '{print $2}'`
    for i in $PROCESS
    do
        echo "[INFO] 清理${1}进程 [${i}]"
        kill -9 $i
    done
    echo "[INFO] 清理${1}进程结束"
}

#testing  是否具备执行条件    基础环境检查
function testEnvironment(){
    if [ -d "${SCRIPT_PATH}" ]; then
        echo "[ok] SCRIPT_PATH 存在"
    else
        echo "[WARNING] SCRIPT_PATH 不存在"
        exit 1
    fi
    which ${PHPBIN}
    if [ $? -ne 0 ]; then
        echo "[WARNING] PHP 不存在"
        exit 1
    else
        echo "[ok] PHP 存在"
    fi
    which ${PYTHONBIN}
    if [ $? -ne 0 ]; then
        echo "[WARNING] PYTHON 不存在"
        exit 1
    else
        echo "[ok] PYTHON 存在"
    fi
}

#创建文件夹，下载新增数据
function setup(){
    if [ -d "${WORKPATH}/${DATE}/" ]; then
        echo "[INFO] 日期文件夹已经存在，当日文件可能已经备份过"
    else
        # 生成目录结构
        mkdir ${WORKPATH}/${DATE}
        if [ -d "${WORKPATH}/${DATE}/" ]; then
            echo "[INFO] 创建目录：${WORKPATH}/${DATE}/ 成功。"
        else
            echo "[error] 创建目录：${WORKPATH}/${DATE}/ 失败"
            exit 1
        fi
        cd ${WORKPATH}/${DATE}/
    
        #下载文件到本地
        wget "$DATA_SOURCE" -O "${WORKPATH}/${DATE}/aoi_yuanqu"
    fi
}


#更改数据格式，发送上线
function sendOnline(){
    awk -F "\t" '{faceid=$1; shape=$14; bid=$2; name=$3;
        print faceid "\t" shape "\t" bid "\t" name}' ${WORKPATH}/${DATE}/aoi_yuanqu > ${WORKPATH}/${DATE}/input
    ${PYTHONBIN} ${LIB_PATH}/geo_clockwise.py ${WORKPATH}/${DATE}/input ${WORKPATH}/${DATE}/online

    killprocess aoi_online.sh
    echo "[INFO] 上线数据：${WORKPATH}/${DATE}/online  行数："
    wc -l ${WORKPATH}/${DATE}/online
    mkdir ${WORKPATH}/${DATE}/log
    touch ${WORKPATH}/${DATE}/log/aoi-to-pds.log
    sh ${SCRIPT_PATH}/aoi_online.sh ${WORKPATH}/${DATE}/online > ${WORKPATH}/${DATE}/online.log 
    echo "[INFO] 上线完成"

}

function main(){

    testEnvironment;
    setup;
    sendOnline;
    
}

main;
