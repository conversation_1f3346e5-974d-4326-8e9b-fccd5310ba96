# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
公共资料库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class AcceptTraj(PgDao):
    """
    采纳轨迹资料库
    """

    def __init__(self):
        """
        采纳数据库
        """
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "accept_traj" in conf:
            poi_online_r_conf = conf['accept_traj']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("accept_traj 配置不存在")

    def get_traj_by_park_bid(self, park_bid: str, limit=5000):
        """
        采纳率轨迹获取
        """
        args = []
        sql = f"""
            select st_astext(arrived_traj) 
            from park_accept_data 
            where  is_accept = 1
           """
        if park_bid:
            sql += " and reco_park_bid = %s"
            args.append(park_bid)
        sql += " order by id desc"
        if limit:
            sql += " limit %s"
            args.append(limit)
        return self.queryall(sql, args)

    def get_hd_traj_by_geom(self, geom, traj_time=None, src=None, limit=2000):
        """
        高精轨迹获取，byd精准更高
        src = '42' # tesla
        src = '48' # byd
        """
        args = [geom]
        sql = f"""
            select st_astext(geom) 
            from hd_traj 
            where st_intersects(geom,st_geomfromtext(%s,4326))
           """
        if traj_time:
            sql += " and traj_time>=%s"
            args.append(traj_time)
        if src:
            sql += " and src>=%s"
            args.append(src)
        sql += f" order by traj_time desc"
        if limit:
            sql += f" limit %s"
            args.append(limit)
        return self.queryall(sql, args)
