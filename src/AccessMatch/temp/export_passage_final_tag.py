# !/usr/bin/env python3
# encoding=utf-8
"""
【紧急】【五一专项】景区门通行标签建设
prd: https://console.cloud.baidu-int.com/devops/icafe/space/poiinshanghai/planbox/968152/issue?viewId=59176&_limit=30
"""
from pg_tool import PgTool
import sys
import os
import requests
import tqdm

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")


def save_result(result):
    """
    成果写入文件
    """
    file = open("./tmp_passage_final_tag_result.txt", "w")
    for item in result:
        strs = f"""
            {item['jq_bid']}\t{item['jq_name']}\t{item['gate_bid']}\t{item['gate_name']}\t{item['passage_final']}\t{item['gate_node_id']}
        """
        file.write(strs + "\n")
    file.close()


def get_gate_node_id(res):
    """
    获取大门node_id
    """
    with PgTool() as pg:
        for item in res:
            gate_bid = item["gate_bid"]
            sql = f"""
                select b.node_id
                from blu_access a inner join blu_access_gate_rel b
                on a.access_id = b.access_id
                where a.access_poi_bid = '{gate_bid}'
            """
            pg.cursor_aoi.execute(sql)
            node_res = pg.cursor_aoi.fetchone()
            if node_res is None:
                item["gate_node_id"] = ""
            else:
                item["gate_node_id"] = node_res[0]
    return res


def get_passage_final(res):
    """
    获取POI passage_final
    """
    for item in tqdm.tqdm(res):
        item["passage_final"] = ""
        gate_bid = item["gate_bid"]
        if gate_bid is None:
            continue

        res_json = get_info_by_bid(gate_bid)
        if res_json is None or res_json["code"] != 0:
            continue

        passage_final = res_json["data"]["travel_info"]["passage_attribute"][
            "passage_final"
        ]["text"]
        item["passage_final"] = passage_final.strip()
    return res


def get_infos_from_txt():
    """
    读取原始输入
    景区主点bid\t景区名称\t大门bid\t大门名称\n
    """
    res = []
    with open("passage_tag_wait_query.txt", "r") as file:
        lines = file.readlines()  # 读取所有行

    for line in lines:
        fields = line.split("\t")
        if len(fields) < 4:
            continue

        gate_type = ""
        if len(fields) == 5:
            gate_type = fields[4]

        res.append(
            {
                "jq_bid": fields[0].strip(),
                "jq_name": fields[1].strip(),
                "gate_bid": fields[2].strip(),
                "gate_name": fields[3].strip(),
                "gate_type": gate_type,
            }
        )
    return res


def get_info_by_bid(bid):
    """
    获取poi信息
    """
    req = requests.get(
        "https://mapde-poi.baidu-int.com/prod/api/getPoiDetail",
        params={"bid": f"{bid}"},
        timeout=20.0,
    )
    req.close()
    resp = req.json()
    if resp["data"] != 0:
        return None
    return req.json()


if __name__ == "__main__":
    """
    main
    """
    result = get_infos_from_txt()
    result = get_passage_final(result)
    result = get_gate_node_id(result)
    save_result(result)
