<?php
//初始化
$curl = curl_init();
//$curl_g = curl_init();
//设置抓取的url
curl_setopt($curl, CURLOPT_URL, 'http://poi-panchong.baidu-int.com:8090/panchong/api/taskadd');
//'http://cp01-map-ssd-2016-14.epc.baidu.com:8090/panchong/api/taskadd'
//设置获取的信息以文件流的形式返回，而不是直接输出。
curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
//设置post方式提交
curl_setopt($curl, CURLOPT_POST, 1);
//设置post数据
$ftp_add=$argv[1];
$date_str=$argv[2];
$result_file=$argv[3];
$post_data = array(
        "user_name" => "chentong",   //用户名
        "file_type" => "1",     //文件类型，ftp形式的为1
        "task_name" => "AOI",  //创建任务名
        "priority" => "3",  //优先级，1-5，1最低，建议用1
        "note" => "AOI",   //备注
        "pc_type" => "common_b_pc",   //正常判重
        "project_id" => "8", //常规判重
        "anto_flow" => "2", ////是否自动流转 1是，2否
        "file_src_path" => $ftp_add, //输入文件ftp地址
        "cmp_type" => 1, // 使用GPU运算
		);
$fail_result = array(
        "1" => "未分配",
        "2" => "数据拉取中",
        "3" => "数据拉取失败",
        "4" => "数据拉取完成",
        "5" => "分类中",
        "6" => "分类失败",
        "7" => "分类完成",
        "9" => "任务失败",
        "10" => "任务完成",
        "15" => "任务杀死",
        "16" => "上传文件有误",
        );
curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
//执行命令
$data = json_decode(curl_exec($curl), true);
print_r($data);
if($data['status'] === 0 ){
    echo "success\n";
    $task_id=$data['data']['super_task_id'];
    $post_getaddr_data = array(
            "super_task_id" => $task_id,
            );
    sleep(3600);
    $flag=true;
    curl_setopt($curl, CURLOPT_URL, 'http://poi-panchong.baidu-int.com:8090/panchong/api/taskquery');
    while($flag){
        //print_r($post_getaddr_data);
        sleep(300);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_getaddr_data);
        $data_addr = json_decode(curl_exec($curl), true);
        print_r($data_addr);
        if($data_addr['status'] === 0){
            if($data_addr['data'][0]['status']==="10"){
                print_r($data_addr['data'][0]['status']);
                echo "hh"."\n";
                $flag=false;
                $wget_addr=$data_addr['data'][0]['output_ftp_path'];
                file_put_contents($result_file, $date_str."\t".$wget_addr."\n", FILE_APPEND);

            }
			$job_status=$data_addr['data'][0]['status'];
            if($job_status==="3" || $job_status==="6" || $job_status==="9" || $job_status==="15" || $job_status==="16"){
                echo "job fail" . $fail_result[$job_status];
                $flag=false;
                continue;
            }
        }

    }

}else{
    echo "failed\n";
}
//关闭URL请求
curl_close($curl);
?>
