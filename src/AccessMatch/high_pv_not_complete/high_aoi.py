"""
高准aoi统计和闭环处理
"""
from src.common import pgsql
import pandas as pd
import numpy as np
import tqdm
from src.door_naming.strategy_v2 import common
from src.AccessMatch.high_pv_not_complete.flow import utils

TARGET_TAG = "旅游景点,旅游景点;博物馆,旅游景点;动物园,旅游景点;风景区,旅游景点;公园,旅游景点;海滨浴场," \
             "旅游景点;教堂,旅游景点;景点,旅游景点;其他,旅游景点;水族馆,旅游景点;寺庙,旅游景点;文物古迹," \
             "旅游景点;游乐园,旅游景点;植物园,交通设施;飞机场,交通设施;火车站,交通设施;长途汽车站,购物," \
             "购物;百货商场,购物;购物中心,购物;家居建材,购物;市场,教育培训,教育培训;成人教育,教育培训;高等院校," \
             "教育培训;科技馆,教育培训;科研机构,教育培训;培训机构,教育培训;其他,教育培训;特殊教育学校,教育培训;图书馆," \
             "教育培训;小学,教育培训;幼儿园,教育培训;中学,医疗,医疗;疗养院,医疗;其他,医疗;专科医院,医疗;综合医院,运动健身;体育场馆," \
             "文化传媒;广播电视,文化传媒;美术馆,文化传媒;文化宫,文化传媒;展览馆,休闲娱乐;度假村,休闲娱乐;剧院," \
             "房地产,房地产;其他,房地产;写字楼,房地产;住宅区,公司企业,公司企业;厂矿,公司企业;公司,公司企业;农林园艺," \
             "公司企业;其他,公司企业;园区,政府机构;各级政府,政府机构;公检法机构,政府机构;涉外机构,政府机构;行政单位," \
             "政府机构;政治教育机构,政府机构;中央机构,酒店;星级酒店,生活服务;物流公司,汽车服务;汽车检测场," \
             "汽车服务;汽车配件,汽车服务;汽车维修,汽车服务;汽车销售".split(',')


def get_poi_info_by_bid(bid_list):
    """
    根据bid获取poi信息
    :param bid_list:
    :return:
    """
    sql = f"select mid, name, bid, std_tag, mesh_id, city, st_astext(geometry) as geom, relation_bid," \
          f" click_pv, status " \
          f" from poi where bid in :bid_list"
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid_list": tuple(bid_list)})
    dict_data = {}
    for item in ret:
        dict_data[item.bid] = item
    return dict_data


def get_pv_by_bid(bid_list):
    """
    查询pv值
    :param bid_list:
    :return:
    """
    sql = 'select bid, pv from poi_nav_500m_pv where bid in :bid_list'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid_list": tuple(bid_list)})
    dict_data = {}
    for item in ret:
        dict_data[item.bid] = item.pv
    return dict_data


def in_white_list(main_bid_list):
    """
    白名单查询
    :param main_bid_list:
    :return:
    """
    sql = 'select bid from aoi_whitelist where bid in :bid_list'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple(main_bid_list)})
    dict_data = {}
    for item in ret:
        dict_data[item.bid] = 1
    return dict_data


def run(v=1):
    """
    主筛选函数
    :return:
    """
    data_all = []
    sql = 'select main_bid, aoi_complete from blu_face_complete'
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql)
    groups = np.array_split(ret, len(ret) // 1000 + 1)
    all_high_aoi = 0
    complete = 0
    for group in tqdm.tqdm(groups):
        # 一组数据
        main_bid_list = []
        aoi_complete_dict = {}
        for (main_bid, aoi_complete) in group:
            main_bid_list.append(main_bid)
            aoi_complete_dict[main_bid] = aoi_complete
        poi_all = get_poi_info_by_bid(main_bid_list)
        pv_all = get_pv_by_bid(main_bid_list)
        in_white_dict = in_white_list(main_bid_list)
        aoi_level_dict = common.multi_get_aoi_by_main_bid(main_bid_list)
        for main_bid in main_bid_list:
            if main_bid in pv_all and main_bid in poi_all:
                pv = pv_all[main_bid]
                poi_detail = poi_all[main_bid]
                current_aoi = aoi_level_dict.get(main_bid, None)
                if not current_aoi or current_aoi.aoi_level not in (1, 2):
                    continue
                std_tag = poi_detail.std_tag
                if std_tag not in TARGET_TAG:
                    continue
                if v == 1:
                    if (poi_detail.city == '上海市' and pv >= 10) or (poi_detail.city != '上海市' and pv >= 30):
                        data_all.append({
                            'bid': main_bid,
                            'aoi_complete': aoi_complete_dict[main_bid],
                            'in_white': main_bid in in_white_dict,
                            "pv": pv,
                            'click_pv': poi_detail.click_pv,
                            'aoi_level': current_aoi.aoi_level,
                            "name": poi_detail.name,
                            "city": poi_detail.city,
                        })
                        all_high_aoi += 1
                        if int(aoi_complete_dict[main_bid]) >= 3 or main_bid in in_white_dict:
                            complete += 1
                elif v == 2:
                    if pv >= 30 or poi_detail.click_pv >= 300:
                        data_all.append({
                            'bid': main_bid,
                            'aoi_complete': aoi_complete_dict[main_bid],
                            'in_white': main_bid in in_white_dict,
                            "pv": pv,
                            'click_pv': poi_detail.click_pv,
                            'aoi_level': current_aoi.aoi_level,
                            "name": poi_detail.name,
                            "city": poi_detail.city,
                        })
                        all_high_aoi += 1
                        if int(aoi_complete_dict[main_bid]) >= 3 or main_bid in in_white_dict:
                            complete += 1
    pd.DataFrame(data_all).to_csv(f"高准集合_{v}.tsv", sep='\t', index=False)
    print(f'总量:{all_high_aoi}, 高准集合量: {complete}')


def st(version):
    """
    统计数据
    :return:
    """
    city_list = ['北京市', '上海市', '广州市', '深圳市', '重庆市', '成都市']
    df = pd.read_csv(f"高准集合_{version}.tsv", converters={"bid": str, 'aoi_complete': int}, sep='\t')
    df_city = df[df['city'].isin(city_list)]
    df_city.to_csv(f"高准城市集合_{version}.tsv", sep='\t', index=False)
    exit(100)
    df_common = df[df['aoi_level'] == 2]
    df_common_complete = df_common[df_common['aoi_complete'] >= 3 | df_common['in_white']]
    df_common_not_complete = df_common[df_common['aoi_complete'] < 3 & ~df_common['in_white']]
    df_common_not_complete.to_csv("高准基础院落集合_未完成.tsv", sep='\t', index=False)

    df_gather = df[df['aoi_level'] == 1]
    df_gather_complete = df_gather[df_gather['aoi_complete'] >= 3 | df_gather['in_white']]
    rate = round(((len(df_common_complete) + len(df_gather_complete)) / (len(df_common) + len(df_gather))) * 100, 4)
    print(f"聚合院落: {len(df_gather)}, 完成度: {len(df_gather_complete)},"
          f" 比例: {round((len(df_gather_complete) / len(df_gather)), 4)}")
    print(f"普通院落: {len(df_common)}, 完成度: {len(df_common_complete)}, "
          f"比例: {round((len(df_common_complete) / len(df_common)), 4)}")
    print(f"总量: {len(df_common) + len(df_gather)}, 闭环: {len(df_common_complete) + len(df_gather_complete)} ,"
          f" 比例: {rate}")
    df_complete_not_complete_bids = df_common_not_complete['bid'].tolist()
    for bid in tqdm.tqdm(df_complete_not_complete_bids, desc='添加到闭环集合'):
        utils.add_to_flow(bid, 2)


if __name__ == '__main__':
    v = 2
    run(v)
    st(v)
