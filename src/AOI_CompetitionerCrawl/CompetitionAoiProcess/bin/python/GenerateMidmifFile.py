#encoding=gbk
"""
Generate Mapinfo Style File
<EMAIL> at 20171012
"""

import os
import sys
import shapely.wkt
import shapely.geometry

def Main():
    """
    Main Function
    """
    mif_outfile = open(sys.argv[2] + ".mif", "w")
    mid_outfile = open(sys.argv[2] + ".mid", "w")   
    print >> mif_outfile, "%s\n%s\n%s" % ("Version 300", \
        "Charset \"WindowsSimpChinese\"", "Delimiter \", \"")
    print >> mif_outfile, "%s" % ("CoordSys NonEarth Units \"m\" \
        Bounds (-40075452.7386, -19928981.8896) (40075452.7386, 19928981.8896)")
    print >> mif_outfile, "%s\n    %s\n    %s\n    %s\n    %s\n    %s\n%s" % ("Columns 5",\
        "mid Char(64)", "bid Char(64)", "name Char(64)", "address Char(64)", \
        "faceid_list Char(64)", "Data")
    with open(sys.argv[1], "r") as infile:
        for line in infile.readlines():
            mid = line.split('\t')[0]
            bid = line.split('\t')[2]
            name = line.split('\t')[3]
            address = line.split('\t')[10]
            faceid_list = line.split('\t')[13]
            polygon = shapely.wkt.loads(line.split('\t')[1])
            if isinstance(polygon, shapely.geometry.multipolygon.MultiPolygon):
                parts = list(polygon.geoms)
                for item in parts:
                    coor_list = list(item.exterior.coords)
                    print >> mid_outfile, "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"" % (mid, \
                        bid, name, address, faceid_list)
                    print >> mif_outfile, "%s" % ("Region 1")
                    print >> mif_outfile, "  %s" % (str(len(coor_list)))
                    for pt in coor_list:
                        tmp_pt = shapely.geometry.Point(pt)
                        print >> mif_outfile, "%s %s" % (str(tmp_pt.x), str(tmp_pt.y))
                    print >> mif_outfile, "    %s" % ("Pen (1,2,0)")
            if isinstance(polygon, shapely.geometry.multipolygon.Polygon):
                coor_list = list(polygon.exterior.coords)
                print >> mid_outfile, "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"" % (mid, \
                    bid, name, address, faceid_list)
                print >> mif_outfile, "%s" % ("Region 1")
                print >> mif_outfile, "  %s" % (str(len(coor_list)))
                for pt in coor_list:
                    tmp_pt = shapely.geometry.Point(pt)
                    print >> mif_outfile, "%s %s" % (str(tmp_pt.x), str(tmp_pt.y))
                print >> mif_outfile, "    %s" % ("Pen (1,2,0)")
    mid_outfile.close()
    mif_outfile.close()
    infile.close()              

if __name__ == '__main__':
    Main()
