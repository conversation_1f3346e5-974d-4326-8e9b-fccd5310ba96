#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../conf/globalConf.sh
date=$(date +"%Y%m%d")

RESULT_PATH="$DATA_PATH/poi"
mkdir -p "$RESULT_PATH"

if [ -f "$RESULT_PATH/top.client" ]; then
  mv "$RESULT_PATH/top.client" "$RESULT_PATH/top.client.bak.$date"
fi
wget ftp://map:<EMAIL>/home/<USER>/client_pv/output/top.client -O "$RESULT_PATH/top.client"

if [ ! -f "$RESULT_PATH/top.client" ]; then
  sh "$LIB_PATH/sendMail.sh" "pv data get failed"
  cp "$RESULT_PATH/top.client.bak.$date" "$RESULT_PATH/top.client"
fi

#AFS_COMMAND="$AFS_SHELL --username=poi-platform --password=4a276f537e8f71dbe1a833ca8c2fa2d6"
#POI_AFS_PATH="afs://kunpeng.afs:9902/user/poi-platform/data_extration/POI-Data-chouqu-222/"

