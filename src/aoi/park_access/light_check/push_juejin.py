"""
推送掘金 step 2
"""
from distutils.log import info
import sys
import os
import time
import hashlib 
import tqdm
import requests
import subprocess
import math
import json
import copy 
import shutil
import tarfile
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import nearest_points
from shapely.affinity import translate
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.light_check import push_juejin_context
from src.aoi.park_access.light_check import config
from src.aoi import utils
from src.aoi.park_access import park_utils
from collections import defaultdict, deque
import logging as log 
log.basicConfig(level=log.ERROR, format='%(asctime)s %(filename)s[line:%(lineno)d] %(message)s')


light_status = config.light_status
light_check_status = config.light_check_status


config_dict = {
    "limit": 25000,             # 下发总量
    "batch_num": 500,           # 每个压缩包的情报量
}

def process(ctx):
	"""
	开始选图
	"""
	pipe_list = pipeline.Pipeline(
        # init 工作目录
        init_workplace,

		#下载图片
        download_img,

        # 打包,请求轻核实接口
        push_light_check,

        # 更新数据库  重置状态
        update_db,
	)
	pipe_list(ctx)


def update_db(ctx: push_juejin_context.Context, proceed):
    """
    更新数据库
    """
    if ctx.process_info.process_status != light_status['light_check_pushed']:
        return proceed()

    # 清空工作目录
    logger.info(f"清空工作目录:{ctx.process_info.workplace_dir}")
    if 'workplace' in ctx.process_info.tar_path and os.path.exists(ctx.process_info.tar_path):
        os.remove(ctx.process_info.tar_path)
        logger.info(f"已清空压缩包目录:{ctx.process_info.tar_path}")
    if 'workplace' in ctx.process_info.img_dir and os.path.exists(ctx.process_info.img_dir):
        shutil.rmtree(ctx.process_info.img_dir)
        logger.info(f"已清空图片目录:{ctx.process_info.img_dir}")


    # 更新数据库状态
    logger.info(f"开始更新任务状态")
    if len(ctx.process_info.light_check_id_list) > 0: 
        update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        push_time = update_time
        update_num = ctx.dao.poi_online_rw.update_light_check_status_by_id(
            light_check_id_list=ctx.process_info.light_check_id_list, 
            status=ctx.process_info.process_status,
            push_time=push_time,
            update_time=update_time,
            batch=ctx.process_info.batch)
        logger.info(f"更新完成：受影响行数为：{update_num}")
    
    # 重置任务状态
    if ctx.process_info.total_light_num != ctx.process_info.now_light_num:
        logger.info(f"非最后一组，重置流程状态:{ctx.process_info.total_light_num},{ctx.process_info.now_light_num}")
        reset_process(ctx)
    return  proceed()


def push_light_check(ctx: push_juejin_context.Context, proceed):
    """
    推送轻核实
    """
    if ctx.light_check.status != light_status['light_check_wait']:
        return proceed()
    light_check_id_list_num = len(ctx.process_info.light_check_id_list)
    if light_check_id_list_num == ctx.process_info.batch_num or ctx.process_info.total_light_num == ctx.process_info.now_light_num:
        # 到达批次数上线或者最后一个 开始打包
        create_tar(ctx)
        if not os.path.exists(ctx.process_info.tar_path):
            logger.error('tar-error')
            return proceed()
        upload_afs(ctx)
        # todo 加一下 判断afs是否存在压缩包了
        # 推送路淘
        push_to_lutao(ctx)
        ctx.process_info.process_status = light_status['light_check_pushed']
    return proceed()
    

def push_to_lutao(ctx):
    """
    发送到路淘
    """
    tar_name = ctx.process_info.batch + '.tar'
    data = {
        "afs_path": "/user/map-data-streeview/aoi-ml/parking/access/access_check/{}".format(tar_name),
        "token": "gD2fI9UKZUtYhvww",
        "type": 3
    }
    print('发送请求', json.dumps(data))
    rep = requests.post('http://inner-lutao.baidu-int.com/audit/api/inter/parkingfrontpic', json=data)
    print(rep.text)   

def upload_afs(ctx):
    """
    上传文件到 AFS
    """
    tar_path = ctx.process_info.tar_path

    process = subprocess.Popen(
        ['/home/<USER>/chengqiong/tool/afs_agent/bin/afsshell', '--username=map-data-streeview', '--password=map-data-streeview', 'put',
         '{}'.format(tar_path),
         f"afs://aries.afs.baidu.com:9902/user/map-data-streeview/aoi-ml/parking/access/access_check/{ctx.process_info.batch}.tar"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    stdout, stderr = process.communicate()
    print(stdout)
    print(stderr)
    return stdout, stderr


def create_tar(ctx):
    """
    将指定目录下的所有内容打包成 tar 文件
    :param path: 要打包的目录路径
    :param out_file: 输出 tar 文件的路径
    """
    path = ctx.process_info.img_dir
    out_file = ctx.process_info.tar_path
    if not os.path.isdir(path):
        raise ValueError(f"{path} 不是有效的目录")
    with tarfile.open(out_file, "w") as tar:
        for root, _, files in os.walk(path):
            print(f"当前目录: {root}, 文件数: {len(files)}")
            for file in files:
                if file.lower().endswith(".jpg"):
                    full_path = os.path.join(root, file)
                    full_path = str(full_path)
                    current_path_dir = str(path)
                    # 计算相对路径
                    arc_name = os.path.relpath(full_path, current_path_dir)
                    tar.add(full_path, arcname=arc_name)
    print(f"目录 {path} 已打包成 {out_file}")


def init_workplace(ctx: push_juejin_context.Context, proceed):
    """
    初始化工作目录
    total_light_num: int = field(default=0, init=False) # 总待轻核实数
    now_light_num: int = field(default=0, init=False)   # 当前任务数
    batch: str = field(default="", init=False)          # 批次
    batch_num: int = field(default=0, init=False)       # 每个批次包含停车场数量
    process_status: int = field(default=0, init=False)  # 流程状态
    total_img_num: int = field(default=0, init=False)   # 总图片数
    light_check_id_list: list = field(default_factory=list, init=False) # 下载图片成功的id 打包成功并下发后轻核实 统一更新状态
    workplace_dir: str = field(default="", init=False)  # 工作目录
    img_dir: str = field(default="", init=False)        # 图片目录
    tar_path: str = field(default="", init=False)       # 压缩包完整文件路径

    """
    if ctx.process_info.batch == '':
        reset_process(ctx)
    if not os.path.exists(ctx.process_info.workplace_dir):
        os.makedirs(ctx.process_info.workplace_dir)
    return proceed()


def reset_process(ctx):
    """
    重置流程数据
    """
    # 通过batch 控制初始化
    ctx.process_info.batch = datetime.now().strftime("%Y%m%d%H%M%S")
    ctx.process_info.light_check_id_list = []
    ctx.process_info.process_status = 0
    ctx.process_info.img_dir = os.path.join(ctx.process_info.workplace_dir, ctx.process_info.batch)
    if not os.path.exists(ctx.process_info.img_dir):
        os.makedirs(ctx.process_info.img_dir)
    ctx.process_info.tar_path = os.path.join(ctx.process_info.workplace_dir, ctx.process_info.batch + '.tar')


def download_img(ctx: push_juejin_context.Context, proceed):
    """
    下载图片
    """
    if ctx.light_check.status != light_status['light_check_wait']:
        return proceed()
    img_list = ctx.light_check.img_list
    img_num = ctx.light_check.img_num
    new_img_num = 0
    intelligence_id = ctx.light_check.intelligence_id
    light_check_id = ctx.light_check.id
    img_dir = ctx.process_info.img_dir
    for img in img_list:
        new_name = f"{intelligence_id}_{img_num}_{intelligence_id}_{light_check_id}_{new_img_num}.jpg"
        bos_key = img['bos_key']
        new_name_path = os.path.join(img_dir, new_name)
        res = download_img_req(bos_key, new_name_path)
        if res:
            new_img_num += 1
    if new_img_num == img_num:
        # 等待打包
        ctx.process_info.light_check_id_list.append(light_check_id)
        ctx.process_info.now_light_num += 1
    else:
        # 下载失败了 更新名字再 下发
        logger.info(ctx.light_check)
        ctx.light_check.status = light_status['light_check_fail']
        ctx.light_check.msg = "下载图片失败"
    return proceed()


def download_img_req(bos_key, file_name):
    """
    请求下载
    """
    url = f"http://m.map.baidu.com:8011/{bos_key}"
    try:
        r = requests.get(url)
        if r.status_code != 200:
            time.sleep(0.05)
            r = requests.get(url)
        if r.status_code == 200:
            with open(file_name, 'wb') as f:
                f.write(r.content)
            return True
        return False
    except Exception as e:
        print(e)
        return False


def main():
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    bids_arr = None

    bid_file = sys.argv[1]
    bid = sys.argv[2]
    if bid != '0':
        bids_arr = [bid]
    if bid_file != '0':
        with open(bid_file, "r") as f:
            bids_arr = f.read().replace("\n", ",").strip(",").split(",")
    limit = config_dict['limit']
    # light_check_list = poi_online_query.get_light_check_list(bids=bids_arr, 
    light_check_list = poi_online_query.get_light_check_list_cp(bids=bids_arr, 
        status=[light_status['light_check_wait']],
        intelligence_type=['park_access_overground_add', 'park_access_underground_add', 'park_access_overground_out_add'],
        # intelligence_type=['park_access_underground_add'],
        limit=limit)
    if not light_check_list:
        logger.error("No light_check_list found")
        return
    logger.info(f"待投放掘进{len(light_check_list)}")
    
    process_info = push_juejin_context.ProcessInfo()
    # process_info.start_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    process_info.total_light_num = len(light_check_list)
    process_info.batch_num = config_dict['batch_num']     # 每个批次包任务数量  正式改为 500
    process_info.workplace_dir = os.path.join(os.path.dirname(__file__), 'workplace')
    for light_check in tqdm.tqdm(light_check_list):
        # light_check_id = light_check['id']
        # logger.info(f"start--light_check_id-----\t{light_check_id}")
        # ctx = push_juejin_context.Context(light_check, process_info)
        # process(ctx)
        # exit(1)
        try:
            light_check_id = light_check['id']
            logger.info(f"start---light_check_id-----\t{light_check_id}")
            ctx = push_juejin_context.Context(light_check, process_info)
            process(ctx)
        except Exception as e:
            #log.error("HTTP GET request failed: {} \n{}".format(e, url))
            logger.info(f"light_check start-error:{e}")
            logger.info(light_check)


if __name__ == '__main__':
	main()
