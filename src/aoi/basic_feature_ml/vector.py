# -*- coding: utf-8 -*-
"""
绿地
"""
import math
from osgeo import ogr, gdal, gdalconst
import json
import shapely.ops
import os
import sys
import glob


from shapely.validation import make_valid
import psycopg2
import shapely
from shapely import *
from shapely import wkt, geometry
import numpy as np

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
import PgQuery
import CommTool
import smooth_wkt
from aoi.dao.poi_online_query import PoiOnlineQuery

dao = PgQuery.PgQuery()

poiOnlineConf = {"db": "poi_online", "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
                 "host": "gzbh-ns-map-de16.gzbh.baidu.com",
                 "port": 8532}
# 连接线上库
poiOnlineDbConn = psycopg2.connect(database=poiOnlineConf["db"],
                                   user=poiOnlineConf["user"],
                                   password=poiOnlineConf["pwd"],
                                   host=poiOnlineConf["host"],
                                   port=poiOnlineConf["port"])
poiOnlineDao = poiOnlineDbConn.cursor()

poi_online_query = PoiOnlineQuery()


# 抽稀距离
SIMPLIFY_DISTANCE = 0.00003

def green_land(discern_result_dir, batch, geojson_file_dir):
    """
    绿地
    :param discern_result_dir: 识别成果目录
    :param geojson_file_dir:
    :return:
    """
    # 开始矢量化
    files = glob.glob("{}/*.png".format(discern_result_dir))
    for file in files:
        vector_file = img_2_geojson(file, batch, geojson_file_dir)
        if vector_file is None:
            print("矢量化失败：{}".format(file))
            break
        # 矢量化成果入库
        # res = save_vector_result(vector_file, batch)
        # if res is None:
        #     print("矢量化成果入库失败：{}".format(file))
        #     break
        print("{}矢量化完成".format(file))


def img_2_geojson(img_path, batch, out):
    """ 把图片转换成geoJson
    :param img_path:  图片地址
    :param out:  json文件地址
    :param batch:  批次
    :return: 矢量化成果文件
    """
    # 获取识别图原始信息
    origin_image_info = poi_online_query.get_image_geom(batch, img_path.rsplit("/", 1)[1])
    if origin_image_info is None:
        print("图片不存在：{}".format(img_path))
        return True
    geom_text = origin_image_info[1]
    geom = wkt.loads(geom_text)
    minx, miny, maxx, maxy = geom.bounds
    gdal.AllRegister()
    ds = gdal.Open(img_path, gdalconst.GA_ReadOnly)

    ds.SetGeoTransform((minx,
                        0.000002680394224609375000,
                        0,
                        maxy,
                        0,
                        -0.000002682494975000000000))
    # 波段，从1开始
    src_band = ds.GetRasterBand(1)
    # osr.SpatialReference()

    image_name = os.path.basename(img_path)
    image_hash = image_name.split(".")[0]

    drv = ogr.GetDriverByName("GeoJSON")
    geojson_file = "{}/{}.json".format(out, image_hash)
    dst_ds = drv.CreateDataSource(geojson_file)
    fld = ogr.FieldDefn("DN", ogr.OFTInteger)
    dst_layer = dst_ds.CreateLayer(out, srs=None)
    dst_layer.CreateField(fld)
    dst_field = dst_layer.GetLayerDefn().GetFieldIndex("DN")
    options = ["8CONNECTED=8"]
    # 矢量化
    result = gdal.Polygonize(src_band, None, dst_layer, dst_field, options, callback=None)
    if result is None:
        return False
    # 矢量化文件入库
    # return update_image_vector_file_by_id("{}.json".format(image_hash), origin_image_info[0])

def save_vector_result(vector_file, batch):
    """
    矢量化成果入库
    :param vector_file: 矢量化文件
    :param batch: 批次
    :return:
    """
    green_discern_wkt = None
    with open(vector_file) as geojson1:
        geojson = json.load(geojson1)
        for values in geojson["features"]:
            if values["properties"]["DN"] == 1:
                if green_discern_wkt is None:
                    green_discern_wkt = shapely.geometry.shape(values['geometry'])
                else:
                    green_discern_wkt = green_discern_wkt.union(shapely.geometry.shape(values['geometry']))
    return update_image_vector_result_by_id(green_discern_wkt.wkt, batch)


def after_progress(batch, geojson_file_dir):
    """
    绿地后处理
    :param batch: 批次
    :return:
    """
    # 查询矢量化后的所有识别框
    # id = 0
    # limit = 1000
    # discern_wkt_list = []
    # discern_id_list = []
    # discern_wkt_simplify_distance = 0.00003
    # print("开始后处理")
    # while 1:
    #     discern_info_list = dao.get_geojson_list_by_id(id, batch, limit)
    #     if discern_info_list is None or len(discern_info_list) == 0:
    #         print("识别框查询完成")
    #         break
    #     for discern_info in discern_info_list:
    #         id = discern_info[0]
    #         geojson_file = "{}/{}".format(geojson_file_dir, discern_info[1])
    #         with open(geojson_file) as geojson1:
    #             geojson = json.load(geojson1)
    #             for values in geojson["features"]:
    #                 if values["properties"]["DN"] == 1:
    #                     object = shapely.geometry.shape(values['geometry'])
    #                     discern_wkt_list.append(make_valid(object.simplify(discern_wkt_simplify_distance, preserve_topology=True)))
    #                     discern_id_list.append(id)
    #     print("矢量化进度：{}".format(len(discern_wkt_list)))
    #
    # # 开始合并间距小于5米的框
    # print("开始合并")
    # merge_result_wkt_list = merge_green_discern_wkt(discern_wkt_list, discern_id_list)

    # 使用现有合并成果直接开始后处理
    shapely_wkt_list = []
    green_merge_result_list = dao.get_green_merge_result_by_batch()
    for green_merge_info in green_merge_result_list:
        shapely_wkt_list.append(wkt.loads(green_merge_info[1]))

    # 消除合并间隙
    shapely_wkt_list = deal_merge_gap(split_multi_polygon(shapely_wkt_list))


    # 开始面擦除处理 建筑面擦除
    print("开始建筑面擦除")
    shapely_wkt_list = diff_bud_green_wkt(shapely_wkt_list)

    # 建筑面空洞处理
    shapely_wkt_list = deal_green_hole_wkt(shapely_wkt_list)

    # 腐蚀处理
    shapely_wkt_list = corrode_bud_wkt_deal(shapely_wkt_list)

    # LD道路面擦除
    print("开始LD道路面擦除")
    shapely_wkt_list = diff_ld_road_green_wkt(shapely_wkt_list)

    # 无LD外部路道路面擦除
    print("开始无LD外部道路面擦除")
    shapely_wkt_list = diff_no_ld_road_green_wkt(shapely_wkt_list)

    # 内部路擦除
    print("开始内部路擦除")
    shapely_wkt_list = diff_inner_road_green_wkt(shapely_wkt_list)

    # 根据AOI面及道路面提取识别面
    print("开始AOI与道路面提取")
    shapely_wkt_list = get_discern_wkt_by_aoi_and_buffer_road(shapely_wkt_list, batch)

    # 过滤角度较小的夹角
    shapely_wkt_list = filter_min_angle_point(shapely_wkt_list)

    # 圆滑处理
    shapely_wkt_list = smooth_shapely_wkt(shapely_wkt_list)

    # 成果入库
    last_deal_result_save(shapely_wkt_list, batch)
    print("处理完成")


def merge_green_discern_wkt(discern_wkt_list, discern_id_list):
    """
    合并绿地识别框，间距小于5米的绿地面合并，去除间隙
    :param discern_wkt_list: 识别矢量后的框
    :return: 合并后的shapely list
    """
    # 构建R树
    # tree = STRtree(discern_wkt_list)
    print("开始合并，识别框总量：{}".format(len(discern_wkt_list)))
    already_merge_wkt_index_list = []
    merge_result_wkt_list = []
    while True:
        print("识别框总量：{}, 已合并识别框：{},合并成果：{}".format(len(discern_wkt_list),
              len(already_merge_wkt_index_list), len(merge_result_wkt_list)))
        merge_wkt = None
        # 先取一个还没有合并到WKT作为初始合并的WKT
        for key, temp_wkt in enumerate(discern_wkt_list):
            if key in already_merge_wkt_index_list:
                continue
            merge_wkt = temp_wkt
            already_merge_wkt_index_list.append(key)
            break

        if merge_wkt is None:
            break
        # 查询与当前WKT距离小于 5 米的WKT开始合并
        merge_discern_id = []
        record = {}
        origin_geom = None
        while True:
            is_continue_merge = False
            for key, wkt_shapely in enumerate(discern_wkt_list):
                print("当前总量：{},合并进度：{},合并成果：{}".format(len(discern_wkt_list), key, len(merge_result_wkt_list)))
                if key in already_merge_wkt_index_list:
                    continue
                # 查询WKT与合并的WKT距离，小于5米就合并
                if merge_wkt.distance(wkt_shapely) > 0.00005:
                    continue
                merge_wkt = merge_polygon(merge_wkt, wkt_shapely)
                is_continue_merge = True
                already_merge_wkt_index_list.append(key)
                merge_discern_id.append(str(discern_id_list[key]))
                if origin_geom is None:
                    origin_geom = wkt_shapely
                else:
                    origin_geom = origin_geom.union(wkt_shapely)
            if not is_continue_merge:
                break
        merge_result_wkt_list.append(merge_wkt)
        #合并结果入库
        record["geom"] = merge_wkt
        if origin_geom is None:
            record["origin_discern_geom"] = merge_wkt
            record["discern_id"] = ",".join(merge_discern_id)
        else:
            record["origin_discern_geom"] = origin_geom
            record["discern_id"] = str(discern_id_list[key])
        insert_green_merge_result(record)

    print("合并完成：{}".format(len(merge_result_wkt_list)))
    return merge_result_wkt_list


def merge_polygon(polygon1, polygon2):
    """
    buffer 合并
    :param polygon1:
    :param polygon2:
    :return:
    """
    # 两个几何体分别buffer 后去交集
    distance = polygon1.distance(polygon2)

    buffer_wkt1 = polygon1
    buffer_wkt2 = polygon2
    init_buffer_distance = distance / 5
    if distance < 0.000001:
        init_buffer_distance = 0.000005
    elif distance < 0.000005:
        init_buffer_distance = 0.000006
    elif distance < 0.00001:
        init_buffer_distance = 0.000007
    while True:
        buffer_distance = init_buffer_distance
        buffer_wkt1 = buffer_wkt1.buffer(buffer_distance, 16, 2, 2)
        while not buffer_wkt1.intersects(polygon2):
            buffer_distance = buffer_distance + distance / 5
            buffer_wkt1 = buffer_wkt1.buffer(buffer_distance, 16, 2, 2)
        buffer_distance = init_buffer_distance
        buffer_wkt2 = buffer_wkt2.buffer(buffer_distance, 16, 2, 2)
        while not buffer_wkt2.intersects(polygon1):
            buffer_distance = buffer_distance + distance / 5
            buffer_wkt2 = buffer_wkt2.buffer(buffer_distance, 16, 2, 2)
        # 获取两个几何体buffer之后交集然后合并
        intersects_wkt = buffer_wkt1.intersection(buffer_wkt2)
        if (intersects_wkt.boundary.length * 100000 > 100 and intersects_wkt.area * \
            10000000 < 0.1) or intersects_wkt.area * 10000000 < 0.01:
            init_buffer_distance = init_buffer_distance + init_buffer_distance
            continue
        break

    last_merge_wkt_shapely = intersects_wkt.union(polygon1).union(polygon2)
    if last_merge_wkt_shapely.geom_type == "MultiPolygon":
        multi_wkt_shapely_list = last_merge_wkt_shapely.geoms
        filter_small_wkt = None
        for wkt_shapely in multi_wkt_shapely_list:
            if wkt_shapely.area * 10000000 < 0.1:
                continue
            if filter_small_wkt is None:
                filter_small_wkt = wkt_shapely
                continue
            filter_small_wkt = filter_small_wkt.union(wkt_shapely)
        return filter_small_wkt.simplify(0.00001, preserve_topology=True)

    return last_merge_wkt_shapely.simplify(0.00001, preserve_topology=True)


def merge_polygon2(polygon1, polygon2):
    """
    合并polygon 先取凸包 再diff调凸出来与原来polygon不相交的WKT
    :param polygon1:
    :param polygon2:
    :return:
    """
    # 先取凸包
    convex_wkt = polygon1.union(polygon2).convex_hull
    # diff出来多余的WKT
    need_delete_wkt_list = []
    if convex_wkt.difference(polygon1).geom_type == "MultiPolygon":
        for geom in convex_wkt.difference(polygon1).geoms:
            if polygon2.intersects(geom):
                continue
            need_delete_wkt_list.append(geom)
    if convex_wkt.difference(polygon2).geom_type == "MultiPolygon":
        for geom in convex_wkt.difference(polygon2).geoms:
            if polygon1.intersects(geom):
                continue
            need_delete_wkt_list.append(geom)
    # 删除多余的WKT
    for need_delete_wkt in need_delete_wkt_list:
        convex_wkt = convex_wkt.difference(need_delete_wkt)
    return convex_wkt


def diff_bud_green_wkt(wkt_shapely_list):
    """
    擦除识别面 建筑面
    :param wkt_list: 合并后的绿地面
    :return:
    """
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    diff_bud_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "BUD_DIFF"}
        print("建筑面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 查询相交建筑面
        bud_face_list = dao.get_building_by_geom(wkt_shapely.wkt)
        if bud_face_list is None or len(bud_face_list) == 0:
            diff_bud_result.append(wkt_shapely)
            insert_green_after_progress_rcord(deal_progress_record)
            continue
        struct_id = []
        for bud_face in bud_face_list:
            if not wkt_shapely.overlaps(wkt.loads(bud_face[1])):
                continue
            struct_id.append(bud_face[0])
            wkt_shapely = check_valid_shapely(wkt_shapely.difference(wkt.loads(bud_face[1])))
        diff_bud_result.append(wkt_shapely)

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(struct_id)
        deal_progress_record["geom"] = wkt_shapely.wkt
        insert_green_after_progress_rcord(deal_progress_record)
    return diff_bud_result


def diff_ld_road_green_wkt(wkt_shapely_list):
    """
    根据LD道路面擦除
    LD道路面：nav_lane_group 的boundary_list 第一个与最后一个就是nav_lane_marking_pl两条边界线
    :param wkt_shapely_list:
    :return:
    """
    diff_ld_result = []
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "LD_ROAD_DIFF"}
        print("根据LD道路面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 获取LD道路面
        ld_wkt_list, boundary_list = dao.get_ld_road_by_wkt(wkt_shapely.wkt, 0.000005)
        if ld_wkt_list is None or len(ld_wkt_list) == 0:
            diff_ld_result.append(wkt_shapely)
            insert_green_after_progress_rcord(deal_progress_record)
            continue
        # buffer 0.5LD道路面然后裁切识别面
        merge_ld_wkt = wkt.loads(ld_wkt_list[0])
        for ld_wkt in ld_wkt_list:
            merge_ld_wkt = merge_ld_wkt.union(wkt.loads(ld_wkt))
        buffer_ld_wkt = merge_ld_wkt.buffer(0.000005, 16, None, geometry.CAP_STYLE.flat)
        diff_ld_result.append(check_valid_shapely(wkt_shapely.difference(buffer_ld_wkt)))

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(boundary_list)
        deal_progress_record["geom"] = wkt_shapely.wkt
        insert_green_after_progress_rcord(deal_progress_record)
    return diff_ld_result


def diff_no_ld_road_green_wkt(wkt_shapely_list):
    """
    无LD道路面擦除 buffer 0.5m
    :param wkt_shapely_list:
    :return:
    """
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    diff_no_ld_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "NO_LD_ROAD_DIFF"}
        print("根据无LD道路面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 获取非LD道路
        no_ld_road_list = dao.get_not_ld_out_road(wkt_shapely.wkt, 0.000005)
        if no_ld_road_list is None or len(no_ld_road_list) == 0:
            diff_no_ld_result.append(wkt_shapely)
            insert_green_after_progress_rcord(deal_progress_record)
            continue
        # 计算道路宽度
        merge_road_wkt = wkt.loads(no_ld_road_list[0][6])
        link_id_list = []
        for no_lod_road in no_ld_road_list:
            link_id_list.append(no_lod_road[0])
            road_shapely_wkt = wkt.loads(no_lod_road[6])
            road_width = CommTool.get_road_with(
                no_lod_road[3], no_lod_road[1], no_lod_road[4], no_lod_road[5], no_lod_road[2])
            road_buffer_width = road_width * 0.000005
            road_buff_wkt = road_shapely_wkt.buffer(road_buffer_width, 16, None, geometry.CAP_STYLE.flat)
            merge_road_wkt = merge_road_wkt.union(road_buff_wkt)
        diff_wkt_result = check_valid_shapely(wkt_shapely.difference(merge_road_wkt))
        diff_no_ld_result.append(diff_wkt_result)

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(link_id_list)
        deal_progress_record["geom"] = diff_wkt_result.wkt
        insert_green_after_progress_rcord(deal_progress_record)
    return diff_no_ld_result


def diff_inner_road_green_wkt(wkt_shapely_list):
    """
    通过内部路擦除  buffer 车道数>1 && 车道数 * 3m
    :param wkt_shapely_list:
    :return:
    """
    diff_inner_road_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "INNER_ROAD_DIFF"}
        print("根据内部路擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        inner_road_list = dao.get_inner_road(wkt_shapely.wkt)
        if inner_road_list is None or len(inner_road_list) == 0:
            diff_inner_road_result.append(wkt_shapely)
            insert_green_after_progress_rcord(deal_progress_record)
            continue
        merge_inner_road = wkt.loads(inner_road_list[0][1])
        link_id_list = []
        for inner_road in inner_road_list:
            link_id_list.append(inner_road[2])
            inner_road_shapely_wkt = wkt.loads(inner_road[1])
            buffer_width = inner_road[0] * 0.00003
            inner_road_buffer_wkt = inner_road_shapely_wkt.buffer(buffer_width, 16, None, geometry.CAP_STYLE.flat)
            merge_inner_road = merge_inner_road.union(inner_road_buffer_wkt)
        diff_result_wkt = check_valid_shapely(wkt_shapely.difference(merge_inner_road))
        diff_inner_road_result.append(diff_result_wkt)

        #过程量记录入库，用于前期策略快速迭代分析问题
        deal_progress_record["filter_element"] = ','.join(link_id_list)
        deal_progress_record["geom"] = diff_result_wkt.wkt
        insert_green_after_progress_rcord(deal_progress_record)
    return diff_inner_road_result


def corrode_bud_wkt_deal(wkt_shapely_list):
    """
    建筑物腐蚀处理
    :param wkt_shapely_list:
    :return:
    """
    bud_buffer_distance = 0.0001
    corrode_simplify_distance = 0.0001
    buffer_to_origin_area_rate = 0.35
    protected_distance = 0.00007
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    deal_hole_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        corrode_buffer_distance = 0.0001
        print("建筑物腐蚀：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 校验识别框附近是否存在建筑物
        bud_buffer_list = dao.get_building_by_geom(wkt_shapely.buffer(bud_buffer_distance))
        if bud_buffer_list is None or len(bud_buffer_list) == 0:
            deal_hole_result.append(wkt_shapely)
            continue

        # LD 道路buffer 5m 保护下，防止腐蚀完了
        merge_shapely = None
        ld_wkt_list, boundary_list = dao.get_ld_road_by_wkt(wkt_shapely.wkt, protected_distance)
        if ld_wkt_list is not None and len(ld_wkt_list) > 0:
            for ld_wkt in ld_wkt_list:
                if merge_shapely is None:
                    merge_shapely = wkt.loads(ld_wkt)
                else:
                    merge_shapely = merge_shapely.union(wkt.loads(ld_wkt))
        # 非LD外部路 buffer 5m 保护
        no_ld_road_list = dao.get_not_ld_out_road(wkt_shapely.wkt, protected_distance)
        if no_ld_road_list is not None and len(no_ld_road_list) > 0:
            for no_ld_road in no_ld_road_list:
                if merge_shapely is None:
                    merge_shapely = wkt.loads(no_ld_road[6])
                else:
                    merge_shapely = merge_shapely.union(wkt.loads(no_ld_road[6]))
        road_proteced_shapely = None
        if merge_shapely is not None:
            merge_shapely = merge_shapely.buffer(protected_distance, 16, None, geometry.CAP_STYLE.flat)
            road_proteced_shapely = merge_shapely.intersection(wkt_shapely)
            road_proteced_shapely = road_proteced_shapely.buffer(-0.00001).simplify(0.000005).buffer(0.00001)
            road_proteced_shapely = road_proteced_shapely.simplify(0.00003).buffer(0.00003).buffer(-0.00003)


        # 计算buffer距离，防止向内部buffer直接给面干没了
        current_buffer_to_origin_area_rate = wkt_shapely.buffer(-corrode_buffer_distance).area / wkt_shapely.area
        while current_buffer_to_origin_area_rate < buffer_to_origin_area_rate:
            corrode_buffer_distance -= corrode_buffer_distance / 8
            current_buffer_to_origin_area_rate = wkt_shapely.buffer(-corrode_buffer_distance).area / wkt_shapely.area


        corrode_wkt = wkt_shapely.buffer(-corrode_buffer_distance).simplify(
            corrode_simplify_distance).buffer(corrode_buffer_distance)
        intersection_wkt = wkt_shapely.intersection(corrode_wkt)

        # 道路保护面合并回去
        if road_proteced_shapely is not None:
            intersection_wkt = intersection_wkt.union(road_proteced_shapely)
        corrode_result_wkt = intersection_wkt.simplify(corrode_simplify_distance)
        deal_hole_result.append(corrode_result_wkt)
    return deal_hole_result


def deal_merge_gap(wkt_shapely_list):
    """
    消除合并间隙
    :param wkt_shapely_list:
    :return:
    """
    deal_hole_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("消除合并间隙：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        deal_hole_result.append(wkt_shapely.buffer(0.000015).simplify(0.00002).buffer(-0.000015))
    return deal_hole_result


def get_discern_wkt_by_aoi_and_buffer_road(wkt_shapely_list, batch):
    """
    根据AOI与道路识别面提取绿地识别面
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000001000  # 根据AOI过滤小于1000平米 道路100平米
    filter_road_area = 0.0000000100
    aoi_pick_up_buffer_distance = 0.0001 # AOI buffer 10米提取绿地
    road_pick_up_buffer_distance = 0.00005 # 道路 buffer 5米提取绿地
    # 拆分multipolygon
    split_wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    filter_shapely_wkt = []
    for key, wkt_shapely in enumerate(split_wkt_shapely_list):
        pick_up_wkt = None
        print("根据AOI与道路识别面提取绿地识别面：边框总量：{}, 当前进度：{}".format(len(split_wkt_shapely_list), key))
        # 记录处理过程，用于分析迭代
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 0,
            "remark": "", "source":"", "filter_element":"", "step":"PICK_UP"}
        # 根据AOI提取识别面，AOI buffer 10 提取绿地
        intersects_aoi_list = dao.get_intersection_not_sq_aoi(wkt_shapely.wkt, aoi_pick_up_buffer_distance)
        if intersects_aoi_list is not None and len(intersects_aoi_list) > 0:
            # 合并相交AOI面
            merge_intersects_aoi = wkt.loads(intersects_aoi_list[0][1]).buffer(aoi_pick_up_buffer_distance)
            face_id_list = []
            for intersects_aoi in intersects_aoi_list:
                face_id_list.append(intersects_aoi[0])
                buffer_aoi = wkt.loads(intersects_aoi[1]).buffer(aoi_pick_up_buffer_distance)
                merge_intersects_aoi = merge_intersects_aoi.union(buffer_aoi)
            # 相交面积过滤
            intersection_shapely = merge_intersects_aoi.intersection(wkt_shapely)
            intersection_area = intersection_shapely.area
            deal_progress_record["remark"] = "AOI:area|{}".format(intersection_area)
            if intersection_area > filter_aoi_area:
                # filter_shapely_wkt.append(intersection_shapely)
                pick_up_wkt = intersection_shapely
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = "AOI"
                deal_progress_record["filter_element"] = ','.join(face_id_list)

        # 根据LD道路面 buffer 5米提取识别面
        intersection_ld_road_list, boundary_list = dao.get_ld_road_by_wkt(wkt_shapely.wkt, road_pick_up_buffer_distance)
        if intersection_ld_road_list is not None and len(intersection_ld_road_list) > 0:
            merge_ld_wkt = wkt.loads(intersection_ld_road_list[0])
            for intersection_ld_wkt in intersection_ld_road_list:
                merge_ld_wkt = merge_ld_wkt.union(wkt.loads(intersection_ld_wkt).buffer(
                    road_pick_up_buffer_distance, 16, None, geometry.CAP_STYLE.flat))
            ld_intersection_shapely = merge_ld_wkt.intersection(wkt_shapely)
            ld_intersection_area = ld_intersection_shapely.area
            deal_progress_record["remark"] = deal_progress_record["remark"] + ";LD:area|{}".format(ld_intersection_area)
            if ld_intersection_area > filter_road_area:
                # filter_shapely_wkt.append(ld_intersection_shapely)
                if pick_up_wkt is None:
                    pick_up_wkt = ld_intersection_shapely
                else:
                    pick_up_wkt = pick_up_wkt.union(ld_intersection_shapely)
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = deal_progress_record["source"] + "&LD"
                deal_progress_record["filter_element"] = deal_progress_record["filter_element"] + \
                    " & " + ','.join(boundary_list)


        # 根据非LD道路面 buffer 5米提取识别面
        no_ld_road_list = dao.get_not_ld_out_road(wkt_shapely.wkt, road_pick_up_buffer_distance)
        if no_ld_road_list is not None and len(no_ld_road_list) > 0:
            merge_road_wkt = wkt.loads(no_ld_road_list[0][6])
            link_id_list = []
            for no_lod_road in no_ld_road_list:
                link_id_list.append(no_lod_road[0])
                road_shapely_wkt = wkt.loads(no_lod_road[6])
                road_width = CommTool.get_road_with(no_lod_road[3], no_lod_road[1], no_lod_road[4], no_lod_road[5],
                                                    no_lod_road[2])
                road_buffer_width = road_width * road_pick_up_buffer_distance
                road_buff_wkt = road_shapely_wkt.buffer(road_buffer_width, 16, None, geometry.CAP_STYLE.flat)
                merge_road_wkt = merge_road_wkt.union(road_buff_wkt)
            intersection_no_ld_shapely = merge_road_wkt.intersection(wkt_shapely)
            intersection_no_ld_area = intersection_no_ld_shapely.area
            deal_progress_record["remark"] = deal_progress_record["remark"] + ";NO_LD:area|{}".format(
                intersection_no_ld_area)
            if intersection_no_ld_area > filter_road_area:
                # filter_shapely_wkt.append(intersection_no_ld_shapely)
                if pick_up_wkt is None:
                    pick_up_wkt = intersection_no_ld_shapely
                else:
                    pick_up_wkt = pick_up_wkt.union(intersection_no_ld_shapely)
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = deal_progress_record["source"] + "&NO_LD"
                deal_progress_record["filter_element"] = deal_progress_record["filter_element"] + \
                    " & " + ','.join(link_id_list)
                # 过程记录入库
        insert_green_after_progress_rcord(deal_progress_record)
        if pick_up_wkt is not None:
            pick_wkt_list = split_multi_polygon([pick_up_wkt])
            filter_shapely_wkt = filter_shapely_wkt + pick_wkt_list
            for pick_up_wkt in pick_wkt_list:
                deal_progress_record = {"geom": pick_up_wkt.wkt, "batch": batch, "is_valid": 1, "remark": "",
                                        "source": "",
                                        "filter_element": "", "step": "PICK_UP_RESULT"}
                insert_green_after_progress_rcord(deal_progress_record)

    return filter_shapely_wkt


def check_valid_shapely(wkt_shapely):
    """
    校验WKT是否自相交或者异常，修正返回
    :param wkt_shapely:
    :return:
    """
    if wkt_shapely.is_valid:
        return wkt_shapely
    return make_valid(wkt_shapely)


def split_multi_polygon(origin_shapely_wkt_list):
    """
    分割multipolygon
    :param origin_shapely_wkt_list:
    :return:
    """
    split_wkt_shapely_list = []
    for wkt_shapely in origin_shapely_wkt_list:
        if wkt_shapely.is_empty:
            continue
        wkt_shapely = check_valid_shapely(wkt_shapely)

        if wkt_shapely.geom_type in ["GeometryCollection", "MultiPolygon"]:
            for current_wkt_shapely in wkt_shapely.geoms:
                if current_wkt_shapely.geom_type == 'LineString':
                    continue
                split_wkt_shapely_list.append(current_wkt_shapely)
        elif wkt_shapely.geom_type == "Polygon":
            split_wkt_shapely_list.append(wkt_shapely)
        else:
            print("当前类型无效：{}".format(wkt_shapely))
    return split_wkt_shapely_list


def deal_green_hole_wkt(wkt_shapely_list):
    """
    空洞处理
    :param wkt_shapely:
    :return:
    """
    # 空洞过滤面积
    filter_hole_area = 0.0000000250
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    # 开始拆分空洞
    filter_hole_shapely_list = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("空洞过滤处理：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        wkt_str = wkt_shapely.wkt
        wkt_str = wkt_str.replace('POLYGON ((', '').replace('))', '')
        wkt_str_list = wkt_str.split('), (')
        no_hole_shapely = wkt.loads("POLYGON ((" + wkt_str_list[0] + "))")
        for key, item in enumerate(wkt_str_list):
            if key == 0:
                continue
            hole_wkt_shapely = wkt.loads("POLYGON ((" + item + "))")
            if hole_wkt_shapely.area < filter_hole_area:
                continue
            no_hole_shapely = no_hole_shapely.difference(hole_wkt_shapely)
        filter_hole_shapely_list.append(no_hole_shapely)
    return filter_hole_shapely_list


def smooth_shapely_wkt(wkt_shapely_list):
    """
    圆滑处理
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000000040  # 过滤提取之后较小的面积
    smooth_deal_result_list = []
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("圆滑处理：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        if wkt_shapely.area < filter_aoi_area:
            continue
        smooth_wkt_str = smooth_wkt.smooth_polygon(wkt_shapely.simplify(SIMPLIFY_DISTANCE).wkt)
        if smooth_wkt_str is not None:
            if wkt.loads(smooth_wkt_str).area < filter_aoi_area:
                continue
            smooth_deal_result_list.append(wkt.loads(smooth_wkt_str))
    return smooth_deal_result_list


def filter_min_angle_point(wkt_shapely_list):
    """
    角度填平
    :param wkt_shapely_list:
    :return:
    """
    # 过滤角度, 去除细缝
    filter_angle = 60
    new_filter_angle = 30
    filter_angle_shapely_list = []
    wkt_shapely_list = [wkt_shapely.simplify(0.00001) for wkt_shapely in wkt_shapely_list]
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("角度过虑：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        while True:
            wkt_shapely, is_exists_filter_angle = angle_straightening(filter_angle, new_filter_angle, wkt_shapely)
            if not is_exists_filter_angle:
                break
        filter_angle_shapely_list.append(wkt_shapely)
    return filter_angle_shapely_list


def angle_straightening(filter_angle, new_filter_angle, wkt_shapely):
    """
    拉直夹角
    :param filter_angle:
    :param wkt_shapely:
    :return:
    """
    # 拉直面积过滤与比例过滤，如果小于限制值则不拉直，防止失真太多
    straight_area_limit = 0.0000000500
    straight_area_rate_limit = 0.2
    wkt_shapely_area = wkt_shapely.area
    coords_list = wkt_shapely.exterior.coords
    point_list = []
    is_exists_filter_angle = False
    del_coords_keys = []
    for key, coords in enumerate(coords_list):
        if (len(coords_list) - len(del_coords_keys)) <= 4:
            point_list.append(geometry.Point(coords[0], coords[1]))
            continue
        # 如果起点与第二点向量 起点与倒数第二个点的向量夹角小于给定夹角，也过滤
        if key == 0 and len(coords_list) > 4:
            bgein_end_line_a = np.array([coords[0] - coords_list[1][0], coords[1] - coords_list[1][1]])
            bgein_end_line_b = np.array(
                [coords[0] - coords_list[len(coords_list) - 2][0], coords[1] - coords_list[len(coords_list) - 2][1]])
            bgein_end_angle = angle_cal(bgein_end_line_a, bgein_end_line_b)

            # 计算过滤掉的面积
            current_deleted_coords_list = get_deleted_point_wkt_shapely(
                [geometry.Point(coord[0], coord[1]) for coord in coords_list], del_coords_keys + [key])
            if geometry.Polygon(current_deleted_coords_list).is_valid:
                filter_wkt_area = wkt_shapely.difference(check_valid_shapely(
                    geometry.Polygon(current_deleted_coords_list))).area
                if bgein_end_angle < filter_angle and filter_wkt_area < straight_area_limit and filter_wkt_area / \
                    wkt_shapely_area < straight_area_rate_limit:
                    is_exists_filter_angle = True
                    del_coords_keys.append(key)
                    continue
        if key == 0 or key == len(coords_list) - 1 or len(coords_list) == 4:
            point_list.append(geometry.Point(coords[0], coords[1]))
            continue
        # 求向量夹角，拉直后如果产生新的较小夹角则不拉，四个点 p1 p2 p3 p4；line_a:p2->p1  line_b:p2->p3  linc  p3->p1  line_d p3->p4
        line_a = np.array([coords[0] - coords_list[key - 1][0], coords[1] - coords_list[key - 1][1]])
        line_b = np.array([coords[0] - coords_list[key + 1][0], coords[1] - coords_list[key + 1][1]])
        angele = angle_cal(line_a, line_b)

        # 求拉直后产生的新夹角
        new_angle = 180
        if key < (len(coords_list) - 1):
            line_c = np.array(
                [coords_list[key + 1][0] - coords_list[key - 1][0], coords_list[key + 1][1] - coords_list[key - 1][1]])

            if key == len(coords_list) - 2:
                line_d = np.array([coords_list[key + 1][0] - coords_list[1][0],
                                   coords_list[key + 1][1] - coords_list[1][1]])
            else:
                line_d = np.array([coords_list[key + 1][0] - coords_list[key + 2][0],
                                   coords_list[key + 1][1] - coords_list[key + 2][1]])
            new_angle = angle_cal(line_c, line_d)
        # 计算过滤掉的面积
        current_deleted_coords_list = get_deleted_point_wkt_shapely(
            [geometry.Point(coord[0], coord[1]) for coord in coords_list], del_coords_keys + [key])
        if geometry.Polygon(current_deleted_coords_list).is_valid:
            filter_wkt_area = wkt_shapely.difference(geometry.Polygon(current_deleted_coords_list)).area
            if (math.isnan(angele) or angele < filter_angle) \
                    and new_angle > new_filter_angle \
                    and filter_wkt_area < straight_area_limit \
                    and filter_wkt_area / wkt_shapely_area < straight_area_rate_limit:
                is_exists_filter_angle = True
                del_coords_keys.append(key)
                continue
        point_list.append(geometry.Point(coords[0], coords[1]))
    if len(point_list) < 3:
        return wkt_shapely, False
    return geometry.Polygon(point_list), is_exists_filter_angle


def angle_cal(a, b):
    a_norm = np.sqrt(np.sum(a * a))
    b_norm = np.sqrt(np.sum(b * b))
    cos_value = np.dot(a, b) / (a_norm * b_norm)
    arc_value = np.arccos(cos_value)
    angle_value = arc_value * 180 / np.pi
    return angle_value


def get_deleted_point_wkt_shapely(coords_list, del_key_list):
    """
    删除polygon coords_list 的点
    :param coords_list:
    :param del_key_list:
    :return:
    """
    del_key_list.sort(reverse=True)
    for del_key in del_key_list:
        coords_list.pop(del_key)
    return coords_list


def last_deal_result_save(wkt_shapely_list, batch):
    """
    处理成果入库
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000000040  # 过滤提取之后较小的面积
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    for wkt_shapely in wkt_shapely_list:
        print("入库polyghon类型操作：{}".format(wkt_shapely.geom_type))
        if wkt_shapely.area < filter_aoi_area:
            continue
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": batch, "is_valid": 1, "remark": "", "source": "",
                                "filter_element": "", "step": "RESULT"}
        insert_green_after_progress_rcord(deal_progress_record)


def update_image_vector_result_by_id(vector_result, batch):
    """ 更新矢量化成果
    :param vector:
    :return:
    """
    sql = "update basic_feature_ml set vector_result=st_geomfromtext('{}',4326) where batch='{}'".format(
        vector_result, batch)
    poiOnlineDao.execute(sql)
    return poiOnlineDbConn.commit()


def update_image_vector_file_by_id(vector_geojson_file, id):
    """ 更新矢量化成果
    :param vector:
    :return:
    """
    sql = "update basic_feature_ml set vector_geojson_file='{}' where id={}".format(vector_geojson_file, id)
    poiOnlineDao.execute(sql)
    return poiOnlineDbConn.commit()


def insert_green_after_progress_rcord(record):
    """
    后处理记录入库
    :param record:
    :return:
    """
    sql = "insert into basic_feature_ap_result(batch,geom,source,is_valid,filter_element,remark, step) " "values('{}', st_geomfromtext('{}',4326), '{}', {}, '{}', '{}', '{}')".format(
        record["batch"], record["geom"], record["source"], record["is_valid"], record["filter_element"], record["remark"], record["step"])

    poiOnlineDao.execute(sql)
    return poiOnlineDbConn.commit()


def insert_green_merge_result(record):
    """
    合并结果入库
    :param record:
    :return:
    """
    sql = "insert into basic_feature_merge_result(geom,discern_id,origin_discern_geom) " \
          "values(st_geomfromtext('{}',4326), '{}', st_geomfromtext('{}',4326))".format(record["geom"], record["discern_id"], record["origin_discern_geom"])
    poiOnlineDao.execute(sql)
    return poiOnlineDbConn.commit()

if __name__ == '__main__':
    wkt1 = ""
    print(smooth_shapely_wkt([wkt.loads(wkt1)]))

