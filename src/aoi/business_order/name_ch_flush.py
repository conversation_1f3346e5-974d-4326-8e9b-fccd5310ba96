#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库同步脚本：将master_back数据库中poi表的name字段同步到business_order数据库st_aoi_tj表的name_ch字段
"""

import os
import sys
import time
import logging
import traceback
import csv
from typing import List, Dict, Tuple, Optional
import psycopg2
import psycopg2.extras
from contextlib import contextmanager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('name_ch_flush.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
MASTER_BACK_CONF = {
    "db": "master_back",
    "user": "master_back",
    "pwd": "master_back",
    "host": "*************",
    "port": 8033
}

BUSINESS_ORDER_CONF = {
    "db": "business_order",
    "user": "business_order_se_rw",
    "pwd": "kpigzear",
    "host": "**************",
    "port": 7432
}


POI_ONLINE_CONF = {
    "db": "poi_online",
    "user": "poi_aoi_rw",
    "pwd": "poi_aoi_rw",
    "host": "*************",
    "port": 8032
}
# 同步配置
BATCH_SIZE = 1000  # 批处理大小
MAX_RETRY_TIMES = 3  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟（秒）


class DatabaseManager:
    """数据库连接管理类"""

    def __init__(self, config: Dict[str, str]):
        self.config = config
        self._connection = None

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = self._create_connection()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def _create_connection(self):
        """创建数据库连接"""
        for attempt in range(MAX_RETRY_TIMES):
            try:
                conn = psycopg2.connect(
                    database=self.config["db"],
                    user=self.config["user"],
                    password=self.config["pwd"],
                    host=self.config["host"],
                    port=self.config["port"]
                )
                conn.autocommit = False
                return conn
            except Exception as e:
                logger.warning(f"数据库连接失败 (尝试 {attempt + 1}/{MAX_RETRY_TIMES}): {e}")
                if attempt < MAX_RETRY_TIMES - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    raise

    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict]:
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
                cursor.execute(sql, params)
                return [dict(row) for row in cursor.fetchall()]

    def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行更新操作并返回影响的行数"""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                conn.commit()
                return cursor.rowcount

    def execute_batch_update(self, sql: str, params_list: List[Tuple]) -> int:
        """批量执行更新操作"""
        total_affected = 0
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                for params in params_list:
                    cursor.execute(sql, params)
                    total_affected += cursor.rowcount
                conn.commit()
        return total_affected


class NameSyncService:
    """名称同步服务类"""

    def __init__(self):
        self.master_back_db = DatabaseManager(MASTER_BACK_CONF)
        self.business_order_db = DatabaseManager(BUSINESS_ORDER_CONF)
        self.poi_online_db = DatabaseManager(POI_ONLINE_CONF)
        self.stats = {
            'total_records': 0,
            'processed_records': 0,
            'updated_records': 0,
            'failed_records': 0,
            'empty_poi_bid_count': 0,
            'empty_poi_bid_face_ids': [],
            'missing_poi_name_count': 0,
            'missing_poi_name_bids': [],
            'no_poi_bid_count': 0,
            'no_poi_bid_face_ids': [],
            'query_error_count': 0,
            'query_error_face_ids': [],
            'start_time': None,
            'end_time': None
        }

        # 用于累积统计的实例变量
        self.missing_poi_name_bids_set = set()  # 使用set避免重复统计同一个poi_bid
        self.empty_poi_bid_face_ids_set = set()  # 使用set避免重复统计同一个face_id
        self.no_poi_bid_face_ids_set = set()  # 第一步查询失败的face_id
        self.query_error_face_ids_set = set()  # 查询异常的face_id

    def get_face_ids_to_sync(self, limit: Optional[int] = None) -> List[Dict]:
        """获取需要同步的face_id列表"""
        sql = """
            SELECT face_id
            FROM st_aoi_tj
            WHERE face_id IS NOT NULL
        """
        if limit:
            sql += f" LIMIT {limit}"

        logger.info("开始获取需要同步的face_id列表...")
        results = self.business_order_db.execute_query(sql)
        logger.info(f"获取到 {len(results)} 条需要同步的记录")
        return results

    def get_poi_name_by_face_id(self, face_id: str) -> Optional[str]:
        """
        根据face_id获取对应的poi名称
        查询逻辑：face_id -> blu_face.poi_bid -> poi.name
        """
        try:
            # 第一步：通过face_id在blu_face_poi表中查找poi_bid
            sql_step1 = """
                SELECT poi_bid
                FROM blu_face_poi
                WHERE face_id = %s AND poi_bid IS NOT NULL
            """

            results = self.master_back_db.execute_query(sql_step1, (face_id,))
            if not results:
                # 统计第一步查询失败的情况（face_id在blu_face_poi表中没有对应记录）
                self.no_poi_bid_face_ids_set.add(face_id)
                logger.debug(f"第一步查询失败: face_id={face_id} 在blu_face_poi表中没有对应记录")
                return None

            poi_bid = results[0]['poi_bid']

            # 检查poi_bid是否为空字符串并进行统计
            if poi_bid == '':
                self.empty_poi_bid_face_ids_set.add(face_id)
                logger.debug(f"发现空字符串poi_bid: face_id={face_id}")
                return None

            # 第二步：通过poi_bid在poi表中查找name
            sql_step2 = """
                SELECT name
                FROM poi
                WHERE bid = %s AND name IS NOT NULL AND name != ''
            """

            results = self.poi_online_db.execute_query(sql_step2, (poi_bid,))
            if not results:
                # 统计在poi表中未找到name的poi_bid
                self.missing_poi_name_bids_set.add(poi_bid)
                logger.debug(f"在poi表中未找到name: poi_bid={poi_bid}")
                return None

            return results[0]['name']

        except Exception as e:
            # 统计查询异常的情况
            self.query_error_face_ids_set.add(face_id)
            logger.error(f"查询face_id {face_id} 对应的poi名称时出错: {e}")
            return None
    def export_aoi_data_to_csv(self, output_file: str = "aoi_data_export.csv") -> Dict:
        """
        从 st_aoi_tj 表导出数据到CSV文件

        Args:
            output_file: 输出CSV文件路径

        Returns:
            导出统计信息
        """
        export_stats = {
            'total_exported': 0,
            'export_file': output_file,
            'start_time': time.time(),
            'end_time': None,
            'success': False,
            'error_message': None
        }

        logger.info(f"开始导出AOI数据到CSV文件: {output_file}")

        try:
            # 构建SQL查询语句，按照要求的字段顺序和别名
            sql = """
                SELECT
                    face_id,
                    poi_id,
                    ST_Area(geom::geography) AS area,
                    ST_Perimeter(geom::geography) AS perimeter,
                    name_ch,
                    st_astext(geom) As geom
                FROM st_aoi_tj
                WHERE face_id IS NOT NULL
            """

            logger.info("执行数据库查询...")
            results = self.business_order_db.execute_query(sql)

            if not results:
                logger.warning("查询结果为空，没有数据可导出")
                export_stats['end_time'] = time.time()
                return export_stats

            logger.info(f"查询到 {len(results)} 条记录，开始写入CSV文件...")

            # 定义CSV文件的列标题（按照要求的映射关系）
            csv_headers = ['face_id', 'poi_id', 'area', 'perimeter', 'name', 'geom']

            # 写入CSV文件
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # 写入标题行
                writer.writerow(csv_headers)

                # 写入数据行
                for row in results:
                    # 按照CSV标题的顺序重新组织数据
                    csv_row = [
                        row['face_id'],           # face_id -> face_id
                        row['poi_id'],           # poi_bid -> poi_id
                        row['area'],              # area -> area
                        row['perimeter'],         # perimeter -> perimeter
                        row['name_ch'],           # name_ch -> name
                        row['geom']               # geom -> geom
                    ]
                    writer.writerow(csv_row)

            export_stats['total_exported'] = len(results)
            export_stats['success'] = True
            export_stats['end_time'] = time.time()

            duration = export_stats['end_time'] - export_stats['start_time']
            logger.info(f"CSV导出完成！")
            logger.info(f"导出文件: {output_file}")
            logger.info(f"导出记录数: {export_stats['total_exported']}")
            logger.info(f"耗时: {duration:.2f} 秒")

            return export_stats

        except Exception as e:
            export_stats['end_time'] = time.time()
            export_stats['error_message'] = str(e)
            logger.error(f"CSV导出失败: {e}")
            logger.error(traceback.format_exc())
            raise





    def update_name_ch_batch(self, updates: List[Tuple[str, str]], dry_run: bool = False) -> int:
        """批量更新name_ch字段"""
        if not updates:
            return 0

        if dry_run:
            logger.info("=== 试运行模式：以下是将要执行的更新操作 ===")
            for i, (name_ch, face_id) in enumerate(updates, 1):
                logger.info(f"更新 {i}: face_id={face_id} -> name_ch='{name_ch}'")
            logger.info(f"=== 试运行模式：共 {len(updates)} 条更新操作（未实际执行） ===")
            return len(updates)
        else:
            for i, (name_ch, face_id) in enumerate(updates, 1):
                logger.info(f"更新 {i}: face_id={face_id} -> name_ch='{name_ch}'")

        try:
            sql = """
                UPDATE st_aoi_tj
                SET name_ch = %s
                WHERE face_id = %s
            """

            return self.business_order_db.execute_batch_update(sql, updates)

        except Exception as e:
            logger.error(f"批量更新name_ch字段时出错: {e}")
            raise

    def sync_names(self, limit: Optional[int] = None, batch_size: int = BATCH_SIZE, dry_run: bool = False) -> Dict:
        """
        执行名称同步的主要方法

        Args:
            limit: 限制处理的记录数量（用于测试）
            batch_size: 批处理大小
            dry_run: 是否为试运行模式

        Returns:
            同步统计信息
        """
        self.stats['start_time'] = time.time()

        if dry_run:
            logger.info("=== 试运行模式：开始执行名称同步任务 ===")
            logger.info("注意：此为试运行模式，不会执行实际的数据库更新操作")
        else:
            logger.info("=== 开始执行名称同步任务 ===")
            logger.info("注意：此为生产模式，将执行实际的数据库更新操作")

        try:
            # 获取需要同步的face_id列表
            face_id_records = self.get_face_ids_to_sync(limit)
            self.stats['total_records'] = len(face_id_records)

            if not face_id_records:
                logger.info("没有需要同步的记录")
                return self.stats

            logger.info(f"共找到 {self.stats['total_records']} 条需要同步的记录")

            # 分批处理
            for i in range(0, len(face_id_records), batch_size):
                batch_records = face_id_records[i:i + batch_size]
                face_ids = [record['face_id'] for record in batch_records]

                logger.info(f"处理批次 {i//batch_size + 1}: {len(face_ids)} 条记录")

                # 使用单个记录查询方式
                updates = []
                for face_id in face_ids:
                    # 使用单个查询获取poi名称
                    poi_name = self.get_poi_name_by_face_id(face_id)
                    if poi_name:
                        updates.append((poi_name, face_id))
                        logger.debug(f"找到匹配: face_id={face_id}, name='{poi_name}'")
                    else:
                        logger.debug(f"未找到匹配: face_id={face_id}")

                # 批量更新
                if updates:
                    updated_count = self.update_name_ch_batch(updates, dry_run=dry_run)
                    self.stats['updated_records'] += updated_count
                    if dry_run:
                        logger.info(f"批次处理完成，将更新 {updated_count} 条记录（试运行模式 - 未实际执行）")
                    else:
                        logger.info(f"批次更新完成，成功更新了 {updated_count} 条记录")

                self.stats['processed_records'] += len(face_ids)

                # 显示进度
                progress = (self.stats['processed_records'] / self.stats['total_records']) * 100
                logger.info(f"同步进度: {progress:.1f}% ({self.stats['processed_records']}/{self.stats['total_records']})")

            self.stats['end_time'] = time.time()
            duration = self.stats['end_time'] - self.stats['start_time']

            # 汇总所有统计信息
            self.stats['missing_poi_name_count'] = len(self.missing_poi_name_bids_set)
            self.stats['missing_poi_name_bids'] = list(self.missing_poi_name_bids_set)

            self.stats['empty_poi_bid_count'] = len(self.empty_poi_bid_face_ids_set)
            self.stats['empty_poi_bid_face_ids'] = list(self.empty_poi_bid_face_ids_set)

            self.stats['no_poi_bid_count'] = len(self.no_poi_bid_face_ids_set)
            self.stats['no_poi_bid_face_ids'] = list(self.no_poi_bid_face_ids_set)

            self.stats['query_error_count'] = len(self.query_error_face_ids_set)
            self.stats['query_error_face_ids'] = list(self.query_error_face_ids_set)

            # 计算失败记录总数（所有未成功更新的记录）
            self.stats['failed_records'] = (
                self.stats['empty_poi_bid_count'] +
                self.stats['missing_poi_name_count'] +
                self.stats['no_poi_bid_count'] +
                self.stats['query_error_count']
            )

            # 输出统计汇总信息
            if self.stats['missing_poi_name_count'] > 0:
                logger.info(f"在poi表中未找到name的poi_bid统计: {self.stats['missing_poi_name_count']} 个")
            if self.stats['empty_poi_bid_count'] > 0:
                logger.info(f"空字符串poi_bid统计: {self.stats['empty_poi_bid_count']} 条记录")
            if self.stats['no_poi_bid_count'] > 0:
                logger.info(f"第一步查询失败统计: {self.stats['no_poi_bid_count']} 条记录")
            if self.stats['query_error_count'] > 0:
                logger.info(f"查询异常统计: {self.stats['query_error_count']} 条记录")

            # 验证统计完整性
            calculated_total = self.stats['updated_records'] + self.stats['failed_records']
            if calculated_total != self.stats['processed_records']:
                logger.warning(f"统计数据不一致: updated_records({self.stats['updated_records']}) + failed_records({self.stats['failed_records']}) = {calculated_total}, 但 processed_records = {self.stats['processed_records']}")
            else:
                logger.info(f"统计数据验证通过: updated_records + failed_records = processed_records = {self.stats['processed_records']}")

            if dry_run:
                logger.info("=== 试运行模式：名称同步任务完成 ===")
                logger.info(f"总记录数: {self.stats['total_records']}")
                logger.info(f"处理记录数: {self.stats['processed_records']}")
                logger.info(f"将更新记录数: {self.stats['updated_records']} (试运行模式 - 未实际执行)")
                logger.info(f"失败记录数: {self.stats['failed_records']}")
                logger.info("--- 失败记录详细分类 ---")
                logger.info(f"  空字符串poi_bid: {self.stats['empty_poi_bid_count']} 条")
                if self.stats['empty_poi_bid_count'] > 0:
                    sample_ids = self.stats['empty_poi_bid_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"  未找到poi_name: {self.stats['missing_poi_name_count']} 条")
                if self.stats['missing_poi_name_count'] > 0:
                    sample_bids = self.stats['missing_poi_name_bids'][:5]
                    logger.info(f"    示例poi_bid: {sample_bids}")
                logger.info(f"  第一步查询失败: {self.stats['no_poi_bid_count']} 条")
                if self.stats['no_poi_bid_count'] > 0:
                    sample_ids = self.stats['no_poi_bid_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"  查询异常: {self.stats['query_error_count']} 条")
                if self.stats['query_error_count'] > 0:
                    sample_ids = self.stats['query_error_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"耗时: {duration:.2f} 秒")
            else:
                logger.info("=== 名称同步任务完成 ===")
                logger.info(f"总记录数: {self.stats['total_records']}")
                logger.info(f"处理记录数: {self.stats['processed_records']}")
                logger.info(f"成功更新记录数: {self.stats['updated_records']}")
                logger.info(f"失败记录数: {self.stats['failed_records']}")
                logger.info("--- 失败记录详细分类 ---")
                logger.info(f"  空字符串poi_bid: {self.stats['empty_poi_bid_count']} 条")
                if self.stats['empty_poi_bid_count'] > 0:
                    sample_ids = self.stats['empty_poi_bid_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"  未找到poi_name: {self.stats['missing_poi_name_count']} 条")
                if self.stats['missing_poi_name_count'] > 0:
                    sample_bids = self.stats['missing_poi_name_bids'][:5]
                    logger.info(f"    示例poi_bid: {sample_bids}")
                logger.info(f"  第一步查询失败: {self.stats['no_poi_bid_count']} 条")
                if self.stats['no_poi_bid_count'] > 0:
                    sample_ids = self.stats['no_poi_bid_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"  查询异常: {self.stats['query_error_count']} 条")
                if self.stats['query_error_count'] > 0:
                    sample_ids = self.stats['query_error_face_ids'][:5]
                    logger.info(f"    示例face_id: {sample_ids}")
                logger.info(f"耗时: {duration:.2f} 秒")

            return self.stats

        except Exception as e:
            logger.error(f"同步任务执行失败: {e}")
            self.stats['end_time'] = time.time()
            raise


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='数据库名称同步脚本')
    parser.add_argument('--limit', type=int, help='限制处理的记录数量（用于测试）')
    parser.add_argument('--batch-size', type=int, default=BATCH_SIZE, help=f'批处理大小（默认: {BATCH_SIZE}）')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，只查询不更新')
    parser.add_argument('--simple', action='store_true', help='简单模式，只显示查询结果不执行批量处理')
    parser.add_argument('--export-csv', type=str, help='导出AOI数据到CSV文件，指定输出文件路径')

    args = parser.parse_args()

    logger.info("=" * 60)
    logger.info("数据库名称同步脚本")
    if args.dry_run:
        logger.info("运行模式：试运行模式（不会执行数据库更新操作）")
    else:
        logger.info("运行模式：生产模式（将执行实际的数据库更新操作）")
        logger.warning("⚠️  注意：此脚本将执行实际的数据库更新操作！")
    logger.info("=" * 60)

    try:
        sync_service = NameSyncService()

        # 处理CSV导出功能
        if args.export_csv:
            logger.info("=== CSV导出模式 ===")
            try:
                export_stats = sync_service.export_aoi_data_to_csv(args.export_csv)

                # 输出导出统计信息
                logger.info("=== CSV导出统计信息 ===")
                logger.info(f"导出文件: {export_stats['export_file']}")
                logger.info(f"导出记录数: {export_stats['total_exported']}")
                logger.info(f"导出状态: {'成功' if export_stats['success'] else '失败'}")
                if export_stats['error_message']:
                    logger.error(f"错误信息: {export_stats['error_message']}")

                duration = export_stats['end_time'] - export_stats['start_time']
                logger.info(f"总耗时: {duration:.2f} 秒")

                if export_stats['success']:
                    logger.info("CSV导出任务完成！")
                else:
                    logger.error("CSV导出任务失败！")
                    sys.exit(1)

            except Exception as e:
                logger.error(f"CSV导出过程中发生错误: {e}")
                sys.exit(1)
            return

        if args.simple:
            logger.info("=== 简单查询模式 ===")
            # 只获取前几条记录进行测试
            test_limit = min(args.limit or 10, 10)
            face_id_records = sync_service.get_face_ids_to_sync(test_limit)

            logger.info(f"将处理 {len(face_id_records)} 条记录")

            for record in face_id_records:
                face_id = record['face_id']
                poi_name = sync_service.get_poi_name_by_face_id(face_id)
                logger.info(f"face_id: {face_id} -> name: {poi_name}")
        else:
            # 生产模式确认
            if not args.dry_run:
                logger.warning("⚠️  您即将执行实际的数据库更新操作！")
                if args.limit:
                    logger.info(f"将处理最多 {args.limit} 条记录")
                else:
                    logger.info("将处理所有符合条件的记录")

                # 简单的确认机制（可以根据需要调整）
                logger.info("如需继续，请确保您已经备份了相关数据")

            # 执行完整的同步流程
            stats = sync_service.sync_names(
                limit=args.limit,
                batch_size=args.batch_size,
                dry_run=args.dry_run
            )

            # 输出最终统计信息
            if args.dry_run:
                logger.info("=== 试运行模式统计信息 ===")
            else:
                logger.info("=== 同步统计信息 ===")

            for key, value in stats.items():
                if key.endswith('_time') and value:
                    logger.info(f"{key}: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(value))}")
                else:
                    logger.info(f"{key}: {value}")

    except KeyboardInterrupt:
        logger.info("用户中断了同步任务")
    except Exception as e:
        logger.error(f"同步任务执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()