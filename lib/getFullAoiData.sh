#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../conf/globalConf.sh

RAW_DATA="$DATA_PATH/aoi/$(date +%Y-%m-%d).csv"
mkdir -p "$DATA_PATH/aoi"
MD5_FILE="$DATA_PATH/aoi/$(date +%Y-%m-%d).md5"
$PG_CONN -d postgres -c "\copy  (select
    face_id,geom,bid,name_ch,kind,area,scene_id,display_flag,proj_flag,mesh_id,city_name,cp,update_date,memo
  from blu_face_from_se
  where  status='1' and poi_status in ('1') and bid !~'[^0-9]' and name_ch<>'' and ST_IsValid(geom)='t')
  to  '$RAW_DATA'"
md5sum "$RAW_DATA" | awk '{print $1}' > "$MD5_FILE"
python $LIB_PATH/geo_clockwise.py "$RAW_DATA" "$DATA_PATH/poi_region.txt"
md5sum "$DATA_PATH/poi_region.txt" > "$DATA_PATH/poi_region.txt.md5"
