# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
全时轨迹库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class TrajDB2Byd(PgDao):
    """
    采纳轨迹
    """

    def __init__(self, shared_pool=True, pool=None):
        conf = CommTool.get_config("pg").get("traj_db2_byd")
        if not conf:
            raise Exception("traj_db2_byd 配置不存在")
        self.db_conf = conf

        # 传递配置和连接池给父类，由父类统一管理连接池
        super().__init__(pg_conf=conf, pool=pool, use_shared_pool=shared_pool)

    def get_byd_all_traj_by_geom(self, geom, traj_time=None, limit=5000):
        """
        获取车机轨迹
        Returns:
        """
        args = [geom]
        # sql = f"""
        #     select st_astext(geom) 
        #     from byd_all_traj 
        #     where st_intersects(geom,st_geomfromtext(%s,4326))
        #    """
        # if traj_time:
        #     sql += " and traj_time>=%s"
        #     args.append(traj_time)
        # sql += f" order by traj_time desc"
        # if limit:
        #     sql += f" limit %s"
        #     args.append(limit)
        # return self.queryall(sql, args)
        args = []
        sql = f"""
            select st_astext(geom) geom, uid from byd_all_traj a 
                inner join byd_all_traj_points b on a.id=b.id
            where 1=1
        """
        if geom:
            sql += " and (st_intersects(st_geomfromtext(%s, 4326), b.start_geom) or st_intersects(st_geomfromtext(%s, 4326), b.end_geom))"
            args.extend([geom, geom])
        if limit:
            sql += f" limit %s"
            args.append(limit)
        return self.queryall_by_kv_cursor(sql, args)