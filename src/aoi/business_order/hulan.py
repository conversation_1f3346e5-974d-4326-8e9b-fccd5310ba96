# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDD商单数据输出
"""
import os
import sys

import psycopg2

root_dir = "/home/<USER>/gengyong/business_order/guangdong_expressway"
bud_path = root_dir + "/background/00-building"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-division"
bad_county_path = root_dir + "/background/04-division/county"
bad_city_path = root_dir + "/background/04-division/city"
bad_town_path = root_dir + "/background/04-division/town"
bad_village_path = root_dir + "/background/04-division/village"
bad_province_path = root_dir + "/background/04-division/province"
province = "广东省"
mesh_ids = ""


def run():
    """
    成果输出
    Returns:

    """
    print(f"开始bad导出")
    bad_export()
    print(f"导出完成")


def bad_export():
    """
    行政区划数据输出
    Returns:

    """
    export_dir = bad_town_path
    sql = f"""
                select 
                     guid as face_id,towncode as admin_id,townname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
                from bad_town where proname = '{province}'
            """
    cmd = f"""
                ogr2ogr -f 'MapInfo File' {export_dir}/division_town.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
            """
    os.system(cmd)
    export_dir = bad_village_path
    sql = f"""
                    select 
                         guid as face_id,village_code as admin_id,village_name as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
                    from bad_admin_vil_gcj where pro_name = '湖南省' and city_name='娄底市'
                """
    cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/division_village.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
                """
    os.system(cmd)


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/background")
    make_dir(bad_path)
    make_dir(bad_town_path)
    make_dir(bad_village_path)

def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)

def get_mesh_ids_by_file(mesh_id_file):
    """
    获取图幅信息
    Args:
        mesh_id_file:

    Returns:

    """
    mesh_ids = []
    with open(mesh_id_file, 'r') as f:
        for line in f.readlines():
            line = line.strip()
            if len(line) > 0:
                mesh_ids.append(line)
    return mesh_ids


if __name__ == '__main__':
    data_version = sys.argv[1]
    mesh_conf_file = sys.argv[2]
    cmd = "rm -rf ./background"
    os.system(cmd)
    init_dir()
    print("目录初始化完成,开始数据导出")
    mesh_id_list = get_mesh_ids_by_file(mesh_conf_file)
    mesh_ids = "','".join(mesh_id_list)
    run()
    zip_cmd = f"zip -r {data_version} background"
    os.system(zip_cmd)