# -*- coding: UTF-8 -*-
################################################################################
"""
输出文件以创建内业计划，并分析过滤的场景
Authors: <AUTHORS>
Date:    2022/05/27 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import sys
import shapely.wkt
import shapely.geometry
import uuid
import requests
import time
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize


API_URL = 'http://api.guiji.baidu-int.com/v1/match'


def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../conf/config.ini')

dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online_test"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

dbname = "trans_id"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_trans = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_trans = conn_trans.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)
conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()


def get_tra_data_by_bid(bid):
    """
    根据bid查询轨迹信息
    Args:
        bid: poi的bid
    Return:
        res: 轨迹信息
    """
    sql = "select tra_info from bid_tra where bid = %s"
    cursor_poi.execute(sql, [bid])
    res = cursor_poi.fetchone() 
    if res is None:
        return ""
    return res[0]


def convert_short_id(link_id):
    """linkid转短id
    Args:
        link_id: 道路的linkid
    Return:
        res: 道路的短id
    """
    sql = "select tid from image_r where sid = %s"
    cursor_trans.execute(sql, [link_id])
    res = cursor_trans.fetchone()
    if res is None:
        return ""
    return res[0]


def find_tra_info_step_1(tra_info_list, bid, node_id, in_linkid, out_linkid, fw_tra):
    """
    判断是否有经过大门的轨迹信息
    """
    # valid_tra_list = []
    match_res = "fail"
    for tra_info_tmp in tra_info_list:
        pos_info = ""
        tra_data = tra_info_tmp.split("@", 1)[1].split(";")
        #判断轨迹的终点是否在AOI范围内
        length = len(tra_data)
        end_point_geom = "POINT(%s %s)" % (tra_data[length - 1].split(',')[0], tra_data[length - 1].split(',')[1])
        sql = "select count(*) from blu_face a inner join blu_face_poi b on a.face_id = b.face_id where poi_bid = %s \
                and st_contains(geom, wgs2gcj(st_geomfromtext(%s, 4326)))"
        cursor_aoi.execute(sql, [bid, end_point_geom])
        if cursor_aoi.fetchone()[0] == 0:
            continue

        #初步判断轨迹是否经过大门
        link_list = []
        index_list = []
        for index, tra_data_tmp in enumerate(tra_data):
            link_tmp = tra_data_tmp.split(',')[3][:-1]
            link_list = link_list + link_tmp.split("|")
            for i in range(len(link_tmp.split("|"))):
                index_list.append(index)
            pos_info = pos_info + tra_data_tmp.split(',')[0] + "," + tra_data_tmp.split(',')[1] + ";"

        for i in range(len(link_list) - 1, 1, -1):
            if (link_list[i] == out_linkid and link_list[i - 1] == in_linkid) \
                or (link_list[i] == in_linkid and link_list[i - 1] == out_linkid):
                #判断该轨迹是否是优质轨迹
                res, traj_prop, ep = judge_tra_valid(tra_info_tmp, index_list[i])
                if res:
                    fw_tra.write("%s\t%s\t%s\t%s\t%s\t%s\n" %
                                 (bid, node_id, traj_prop, ep[0], ep[1], pos_info.strip(';')))
                    match_res = "success"
                    break
                    # valid_tra_list.append(tra_info_tmp)
    return match_res


def judge_tra_valid(tra_info_tmp, index):
    """
    判断轨迹数据的置信度
    """
    userid = tra_info_tmp.split('@', 1)[0].split(',')[0]
    pos_info_list = []
    for tra_info_data in tra_info_tmp.split('@', 1)[1].split(';'):
        x = tra_info_data[0]
        y = tra_info_data[1]
        timestamp = tra_info_data[2]
        pos_info_list.append(str(timestamp + ',' + x + ',' + y))
    points = '|'.join(pos_info_list)
    ret = get_mm_info(userid, userid, points)
    tra_param_list = ret.split('\n')
    ep = []
    ep.append(tra_param_list[index].split(',')[24])
    ep.append(tra_param_list[index - 1].split(',')[24])
    #判断轨迹的置信度
    traj_prop = tra_param_list[0].split(',')[28]
    if traj_prop < 0.6:
        return False, traj_prop, ep
    #判断大门点位置的放射概率
    if ep[0] < 0.7 and ep[1] < 0.7:
        return False, traj_prop, ep
    return True, traj_prop, ep


def get_mm_info(userid, traceid, points):
    """

    @param points:
    @param range:
    @param coordtype:
    @return:
    """
    ret = {}
    request_dict = {}
    request_dict['function'] = 'common'
    traces = [{"userid": userid, "traceid": traceid, "points": points}]
    request_dict['traces'] = traces
    request_json = json.dumps(request_dict)
    retry = 0
    while retry <= 3:
        try:
            response = requests.post(API_URL, request_json).text
            #print response
            response = json.loads(response)
            break
        except Exception as e:
            retry = retry + 1
            time.sleep(0.2)
            continue
    error = response.get('error')
    if error != 0:
        return ret
    ret = response.get('data', {})
    return ret


def main():
    """
    根据轨迹信息判断出入口和AOI的关联关系
    未完成优化，需进一步提高准确率
    """

    bid_gate_list = []
    with open("fail_case_bid_sort_100_allinfo.txt", "r") as f:
        lines = f.readlines()
        for line in lines:
            data = line.split('\t')
            bid_gate_list.append([data[1], data[3]])
    fw = open("gate_bid_tra_match.txt", "w")
    fw_tra = open("gate_bid_tra_match_tra.txt", "w")
    for data in bid_gate_list:
        bid = data[0]
        node_id = data[1]
        sql = "select in_linkid, out_linkid from nav_gate where node_id = %s limit 1"
        cursor_road.execute(sql, [node_id])
        res_road = cursor_road.fetchone()
        if res_road is None:
            continue
        match_res = "fail"
        in_linkid = convert_short_id(res_road[0])
        out_linkid = convert_short_id(res_road[1])
        tra_info = get_tra_data_by_bid(bid)
        if tra_info == "":
            continue
        tra_info_list = tra_info.split("#")
        match_res = find_tra_info_step_1(tra_info_list, bid, node_id, in_linkid, out_linkid, fw_tra)
        if match_res == "fail":
            continue

        fw.write("%s\t%s\t%s\n" % (bid, node_id, match_res))


if __name__ == '__main__':
    main()
