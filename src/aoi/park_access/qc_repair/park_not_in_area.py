# -*- coding: utf-8 -*-
"""
停车场坐标不在面内
"""
import datetime
import multiprocessing
import sys
import os
import tqdm
import requests
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi import utils

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()
COORDINATE_FACTOR = 100000

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_coordinate.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_coordinate.txt"


def main(bids: list):
    """
    停车场关联高通量Link 点不在 Link 上
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids.tolist())
    for park in tqdm.tqdm(park_list):
        try:
            logger.info(f"\n{park['bid']} 开始处理")
            if not park['show_area']:
                logger.info(f"{park['bid']} 没有show_area, 不处理")
                continue
            park_sp = wkt.loads(park['geom'])
            show_area_sp = wkt.loads(park['show_area'])
            if show_area_sp.contains(park_sp):
                logger.info(f"{park['bid']} 在show_area内, 不处理")
                continue
            new_park_wkt = re_match_park_location(park['bid'], utils.mc_to_gcj(park['show_area']), park['geom'])
            if not new_park_wkt:
                continue
            push(park["bid"], new_park_wkt)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"{park['bid']} 异常:{e}")


def re_match_park_location(bid, park_area, park_wkt):
    """
    重新匹配停车场位置
    Args:
        bid:

    Returns:

    """
    # 查询竞品数据
    park_sp = wkt.loads(park_wkt)
    park_area_sp = wkt.loads(park_area)
    if park_sp.distance(park_area_sp) > 100 / COORDINATE_FACTOR:
        logger.info(f"{bid} 与show_area距离过远, 不处理")
        return False
    competitor_list = poi_online_query.get_competitor_park(bid)
    if competitor_list:
        competitor_sp = wkt.loads(competitor_list[0]['geom'])
        if park_area_sp.contains(competitor_sp):
            logger.info(f"{bid} 竞品在show_area内召回")
            return competitor_sp.wkt
        else:
            logger.info(f"{bid} 存在竞品不在show_area内, 不处理")
            return False
    # 如果距离30m以内, 直接取面中心点
    if park_sp.distance(park_area_sp) > 30 / COORDINATE_FACTOR:
        return False
    logger.info(f"{bid} 距离show_area过近, 取面中心点")
    return park_area_sp.representative_point().wkt


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in f:
            data_list = line.strip().split("\t")
            if data_list[8] != '【R1006】停车场坐标未落在停车场面内':
                continue
            bids.append(data_list[2])
    return bids


def push(bid, park_wkt):
    """
    更新park_wkt
    Args:
        bid:
        road_relation:

    Returns:

    """
    # 写入成果数据
    with open("./park_not_in_area.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{park_wkt}\n")
        f.flush()
        logger.info(f"{bid} 写入成果数据:{park_wkt}")
    logger.info(f"{bid} 开始推送:{park_wkt}")
    park_mc_wkt = utils.gcj_to_mc(park_wkt)
    park_mc_sp = wkt.loads(park_mc_wkt)
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_PARK_NOT_IN_AREA",
        "source_id": f"STRATEGY_PARK_NOT_IN_AREA_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_PARK_NOT_IN_AREA_1214",
        "point_x": park_mc_sp.x,
        "point_y": park_mc_sp.y,
    }
    logger.info(f"停车场点不在面内修复推送:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if len(bid_list) == 0:
        bid_list = get_need_deal_bids_by_afs()
    if len(bid_list) == 0:
        exit(f"没有可执行BID")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
