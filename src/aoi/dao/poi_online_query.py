# -*- coding: utf-8 -*-
"""
poi_online 库查询：封装对 blu_face_complete 表的访问
"""

import os
import sys
from typing import Optional, List, Any

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class PoiOnlineQuery(PgDao):
    """
    用于查询 poi_online_r 数据库中的 blu_face_complete 表等
    """

    def __init__(self, shared_pool=True, pool=None):
        conf = CommTool.get_config("pg").get("poi_online_r")
        if not conf:
            raise Exception("poi_online_r 配置不存在")
        self.db_conf = conf

        # 传递配置和连接池给父类，由父类统一管理连接池
        super().__init__(pg_conf=conf, pool=pool, use_shared_pool=shared_pool)

    def get_blu_face_complete_by_bids(self, bids=None):
        """
        获取AOI完整性等级
        param bids:  bid list
        :return:
        """
        sql = f"""
            select main_bid, aoi_complete, complete_info_id, face_id, update_time, is_white from blu_face_complete 
            where 1 = 1
        """
        if bids:
            sql += f" and main_bid = ANY(%s)"
        return self.queryall(sql, [bids])

    def get_parking_list(self, show_tags=None, bids=None, limit=None, parent_id=None, intersects_geom=None):
        """
        停车场数据获取
        Args:
            show_tags:
            bids:
        Returns:
        """
        sql = f"""
          select a.name,a.point_x,a.point_y,a.std_tag,a.show_tag,a.parent_id,st_astext(a.area),a.bid, 
            st_astext(a.show_area), a.road_relation, st_astext(a.gcj_geom), a.address, a.open_limit_new, 
            a.road_relation_childrens,b.root_bid
          from park_online_data a
            left join park_statistical_data b on a.bid=b.bid
          where a.std_tag != '出入口;停车场出入口' and a.status in(1, 15)
        """
        args = []
        if show_tags:
            sql += " and a.show_tag = ANY(%s)"
            args.append(show_tags)
        if bids:
            sql += " and a.bid = ANY(%s)"
            args.append(bids)
        if parent_id:
            sql += " and a.parent_id = %s"
            args.append(parent_id)
        if intersects_geom:
            sql += " and st_intersects(st_geomfromtext(%s, 4326), a.gcj_geom)"
            args.append(intersects_geom)
        if limit:
            sql += " limit %s"
            args.append(limit)
        return self.queryall(sql, args)

    def get_image_geom(self, batch: str, filename: str):
        """ 获取影像图原始坐标
        :param batch: 批次
        :param filename: 影像图名称
        :return:
        """
        sql = f"""
            select id, st_astext(image_geom), vector_geojson_file from basic_feature_ml 
            where pseudo_img_url=%s and batch=%s
        """

        return self.queryone(sql, [filename, batch])

    def get_geojson_list_by_id(self, id: int, batch: str, limit: int):
        """ 获取影像图原始坐标
        :param batch: 批次
        :param id: id
        :param limit: id
        :return: 影像图gcj geom
        """
        sql = f"""
            select id, vector_geojson_file from basic_feature_ml 
            where batch=%s
                and id>%s
                and vector_result is null 
                and vector_geojson_file != ''
            order by id 
            limit %s
        """
        return self.queryall(sql, [batch, id, limit])

    def get_discern_wkt_list(self, batch: str):
        """
        获取识别框
        :param batch:
        :return:
        """
        sql = f"""
            select id, st_astext(vector_result) from basic_feature_ml 
            where batch=%s and vector_result is not null
        """
        return self.queryall(sql, [batch])

    def get_poi_by_bid(self, bid: str):
        """
        查询POI信息
        :param bid:
        :return:
        """
        sql = f"""
            select 
                std_tag, name, city, click_pv, arrive_x, arrive_y,poi_pv.pv, address, click_pv
            from poi 
                left join poi_pv on poi.bid=poi_pv.bid 
            where poi.bid = %s
        """
        return self.queryone(sql, [bid])

    def get_parking_and_poi_list(self, show_tag=None, bids=None):
        """
        停车场 & POI数据获取
        Args:
            show_tag:

        Returns:

        """
        sql = f"""
            select pod.bid,pod.parent_id,poi.click_pv,pod.std_tag,precise from park_online_data pod 
            inner join poi on poi.bid=pod.bid 
            where 1 = 1
        """
        args = []
        if show_tag:
            sql += f" and pod.show_tag = %s "
            args.append(show_tag)
        if bids:
            sql += f" and pod.bid = ANY(%s) "
            args.append(bids)
        data_list = self.queryall(sql, args)
        if not data_list:
            return data_list
        res = []
        for data in data_list:
            current_data = {"bid": data[0], "parent_id": data[1], "click_pv": data[2], "std_tag": data[3],
                            "precise": data[4]}
            res.append(current_data)
        return res

    def get_poi_by_bids(self, bids: list):
        """
        查询POI信息
        :param bids:
        :return:
        """
        sql = f"""
            select 
                bid, std_tag, click_pv,st_astext(geometry)
            from poi 
            where poi.bid = ANY(%s)
        """
        data_list = self.queryall(sql, [bids])
        if not data_list:
            return data_list
        res = []
        for data in data_list:
            current_data = {"bid": data[0], "std_tag": data[1], "click_pv": data[2], "wkt": data[3]}
            res.append(current_data)
        return res

    def get_aoi_by_std_tag(self, match_std_tag: str = "", full_match_std_tags=None, match_name: str = ""):
        """
        根据标准标签获取AOI信息
        :param match_std_tag: 模糊匹配标签
        :param full_match_std_tags: list 完全匹配标签
        :param match_name: 匹配名称
        :return:
        """
        sql = """
            select bfc.main_bid,poi.click_pv,poi.std_tag from blu_face_complete bfc 
                inner join poi on poi.bid=bfc.main_bid 
            where 1 = 1 
        """
        args = []
        if match_std_tag:
            sql += f" and poi.std_tag ~ %s"
            args.append(match_std_tag)
        if full_match_std_tags:
            sql += f" and poi.std_tag = ANY(%s)"
            args.append(full_match_std_tags)
        if match_name:
            sql += f" and poi.name ~ %s"
            args.append(match_name)
        data_list = self.queryall(sql, args)
        if not data_list:
            return data_list
        res = []
        for data in data_list:
            current_data = {"bid": data[0], "click_pv": data[1], "std_tag": data[2]}
            res.append(current_data)
        return res

    def get_intersects_recognition_park_face_by_geom(self, geom: str):
        """
        获取相交停车场识别面
        :param bid:
        :return:
        """
        sql = f"""
            select face_id from recognition_park_face 
                where st_intersects(geom,st_buffer(st_geomfromtext('%s',3857), 0.00050))
        """
        return self.queryone(sql, [geom])

    def get_space_attr_by_bid(self, bid, type="IMPROVE_RECALL"):
        """
        获取空间属性
        :param bid:
        :return:
        """
        sql = f"select bid,space_attr from park_space_attr where bid=%s and type=%s"
        return self.queryone(sql, [bid, type])

    def get_nearby_park_sub_pois(self, node_wkt, dist):
        """
        获取范围内的停车产子点入口
        Args:
            node_wkt:
            dist:

        Returns:

        """
        sql = f"""
            select parent_id from park_online_data
            where ST_DWithin(st_geomfromtext(%s, 4326), gcj_geom, %s)
                and parent_id != '' and parent_id != '0'
                and std_tag='出入口;停车场出入口' and name like '%地下停车场%入口'

        """
        return self.queryall(sql, [node_wkt, dist])

    def get_sub_pois(self, park_bid, access_type='入口', open_limit_new_not=None):
        """
        get_sub_pois
        """
        args = [park_bid]
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom), road_relation, status, name, open_limit_new 
            from park_online_data
            where parent_id = %s  and status = 1
            and std_tag = '出入口;停车场出入口' 
        """
        if access_type:
            if access_type == '入口':
                sql += f" and name like %s"
                args.append('%入口')
            elif access_type == '出口':
                sql += f" and (name like %s or name like %s)"
                args.append('%出口')
                args.append('%出入口')
        # print(sql)
        return self.queryall(sql, args)

    def get_precise_access(self, park_bid, open_limit_new_not=None):
        """
        get_precise_access
        """
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom), road_relation,status from park_online_data
            where parent_id = %s 
            and EXISTS (
                SELECT 1
                FROM jsonb_array_elements(road_relation::jsonb->'link_info') AS a_item
                WHERE (a_item->>'orientation')::int = 1  
            )
            and std_tag = '出入口;停车场出入口' and name like %s
        """
        args = [park_bid, '%入口']
        if open_limit_new_not:
            sql += f" and open_limit_new != %s"
            args.append(open_limit_new_not)
        return self.queryall(sql, args)

    def is_person_vehicle_house(self, bid):
        """
        住宅区人车分流数据查询
        Args:
            bid:

        Returns:

        """
        sql = f"select is_person_vehicle from person_vehicle_hourse where bid=%s"
        res = self.queryone(sql, [bid])
        if not res:
            return False
        if res[0] == 1:
            return True
        return False

    def get_park_list_by_parent_id(self, parent_ids, std_tag=None):
        """
        获取停车场信息
        Returns:

        """
        sql = f"select bid,road_relation,precise,show_tag from park_online_data where parent_id=ANY(%s)"
        args = [parent_ids]
        if std_tag:
            sql += f" and std_tag = %s"
            args.append(std_tag)
        return self.queryall(sql, args)

    def get_not_accurate_parking_list(self, bid_list=None):
        """
        获取出入口非全精准的停车场集合
        Args:
            bid_list:
            bids:

        Returns:

        """
        sql = f"""
          select psd.bid, psd.parent_bid, psd.parent_std_tag,psd.name,psd.pv,psd.show_tag,st_astext(pod.area),st_astext(pod.gcj_geom)
          from park_statistical_data psd
            left join park_online_data pod on psd.bid = pod.bid
          where value_tag='TOP70' 
            and precise20 != 1 
            and churukou_all_jz != 1 
        """
        args = []
        if bid_list and len(bid_list) > 0:
            sql += f" and psd.bid = ANY(%s)"
            args.append(bid_list)
        return self.queryall(sql, args)

    def get_pushed_park_access_intelligence_by_geom(self, geom=None, park_bid=None, outside_id=None, created_at=None):
        """
        获取已推送的停车场情报
        Args:
            geom:
            park_bid:
            outside_id:

        Returns:

        """
        sql = f"""
            select qb_id,node_id,st_astext(geom) as geom,created_at from parking_manual_prepush 
            where model ~ 'strategy_park_access' and is_push = 1
        """
        args = []
        if geom:
            sql += " and st_intersects(st_geomfromtext(%s, 4326), geom)"
            args.append(geom)
        if park_bid:
            sql += " and parking_bid = %s"
            args.append(park_bid)
        if outside_id:
            sql += " and outside_id = %s"
            args.append(outside_id)
        if created_at:
            sql += " and created_at > %s"
            args.append(created_at)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_competitor_list_by_bid(self, bid):
        """
        获取竞品停车场列表
        Args:
            bid:
        """
        if not bid:
            return None
        sql = f"""
            select matched_poi_bid, st_astext(navigate_point) from park_competitor_20240731 
            where matched_poi_bid = %s
        """
        return self.queryall(sql, [bid])

    def get_park_access_list(self, parent_id=None, intersects_geom=None):
        """
        停车场数据获取
        Args:
            parent_id:
            intersects_geom:

        Returns:

        """
        sql = f"""
          select name,point_x,point_y,std_tag,show_tag,parent_id,st_astext(area),bid, st_astext(show_area), 
            road_relation, st_astext(gcj_geom)
          from park_online_data
              where std_tag = '出入口;停车场出入口'
        """
        args = []
        if parent_id:
            sql += " and parent_id = %s"
            args.append(parent_id)
        if intersects_geom:
            sql += " and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)"
            args.append(intersects_geom)
        return self.queryall(sql, args)

    def get_park_access_mistake_qb(self, park_bid, park_access_bid,
                                   strategy_type="park_access_mistake", status="PUSHED"):
        """
        获取停车场关联错情报
        """
        sql = f"""
          select id
          from park_access_intelligence
              where type = '{strategy_type}'
        """
        args = []
        if status:
            sql += " and status = %s"
            args.append(status)
        if park_bid:
            sql += " and bid = %s"
            args.append(park_bid)
        if park_access_bid:
            sql += " and park_access_bid = %s"
            args.append(park_access_bid)
        return self.queryall(sql, args)

    def get_parking_competitors_by_bid(self, bid=None, parent_id=None):
        """
        获取竞品引导点信息
        Returns:

        """
        sql = f"""
            select st_astext(navigate_point),competitor_short_name,competitor_full_name,matched_poi_bid 
            from park_competitor_20240731 where 1=1 and navigate_point is not null
        """
        args = []
        if bid:
            sql += ' and matched_poi_bid = %s'
            args.append(bid)
        if parent_id:
            sql += ' and parent_poi_bid = %s'
            args.append(parent_id)
        return self.queryall(sql, args)

    def get_near_street_poi_by_area(self, area_wkt: str):
        """
        根据区域获取临街POI
        :param area_wkt:
        :return:
        """
        sql = f"""
            select bid from poi where st_intersects(st_geomfromtext(%s,4326), geometry)
        """
        poi_list = self.queryall(sql, [area_wkt])
        if not poi_list:
            return None
        poi_bid_list = [item[0] for item in poi_list]
        sql = f"""
            select bid from poi_spatial_classify where classify ~ '临街POI' and bid=Any(%s)
        """
        return self.queryall(sql, [poi_bid_list])

    def get_pushed_intelligence_by_geom(self, geom=None, type=None, outside_ids=None, parent_id=None,
                                        park_access_id=None, bids=None, intelligence_ids=None, status=None,
                                        created_at=None):
        """
        获取已推送的停车场情报
        Args:
            geom:
            type:
            parent_id:

        Returns:

        """
        sql = f"""
            select id from park_access_intelligence  
            where 1=1
        """
        args = []
        if geom:
            sql += " and st_intersects(geom,st_geomfromtext(%s,4326))"
            args.append(geom)
        if type:
            sql += f" and type = %s"
            args.append(type)
        if outside_ids:
            sql += f" and outside_id = ANY(%s)"
            args.append(outside_ids)
        if parent_id:
            sql += f" and bid = %s"
            args.append(parent_id)
        if park_access_id:
            sql += f" and park_access_bid = %s"
            args.append(park_access_id)
        if bids:
            sql += f" and bid = ANY(%s)"
            args.append(bids)
        if intelligence_ids:
            sql += f" and id = ANY(%s)"
            args.append(intelligence_ids)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if created_at:
            sql += f" and created_at >= %s"
            args.append(created_at)
        return self.queryone(sql, args)

    def get_park_intelligence_by_access_bid(self, access_bid, strategy=None, strategy_type=None, status=None):
        """
        通过停车场BID获取策略ID
        Args:
            access_bid:
        Returns:
        """
        sql = f"""
            select id from park_access_intelligence where park_access_bid = %s
        """
        args = [access_bid]
        if strategy:
            sql += f" and strategy = %s"
            args.append(strategy)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if strategy_type:
            sql += f" and type = %s"
            args.append(strategy_type)
        return self.queryone(sql, args)

    def get_park_intelligence(self, status=None, face_ids=None, batch=None):
        """
        获取已推送的停车场情报
        Args:
            status:
            face_ids:

        Returns:

        """
        args = []
        sql = f"""
            select face_id, st_astext(geom),face_id from park_storefront_intelligence where 1=1
        """
        if status:
            sql += " and status = ANY(%s)"
            args.append(status)
        if face_ids:
            sql += " and face_id = ANY(%s)"
            args.append(face_ids)
        if batch:
            sql += " and batch = %s"
            args.append(batch)
        return self.queryall(sql, args)

    def get_access_add_intelligence(self, bids=None, status=None, 
        intelligence_type=None, 
        id=None,
        limit=None):
        """
        获取出入口新增的情报
        Args:
           status 状态
        Returns:
        """
        if status is None:
            status = ["INIT"]
        if intelligence_type is None:
            intelligence_type = ['park_access_overground_add', 'park_access_underground_add', 'park_access_overground_out_add']

        args = ['%服务区%']
        sql = f"""
            select a.id,a.bid,a.node_id,a.link_id,st_astext(a.geom) geom, a.strategy, a.confidence, a.traj_flux, a.type
            from park_access_intelligence a
            left join park_online_data b on a.bid = b.bid
            where 1 = 1
            --- and id in (3495412)
            --- where  a.traj_flux in ('90', '100') and a.node_id != ''
            --- and b.std_tag not like %s
        """
        if id:
            sql += " and a.id = %s"
            args.append(id)
        if status:
            sql += " and a.status = ANY(%s)"
            args.append(status)
        if bids:
            sql += " and a.bid = ANY(%s)"
            args.append(bids)
        if intelligence_type:
            sql += " and a.type = ANY(%s)"
            args.append(intelligence_type)
        sql += " order by random()"
        if limit:
            sql += " limit %s"
            args.append(limit)
        return self.queryall_by_kv_cursor(sql, args)

    def get_categories_bids(self):
        """
        获取四大垂类主点信息
        Args:
            status:

        Returns:

        """
        sql = f"""
            select a.bid,poi.std_tag,poi.click_pv from poi_4categories_list_2024q4 a 
                inner join poi on a.bid=poi.bid
            order by poi.click_pv desc
        """
        return self.queryall(sql, [])

    def get_parking_bid_by_main_bid(self, main_bids):
        """
        获取主点下停车场BID
        Returns:

        """
        sql = "select bid from poi where relation_bid = ANY(%s)"
        poi_list = self.queryall(sql, [main_bids])
        if poi_list:
            bids = [item[0] for item in poi_list]
            main_bids += bids

        sql = f"""
            select distinct bid,show_tag,parent_id from park_online_data 
            where std_tag = '交通设施;停车场' and parent_id = ANY(%s)
        """
        return self.queryall(sql, [main_bids])

    def get_park_access_intelligence_test(self, type: str, status=None, bids=None, strategy=None, pv=0):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = []
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid from park_access_intelligence a
                inner join park_statistical_data b on a.bid = b.bid 
            where 1=1 
        """
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        return self.queryall(sql, args)

    def get_park_access_intelligence(self, type: str, status=None, bids=None, strategy=None, pv=0, work_type=None):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid from park_access_intelligence a
                inner join park_statistical_data b on a.bid = b.bid 
            where type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if strategy:
            sql += f" and a.strategy = ANY(%s)"
            args.append(strategy)
        if pv:
            sql += f" and c.pv > %s"
            args.append(pv)
        print(sql)
        print(args)
        return self.queryall(sql, args)


    def get_park_access_intelligence_mistake(self, type: str, status=None, 
        bids=None, strategy=None, pv=0, work_type=None, is_get_all_access=False):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        # args = [type]
        args = []
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid 
                from park_access_intelligence a
            inner join  park_online_data b on a.bid = b.bid 
            inner join park_statistical_data c on a.bid = c.bid  
            inner join poi_nav_500m_pv d on c.root_bid = d.bid 
            where a.type in ('park_access_mistake') and a.status = 'INIT' 
            and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
            --- and d.pv > 30
            and b.city_name in ('上海市', '北京市', '广州市', '深圳市')
            and a.strategy not in ('PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE_ONE_ACCESS')
            --- and b.city_name in ('上海市', '北京市', '广州市', '深圳市', '成都市', '重庆市')
        """
        print(is_get_all_access)
        print(work_type)
        if is_get_all_access and work_type == 'MISTAKE':
            sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid 
                from park_access_intelligence a
            inner join  park_online_data b on a.bid = b.bid 
            inner join park_statistical_data c on a.bid = c.bid  
            inner join poi_nav_500m_pv d on c.root_bid = d.bid 
            where a.type in ('park_access_mistake') and a.status = 'INIT' 
            and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
        """
        # link 无效
#         sql = """
# select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid 
# from park_access_intelligence a 
# inner join park_online_data d on a.bid = d.bid 
# inner join park_statistical_data b on a.bid = b.bid 
# left join poi_nav_500m_pv c on c.bid = b.root_bid 
# where  a.status = 'INIT'
# and type in ('park_access_mistake') 
# and strategy = 'PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OFFLINE_CHECHANG'
#         """
        if work_type == 'RONGYU':
            sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid 
                from park_access_intelligence a
            inner join  park_online_data b on a.bid = b.bid 
            inner join park_statistical_data c on a.bid = c.bid  
            inner join poi_nav_500m_pv d on c.root_bid = d.bid 
            where a.type in ('park_access_rongyu') and a.status = 'INIT' 
            and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
            --- and d.pv > 30
            and b.city_name in ('上海市', '广州市')
            --- and b.city_name in ('上海市', '北京市', '广州市', '深圳市')
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if strategy:
            sql += f" and a.strategy = ANY(%s)"
            args.append(strategy)
        if pv:
            sql += f" and c.pv > %s"
            args.append(pv)
        print(sql)
        print(args)
        return self.queryall(sql, args)



    def get_park_access_intelligence_accept(self, type: str, status=None, bids=None, strategy=None, pv=0):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid from park_access_intelligence a
                inner join park_low_accept_list b on a.bid = b.park_bid 
                inner join park_online_data c on a.bid = c.bid 
            where a.type = %s
        """
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid from park_access_intelligence a
                inner join park_low_accept_list b on a.bid = b.park_bid 
                inner join park_online_data c on a.bid = c.bid 
            where c.city_name in ('上海市', '北京市') and  a.type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        if pv:
            sql += f" and b.arrived_pv  > %s"
            args.append(pv)
        sql += " order by b.arrived_pv  desc"
        print(sql)
        print(args)
        # exit(1)
        return self.queryall(sql, args)

    def get_park_access_intelligence_rongyu(self, type: str, status=None, bids=None, strategy=None, pv=0):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select a.id,a.bid,a.parent_bid,st_astext(a.geom),a.guid,a.park_access_bid from park_access_intelligence a
                inner join park_statistical_data b on a.bid = b.bid 
            where type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if strategy:
            sql += f" and a.strategy = ANY(%s)"
            args.append(strategy)
        if pv:
            sql += f" and b.parent_pv > %s"
            args.append(pv)
        sql += " order by b.parent_pv desc"
        # print(sql)
        # print(args)
        # exit(1)
        return self.queryall(sql, args)

    def get_park_access_by_wkt(self, park_bid, wkt=None, status=None):
        """
        获取停车场出入口
        Args:
            park_bid:
            wkt:
            status:

        Returns:

        """
        args = [park_bid]
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom), road_relation,status from park_online_data
            where parent_id = %s 
            and std_tag = '出入口;停车场出入口'
        """
        if wkt:
            sql += f" and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)"
            args.append(wkt)
        if status:
            sql += f" and status = %s"
            args.append(status)
        return self.queryall(sql, args)

    def get_gate_front_park_access_intelligence(self, type: str, status=None, face_ids=None, strategy=None):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom),a.source_id,a.park_access_bid,a.node_id,a.link_id
            from park_access_intelligence a
                left join park_storefront_intelligence b on a.outside_id = b.face_id
            where a.type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if face_ids:
            sql += f" and a.outside_id = ANY(%s)"
            args.append(face_ids)
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        return self.queryall(sql, args)

    def get_competitor_park_nav_list_by_bids(self, bids: list):
        """
        获取停车场竞品引导点
        Args:
            bids:

        Returns:

        """
        if not bids or len(bids) == 0:
            return None
        sql = f"""
            select bid,name,st_astext(geom) as geom,gid from competitor_park_nav where bid = ANY(%s)
        """
        return self.queryall_by_kv_cursor(sql, [bids])

    def get_rongyu_park_bids(self):
        """
        获取冗余出入口
        """
        # sql = f"""
        #     select distinct(bid) from park_access_intelligence where type = 'park_access_rongyu' and status = 'INIT'
        # """
        sql = f"""
            select bid from park_online_data where show_tag in ('地上停车场')
        """
        print(sql)
        return self.queryall_by_kv_cursor(sql, [])

    def get_4categories_park_bids(self):
        """
        获取四大垂类主点
        Args:
            bids:

        Returns:

        """
        sql = f"""
            select bid,root_bid,root_std_tag,pv from park_4categories_list_2024q4 
            union all 
            select bid,root_bid,root_std_tag,pv from first_push_park_4categories_list_2024q4
            union all 
            select bid,root_bid,root_std_tag,pv from dynamic_park_4categories_list_2024q4
        """
        return self.queryall_by_kv_cursor(sql, [])

    def get_4categories_near_park_bids(self):
        """
        获取四大垂类主点
        Args:
            bids:

        Returns:

        """
        sql = f"""
            select bid,root_bid,root_std_tag,pv from first_push_park_4categories_list_2024q4
        """
        return self.queryall_by_kv_cursor(sql, [])

    def get_park_pv_change_bids(self):
        """
        获取停车场pv变化的bids
        Args:

        Returns:

        """
        sql = f"""
            select bid,root_bid,root_std_tag,pv from dynamic_park_4categories_list_2024q4
        """
        return self.queryall_by_kv_cursor(sql, [])

    def get_park_add_intelligence(self, handle_status=list, park_intelligence_ids=None, project=None, limit=None):
        """
        获取待处理的停车场新增情报
        Args:
            handle_status:
            park_intelligence_ids:
            project:

        Returns:

        """
        args = []
        sql = f"""
            select id,bid,show_tag,st_astext(geom) as geom,parent_bid,name from effect_raising_create_park
            where 1=1
        """
        if handle_status and len(handle_status) > 0:
            sql += " and handle_status = ANY(%s)"
            args.append(handle_status)
        if park_intelligence_ids:
            sql += " and id = ANY(%s)"
            args.append(park_intelligence_ids)
        if project:
            sql += " and project != %s"
            args.append(project)
        if limit:
            sql += " limit %s"
            args.append(limit)
        return self.queryall_by_kv_cursor(sql, args)

    def get_gate_front_park_access_intelligence_by_kv(self, type: str, status=None, face_ids=None, strategy=None, pv=0,
                                                      bids=None, park_access_bids=None):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id,b.pv,b.parent_pv,a.road_relation,b.root_pv,
                a.strategy,b.show_tag,a.park_access_bid,a.strategy_value,a.batch_number
            from park_access_intelligence a 
                left join park_statistical_data b on a.bid = b.bid
            where a.type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if face_ids:
            sql += f" and a.outside_id = ANY(%s)"
            args.append(face_ids)
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        if int(pv) > 0:
            sql += f" and b.root_nav_pv >= %s"
            args.append(pv)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if park_access_bids:
            sql += f" and a.park_access_bid = ANY(%s)"
            args.append(park_access_bids)

        sql += " order by b.parent_pv desc"
        return self.queryall_by_kv_cursor(sql, args)

    def get_pushed_park_access_intelligence_by_geom_by_kv(self, geom=None, bid=None, outside_id=None, type=None,
                                                          created_at=None, status=None, remark=None, source_ids=None):
        """
        获取已推送的停车场情报
        Args:
            geom:
            park_bid:
            outside_id:
            type:

        Returns:

        """
        args = []
        sql = f"""
            select id,st_astext(geom) geom,node_id,link_id,bid,outside_id,outside_task_id from park_access_intelligence 
            where 1=1
        """
        if geom:
            sql += " and st_intersects(geom,st_geomfromtext(%s,4326))"
            args.append(geom)
        if bid:
            sql += " and bid = %s"
            args.append(bid)
        if outside_id:
            sql += " and outside_id = %s"
            args.append(outside_id)
        if type:
            sql += " and type = ANY(%s)"
            args.append(type)
        if created_at:
            sql += " and created_at > %s"
            args.append(created_at)
        if status:
            sql += " and status = %s"
            args.append(status)
        if remark:
            sql += " and remark = %s"
            args.append(remark)
        if source_ids:
            sql += " and source_id = ANY(%s)"
            args.append(source_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_access_add_intelligence_by_kv(self, type: str, status=None, face_ids=None, strategy=None, pv=0,
                                                      bids=None, is_get_all_access=False):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:
        Returns:
        """
        # args = [type]
        args = []
# 上量
        sql = f"""
        select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id,d.pv,c.parent_pv,c.root_pv
        from park_access_intelligence a
        inner join  park_online_data b on a.bid = b.bid 
        inner join park_statistical_data c on a.bid = c.bid  
        inner join poi_nav_500m_pv d on c.root_bid = d.bid 
        where a.type in ('park_access_underground_add', 'park_access_overground_add') and a.status = 'INIT' 
        and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
        --- and d.pv > 30
        and (c.precise10 = 0 or a.traj_flux = '100') 
        and b.city_name in ('上海市', '广州市')
        --- and b.city_name in ('上海市', '北京市', '广州市', '深圳市')
    """     
        sql = f"""
        select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id,d.pv,c.parent_pv,c.root_pv
        from park_access_intelligence a
        inner join  park_online_data b on a.bid = b.bid 
        inner join park_statistical_data c on a.bid = c.bid  
        inner join poi_nav_500m_pv d on c.root_bid = d.bid 
        where a.type in ('park_access_underground_add', 'park_access_overground_add') and a.status = 'INIT' 
        and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
        and (c.precise10 = 0 or a.traj_flux = '100') 
        and b.city_name in ('上海市', '北京市', '广州市', '深圳市')  
        --- and  c.parent_std_tag != '房地产;住宅区'  
        --- and (c.precise10 = 0 or (c.precise10 != 0 and c.pv > 10))
        """  
# 新增
#         sql = f"""
#             select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
#                 a.park_access_bid,a.node_id,a.link_id,a.outside_id,b.pv,c.parent_pv,c.root_pv
#         from park_access_intelligence a
#          left join park_important_list c on a.bid = c.bid
#          inner join park_online_data d on a.bid = d.bid
#          left join poi_nav_500m_pv b on c.root_bid = b.bid
# where d.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
# and a.status = 'INIT'
# --- and strategy in ('COMMON_POI_NAVI_COMPETITOR_DIFF_JILI')
# and strategy in ('NEW_PARKING_ADD')
# --- and strategy in ('park_access_auto_need_manual_check')
# and type in ('park_access_underground_add', 'park_access_overground_add')
#         """

        sql = f"""
            select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id,b.pv,c.parent_pv,c.root_pv
        from park_access_intelligence a
         left join park_important_list c on a.bid = c.bid
         inner join park_online_data d on a.bid = d.bid
         left join poi_nav_500m_pv b on c.root_bid = b.bid
where d.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
and a.status = 'INIT'
--- and strategy in ('COMMON_POI_NAVI_COMPETITOR_DIFF_JILI')
--- and strategy in ('NEW_PARKING_ADD')
and strategy in ('park_access_auto_need_manual_check')
and type in ('park_access_underground_add', 'park_access_overground_add')
        """
        # # 例行化
        # sql = f"""
        #     select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
        #             a.park_access_bid,a.node_id,a.link_id,a.outside_id,c.pv,c.parent_pv,c.root_pv
        #     from park_access_intelligence a
        #     inner join  park_online_data b on a.bid = b.bid 
        #     inner join park_statistical_data c on a.bid = c.bid  
        #     inner join poi_nav_500m_pv d on c.root_bid = d.bid 
        #     where a.type in ('park_access_underground_add', 'park_access_overground_add') and a.status = 'INIT' 
        #     and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
        #     and d.pv > 30
        #     and (c.precise10 = 0 or a.traj_flux = '100') 
        #     and b.city_name in ('上海市', '北京市', '广州市', '深圳市')
        # """      
        # 例行化
#         sql = """
# select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
#         a.park_access_bid,a.node_id,a.link_id,a.outside_id,b.pv,c.parent_pv,c.root_pv
#         from park_access_intelligence a
#          inner join park_important_list c on a.bid = c.bid
#          inner join park_online_data d on a.bid = d.bid
#          left join poi_nav_500m_pv b on c.root_bid = b.bid
# where d.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
# and a.status = 'INIT'
# --- and d.city_name in ('上海市', '广州市', '成都市', '重庆市', '深圳市', '北京市')
# and b.pv > 30 
# and c.precise10 != 1
# and type in ('park_access_underground_add', 'park_access_overground_add')
# """

        if is_get_all_access:
            sql = """
            select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id
            from park_access_intelligence a
            inner join park_online_data b on a.bid = b.bid 
            where a.type in ('park_access_underground_add', 'park_access_overground_add') and a.status = 'INIT' 
            and b.show_tag in ('地上停车场', '地下停车场', '停车场', '立体停车场')
            """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if face_ids:
            sql += f" and a.outside_id = ANY(%s)"
            args.append(face_ids)
        if strategy:
            sql += f" and a.strategy = ANY(%s)"
            args.append(strategy)
        if int(pv) > 0:
            sql += f" and c.root_pv >= %s"
            args.append(pv)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        print(sql)
        print(args)
        return self.queryall_by_kv_cursor(sql, args)
    

    def get_park_access_by_kv(self, park_bid=None, wkt=None, status=None, show_tags=None, bids=None):
        """
        获取停车场出入口
        Args:
            park_bid:
            wkt:
            status:
            show_tags:
        Returns:

        """
        args = []
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom) as geom, road_relation,status,name,show_tag
            from park_online_data
                where std_tag = '出入口;停车场出入口'
        """
        if park_bid:
            sql += f" and parent_id = %s"
            args.append(park_bid)
        if wkt:
            sql += f" and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)"
            args.append(wkt)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if show_tags:
            sql += f" and show_tag = ANY(%s)"
            args.append(show_tags)
        if bids:
            sql += f" and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_auto_access_by_park_bid(self, park_bid):
        """
        获取停车场出入口
        Args:
            park_bid:

        Returns:

        """
        args = [park_bid]
        sql = f"""
            select node_id, link_id from park_access_intelligence
            where bid = %s and status = 'PUSHED'
        """
        return self.queryall_by_kv_cursor(sql, args)

    def get_auto_access_new_by_park_bid(self, park_bid):
        """
        获取停车场出入口
        Args:
            park_bid:

        Returns:

        """
        args = [park_bid]
        sql = f"""
            select long_node_id as node_id, long_link_id as link_id from park_access_add_task 
            where park_bid = %s and status = 'pushed'
        """
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_from_4category(self, park_bid):
        """
        获取停车场父点 包含的所有停车场
        Args:
            parent_id:
        Returns:
        """
        args = [park_bid]
        sql = f"""
                 select root_std_tag from park_4categories_list_2024q4 where bid = %s
        """
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_from_4category_near(self, park_bid):
        """
        获取停车场父点 包含的所有停车场
        Args:
            parent_id:
        Returns:
        """
        args = [park_bid]
        sql = f"""
                 select root_std_tag from first_push_park_4categories_list_2024q4 where bid = %s
        """
        return self.queryall_by_kv_cursor(sql, args)

    def get_access_by_node_buffer(self, node_buffer_wkt, node_id=None):
        """
        通过节点获取停车场
        Args:
            node_id:

        Returns:

        """
        args = [node_buffer_wkt]

        sql = f"""
            select bid, parent_id
            from park_online_data 
            where std_tag = '出入口;停车场出入口' 
                and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)
        """
        if node_id:
            sql += f""" and road_relation is not null and
            EXISTS (
            SELECT 1
            FROM jsonb_array_elements(road_relation::jsonb -> 'link_info') AS link_info_element
            WHERE (link_info_element ->> 'node_id')::text = %s
            )"""
            args.append(node_id)
        return self.queryall_by_kv_cursor(sql, args)

    def get_poi_info_by_bid(self, bid):
        """
        查询POI信息
        :param bids:
        :return:
        """
        sql = f"""
            select 
                bid, std_tag, relation_bid, name, address, click_pv
            from poi 
            where poi.bid = %s
        """
        return self.queryone(sql, [bid])

    def check_park_has_node(self, short_node_id, park_id):
        """
        通过停车场和node 获取停车场
        :param short_node_id:
        :param park:
        :return:
        """
        sql = f"""
            select bid, road_relation_childrens, parent_id, show_tag
            from park_online_data where std_tag != '出入口;停车场出入口' 
            and  road_relation_childrens is not null and
            EXISTS (
                SELECT 1
                FROM jsonb_array_elements(road_relation_childrens::jsonb -> 'link_info') AS link_info_element
                WHERE (link_info_element ->> 'node_id')::text = %s
            ) and bid = %s
        """
        park = self.queryone(sql, [short_node_id, park_id])
        if park:
            return True
        return False

    def get_parking_list_by_kv(self, show_tags=None, bids=None, gcj_geom=None, area=None, park_spec=None):
        """
        停车场 & POI数据获取
        Args:
            show_tag:

        Returns:

        """
        sql = f"""
            select 
                bid, name, show_tag, parent_id,st_astext(gcj_geom) as geom,road_relation,road_relation_childrens,
                st_astext(show_area) as show_area,park_spec,st_astext(area) as area,st_astext(gcj_geom) as gcj_geom,
                address,city_name,std_tag,status
            from park_online_data where 1 = 1
        """
        args = []
        if show_tags:
            sql += f" and show_tag = ANY(%s) "
            args.append(show_tags)
        if bids:
            sql += f" and bid = ANY(%s) "
            args.append(bids)
        if gcj_geom:
            sql += f" and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)"
            args.append(gcj_geom)
        if area:
            sql += f" and area is not null and st_intersects(st_geomfromtext(%s, 3857), area)"
            args.append(area)
        if park_spec:
            sql += f" and park_spec = %s"
            args.append(park_spec)
        return self.queryall_by_kv_cursor(sql, args)

    def get_competitor_park(self, gid=None, space_attr=None, poi_tag=None, relation_bid=None, bids=None):
        """
        获取竞品停车场
        Args:

        Returns:

        """
        sql = f"""
            select relation_bid,st_astext(geom) as geom from competitor_park where 1 = 1
        """
        args = []
        if gid:
            sql += f" and gid = %s"
            args.append(gid)
        if space_attr:
            sql += f" and space_attr = %s"
            args.append(space_attr)
        if poi_tag:
            sql += f" and poi_tag = %s"
            args.append(poi_tag)
        if relation_bid:
            sql += f" and relation_bid = %s"
            args.append(relation_bid)
        if bids:
            sql += f" and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_competitor_village_by_park_bids(self, park_bids: list):
        """
        获取小区竞品来源
        Args:
            park_bids:

        Returns:

        """
        sql = f"""
            select st_astext(b.geom) as geom from competitor_park a 
                inner join competitor_park b on a.gid = b.parent_id 
            where a.bid=ANY(%s)
        """
        return self.queryall_by_kv_cursor(sql, [park_bids])

    def get_competitor_access_nav_by_park_bids(self, park_bids: list, access_type=None):
        """
        获取竞品停车场出入口引导点
        Args:
            park_bids:
        Returns:
        """
        sql = f"""
            select st_astext(a.geom) as geom from competitor_park a 
                inner join competitor_park_nav b on b.gid = a.parent_id 
            where b.bid=ANY(%s)
        """
        if access_type:
            sql += f" and a.name ~ '({access_type})'"

        print(sql)
        return self.queryall_by_kv_cursor(sql, [park_bids])

    def get_nearby_competitor_infos(self, gcj_wkt):
        """
        获取百度5km内的高德数据
        """
        sql = f"""
            select gid, name, st_astext(geom), space_attr, poi_tag
            from competitor_park 
            where ST_DWithin(geom, st_geomfromtext(%s, 4326), 0.045)
        """
        return self.queryall(sql, [gcj_wkt])

    def get_gid_from_bid(self, bid):
        """
        根据bid获取gid
        """
        sql = f"select competitor_id from competitor_id where bid='{bid}'"
        return self.queryone(sql, [])

    def get_competitor_info_by_gids(self, gids):
        """
        根据bids获取抓取竞品信息
        """
        sql = f"""
               select gid, name, st_astext(geom), space_attr, poi_tag
               from competitor_park 
               where gid=ANY(%s)
        """
        return self.queryall(sql, [gids])

    def get_park_info(self, park_bid, status=None):
        """
        获取百度停车场信息
        """
        args = [park_bid]
        sql = f"""
            select name, show_tag, st_astext(gcj_geom), open_limit_new, fee, std_tag, bid, parent_id, st_astext(area), address
            from park_online_data where bid = %s
        """
        if status:
            sql += f" and status = %s"
            args.append(status)

        return self.queryone(sql, args)

    def is_history_manual_worked(self, bid):
        """
        人工历史作业
        """
        sql = f"""
                select bid from park_manual_work_achieve where bid = '{bid}'
            """
        return self.queryone(sql, [])

    def get_park_category(self, park_bid):
        """
        停车场垂类
        医院                 |   18603
        公司                 |  183349
        机场&火车站          |    4333
        体育场馆&展览馆&剧院 |    6056
        酒店                 |    4472
        政府                 |   72358
        学校                 |   44850
        景区                 |   34578
        其他                 |  238258
        写字楼               |   54809
        长途汽车站           |    3919
        住宅区               |  337222
        大学                 |    7343
        购物中心             |   14825
        """
        sql = f"""
            select category from park_statistical_data where bid = '{park_bid}'
        """
        return self.queryone(sql, [])

    def get_poi_by_bids_kv(self, bids=None, geom=None):
        """
        查询POI信息
        Args:
            bids:
            geom:

        Returns:

        """
        args = []
        sql = f"""
            select bid, st_astext(geometry) as geom, relation_bid, click_pv, name, address,city,show_tag,std_tag 
            from poi where 1=1
        """
        if bids:
            sql += " and bid = ANY(%s)"
            args.append(bids)
        if geom:
            sql += " and st_intersects(st_geomfromtext(%s, 4326), geometry)"
            args.append(geom)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_intelligence_by_project(self, handle_status=list):
        """
        获取待处理的停车场情报
        Args:
            handle_status:
            park_intelligence_ids:
            project:

        Returns:

        """
        sql = f"""
            select id,bid,show_tag,st_astext(geom) as geom,parent_bid,name from effect_raising_create_park
            where 1=1 and handle_status = ANY(%s)
        """
        return self.queryall_by_kv_cursor(sql, [handle_status])

    def get_gate_front_achieve(self):
        """
        获取门前停车场面
        Returns:

        """
        sql = f"""
            select distinct st_astext(psi.geom) as geom,gfa.link_id,gfa.node_id from gate_front_achieve gfa 
                inner join park_access_intelligence pai on gfa.source_id=pai.source_id 
                inner join park_storefront_intelligence psi on pai.outside_id=psi.face_id 
            where pai.outside_id != ''
        """
        return self.queryall_by_kv_cursor(sql, [])

    def is_manual_check(self, park_bid, created_at=None):
        """
        判断是否人工核实过出入口
        Args:
            pg:
            park_bid:

        Returns:

        """
        # 判断人工是否干预过的出入口
        sql = f"""
            select id from park_manual_work_achieve where bid=%s
        """
        args = [park_bid]
        if created_at:
            sql += " and created_at >= %s"
            args.append(created_at)
        return self.queryone(sql, args)

    def get_sub_pois_new(self, park_bid):
        """
        get_sub_pois
        """
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom) as geom, road_relation,status from park_online_data
            where parent_id = %s 
            and std_tag = '出入口;停车场出入口'
        """
        return self.queryall_by_kv_cursor(sql, [park_bid])

    def get_no_conclusion_park_access_intelligence(self, bids, intelligence_types=None):
        """
        获取没有结论情报数据
        Args:
            geom:
            park_bid:
            outside_id:
            type:

        Returns:

        """
        sql = f"""
            select id,bid,st_astext(geom) geom,node_id,link_id,type,source_id,guid from park_access_intelligence 
            where conclusion = '' and status = 'PUSHED'
        """
        args = []
        if bids:
            sql += f"and bid=ANY(%s)"
            args.append(bids)
        if intelligence_types:
            sql += f"and type=ANY(%s)"
            args.append(intelligence_types)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_storefront_result(self, bids=None, geom=None, park_spec=None, show_tag=None, status=None, source=None,
                                   park_ids=None, city=None, type=None, id=None):
        """
        获取门前停车场面成果数据
        Args:
            bids:
            geom:

        Returns:

        """
        sql = f"""
            select a.bid,st_astext(geom) as geom,a.face_id,a.task_id,a.id,a.name,a.address,a.type,a.status,
                st_astext(a.central_line) as central_line, track_ids, track_url, zhongyuan_complete 
            from park_storefront_prod_parking a 
            where a.bid != '' and bid_status='effected'
        """
        args = []
        if bids:
            sql += f" and a.bid=ANY(%s)"
            args.append(bids)
        if geom:
            sql += f" and st_intersects(a.geom,ST_GeomFromText(%s))"
            args.append(geom)
        if park_spec:
            sql += f" and park_spec = %s"
            args.append(park_spec)
        if show_tag:
            sql += f" and show_tag = %s"
            args.append(show_tag)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if source:
            sql += f" and source = %s"
            args.append(source)
        if park_ids:
            sql += f" and id = ANY(%s)"
            args.append(park_ids)
        if city:
            sql += f" and city = %s"
            args.append(city)
        if type:
            sql += f" and type = %s"
            args.append(type)
        if id:
            sql += f" and id = %s"
            args.append(id)
        return self.queryall_by_kv_cursor(sql, args)

    def get_repaired(self, status=None, reason=None, prev_tab=None, username=None):
        """
        获取修复消息
        """
        qry = f"""
        select id, status, args, prev_id, prev_tab 
        from park_storefront_repair 
        where 1=1 
        """
        args = []
        if status:
            qry += f" and status = %s"
            args.append(status)
        if reason:
            qry += f" and reason = %s"
            args.append(reason)
        if prev_tab:
            qry += f" and prev_tab = %s"
            args.append(prev_tab)
        if username:
            qry += f" and user_name = %s"
            args.append(username)
        return self.queryall_by_kv_cursor(qry, args)

    def get_street_id_by_geom(self, geom):
        """
        根据geom获取街区信息
        Returns:

        """
        sql = f"""
            select face_id, geom from street_region 
            where st_intersects(geom, ST_GeomFromText(%s)) order by st_area(geom) limit 1;
        """
        return self.queryone_by_kv_cursor(sql, [geom])

    def get_tmp_park_access_bids(self, park_bid):
        """
        获取临时停车场出入口情报
        Returns:

        """
        sql = f"""
            select park_access_bid from park_access_intelligence where bid = %s
        """
        return self.queryall_by_kv_cursor(sql, [park_bid])

    def get_park_storefront_strategy_list(self, task_ids=None, face_ids=None):
        """
        获取门前停车场策略表信息
        Returns:

        """
        sql = f"""
           select task_id,face_id,prev_face_ids,id from park_storefront_strategy where 1=1
        """
        args = []
        if task_ids:
            sql += " and task_id=ANY(%s)"
            args.append(task_ids)
        if face_ids:
            sql += " and face_id=ANY(%s)"
            args.append(face_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_storefront_intelligence(self, status=None, face_ids=None, task_ids=None, strategy_ids=None):
        """
        获取差分完成之后待挖掘出入口的门前面
        Args:
            status:
            face_ids:
            task_ids:

        Returns:

        """
        args = []
        sql = f"""
            select a.id,a.face_id,st_astext(b.geom) as geom,a.result,a.bid,a.task_id,a.access_intelligence_ids 
            from park_storefront_strategy_diff a
                left join park_storefront_strategy b on a.strategy_id = b.id
            where 1=1
        """
        if status:
            sql += " and a.status = ANY(%s)"
            args.append(status)
        if face_ids:
            sql += " and a.face_id = ANY(%s)"
            args.append(face_ids)
        if task_ids:
            sql += " and a.task_id = ANY(%s)"
            args.append(task_ids)
        if strategy_ids:
            sql += " and a.strategy_id = ANY(%s)"
            args.append(strategy_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_storefront_prod_access_list(self, task_ids=None, face_ids=None, parent_bids=None, geom=None,
                                             status=None, ids=None, not_statuses=None):
        """
        获取门前停车场策略表信息
        Returns:
        """
        sql = f"""
           select id,bid,road_relation,st_astext(geom) as geom, suggest_is_open, check_memo, status  
            from park_storefront_prod_access where 1=1 and status != 'CANCEL' 
        """
        args = []
        if task_ids:
            sql += " and task_id=ANY(%s)"
            args.append(task_ids)
        if face_ids:
            sql += " and face_id=ANY(%s)"
            args.append(face_ids)
        if parent_bids:
            sql += " and parent_bid=ANY(%s)"
            args.append(parent_bids)
        if geom:
            sql += " and st_intersects(geom,ST_GeomFromText(%s, 4326))"
            args.append(geom)
        if status:
            sql += " and status = ANY(%s)"
            args.append(status)
        if not_statuses:
            sql += f" and status <> ALL (%s)"
            args.append(not_statuses)
        if ids:
            sql += " and id = ANY(%s)"
            args.append(ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_competitor_village_by_park_bids(self, park_bids: list):
        """
        获取小区竞品来源
        Args:
            park_bids:

        Returns:

        """
        sql = f"""
            select st_astext(b.geom) as geom,b.name from competitor_park a 
                inner join competitor_park b on a.gid = b.parent_id 
            where a.bid=ANY(%s)
        """
        return self.queryall_by_kv_cursor(sql, [park_bids])

    def get_park_access_turing_result(self, data_type: int, status='INIT', source_id=None, turing_ids=None):
        """
        获取图灵出入口作业成果
        Args:
            data_type:
            status:
            source_id:

        Returns:

        """
        sql = f"""
            select data_type,turing_id,sourceid,bp_message from parking_turing_result where 1=1
        """
        args = []
        if data_type:
            sql += f" and data_type = %s"
            args.append(data_type)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if source_id:
            sql += f" and sourceid = %s"
            args.append(source_id)
        if turing_ids:
            sql += f" and turing_id = ANY(%s)"
            args.append(turing_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_central_line_by_bid(self, bid: str) -> Optional[str]:
        """
        根据给定的停车场 bid，获取其面的中心线
        """
        sql = """
            select st_astext(central_line) from park_storefront_prod_parking
            where bid = %s and bid_status = 'effected' 
        """
        ret = self.queryone(sql, [bid])
        return ret[0] if ret and ret[0] else None

    def get_invalid_link_origin_geom(self, long_link_ids=None, short_link_ids=None):
        """
        获取失效LINK原始坐标
        Args:
            long_link_ids:
            short_link_ids:

        Returns:

        """
        sql = f"""
            select link_id, geom from nav_link_history where 1=1
        """
        args = []
        if long_link_ids:
            sql += " and long_link_id = ANY(%s)"
            args.append(long_link_ids)
        if short_link_ids:
            sql += " and link_id = ANY(%s)"
            args.append(short_link_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_navigate_traj(self, long_link_ids=None, gps_time=None, geom=None, bids=None):
        """
        获取导航轨迹
        Returns:

        """
        sql = f"""
            select long_link_id, short_link_id, st_astext(geom) as geom,traj_id, bid from navigate_traj where 1=1
        """
        args = []
        if long_link_ids:
            sql += " and long_link_id = ANY(%s)"
            args.append(long_link_ids)
        if gps_time:
            sql += " and gps_time >= %s"
            args.append(gps_time)
        if geom:
            sql += " and st_intersects(geom, st_geomfromtext(%s, 4326))"
            args.append(geom)
        if bids:
            sql += " and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_navigate_traj_new(self, long_link_ids=None, gps_time=None, geom=None, bids=None):
        """
        获取导航轨迹
        Returns:

        """
        sql = f"""
            select long_link_id, short_link_id, st_astext(geom) as geom,traj_id, bid from navigate_traj where 1=1
        """
        args = []
        if long_link_ids:
            sql += " and long_link_id = ANY(%s)"
            args.append(long_link_ids)
        if gps_time:
            sql += " and gps_time >= %s"
            args.append(gps_time)
        if geom:
            sql += " and st_intersects(geom, st_geomfromtext(%s, 4326))"
            args.append(geom)
        if bids:
            sql += " and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_access_intelligence_by_nav_pv(self, type: str, status=None, face_ids=None, strategy=None, pv=0,
                                               bids=None, city_names=None, show_tags=None):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select distinct a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom) as geom,a.source_id,
                a.park_access_bid,a.node_id,a.link_id,a.outside_id,b.pv,b.parent_pv, b.root_nav_pv
            from park_access_intelligence a 
                inner join park_important_list b on a.bid = b.bid
            where a.type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if face_ids:
            sql += f" and a.outside_id = ANY(%s)"
            args.append(face_ids)
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        if int(pv) > 0:
            sql += f" and b.root_nav_pv >= %s"
            args.append(pv)
        if bids:
            sql += f" and a.bid = ANY(%s)"
            args.append(bids)
        if city_names:
            sql += f" and b.city_name = ANY(%s)"
            args.append(city_names)
        if show_tags:
            sql += f" and b.show_tag = ANY(%s)"
            args.append(show_tags)
        sql += " order by b.root_nav_pv desc"
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_import_list(self, bids=None):
        """
        获取停车场高准集合
        Returns:

        """
        args = []
        sql = f"""
            select bid from park_important_list where 1=1
        """
        if bids:
            sql += f" and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_access_intelligence_add_by_strategy(self, strategy=None, bid_lists=None, limit=None):
        """
        获取已推送的停车场情报
        Args:
            geom:
            park_bid:
            outside_id:

        Returns:

        """
        sql = f"""
        # sql = f"""
        # select a.id,a.bid,a.node_id,a.link_id,a.type,a.strategy,st_astext(a.geom) as geom,a.created_at
        # from park_access_intelligence a
        #     inner join park_online_data b on a.bid = b.bid
        #     inner join park_statistical_data c on c.bid = a.bid
        # where a.status = 'INIT'
        #     and a.type in ('park_access_overground_add', 'park_access_underground_add')
        #     and a.strategy not in ('COMMON_POI_NAVI_COMPETITOR_DIFF')
        #     and c.root_pv >= 5000
        # """

        sql = """
            select a.id,a.bid,a.node_id,a.link_id,a.type,a.strategy,st_astext(a.geom) as geom,a.created_at 
            from park_access_intelligence a
                inner join park_online_data b on a.bid = b.bid  
                inner join park_luce_list c on c.bid = a.bid 
            where a.status = 'INIT' 
                and strategy not in ('COMMON_POI_NAVI_COMPETITOR_DIFF')
                and confidence not in ('70','80','90','100')
                and a.type in ('park_access_overground_add') 
        """
        args = []
        if strategy:
            sql += " and a.strategy = %s"
            args.append(strategy)
        if bid_lists:
            sql += " and a.bid = ANY(%s)"
            args.append(bid_lists)
        if limit:
            sql += " limit %s"
            args.append(limit)
        print(sql)
        print(args)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_by_bid(self, bid=None):
        """
        获取停车场信息
        Args:
            bid:

        Returns:

        """
        sql = f"""
            select bid, parent_id, name, std_tag, show_tag, st_astext(gcj_geom) as point_wkt, st_astext(area) as area_wkt 
            from park_online_data
            where bid = %s
        """
        args = [bid]
        return self.queryall_by_kv_cursor(sql, args)

    def get_access_by_bid(self, park_bid, access_type=None, open_limit_new_not=None):
        """
        get_sub_pois
        """
        args = [park_bid]
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom) as gcj_geom, road_relation, status, name, open_limit_new 
            from park_online_data
            where parent_id = %s 
            and std_tag = '出入口;停车场出入口' 
        """
        if access_type:
            sql += " and name like %s"
            args.append(f"%{access_type}")
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_root_bid(self, park_bid):
        """
        获取停车场根节点
        """
        sql = "select root_bid from park_statistical_data where  bid = %s"
        return self.queryall(sql, [park_bid])

    def get_didi_traj_by_park_bid(self, park_bid, limit=5000):
        """
        获取didi轨迹
        """
        sql = f"""
            select st_astext(geom) from navigate_traj where bid = %s limit %s
        """
        return self.queryall(sql, [park_bid, limit])

    def get_qb_add_park_prepush_community(self, bids=None, spacing=None):
        """
        获取小区自动化新增停车场
        Returns:

        """
        args = []
        sql = f"""
            select parent_bid as poi_bid, st_astext(geom) as add_point from park_add_juejin_achievement where 1=1
        """
        if bids:
            sql += " and parent_bid = ANY(%s)"
            args.append(bids)
        if spacing:
            sql += " and spacing = %s"
            args.append(spacing)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_statistical_data(self, bids=None, precise10=None):
        """
        获取停车场统计数据
        Args:
            bids:

        Returns:

        """
        args = []
        sql = f"""
            select a.bid,a.precise10,a.status from park_statistical_data a inner join park_important_list b on a.bid = b.bid where 1=1 
        """
        if bids:
            sql += " and a.bid = ANY(%s)"
            args.append(bids)
        if precise10:
            sql += " and a.precise10 = %s"
            args.append(precise10)
        return self.queryall_by_kv_cursor(sql, args)

    def get_park_add_juejin_achievement(self, bids=None):
        """
        获取小区自动化新增地下停车场
        Returns:

        """
        args = []
        sql = f"""
            select parent_bid,st_astext(geom) as geom from park_add_juejin_achievement where 1=1
        """
        if bids:
            sql += " and parent_bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_light_check(self, intelligence_id=None, status=None):
        """
        获取灯光检测结果
        """
        sql = f"""
            select id,intelligence_id,img_content,status from park_access_light_check 
        """
        args = []
        if intelligence_id:
            sql += " where intelligence_id = %s"
            args.append(intelligence_id)
        return self.queryone_by_kv_cursor(sql, args)
        
    def get_light_check_list(self, bids=None, intelligence_id=None, status=None, conclusion_txt=None, 
        limit=None, update_time_sort=None, intelligence_type=None):
        """
        获取灯光检测结果
        """
        sql = f"""
            select id,intelligence_id,park_bid,img_num,img_content,status,long_node_id,long_link_id,
                st_astext(node_geom) as node_geom,type from park_access_light_check 
            where 1 = 1 
            --- and intelligence_id in (select id from park_access_intelligence where status = 'INIT' and strategy in ('OVERGROUND_OUT_VEHICLE_TRAJ_AREA_INTERSECT', 'OVERGROUND_OUT_VEHICLE_TRAJ_SAME_CUID', 'OVERGROUND_BYD_TRAJ_AREA_INTERSECT_LINK', 'OVERGROUND_BYD_TRAJ_AREA_INTERSECT_GATE'))
        """
        args = []
        if bids:
            sql += " and park_bid = ANY(%s)"
            args.append(bids)
        if intelligence_id:
            sql += " and intelligence_id = ANY(%s)"
            args.append(intelligence_id)
        if status:
            sql += " and status = ANY(%s)"
            args.append(status)
        if conclusion_txt:
            sql += " and conclusion_txt = %s"
            args.append(conclusion_txt)
        if intelligence_type:
            sql += " and type = ANY(%s)"
            args.append(intelligence_type)
        if update_time_sort:
            sql += " order by random()"
            # sql += " order by update_time desc"
        if limit:
            sql += " limit %s"
            args.append(limit)
        # print(sql)
        # print(args)
        return self.queryall_by_kv_cursor(sql, args)

    def get_light_check_verify_by_img_id(self, img_id):
        """
        获取灯轻核实结果
        """
        sql = "select conclusion,conclusion_txt from park_storefront_verify where image_id = %s \
         and create_at > '2025-06-11 00:00:00'"
        return self.queryone_by_kv_cursor(sql, [img_id])

    def get_access_by_buffer(self, buffer_wkt, node_id=None, parent_show_tag=None, access_type='入口'):
        """
        通过节点获取停车场
        Args:
            node_id:

        Returns:

        """
        args = [buffer_wkt]
        sql = f"""
            select a.bid, a.parent_id
            from park_online_data a
            inner join park_online_data b on a.parent_id = b.bid
            where a.std_tag = '出入口;停车场出入口' 
                and st_intersects(st_geomfromtext(%s, 4326), a.gcj_geom)
        """
        if access_type:
            if access_type == '入口':
                sql += f" and a.name like %s"
                args.append('%入口')
            elif access_type == '出口':
                sql += f" and (a.name like %s or a.name like %s)"
                args.append('%出口')
                args.append('%出入口')
        if parent_show_tag:
            sql += " and b.show_tag = ANY(%s) "
            args.append(parent_show_tag)
        if node_id:
            sql += f""" and a.road_relation is not null and
            EXISTS (
            SELECT 1
            FROM jsonb_array_elements(a.road_relation::jsonb -> 'link_info') AS link_info_element
            WHERE (link_info_element ->> 'node_id')::text = %s
            )"""
            args.append(node_id)
        # print(sql)
        # print(args)
        return self.queryall_by_kv_cursor(sql, args)
    
    def get_store_poi_by_geom(self, buffer_wkt, std_tag=None):
        """
        临街poi数量获取
        """
        args = [buffer_wkt]
        sql = f"""
            select bid
            from poi
            where st_intersects(st_geomfromtext(%s, 4326), geometry)
        """
        if std_tag:
            sql += " and std_tag = ANY(%s) "
            args.append(std_tag)
        # print(sql)
        # print(args)
        return self.queryall_by_kv_cursor(sql, args)
        
    def get_sub_pois_by_main_id(self, geom=None, main_bid=None, 
        parent_show_tag=None, access_type='入口', exlude_park_bid=None):
        """
        get_sub_pois_by_main_id
        """
        sql = f"""
            select a.bid, a.parent_id, st_astext(a.gcj_geom), a.road_relation, a.status, a.name, a.open_limit_new 
            from park_online_data a
            inner join park_online_data b on a.parent_id = b.bid
            where b.parent_id = %s  and a.status = 1
            and a.std_tag = '出入口;停车场出入口' and a.name like %s
        """
        name_end_index = '%入口' if access_type == '入口' else '%出口'
        args = [main_bid, name_end_index]
        if exlude_park_bid:
            sql += " and a.bid != ANY(%s)"
            args.append(exlude_park_bid)
        if parent_show_tag:
            sql += " and b.show_tag = ANY(%s)"
            args.append(parent_show_tag)
        if geom:
            sql += "and st_intersects(st_geomfromtext(%s, 4326), a.gcj_geom)"
            args.append(geom)
        return self.queryall(sql, args)

    def get_park_statics_data(self, park_bid):
        """
        get_park_statics_data
        """
        sql = f"""
            select a.root_pv,a.precise10,b.pv as root_nav_pv from park_statistical_data a 
            left join poi_nav_500m_pv b on a.root_bid = b.bid
            where a.bid = %s
        """
        return self.queryall(sql, [park_bid])