"""
获取人工作业结果
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())

from src.door_naming.DaoBeeflow import <PERSON>o<PERSON><PERSON><PERSON>
from typing import List, Dict
from src.aoi.aoi_protection import cfg, context
from src.aoi.aoi_protection.parse_human import unify_parser, sqlite_parser
import datetime

month_ago = datetime.date.today() - datetime.timedelta(days=60)


class Ctx:
    """
    context
    """
    batch_type: int = cfg.WORK_TYPE_MANUAL
    wait_handle_task_id = []
    task_human_data: Dict[str, List[context.HumanHandleData]] = {}


def load_wait_handle_task(ctx: Ctx):
    """
    获取等待处理的任务
    :param ctx:
    :return:
    """
    with DaoBeeflow(rw=True) as dao:
        sql = 'select distinct(b.task_id) from' \
              ' aoi_protect_information a' \
              ' inner join strategy_feature_list b on a.qb_id=b.id ' \
              ' where a.batch_type=%s and a.handle_status=%s and b.work_status > 1 and b.task_id > 0' \
              ' and a.created_at > %s limit 500'
        dao.cursor_beeflow.execute(sql, [
            ctx.batch_type,
            cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE,
            month_ago.strftime('%Y-%m-%d')
        ])
        ret = dao.cursor_beeflow.fetchall()
        diff_task_id = [x[0] for x in ret]
        for item in diff_task_id:
            print(f'开始处理任务id: {item}')
            # 检查任务是否有
            sql = 'select wpf_id, wpf_section, wpf_status from work_package_flow' \
                  ' where wpf_task_id=%s order by wpf_id asc'
            dao.cursor_beeflow.execute(sql, [item])
            ret = dao.cursor_beeflow.fetchall()
            # 10 数据准备, 20 作业 , 30 质检 , 40 验收
            # 50 融合 55 图幅拆分 ...
            print(f"==== 任务id:{item}, 当前存在的状态:{[(x[1], x[2]) for x in ret]}")
            has_merge_task = [(x[1], x[2]) for x in ret if x[1] >= 50]
            if len(has_merge_task) < 1:
                print(f" 任务id:{item} 当前无任务进行融合状态,跳过")
            else:
                last_wpf_id = None
                # 获取最后一个作业任务
                for (wpf_id, wpf_section, wpf_status) in ret:
                    if wpf_section == 20:
                        if wpf_status != 5:
                            continue
                        last_wpf_id = wpf_id
                print(f"任务id:{item}-获取任务:{last_wpf_id}")
                if last_wpf_id is None:
                    print(f"任务id:{item}-无法获取作业任务, 确认是否有问题")
                else:
                    ctx.wait_handle_task_id.append((item, last_wpf_id))


def parse_human_data(ctx: Ctx):
    """
    解析人工结果
    :param ctx:
    :return:
    """
    if len(ctx.wait_handle_task_id) < 1:
        print("==== 无需要解析的人工作业任务 ====")
        return
    for (task_id, wpf_id) in ctx.wait_handle_task_id:
        print(f"解析任务:{task_id} - 对应的wpf_id: {wpf_id}")
        # 从履历获取. 没有就尝试从sqlite获取
        try:
            ret = unify_parser.load_from_unify(task_id, wpf_id)
        except Exception as e:
            print("解析履历失败, 尝试从sqlite获取")
            ret = None
        if not ret:
            ret = sqlite_parser.load_from_sqlite(task_id, wpf_id)
        if ret:
            ctx.task_human_data[task_id] = ret


def sync_human_data(ctx: Ctx):
    """
    同步人工作业结论
    :param ctx:
    :return:
    """
    if len(ctx.task_human_data) < 1:
        print("==== 无任务需要同步 ====")
        return
    with DaoBeeflow(rw=True) as dao:
        for task_id, task_lists in ctx.task_human_data.items():
            for task_item in task_lists:
                task_item: context.HumanHandleData
                sql = 'select id from strategy_feature_list where bid=%s and task_id=%s'
                dao.cursor_beeflow.execute(sql, [task_item.bid, task_id])
                ret = dao.cursor_beeflow.fetchone()
                if ret:
                    print(f"同步人工作业状态: {task_item.bid} - {ret[0]} - 结论: {task_item.wpf_human_status}")
                    sql = 'update aoi_protect_information set can_offline=%s, handle_status=%s where qb_id=%s'
                    dao.cursor_beeflow.execute(sql, [task_item.wpf_human_status, cfg.HANDLE_STATUS_SUCCESS, ret[0]])


def run(work_type):
    """
    任务处理
    :return:
    """
    work_type_int = cfg.MANUAL_HIGH if work_type == 'high' else cfg.MANUAL_COMMON
    ctx = Ctx()
    ctx.batch_type = work_type_int
    print("step 1: 获取待处理的任务")
    load_wait_handle_task(ctx)
    print("step 2: 解析人工作业结果")
    parse_human_data(ctx)
    print("step 3: 同步人工作业结果")
    sync_human_data(ctx)


if __name__ == '__main__':
    if len(sys.argv) >= 2:
        action = sys.argv[1]
        if action in ['common', 'high']:
            run(action)
    else:
        print("\n使用方式: py39 run_parse_manual_result.py <type> \n"
              "     其中: <type> common = 常规任务  high = 高优任务")
