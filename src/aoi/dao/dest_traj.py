# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
公共资料库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class DestTraj(PgDao):
    """
    公共资料库
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "dest_traj" in conf:
            poi_online_r_conf = conf['dest_traj']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("dest_traj 配置不存在")

    def get_nav_gate_traffic(self, long_node_ids: list):
        """
        获取大门通行性
        Args:
            long_node_ids:

        Returns:

        """
        sql = f"""
           select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
           where long_node_id = ANY(%s)
       """
        return self.queryall(sql, [long_node_ids])

    def get_turn_off_points(self, bounds_wkt: str):
        """
        获取熄火点信息
        Returns:

        """
        sql = """
            select points from doctor_traj_turn_off_cluster_20240530 
            where st_intersects(bounds, st_geomfromtext(%s, 4326)) ;
        """
        return self.queryall(sql, [bounds_wkt])

    def get_dest_traj_list_by_bid(self, bid: str, geom: None, limit: None):
        """
        获取终点轨迹
        Args:
            limit:
            bid:
            geom:

        Returns:

        """
        sql = f"""
            select st_astext(geom) from dest_traj_cluster where bid= %s
        """
        args = [bid]
        if geom:
            sql += f" and st_intersects(geom, st_geomfromtext(%s, 4326))"
            args.append(geom)
        if limit:
            sql += f" limit %s"
            args.append(limit)
        return self.queryall(sql, args)

    def get_pushed_park_access(self, park_bids=None, create_time=None):
        """
        获取已推送的停车场信息
        Args:
            park_bids:
            create_time:

        Returns:

        """
        sql = f"""
            select road_relation,park_bid from push_park_access_re_record 
            where status=2
        """
        args = []
        if park_bids and len(park_bids) > 0:
            sql += f" and park_bid=ANY(%s)"
            args.append(park_bids)
        if create_time:
            sql += f" and create_time > %s"
            args.append(create_time)
        return self.queryall(sql, args)

    def get_push_park_access_by_bid(self, park_bids=None):
        """
        获取推送的停车场信息
        Args:
            park_bids:

        Returns:

        """
        sql = f"select park_bid,status from push_park_access_re_record where 1=1 and status = 2"
        arags = []
        if park_bids:
            sql += f" and park_bid=ANY(%s)"
            arags.append(park_bids)
        return self.queryall(sql, arags)

    def get_node_passage_by_long_node_id(self, long_node_id):
        """
        根据node_id获取node的所有passage
        """
        args = [long_node_id]
        sql = f"""
            select id,gate_id,node_id,tag,type_desc,dir_desc,long_node_id,traversability,type,nnww_tag,in_link_id,
                out_link_id,passage,node_geom,is_rengong_result,create_time from gates_semantic 
            where long_node_id=%s
        """
        return self.queryall_by_kv_cursor(sql, args)

    def get_node_passage_by_short_node_id(self, short_node_id):
        """
        根据node_id获取node的所有passage
        """
        args = [short_node_id]
        sql = f"""
            select id,gate_id,node_id,tag,type_desc,dir_desc,long_node_id,traversability,type,nnww_tag,in_link_id,
                out_link_id,passage,node_geom,is_rengong_result,create_time from gates_semantic 
            where node_id=%s
        """
        return self.queryall_by_kv_cursor(sql, args)
