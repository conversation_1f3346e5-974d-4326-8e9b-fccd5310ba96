"""
人工作业监控
"""
import os
import sys

from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())

from src.door_naming.DaoBeeflow import Dao<PERSON>ee<PERSON>
from src.common import common_tool
import time


def run_aoiv1():
    # 忽略的批次
    batch_ignore = ['20240614184004_xdisg', '20240615233013_odhah', '20240616233014_duklf', '20240617233013_yrhrq',
                    '20240618233025_iozrw', '20240619233019_oelrq', '20240621233013_fvavy', '20240622233011_ixlcx',
                    '20240623233011_xzkka', '20240624150324_urcff', '20240625233014_cnnuh', '20240626233020_atemy',
                    '20240627233112_sfefc', '20240628233055_hsuts', '20240629233013_ktvdr', '20240630233049_gathi',
                    '20240702020608_hvzfo', '20240703094633_fghwx', '20240704233015_uvfol', '20240705233032_szugg',
                    '20240706233013_vtnbc', '20240707233009_vbdwv', '20240708233023_pxjib', '20240709233010_xsjwl',
                    '20240710233021_tgfkr', '20240711233011_qgvqf', '20240713233017_olkqc', '20240714233041_dpmnc',
                    '20240715233026_lshbb', '20240719165706_aleyr', '20240720233008_belcs', '20240722233050_xilty',
                    'low_lt_200_pv', 'lt_200_pv', 'manual_20240702', 'mis_recall_relation_20240613',
                    'mis_recall_relation_20240614', 'mis_recall_relation_20240615', 'mis_recall_relation_20240616',
                    'mis_recall_relation_20240617', 'mis_recall_relation_20240618', 'mis_recall_relation_20240620',
                    'mis_recall_relation_20240621', 'mis_recall_relation_20240622', 'mis_recall_relation_20240623',
                    'mis_recall_relation_20240624', 'mis_recall_relation_20240625', 'mis_recall_relation_20240627',
                    'mis_recall_relation_20240628', 'mis_recall_relation_20240703', 'mis_recall_relation_20240723',
                    'sub_poi_gate_strategy_2024-06-13', 'sub_poi_gate_strategy_2024-06-14',
                    'sub_poi_gate_strategy_2024-06-17', 'sub_poi_gate_strategy_2024-06-18',
                    'sub_poi_gate_strategy_2024-06-20', 'sub_poi_gate_strategy_2024-06-21',
                    'sub_poi_gate_strategy_2024-06-22', 'sub_poi_gate_strategy_2024-06-23',
                    'sub_poi_gate_strategy_2024-06-24', 'sub_poi_gate_strategy_2024-06-25',
                    'sub_poi_gate_strategy_2024-06-26', 'sub_poi_gate_strategy_2024-06-27',
                    'sub_poi_gate_strategy_2024-06-28', 'sub_poi_gate_strategy_2024-06-29',
                    'sub_poi_gate_strategy_2024-07-02', 'sub_poi_gate_strategy_2024-07-03',
                    'sub_poi_gate_strategy_2024-07-04', 'sub_poi_gate_strategy_2024-07-05',
                    'sub_poi_gate_strategy_2024-07-06', 'sub_poi_gate_strategy_2024-07-07',
                    'sub_poi_gate_strategy_2024-07-08', 'sub_poi_gate_strategy_2024-07-09',
                    'sub_poi_gate_strategy_2024-07-10', 'sub_poi_gate_strategy_2024-07-11',
                    'sub_poi_gate_strategy_2024-07-13', 'sub_poi_gate_strategy_2024-07-14',
                    'sub_poi_gate_strategy_2024-07-24']
    batch2 = ('交通设施', '交通设施;其他',
              '交通设施;飞机场', '交通设施;火车站',
              '交通设施;地铁站', '旅游景点',
              '旅游景点;其他', '旅游景点;公园',
              '旅游景点;动物园', '旅游景点;植物园'
              )

    with DaoBeeflow() as dao:
        sql = 'select count(*) as ct from strategy_feature_list' \
              ' where batch_id not in %s and strategy_type = 62 and work_status = 1' \
              ' and std_tag not in %s and create_time > %s'
        data = (tuple(batch_ignore), batch2, '2024-07-24')
        dao.cursor_beeflow.execute(sql, data)
        ret = dao.cursor_beeflow.fetchone()
        print('总数：{}'.format(ret[0]))
        today = time.strftime("%Y-%m-%d %H:%M", time.localtime())
        common_tool.send_hi('统计时间:{} 人工待作业详情同步，'
                            '存在空产风险请及时反馈， 谢谢\n '
                            '【AOI1.0 关联关系】待作业量级: {} '.format(today, ret[0]),
                            atuserids=['v_yinyu01'],
                            token='d8a9c953b21bc221ee856296c0b3e7203')


def run_aoiv2():
    """
    AOI2.0 统计
    :return:
    """
    strategy_type_dir = 63
    strategy_type_dir_merge = 68
    strategy_type_final = 67
    zh_cn = {
        63: 'AOI2.0 方位错误',
        68: 'AOI2.0 关联合并',
        67: 'AOI2.0 终极语义化',
    }
    batch2 = ('交通设施', '交通设施;其他',
              '交通设施;飞机场', '交通设施;火车站',
              '交通设施;地铁站', '旅游景点',
              '旅游景点;其他', '旅游景点;公园',
              '旅游景点;动物园', '旅游景点;植物园',
              '购物;购物中心', '购物', '购物;超市',
              '购物;便利店', '购物;其他',
              )
    msg = ""
    for strategy_type in [strategy_type_dir, strategy_type_dir_merge, strategy_type_final]:
        with DaoBeeflow() as dao:
            sql = 'select count(*) as ct from strategy_feature_list' \
                  ' where strategy_type = %s and work_status = 1' \
                  ' and std_tag not in %s'
            dao.cursor_beeflow.execute(sql, [strategy_type, batch2])
            ret = dao.cursor_beeflow.fetchone()
            ct = ret[0]
            msg += "【{}】:待作业量级: {} \n".format(zh_cn[strategy_type], ct)
    common_tool.send_hi('AOI2.0待作业统计信息：\n {}'.format(msg),
                        atuserids=['v_yinyu01'],
                        token='d8a9c953b21bc221ee856296c0b3e7203')


if __name__ == '__main__':
    run_aoiv1()
    run_aoiv2()
