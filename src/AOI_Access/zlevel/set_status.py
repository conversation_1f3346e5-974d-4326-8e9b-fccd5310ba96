#-*- coding: utf-8 -*-
"""
根据bid 标记 70 米范围内的标记是否有立交 
"""
import time
from uuid import uuid4
import psycopg2
import requests
import json

curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()

backConf = {"db": "master_back",
            "user": "master_back_se_ro", "pwd": "mapread",
            "host": "**************", "port": 5432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}
# poicd正式
poicdConf = {"db": "poi_cd",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

transConf = {"db": "trans_id",
            "user": "trans_id_se_rw", "pwd": "cpmkukky",
            "host": "*************", "port": 5432}
# psql -U trans_id_se_rw -Wcpmkukky -p 5432 -h ************* -d trans_id

#连接poi库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

#连接poicd库
poicdDbConn = psycopg2.connect(database=poicdConf["db"],
                              user=poicdConf["user"],
                              password=poicdConf["pwd"],
                              host=poicdConf["host"],
                              port=poicdConf["port"])
poicdDao = poicdDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()

 # 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接trans库
transDbConn = psycopg2.connect(database=transConf["db"],
                              user=transConf["user"],
                              password=transConf["pwd"],
                              host=transConf["host"],
                              port=transConf["port"])
transDao = transDbConn.cursor()

suffix = '_20230703'
todo_table = "bid_zlevel_todo_lists"
blu_face = "blu_face" + suffix
nav_zlevel = "nav_zlevel" + suffix

def process():
    """
    process 正式处理流程
    """
    # status 0待处理  1有立交 ，2 70 米内有立交 ，3 无立交 ，4aoi 不存在
    # sql = "select id,bid,node_id,gate_id,type from {} where status = 0".format(parkding_table)
    while True:
        sql = "select id,bid from {} where status = 0".format(todo_table)
        # sql = "select id,bid from {} where bid = '15954667431393339134' ".format(todo_table)
        poiDao.execute(sql)
        bids_lists = poiDao.fetchall()
        if bids_lists is None or len(bids_lists) == 0:
            print('todo list none.')
            break
        # 循环 bids_lists  
        for v in bids_lists:
            try:
                status = handlerBid(v)
                print("result:" + str(status))
            except:
                print('try_error...bid=%s' % (v[1]))
    return True

def handlerBid(v):
    """
    handler 处理函数
    """
    id = v[0]
    bid = v[1]

    # 获取 poi 信息
    print(bid + 'start')
    sql = "select a.geom,ST_Buffer(a.geom,0.0007) from blu_face a \
        inner join blu_face_poi b on a.face_id = b.face_id where b.poi_bid = '{}'".format(bid)
    backgroundDao.execute(sql)
    aoiDate = backgroundDao.fetchone()
    if aoiDate is None:
        updateBidStatusByID(id, 10)
        print('blu_face_poi not exist')
        return 10
    geom = aoiDate[0]
    geom_70 = aoiDate[1]
    sql = "select zlevel_id from nav_zlevel_20230703 where ST_Contains('{}',geom) limit 1".format(geom)
    print("nav_zlevel_sql:" + sql)
    poiDao.execute(sql)
    zlevelData = poiDao.fetchall()
    print(zlevelData)
    if zlevelData and len(zlevelData) > 0:
        updateBidStatusByID(id, 1)
        return 1
    sql = "select zlevel_id from nav_zlevel_20230703 where ST_Contains('{}',geom) limit 1".format(geom_70)
    print("nav_zlevel_sql_70:" + sql)
    poiDao.execute(sql)
    zlevelData70 = poiDao.fetchall()
    print(zlevelData70)
    if zlevelData70 and len(zlevelData70) > 0:
        updateBidStatusByID(id, 2)
        return 2
    updateBidStatusByID(id, 3)
    return 3

def updateBidStatusByID(id, status):
    """
    根据id更新状态
    
    Args:
        id (int): 投标id
        status (int): 投标状态
    
    Returns:
        int: 更新成功的行数
    """
    sql = "update {} set status = {} where id = {}".format(todo_table, status, id)
    print(sql)
    res = poiDao.execute(sql)
    poiDbConn.commit()
    return res


def resetData():
    """
    重置数据的函数,根据不同的suffix执行不同的sql语句
    """
    sql = "update {} set status = 0".format(todo_table)
    print(sql)
    res = poiDao.execute(sql)
    poiDbConn.commit()

if __name__ == "__main__":
    # resetData()
    print("start" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    process()
    print("end" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))