# !/usr/bin/env python3
"""
poi服务端数据库模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session
from sqlalchemy.dialects.postgresql import JSONB
Base = declarative_base()


class BluAccessBidForYuYi(Base):
    """
    语义化2.0 bid池
    """

    __tablename__ = 'blu_access_bid_for_yuyi'
    id = Column(Integer, primary_key=True)
    invalid = Column(Integer, nullable=False, default=0)
    status = Column(Integer, nullable=False, default=0)
    main_bid = Column(String(128), nullable=False, default="")
    memo = Column(String(255), nullable=False, default="")
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return str(id)


class Poi(Base):
    """
    线上POI表
    """

    __tablename__ = 'poi'

    bid = Column(String(64), nullable=False, primary_key=True)
    mid = Column(String(64), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    name = Column(String(254), nullable=False, default="")
    eng_name = Column(String(254), nullable=False, default="")
    alias = Column(Text, nullable=False, default="")
    address = Column(String(1024), nullable=False, default="")
    district = Column(String(10), nullable=False, default="")
    telephone = Column(String(200), nullable=False, default="")
    kind = Column(String(64), nullable=False, default="")
    tag = Column(String(254), nullable=False, default="")
    std_tag = Column(String(254), nullable=False, default="")
    index_tag = Column(String(254), nullable=False, default="")
    show_tag = Column(String(254), nullable=False, default="")
    brand_code = Column(String(12), nullable=False, default="")
    food_type = Column(String(128), nullable=False, default="")
    status = Column(Integer, default=1)
    relation = Column(String(64), nullable=False, default="")
    relation_bid = Column(String(64), nullable=False, default="")
    relation_type = Column(Integer, default=-1)
    admin_code = Column(String(64), nullable=False, default="")
    display_x = Column(Float, nullable=False, default=0)
    display_y = Column(Float, nullable=False, default=0)
    arrive_x = Column(Float, nullable=False, default=0)
    arrive_y = Column(Float, nullable=False, default=0)
    link_id = Column(String(60), nullable=False, default="")
    side = Column(String(2), nullable=False, default="")
    mark = Column(String(4), nullable=False, default="")
    memo = Column(String(254), nullable=False, default="")
    city = Column(String(254), nullable=False, default="")
    geometry = Column(String)
    click_pv = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.bid


class TrafficReleaseRecords(Base):
    """
    TrafficReleaseRecords
    """

    __tablename__ = 'traffic_release_records'

    work_id = Column(String(100), primary_key=True)
    wpf_id = Column(Integer, nullable=False, default=0)
    bid = Column(String(100), nullable=False, default="")
    mid = Column(String(100), nullable=False, default="")
    name = Column(String(100), nullable=False, default="")
    std_tag = Column(String(100), nullable=False, default="")
    extra = Column(JSONB, nullable=False, default="{}")
    conclusion = Column(String(100), nullable=False, default="")
    release_status = Column(Integer, nullable=False, default=0)
    transfer_status = Column(Integer, nullable=False, default=0)
    memo = Column(String(100), nullable=False, default="")
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return self.work_id


class TrafficPushTask(Base):
    """
    traffic_push_task
    """

    __tablename__ = 'traffic_push_task'

    id = Column(Integer, primary_key=True)
    change_id = Column(Integer, nullable=False, default=0)
    status = Column(Integer, nullable=False, default=0)
    create_time = Column(DateTime, nullable=False, default=func.now())
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return str(self.id)


class GateBidChangeHistory(Base):
    """
gate_bid_change_history
    """
    __tablename__ = 'gate_bid_change_history'
    id = Column(Integer, nullable=False, primary_key=True)
    node_id = Column(String(128), nullable=False, default="")
    bid = Column(String(128), nullable=False, default="")
    face_id = Column(String(128), nullable=False, default="")
    action = Column(String(16), nullable=False, default="")
    imp_state = Column(SmallInteger, nullable=False, default=0)
    src = Column(String(1000), nullable=False, default="")
    flow_state = Column(String(1000), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    resource = Column(SmallInteger, nullable=False, default=0)
    create_time = Column(DateTime(timezone=False), nullable=False, default=func.now())
    update_time = Column(DateTime(timezone=False), nullable=False, default=func.now(), onupdate=func.now())
    pri = Column(Integer, nullable=False, default=0)
    plan_id = Column(Integer, nullable=False, default=0)
    fail_reason = Column(String(64), nullable=False, default="")
    uid = Column(String(100), nullable=False, default="")
    extra = Column(JSONB, nullable=False, default=lambda: {})

    def __str__(self):
        return str(self.id)


class BluAccessBidTask(Base):
    """
    语义化情报表
    """
    __tablename__ = 'blu_access_bid_task'

    id = Column(Integer, nullable=False, primary_key=True)  # 要素情报id
    main_bid = Column(String(100), default='')
    mesh_id = Column(String(10), default='')
    handle_status = Column(Integer, nullable=False, default=0)  # -1处理失败 0未处理 1完成 2处理中 3重复数据
    created_at = Column(DateTime(timezone=False), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=False), nullable=False, default=func.now())
    uid = Column(String(100), default='')
    batch_id = Column(String(100), nullable=False, default="")  # 具体情报批次id,可为空
    batch_name = Column(String(100), nullable=False, default="")  # 具体情报批次名称,可为空
    sync_status = Column(Integer, nullable=False, default=0)  # 0默认 1同步成功 2待同步
    is_manual = Column(Integer, nullable=False, default=0)  # 0 否, 1是
    extra = Column(JSONB, nullable=False, default=lambda: {})  # 额外信息
    manual_tag = Column(String(50), default='')  # 人工处理标签
    qb_id = Column(Integer, default=0)
    strategy_type = Column(Integer, default=63)
    change_record_id = Column(Integer, default=0)

    def __str__(self):
        return str(self.id)


class PoiNewStdTag(Base):
    """
    poi_new_std_tag
    """
    __tablename__ = 'poi_new_std_tag'

    bid = Column(String(50), primary_key=True)
    new_std_tag = Column(String(200), default='')
    new_std_tag_id = Column(String(100), default='')

    def __str__(self):
        return self.bid
