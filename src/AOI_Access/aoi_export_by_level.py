#-*- coding: utf-8 -*-
"""
导出aoi level=3的样本导出
输出字段包含：bid、name、node_id_str_match、node_id_all、diff=node_id_all-node_id_str_match
一行1个AOI
"""
import time
import psycopg2
import requests
import json


curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}
 
# poi测试
# poiConf = {"db": "poi_online",
#             "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
#             "host": "**************", "port": 9432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}

transConf = {"db": "trans_id",
            "user": "trans_id_se_rw", "pwd": "cpmkukky",
            "host": "*************", "port": 5432}

#连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()

# 连接trans库
transDbConn = psycopg2.connect(database=transConf["db"],
                              user=transConf["user"],
                              password=transConf["pwd"],
                              host=transConf["host"],
                              port=transConf["port"])
transDao = transDbConn.cursor()


# 获取pv表名称
pv_table_list_sql = "SELECT tablename FROM pg_tables WHERE tablename \
     like 'ft_nav_gate_202%' and length(tablename) = 20 order by tablename desc limit 2"
pvDao.execute(pv_table_list_sql)
pv_table_list_res = pvDao.fetchall()
if pv_table_list_res is None or len(pv_table_list_res) == 0:
    print("pv查询表名查询失败")
    exit()
pv_table = pv_table_list_res[1][0]
print("pv_table:" + pv_table)

# 获取高置信表名称
high_gate_table_sql =  "SELECT tablename FROM pg_tables WHERE tablename \
     like 'gates_track_confidence_bmf_202%' and length(tablename) = 35 order by tablename desc limit 2"
pvDao.execute(high_gate_table_sql)
high_gate_table_res = pvDao.fetchall()
if high_gate_table_res is None or len(high_gate_table_res) == 0:
    print("高置信大门表不存在")
    exit()
high_gate_table = high_gate_table_res[1][0]
print("high_gate_table:" + high_gate_table)     

school = ['教育培训;幼儿园', '教育培训;小学', '教育培训;中学']

def updateAoiGateByBid(id, level):
    """
    更新 aoi level
    """
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = "update aoi_gate_match_batch_result \
        set level=%s, status=1, update_time='%s' where id=%d" % (level, update_time, id)
    print("sql：" + sql)
    poiDao.execute(sql)
    poiDbConn.commit()


def checkAoiIsL2(fw, face_id, node_id_str, is_inculded_emergency_door=False):
    """
    判断是否是 L2 AOI level 
    is_inculded_emergency_door 是否包含紧急门 False 不包含，True 包含
    """
    node_id_list = node_id_str.split(",")
    # 选择AOI范围内大门
    print("开始查询AOI")
    # 查询AOI信息
    sql = "select st_astext(geom) from blu_face where face_id = '{}'".format(face_id)
    backgroundDao.execute(sql)
    aoi_res = backgroundDao.fetchone()
    if aoi_res is None or len(aoi_res) == 0:
        print("aoi不存在{}".format(face_id))
        return False

    print("开始查询道路大门")
    # 查询AOI对应道路大门
    sql = "select node_id from nav_node nn where st_intersects(st_geomfromtext('{}',4326), nn.geom)".format(aoi_res[0])
    roadDao.execute(sql)
    all_node_id_list_res = roadDao.fetchall()
    if all_node_id_list_res is None or len(all_node_id_list_res) == 0:
        print("node大门查询失败{}".format(face_id))
        return node_id_str + "\t" + node_id_str + "\t" + " \t" + 'node大门查询失败' + "\n"

    valid_node_id = set()
    for node_info in all_node_id_list_res:
        valid_node_id.add(node_info[0])

    print("开始查询gate link")
    sql = "select node_id,in_linkid,out_linkid,type \
        from nav_gate where node_id in('{}')".format("','".join(valid_node_id))
    roadDao.execute(sql)
    nav_gate_list_res = roadDao.fetchall()
    if nav_gate_list_res is None or len(nav_gate_list_res) == 0:
        print("gate大门查询为空：{}".format(face_id))
        return node_id_str + "\t" + node_id_str + "\t" + " \t" + 'gate大门查询为空：' + "\n"

    # 高置信数据修复门属性
    high_gate_lists, __= get_high_gate_list(node_id_str)
    high_gate_map = {}
    for gate in high_gate_lists:
        high_gate_map[gate[3]] = gate
    link_node_map = {}
    link_list = set()
    if is_inculded_emergency_door:
        #包含紧急门
        for nav_gate_info in nav_gate_list_res:
            link_list.add(nav_gate_info[1])
            link_list.add(nav_gate_info[2])
            link_node_map[nav_gate_info[1]] = nav_gate_info[0]
            link_node_map[nav_gate_info[2]] = nav_gate_info[0]
    else:
        # 排除紧急门
        for nav_gate_info in nav_gate_list_res:
            if nav_gate_info[0] in high_gate_map:
                temp_list = high_gate_map[nav_gate_info[0]]
                #高置信的紧急门排除
                if temp_list[1] == '0':
                    continue
            else:
                #非高置信中的紧急门排除
                if nav_gate_info[3] == 0:
                    continue
            link_list.add(nav_gate_info[1])
            link_list.add(nav_gate_info[2])
            link_node_map[nav_gate_info[1]] = nav_gate_info[0]
            link_node_map[nav_gate_info[2]] = nav_gate_info[0]

    #查询nav_link
    # sql = "select link_id from nav_link where link_id in('{}') and  position('52' in form) > 0".format("','".join(link_list))
    sql = "select link_id from nav_link where form != '52' and link_id in('{}')".format("','".join(link_list))
    roadDao.execute(sql)
    link_list_res = roadDao.fetchall()
    if link_list_res is None or len(link_list_res) == 0:
        print("nav_link查询为空：{}".format(face_id))
        return node_id_str + "\t" + node_id_str + "\t" + " \t" + 'nav_link查询为空' + "\n"

    # 查询aoi库当前aoi关联的道路大门node 人工作业
    man_sql = "select c.node_id from blu_face a \
        inner join blu_access b  on a.face_id = b.face_id \
        inner join blu_access_gate_rel c  on b.access_id = c.access_id \
        where a.face_id = '{}'".format(face_id)
    backgroundDao.execute(man_sql)
    man_list_res = backgroundDao.fetchall()
    gate_node_id = set()
    for link_info in link_list_res:
        gate_node_id.add(link_node_map[link_info[0]])
    for node_id in node_id_list:
        gate_node_id.add(node_id)
    if man_list_res:
        for man_node_id in man_list_res:
            gate_node_id.add(man_node_id[0])
            node_id_list.append(man_node_id[0])
    # 去重
    node_id_set=set(node_id_list)
    diff_set = set(gate_node_id)-set(node_id_set)
    return ','.join(node_id_set) + "\t" + ','.join(gate_node_id) + "\t" + ','.join(diff_set) + "\n"



def get_high_gate_list(node_id_str):
    """
    获取高置信大门列表
    node_id_str node 列表
    high_gate_lists [short_nodeid,traversability(str),car_restric,long_nodeid]
    """
    high_gate_lists = []
    short_nodeid_sql = "select sid, tid from image_n where sid in('{}')".format(node_id_str)
    transDao.execute(short_nodeid_sql)
    short_nodeid_lists = transDao.fetchall()
    long_to_short_map = {}
    if short_nodeid_lists:
        short_to_long_map = {}
        valid_node_id = set()
        for node_info in short_nodeid_lists:
            valid_node_id.add(node_info[1])
            short_to_long_map[node_info[1]] = node_info[0]
        high_gate_lists_sql = "select nodeid,traversability,car_restric \
            from {} where nodeid in('{}')".format(high_gate_table, "','".join(valid_node_id))
        pvDao.execute(high_gate_lists_sql)
        high_gate_lists = pvDao.fetchall()
        if high_gate_lists:
            for i in range(len(high_gate_lists)):
                temp_list = list(high_gate_lists[i])
                temp_list.append(short_to_long_map[temp_list[0]])
                high_gate_lists[i] = temp_list
                long_to_short_map[short_to_long_map[temp_list[0]]] = temp_list[0]
    return high_gate_lists, long_to_short_map


def checkSpecialAoi(bid):
    """
    特殊情形判断aoi
    """
    flag = False
    # 学校
    sql = "select std_tag from poi where bid = '{}'".format(bid)
    poiDao.execute(sql)
    data = poiDao.fetchone()
    if data:
        if data[0] in school:
            # 学校忽略紧急门 能进能出则是L2
            flag = True
    return flag


def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())) 
    # 初始化数据 
    #获取待处理的数据列表
    aoi_gate_list_sql = "select bid,face_id,success_node_id,id \
        from aoi_gate_match_batch_result where status = 1 and level = 3 order by id asc limit 10000"
    # aoi_gate_list_sql = "select bid,face_id,success_node_id,id from aoi_gate_match_batch_result \
    #    where face_id in ('76a7ff97b7ce4015b6af8266cb18d80d','dfcf2648b4b6454ba84a1ae44683b884')"
    poiDao.execute(aoi_gate_list_sql)
    aoi_gate_list = poiDao.fetchall()
    if aoi_gate_list:
        with open("/home/<USER>/chengqiong/aoi-strategy/aoi_access/aoi_export_by_level.txt", "w") as fw: 
            fw.write("bid\tcity_name\tname\tnode_id_str_match\tnode_id_all\tdiff\n")
            for v in aoi_gate_list:
                bid = v[0]
                face_id = v[1]
                node_id_str = v[2]
                id = v[3]
                sql = "select city_name,name_ch from blu_face where face_id = '{}'".format(face_id)
                backgroundDao.execute(sql)
                res = backgroundDao.fetchone()
                city_name = 'null'
                name_ch = 'null'
                if res:
                    city_name = res[0]
                    name_ch = res[1]
                print("id：" + str(id))
                print("face_id：" + face_id)
                node_id_str = node_id_str.strip(',')
                fw.write(bid + "\t" + city_name + "\t" + name_ch + "\t")

                # 0未匹配到, 1匹配到, 2 L1 AOI, 3 L2 AOI
                level = 0
                if len(node_id_str): 
                    try:
                        is_school = checkSpecialAoi(bid)
                        res = checkAoiIsL2(fw, face_id, node_id_str, is_school)
                        fw.write(res)
                    except:
                        print('try_error...id=%s' % (id))

    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

if __name__ == "__main__":
    process()