# -*- coding:utf8 -*-
"""
    poi送审数据导出
"""

import os
import sys
import re
sys.path.append("util")
import conf
import logging
import PgBase
import json
from nutspg.nutsdbapi import NutsDBApi
from nutspg.namingapi import NutsNamingApi

reload(sys)
sys.setdefaultencoding('utf8')

PG_URL = 'http://rc.guoke.map.baidu.com:8000/rcmanage/dbmanage'
DB = 'poi_data_2022'


def main(fname):
    """
    读取城市和图幅的映射文件，按城市导出poi数据文件
    Args:
        fname: 字符串类型，记录城市和图幅号映射的文件名
    """
    nuts_db=NutsDBApi(PG_URL)
    status, msg, db_info = nuts_db.getdb(DB)
    if status != 0:
        print('get info failed')
        exit()
    str_root_dir = os.getcwd() + "/poi_data"

    if not os.path.isdir(str_root_dir):
        os.mkdir(str_root_dir)
    dic = {}
    f = open(fname)
    for l in f.readlines():
        mesh_id = l.split(',')[0].strip()
        city = l.split(',')[1].strip()
        if city not in dic:
            dic[city] = list()
        dic[city].append(mesh_id)
    f.close()

    pg_helper=PgBase.PgBase(db_info)
    for d in dic:
        str_city_dir = str_root_dir + "/" + d
        if os.path.isdir(str_city_dir) is False:
            os.mkdir(str_city_dir)
        str_out = str_city_dir + "/poi"

        str_mesh = '(\'' + '\',\''.join(dic[d]) + '\')'
        sql = "DROP TABLE IF EXISTS tmp"
        pg_helper.execute(sql)
        sql = "create table tmp as select * from {0} where mesh_id in {1}".format('poi', str_mesh)
        pg_helper.execute(sql)
        sql = "select bid, mid, mesh_id, name, address, kind, admin_code, '' as district, "\
                "telephone, link_id, gcj2ll(st_setsrid(st_makepoint(display_x, display_y), 4326)) "\
                "as geometry from tmp"
        str_out = str_out + "_" + d + ".shp"
        cmd = 'ogr2ogr -f "ESRI Shapefile" %s   PG:"host=%s port=%s user=%s password=%s '\
                'dbname=%s" -dsco FORMAT=SHP -sql "%s" -lco encoding=gbk' % (str_out, \
                db_info['host'], db_info['port'], db_info['user'], db_info['passwd'], DB, sql)
        result = os.system(cmd)
        if result != 0:
            print d, "export failed"

if __name__ == "__main__":
    main(sys.argv[1])
