# -*- coding: UTF-8 -*-
################################################################################
"""
Authors: <AUTHORS>
Date:    2023/11/02 09:42:42
每日触发一次
新增出入口非子点门提准策略
"""
import os
import sys
import traceback
import datetime
import shapely.wkt
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")

from common import common_tool
from pg_tool import PgTool


class SubGatePoiImproveAccurate(PgTool):
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """

    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        提交事务。

        Args:
            无参数。

        Returns:
            无返回值。

        """
        PgTool.commit(self)

    def get_access_changed(self):
        """
        获取每天发生变更的出入口信息
        """
        today = datetime.date.today().strftime("%Y-%m-%d 19:00:00")
        yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d 19:00:00")
        sql = """
            select distinct a.access_id, name, main_bid, st_astext(geom), b.node_id
            from blu_access a inner join blu_access_gate_rel b
            on a.access_id = b.access_id
            where b.update_time > %s and  b.update_time < %s and a.access_poi_bid = ''  and a.access_poi_bid != main_bid and main_bid != ''
        """
        self.cursor_aoi.execute(sql, [yesterday, today])
        return self.cursor_aoi.fetchall()

    def get_poi_info(self, bid):
        """
        查询bid的信息
        """
        sql = "select name, st_astext(geometry) from poi where bid = %s"
        self.cursor_poi.execute(sql, [bid])
        return self.cursor_poi.fetchone()

    def gate_sub_poi_gate_nearby(self, bid, geom):
        """
        获取出入口附近主点poi的子点门poi
        """
        sql = """
            select bid,name,st_astext(geometry) as geom, alias from poi
            where relation_bid = %s
                and st_dwithin(geometry, st_geomfromtext(%s, 4326), 0.0003)
                and (name like '%%门' or name like '%%门)' or name like '%%门岗'
                    or name like '%%号口' or name like '%%号口)' or name like '%%出口' or name like '%%出口)'
                    or name like '%%入口' or name like '%%入口)' or std_tag = '出入口;门' )
                and name not like '%%停车场%%' and name not like '%%检票%%' and name not like '%%停车库%%'
                and name not like '%%停车区%%' and name not like '%%停车楼%%'
                and std_tag not like '%%购物%%' and std_tag not like '%%家居%%' and std_tag not like '%%建材%%'
                and bid != %s
        """
        self.cursor_poi.execute(sql, [bid, geom, bid])
        return self.cursor_poi.fetchall()

    def get_poi_gate_nearby(self, bid, geom):
        """
        获取出入口附近的门poi
        """
        sql = """
            select bid,name,st_astext(geometry) as geom, alias from poi
            where st_dwithin(geometry, st_geomfromtext(%s, 4326), 0.0003) and relation_bid != %s
                and (name like '%%门' or name like '%%门)' or name like '%%门岗' or std_tag = '出入口;门' )
                and name not like '%%停车场%%' and name not like '%%检票%%' and name not like '%%停车库%%'
                and name not like '%%停车区%%' and name not like '%%停车楼%%'
                and std_tag not like '%%购物%%' and std_tag not like '%%家居%%' and std_tag not like '%%建材%%'
                and bid != %s
        """
        self.cursor_poi.execute(sql, [geom, bid, bid])
        return self.cursor_poi.fetchall()

    def match_access_by_bid(self, access_poi_bid):
        """
        判断bid是否已关联了出入口
        """
        sql = f"select count(*) from blu_access where access_poi_bid = %s"
        self.cursor_aoi.execute(sql, [access_poi_bid])
        return self.cursor_aoi.fetchone()[0] > 0

    def check_valid(self, bid, sub_poi_geom, poi_geom):
        """
        验证出入口与子点poi关联数据的有效性
        """
        # 查询blu_access 是否关联, (人工关联部分)
        if self.match_access_by_bid(bid):
            return False
        # 是否穿越高等级路（跨越的需要删除）
        poi_xy = poi_geom.replace("POINT(", "").replace("POINT (", "").replace(")", "")
        door_xy = (
            sub_poi_geom.replace("POINT(", "").replace("POINT (", "").replace(")", "")
        )
        line1 = f"LINESTRING({poi_xy}, {door_xy})"
        sql = "select count(*) from nav_link where st_intersects(st_geomfromtext(%s, 4326), geom) and kind <= 7"
        self.cursor_road.execute(sql, [line1])
        return self.cursor_road.fetchone()[0] == 0

    def get_aoi_geom_by_bid(self, bid):
        """
        根据bid查询AOI的范围框信息
        """
        sql = """
            select st_astext(geom) from blu_face a inner join blu_face_poi b
            on a.face_id = b.face_id
            where poi_bid = %s and kind != '52'
        """
        self.cursor_aoi.execute(sql, [bid])
        return self.cursor_aoi.fetchone()

    def export_to_manu_check(self, bid, node_id):
        """
        下发人工核实
        """
        # 获取大门的位置信息
        sql = "select st_astext(geom) from nav_node where node_id = %s"
        self.cursor_road.execute(sql, [node_id])
        node_geom = self.cursor_road.fetchone()
        if node_geom is None:
            return
        node_geom = node_geom[0]

        # 关联关系作业成果库
        date = datetime.date.today()
        uid = f"{node_id}_{bid}_{date}"

        sql = """
            insert into gate_aoi_match_check_data (bid, node_id, source, ways, uid)
            values(%s, %s, '非子点门逻辑下发人工核实提准', '清查提准', %s)
        """
        self.cursor_poi.execute(sql, [bid, node_id, uid])

        # 一体化关联关系作业情报库
        sql = """
           insert into gate_match_sync_integration(uid, main_bid, node_id, is_manual, action, node_geom, batch_id, batch_name)
           values(%s, %s, %s, 1, 'add', st_geomfromtext(%s, 4326), %s, %s) on conflict(uid) do nothing;
        """
        self.cursor_poi.execute(
            sql,
            [uid, bid, node_id, node_geom, f"sub_poi_gate_strategy_{date}", "非子点门提准策略"],
        )

    def run(self):
        """
        运行函数，对重要的aoi清单进行buffer 40 米的策略。
        该策略有效率相对较低，但召回率高
        Args:
            无
        Returns:
            无
        """
        for (
            access_id,
            access_name,
            bid,
            access_geom,
            node_id,
        ) in self.get_access_changed():
            poi_info = self.get_poi_info(bid)
            if poi_info is None:
                continue
            poi_name = poi_info[0]
            poi_geom = poi_info[1]
            sub_poi_gate_info = self.gate_sub_poi_gate_nearby(bid, access_geom)
            related_bid = ""
            related_bid_name = ""
            for (
                bid_tmp,
                name_tmp,
                geom_tmp,
                alias_tmp,
            ) in sub_poi_gate_info:
                if self.check_valid(bid_tmp, geom_tmp, poi_geom):
                    related_bid = bid_tmp
                    break

            if related_bid != "":
                continue

            poi_gate_info = self.get_poi_gate_nearby(bid, access_geom)
            if len(poi_gate_info) == 0:
                continue
            aoi_geom = self.get_aoi_geom_by_bid(bid)
            if aoi_geom is None:
                continue
            aoi_geom = aoi_geom[0]
            min_dis = 1
            for (
                bid_tmp,
                name_tmp,
                geom_tmp,
                alias_tmp,
            ) in poi_gate_info:
                if (
                    shapely.wkt.loads(aoi_geom).distance(shapely.wkt.loads(geom_tmp))
                    > 0.0003
                ):
                    continue
                if not self.check_valid(bid_tmp, geom_tmp, poi_geom):
                    continue
                if poi_name in name_tmp or poi_name in alias_tmp:
                    continue
                dis_tmp = shapely.wkt.loads(access_geom).distance(
                    shapely.wkt.loads(geom_tmp)
                )
                if min_dis > dis_tmp:
                    min_dis = dis_tmp
                    related_bid_name = name_tmp

            if related_bid_name != "":
                poi_name = poi_name.split("-")[0].replace(")", "").replace("(", "")
                related_bid_name = (
                    related_bid_name.strip()
                    .split("-")[0]
                    .replace(")", "")
                    .replace("(", "")
                )
                num = 0
                for c in poi_name:
                    if c in related_bid_name:
                        num = num + 1
                len_name = min(len(poi_name), len(related_bid_name))
                if len_name > 0 and num / len_name <= 0.6:
                    self.export_to_manu_check(bid, node_id)
                    # print(bid + "\t" + node_id)


if __name__ == "__main__":
    with SubGatePoiImproveAccurate() as obj:
        try:
            obj.run()
        except Exception as e:
            # 报警
            common_tool.send_hi("Not-Sub-Poi-Gate-Run-Failed", ["lifan14"])
            obj.rollback()
            traceback.print_exc()
