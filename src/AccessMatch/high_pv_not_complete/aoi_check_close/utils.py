"""
一些工具
"""
import requests
from src.common import pgsql
import datetime


def try_download(data_url, file_name):
    """
    尝试下载数据
    :param data_url:
    :param file_name
    :return:
    """
    resp = requests.get(data_url, stream=True)
    if file_name.exists():
        file_name.unlink()
    if resp.status_code == 200:
        with open(file_name, 'wb') as f:
            for chunk in resp.iter_content(1024 * 10 * 10):
                f.write(chunk)
    else:
        print("Failed to get file: status code {}".format(resp.status_code))


def add_bid_complete(bid, strategy_code):
    """
    添加bid已经处理完
    :param bid:
    :param strategy_code:
    :return:
    """
    now = datetime.datetime.now()
    sql = 'insert into aoi_quality_white_bid (bid, quality_type, create_time) ' \
          'values(:bid, :strategy_code, :create_time) on conflict do nothing'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql,
                      {'bid': bid, 'strategy_code': strategy_code, 'create_time': now})
