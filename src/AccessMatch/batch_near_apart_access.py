# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
对AOI附近较近，但没有关联的大门下发人工
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""

import time
import collections
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
from access_function import AoiGateMatchGeneralTool
from shapely.geometry import Point, LineString

class AoiNearApartGateMatch(PgTool):
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        PgTool.commit(self)

    def get_gate_by_link(self, link_id):
        """
        查询是否有大门经过该link
        """
        sql = f"""
            select 
                distinct node_id 
            from nav_gate
            where 
                in_linkid = '{link_id}'
                or out_linkid = '{link_id}'
        """
        self.cursor_road.execute(sql)
        node_set = set()
        for node_id, in self.cursor_road.fetchall():
            node_set.add(node_id)
        return node_set

    def find_node_to_process_with_bid(self, bid):
        """
        根据bid寻找附近可能关联的大门信息
        Args:
            bid: poi的bid
        Return:
            face_id: aoi的主键id
            node_id_set: set类型, 大门的nodeid集合
        """
        node_id_set = set()
        node_id_apart = set()
        sql = '''
            select 
                st_astext(geom),
                kind,
                aoi_level, 
                bf.face_id 
            from 
                blu_face bf 
                inner join 
                blu_face_poi bfp
            on 
                bf.face_id = bfp.face_id 
            where 
                bfp.poi_bid = %s 
                and bf.src != 'SD'
        '''
        self.cursor_aoi.execute(sql, [bid])
        res_aoi = self.cursor_aoi.fetchone()
        if res_aoi is None or len(res_aoi) == 0:
            # print("没有关联的AOI\t" + bid)
            return "", node_id_apart
        if res_aoi[1] == '52' or res_aoi[2] != 2:
            # print("AOI为商圈或非基础院落\t" + bid)
            return "", node_id_apart
        aoi_geom = res_aoi[0]
        face_id = res_aoi[3]
        sql = '''
            with tmp as (
                select 
                   link_id, 
                   st_intersection(st_geomfromtext(%s, 4326), geom) as geom
                from nav_link 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom)
            ) 
            select 
                link_id, 
                st_astext((st_dump(geom)).geom) as geom
            from tmp;
        '''
        self.cursor_road.execute(sql, [aoi_geom, aoi_geom])
        res_point = self.cursor_road.fetchall()
        if res_point is not None and len(res_point) > 0:
            for point_geom in res_point:
                sql = """
                    select 
                        string_agg(node_id, ''',''') 
                    from nav_node 
                    where 
                        st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)
                """
                self.cursor_road.execute(sql, [point_geom[1]])
                res_node = self.cursor_road.fetchall()
                if res_node is None or len(res_node) == 0:
                    continue
                node_str = res_node[0][0]
                sql = '''
                    select 
                        node_id, 
                        in_linkid, 
                        out_linkid 
                    from nav_gate 
                    where 
                        node_id in ('%s')
                ''' % (node_str)
                self.cursor_road.execute(sql)
                res_node = self.cursor_road.fetchall()
                for res_node_tmp in res_node:
                    node_id_set.add(res_node_tmp[0])
        
        #找到AOI框外15米内的大门
        sql = f"""
            select 
                node_id
            from nav_node 
            where 
                st_dwithin(geom, st_geomfromtext('{aoi_geom}', 4326), 0.00015)
                and not st_contains(st_geomfromtext('{aoi_geom}', 4326), geom)
        """
        self.cursor_road.execute(sql)
        res_node = self.cursor_road.fetchall()
        if res_node is not None and len(res_node) > 0:
            node_str = res_node[0][0]
            sql = '''
                select 
                    node_id, 
                    link_id,
                    s_nid,
                    e_nid,
                    form
                from 
                    nav_gate a
                    inner join
                    nav_link b
                on 
                    a.in_linkid = b.link_id
                    or a.out_linkid = b.link_id
                where 
                    node_id in ('%s')
            ''' % (node_str)
            self.cursor_road.execute(sql)
            res_node = self.cursor_road.fetchall()
            aoi_region = shapely.wkt.loads(aoi_geom)
            for node_id, link_id, s_nid, e_nid, in res_node:
                if node_id not in node_id_set:
                    if s_nid == node_id:
                        far_node = e_nid
                    else:
                        far_node = s_nid
                    sql = "select st_astext(geom) from nav_node where node_id = '%s'" % (far_node)
                    self.cursor_road.execute(sql)
                    far_geom = self.cursor_road.fetchone()[0]
                    # if shapely.wkt.loads(far_geom).distance(aoi_region) -
                    # node_id_apart.add(res_node_tmp[0])
        return face_id, node_id_apart
    
    def find_near_aoi_by_node(self, node_geom):
        """
        find aoi near the node
        """
        face_dict = dict()
        sql = f"""
            select 
                face_id, 
                st_AsText(st_shortestline(st_geomfromtext('{node_geom}', 4326), geom)),
                st_astext(geom) 
            from blu_face
            where 
                st_dwithin(st_geomfromtext('{node_geom}', 4326), geom, 0.0004)
                and aoi_level = 2
                and kind != '52'
        """
        self.cursor_aoi.execute(sql)
        for face_id, line_geom, geom in self.cursor_aoi.fetchall():
            sql = f"""
                select 
                    count(*) 
                from nav_link 
                where 
                    st_intersects(st_geomfromtext('{line_geom}', 4326), geom) 
                    and kind <= 7
            """
            self.cursor_road.execute(sql)
            if self.cursor_road.fetchone()[0] == 0:
                face_dict[face_id] = geom

        return face_dict
    
    def find_inner_link_node_by_gate(self, node_id):
        """
        return:
        """
        node_list = []
        sql = f"""
            select 
                s_nid,
                e_nid
            from nav_link 
            where 
                (s_nid = '{node_id}'
                    or e_nid = '{node_id}'
                ) 
                and form = '52'
        """
        self.cursor_road.execute(sql)
        for s_nid, e_nid, in self.cursor_road.fetchall():
            node_other = s_nid
            if node_other == node_id:
                node_other = e_nid
            node_list.append(node_other)
        
        if len(node_list) == 2:
            for node_id_tmp in node_list:
                sql = f"""
                    select
                        count(*)
                    from nav_link
                    where 
                        s_nid = '{node_id_tmp}'
                        or e_nid = '{node_id_tmp}'
                """
                self.cursor_road.execute(sql)
                if self.cursor_road.fetchone()[0] == 1:
                    return [node_id_tmp]

        return node_list
    
    def run(self, num): 
        """更新大门通行属性的入口函数
        """
        # print("内外大门批处理策略开始：" + 
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))) 

        #寻找待匹配的大门和AOI的关联关系的bid列表
        aoi_gate_match_general_obj = AoiGateMatchGeneralTool(self.road_dsn)
        #face_bid_dict = aoi_gate_match_general_obj.find_bid_to_match(num)

        face_bid_dict = dict()
        with open("bidlist") as f:
            lines = f.readlines()
            for line in lines:
                bid = line.strip().split('\t')[0]
                face_bid_dict[bid] = ""

        max = len(face_bid_dict)
        process_num = 0.0
        for bid, face_id_dst in face_bid_dict.items():
            process_num = process_num + 1
            #print("附近无交割大门匹配策略进度:" + str(process_num / max))
            face_id_dst_tmp, apart_node_set = self.find_node_to_process_with_bid(bid)
            #apart_node_set是框附近的大门
            if face_id_dst == "":
                face_id_dst = face_id_dst_tmp
            if face_id_dst_tmp == "":
                continue
            if face_id_dst_tmp != face_id_dst:
                #aoi_gate_match_general_obj.set_bid_intel_fail(bid, "关联了两个AOI")
                continue

            for node_id in apart_node_set:
                #需要重新匹配的场景
                node_geom = aoi_gate_match_general_obj.get_node_geom(node_id)
                if node_geom is None:
                    continue
                face_dict = self.find_near_aoi_by_node(node_geom)
                if len(face_dict) == 1 and face_id_dst in face_dict:
                    node_info_tmp = self.find_inner_link_node_by_gate(node_id)
                    if len(node_info_tmp) == 1:
                        node_region = shapely.wkt.loads(node_geom)
                        s_dis = node_region.distance(shapely.wkt.loads(face_dict[face_id_dst]))
                        e_geom = aoi_gate_match_general_obj.get_node_geom(node_info_tmp[0])
                        e_dis = shapely.wkt.loads(e_geom).distance(shapely.wkt.loads(face_dict[face_id_dst]))
                        if e_dis - s_dis > 0.00015:
                            continue

                    print(f"{bid}\t{node_id}\t附近40米只有一个AOI")


                    # aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst, 
                    #         "add", "例行更新流程无交割大门批处理策略召回-附近40米只有一个AOI", "manu-check")
                    
            # for node_id in far_node_set:
            #     print(f"{bid}\t{node_id}\t大门link与AOI相交")
            #     # aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst, 
            #     #     "add", "例行更新流程无交割大门批处理策略召回-大门link与AOI相交", "manu-check")
            
            self.commit()
            aoi_gate_match_general_obj.commit()

        # print("内外大门批处理策略完成：" + 
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
        
            
if __name__ == '__main__':
    obj = AoiNearApartGateMatch()
    obj.run()
