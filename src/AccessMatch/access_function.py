# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
todo：
    1.新增的关联关系如果加入进来（或以bid为基准统计改为以faceid为基准统计？没有关联主点的AOI对导航来讲如何应用？）
    2.当前只记录了匹配关系，未考虑是否已经转为出入口
    3.人工作业的出入口没有记录和维护，可能会影响对L1 L2级别AOI的判断，推动出入口的履历库建设

AOI的修改场景:
1. 新增AOI并关联了主点bid  是否不存在没有关联主点的场景？
2. 删除并重新画了AOI，faceid变更但是主点不变
3. 删除AOI
4. 新增AOI的关联关系
"""

import collections
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely.wkt
import shapely.geometry
from shapely.ops import linemerge, unary_union, polygonize
from pg_tool import PgTool


class AoiGateMatchGeneralTool:
    """
    大门匹配策略通用工具
    """

    def __init__(self, pg_tool: PgTool):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        self.pg_tool = pg_tool

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def find_link_by_node(self, node_id):
        """
        根据nodeid查找候选link组
        Args:
            node_id: 大门的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
        """
        link_dict = collections.OrderedDict()
        inner_link = ""
        outter_link = ""
        sql = '''
            select 
                distinct link_id, s_nid, e_nid, b.node_id, form, st_astext(geom)
            from nav_link a inner join nav_gate b 
            on 
                a.link_id = b.in_linkid 
                or a.link_id = b.out_linkid 
            where 
                b.node_id = %s '''
        self.pg_tool.cursor_road.execute(sql, [node_id])
        node_id_str = ""
        res_link = self.pg_tool.cursor_road.fetchall()
        for v_node in res_link:
            link_dict[v_node[0]] = v_node[5]
            if '52' in v_node[4].split(","):
                inner_link = v_node[0]
                node_id_str = node_id_str + "','" + v_node[1] + "','" + v_node[2]
            else:
                outter_link = v_node[0]
        node_id_str = node_id_str.strip("','")
        sql = """
            select 
                string_agg(adjoin_nid, ''',''') 
            from nav_node 
            where 
                node_id in ('%s') 
                and adjoin_nid != '' """ % (node_id_str)
        self.pg_tool.cursor_road.execute(sql)
        adjoin_nid = self.pg_tool.cursor_road.fetchone()[0]
        if adjoin_nid is not None:
            node_id_str = node_id_str + "','" + adjoin_nid
        sql = '''
            select 
                link_id, st_astext(geom) , form
            from nav_link 
            where 
                (s_nid in ('%s') or 
                    e_nid in ('%s')
                ) 
                ''' % (node_id_str, node_id_str)
        self.pg_tool.cursor_road.execute(sql)
        for link_tmp in self.pg_tool.cursor_road.fetchall():
            if '52' in link_tmp[2].split(','):
                link_dict[link_tmp[0]] = link_tmp[1]
        return link_dict, [inner_link, outter_link]

    def search_inner_link_group(self, link_id, aoi_geom):
        """
        根据linkid和aoi的范围框信息寻找与link相连的框内的内部路组
        """
        aoi_region = shapely.wkt.loads(aoi_geom)
        link_geom_dict = dict()
        link_complete_geom_dict = dict()
        link_set = set()
        link_set_tmp = set()
        link_set.add(link_id)
        link_set_tmp.add(link_id)
        snid_dict = {}
        enid_dict = {}

        sql = '''
            select 
                s_nid, e_nid, st_astext(geom) 
            from nav_link 
            where 
                link_id = %s '''
        self.pg_tool.cursor_road.execute(sql, [link_id])
        res_link = self.pg_tool.cursor_road.fetchone()
        snid_dict[link_id] = res_link[0]
        enid_dict[link_id] = res_link[1]
        link_geom_dict[link_id] = aoi_region.intersection(shapely.wkt.loads(res_link[2])).wkt
        link_complete_geom_dict[link_id] = shapely.wkt.loads(res_link[2]).wkt

        while len(link_set) > 0:
            link_tmp = link_set.pop()
            s_nid_tmp = snid_dict[link_tmp]
            e_nid_tmp = enid_dict[link_tmp]
            sql = '''
                select 
                    adjoin_nid 
                from nav_node 
                where 
                    node_id in (%s, %s) 
                    and adjoin_nid != '' '''
            self.pg_tool.cursor_road.execute(sql, [s_nid_tmp, e_nid_tmp])
            adjoin_nid_res = self.pg_tool.cursor_road.fetchall()
            adjoin_nid_tmp = ""
            if adjoin_nid_res is not None and len(adjoin_nid_res) > 0:
                for adjoin_nid_res_tmp in adjoin_nid_res:
                    adjoin_nid_tmp = adjoin_nid_tmp + adjoin_nid_res_tmp[0] + "','"
                adjoin_nid_tmp = adjoin_nid_tmp[:-3]
            sql = '''
                select 
                    link_id,s_nid,e_nid, st_astext(geom) , form
                from nav_link 
                where 
                    (s_nid in ('%s', '%s', '%s') 
                        or e_nid in ('%s', '%s','%s')) 
                     ''' % (s_nid_tmp, e_nid_tmp, adjoin_nid_tmp,
                                           s_nid_tmp, e_nid_tmp, adjoin_nid_tmp)
            self.pg_tool.cursor_road.execute(sql)
            res = self.pg_tool.cursor_road.fetchall()
            if res is None or len(res) == 0:
                continue
            for tmp_res in res:
                tmp_link = tmp_res[0]
                tmp_snid = tmp_res[1]
                tmp_enid = tmp_res[2]
                tmp_geom = tmp_res[3]
                form = tmp_res[4]
                if '52' not in form.split(","):
                    continue
                if tmp_link not in link_set_tmp and aoi_region.intersects(shapely.wkt.loads(tmp_geom)):
                    link_set.add(tmp_link)
                    link_set_tmp.add(tmp_link)
                    snid_dict[tmp_link] = tmp_snid
                    enid_dict[tmp_link] = tmp_enid
                    link_geom_dict[tmp_link] = aoi_region.intersection(shapely.wkt.loads(tmp_geom)).wkt
                    link_complete_geom_dict[tmp_link] = shapely.wkt.loads(tmp_geom).wkt

        return link_geom_dict, link_complete_geom_dict

    def search_link_group_with_aoi(self, link_id, aoi_geom):
        """
        根据linkid和aoi的范围框信息寻找与link相连的框内的道路组
        """
        aoi_region = shapely.wkt.loads(aoi_geom)
        link_geom_dict = dict()
        link_complete_geom_dict = dict()
        link_set = set()
        link_set_tmp = set()
        link_set.add(link_id)
        link_set_tmp.add(link_id)
        snid_dict = {}
        enid_dict = {}

        sql = '''
            select 
                s_nid, 
                e_nid, 
                st_astext(geom) 
            from nav_link 
            where 
                link_id = %s '''
        self.pg_tool.cursor_road.execute(sql, [link_id])
        res_link = self.pg_tool.cursor_road.fetchone()
        snid_dict[link_id] = res_link[0]
        enid_dict[link_id] = res_link[1]
        link_geom_dict[link_id] = aoi_region.intersection(shapely.wkt.loads(res_link[2])).wkt
        link_complete_geom_dict[link_id] = shapely.wkt.loads(res_link[2]).wkt

        while len(link_set) > 0:
            link_tmp = link_set.pop()
            s_nid_tmp = snid_dict[link_tmp]
            e_nid_tmp = enid_dict[link_tmp]
            sql = '''
                select 
                    adjoin_nid 
                from nav_node 
                where 
                    node_id in (%s, %s) 
                    and adjoin_nid != '' '''
            self.pg_tool.cursor_road.execute(sql, [s_nid_tmp, e_nid_tmp])
            adjoin_nid_res = self.pg_tool.cursor_road.fetchall()
            adjoin_nid_tmp = ""
            if adjoin_nid_res is not None and len(adjoin_nid_res) > 0:
                for adjoin_nid_res_tmp in adjoin_nid_res:
                    adjoin_nid_tmp = adjoin_nid_tmp + adjoin_nid_res_tmp[0] + "','"
                adjoin_nid_tmp = adjoin_nid_tmp[:-3]
            sql = '''
                select 
                    link_id,
                    s_nid,
                    e_nid, 
                    st_astext(geom) 
                from nav_link 
                where 
                    s_nid in ('%s', '%s', '%s') 
                    or e_nid in ('%s', '%s','%s')
            ''' % (s_nid_tmp, e_nid_tmp, adjoin_nid_tmp,
                   s_nid_tmp, e_nid_tmp, adjoin_nid_tmp)
            self.pg_tool.cursor_road.execute(sql)
            res = self.pg_tool.cursor_road.fetchall()
            if res is None or len(res) == 0:
                continue
            for tmp_res in res:
                tmp_link = tmp_res[0]
                tmp_snid = tmp_res[1]
                tmp_enid = tmp_res[2]
                tmp_geom = tmp_res[3]
                if tmp_link not in link_set_tmp and aoi_region.intersects(shapely.wkt.loads(tmp_geom)):
                    link_set.add(tmp_link)
                    link_set_tmp.add(tmp_link)
                    snid_dict[tmp_link] = tmp_snid
                    enid_dict[tmp_link] = tmp_enid
                    link_geom_dict[tmp_link] = aoi_region.intersection(shapely.wkt.loads(tmp_geom)).wkt
                    link_complete_geom_dict[tmp_link] = shapely.wkt.loads(tmp_geom).wkt

        return link_geom_dict, link_complete_geom_dict

    def union_link(self, link_geom_dict):
        """
        把字典中线的几何信息合并起来
        """
        if len(link_geom_dict) < 1:
            return ""
        link_geom_union = ""

        for link_geom in link_geom_dict.values():
            if link_geom_union == "":
                link_geom_union = link_geom
                continue
            sql = '''
                select 
                    st_astext(ST_LineMerge(st_union(st_geomfromtext(%s, 4326), st_geomfromtext(%s, 4326))))'''
            self.pg_tool.cursor_road.execute(sql, [link_geom_union, link_geom])
            link_geom_union = self.pg_tool.cursor_road.fetchone()[0]
        return link_geom_union

    def cut_polygon_by_line(self, polygon, line):
        """
        用线切分面
        """
        line.append(polygon.boundary)
        merged = linemerge(line)
        borders = unary_union(merged)
        polygons = polygonize(borders)
        return list(polygons)

    def aoi_contains_geom(self, face_id, geom):
        """
        判断几何点或线是否在AOI范围框内
        """
        sql = '''
            select 
                st_astext(geom) 
            from blu_face 
            where 
                face_id = %s'''
        self.pg_tool.cursor_aoi.execute(sql, [face_id])
        aoi_res = self.pg_tool.cursor_aoi.fetchone()
        if aoi_res is None or len(aoi_res) == 0:
            return False
        return shapely.wkt.loads(aoi_res[0]).contains(shapely.wkt.loads(geom))

    def get_node_geom(self, node_id):
        """
        根据nodeid查询范围信息
        """
        sql = '''
            select 
                st_astext(geom) 
            from nav_node 
            where 
                node_id = %s'''
        self.pg_tool.cursor_road.execute(sql, [node_id])
        res = self.pg_tool.cursor_road.fetchone()
        if res is None:
            return res
        return res[0]

    def find_bid_to_match(self, num):
        """
        从数据库里寻找未处理的情报源
        """
        face_bid_dict = dict()
        # 这里过滤了bid = ""的条件，候选可以不过滤
        sql = f'''
            select 
                face_id, 
                bid,
                src
            from gate_match_intel_src 
            where 
                bid != '' 
                and status = 1
                and id % 10 = {num}'''
        self.pg_tool.cursor_poi_write.execute(sql)
        res = self.pg_tool.cursor_poi_write.fetchall()
        if res is not None:
            for v in res:

                if v[2] == "aoi-bid-del":
                    if v[1] not in face_bid_dict:
                        # 此时的face_id为数据删除前的face，已不存在，因此改为空
                        face_bid_dict[v[1]] = ""
                    else:
                        continue
                face_bid_dict[v[1]] = v[0]

        return face_bid_dict

    def get_aoi_info(self, face_id, fields):
        """
        查询aoi的指定字段信息
        """
        sql = '''
            select 
                %s 
            from blu_face 
            where 
                face_id = '%s' ''' % (fields, face_id)
        self.pg_tool.cursor_aoi.execute(sql)
        res = self.pg_tool.cursor_aoi.fetchone()
        if res is None:
            return res
        return res

    def set_bid_intel_fail(self, bid, reason):
        """
        更新情报表里更新face_id对应情报为已处理
        """
        sql = '''
            update 
                gate_match_intel_src 
            set 
                fail_reason = '%s',
                status = 3
            where 
                bid = '%s' 
                and status = 1''' % (reason, bid)
        self.pg_tool.cursor_poi_write.execute(sql)
        self.pg_tool.conn_poi_write.commit()

    def find_opt_aoi(self, face_id_dict):
        """
        根据候选的faceid中寻找最优的AOI 
        """
        max_area = 0.0
        max_length = 0.0
        face_id_opt = ""
        for face_id, aoi_info in face_id_dict.items():
            # 匹配到多个AOI，需要筛选置信度最高的
            # 搜索内部路组
            link_dict, link_complete_dict = self.search_inner_link_group(aoi_info[0], aoi_info[1])
            aoi_region = shapely.wkt.loads(aoi_info[1])

            link_region_list = []
            for link_info in link_complete_dict.values():
                link_region_list.append(shapely.wkt.loads(link_info))
            link_all = linemerge(link_region_list)
            # 过滤掉因aoi和道路异常相交引起的badcase
            if len(link_dict) <= 3 and link_all.intersection(aoi_region.boundary).geom_type == "MultiPoint":
                # 判断线拆分的aoi的区域面积所占的比例
                polys = self.cut_polygon_by_line(aoi_region, link_region_list)
                if len(polys) == 2 and (polys[0].area / aoi_region.area <
                                        0.01 or polys[0].area / aoi_region.area > 0.99):
                    continue

            # 计算凸包的面积和周长
            link_length_tmp = link_all.length
            link_area_tmp = link_all.convex_hull.area
            if max_area < link_area_tmp:
                face_id_opt = face_id
                max_area = link_area_tmp
            if max_area == 0.0 and max_length < link_length_tmp:
                max_length = link_length_tmp
                face_id_opt = face_id

        return face_id_opt

    def find_opt_aoi_without_inner_link(self, face_id_dict):
        """
        根据候选的faceid中寻找最优的AOI 
        """
        max_area = 0.0
        max_length = 0.0
        face_id_opt = ""
        for face_id, aoi_info in face_id_dict.items():
            # 匹配到多个AOI，需要筛选置信度最高的
            # 搜索内部路组
            link_dict, link_complete_dict = self.search_link_group_with_aoi(aoi_info[0], aoi_info[1])
            aoi_region = shapely.wkt.loads(aoi_info[1])

            link_region_list = []
            for link_info in link_complete_dict.values():
                link_region_list.append(shapely.wkt.loads(link_info))
            link_all = linemerge(link_region_list)
            # 过滤掉因aoi和道路异常相交引起的badcase
            if len(link_dict) <= 3 and link_all.intersection(aoi_region.boundary).geom_type == "MultiPoint":
                # 判断线拆分的aoi的区域面积所占的比例
                polys = self.cut_polygon_by_line(aoi_region, link_region_list)
                if len(polys) == 2 and (polys[0].area / aoi_region.area <
                                        0.01 or polys[0].area / aoi_region.area > 0.99):
                    continue

            # 计算凸包的面积和周长
            link_length_tmp = link_all.length
            link_area_tmp = link_all.convex_hull.area
            if max_area < link_area_tmp:
                face_id_opt = face_id
                max_area = link_area_tmp
            if max_area == 0.0 and max_length < link_length_tmp:
                max_length = link_length_tmp
                face_id_opt = face_id

        return face_id_opt

    def add_match_achievement(self, node_id, bid, face_id, action, src, process_type):
        """
        策略产出的结果保存
        """
        sql = '''
            insert into 
                gate_bid_match_tmp_res(
                    node_id, 
                    bid, 
                    face_id, 
                    action, 
                    src, 
                    process_type
                ) 
            values(
                %s, 
                %s, 
                %s, 
                %s, 
                %s, 
                %s
            )
        '''

        self.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, face_id, action, src, process_type])
        self.pg_tool.conn_poi_write.commit()

    def find_node_adjacent_high_link(self, face_id, link_geom_dict, node_geom):
        """
        判断AOI是否在内部连接高等级道路
        """
        sql = f"""
            select 
                st_astext(geom)
            from blu_face
            where 
                face_id = '{face_id}'
        """
        self.pg_tool.cursor_aoi.execute(sql)
        geom = self.pg_tool.cursor_aoi.fetchone()
        if geom is None:
            return False
        geom = geom[0]
        if not shapely.wkt.loads(geom).contains(shapely.wkt.loads(node_geom)):
            return False
        sql = f"""
            select 
                st_astext(geom)
            from nav_link
            where 
                st_intersects(st_geomfromtext('{geom}', 4326), geom)
                and kind <= 7
        """
        self.pg_tool.cursor_road.execute(sql)
        for geom_link, in self.pg_tool.cursor_road.fetchall():
            for _, geom_tmp in link_geom_dict.items():
                if shapely.wkt.loads(geom_link).intersects(shapely.wkt.loads(geom_tmp)):
                    return True
        return False

    def commit(self):
        """
        数据库提召
        """
        self.pg_tool.commit()
