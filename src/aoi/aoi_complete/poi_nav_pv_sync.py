"""
同步导航终点500米pv
需要下载afs文件，合并成一个文件。然后处理
"""
import os
import sys
from pathlib import Path
from src.door_naming.DaoAoiPoi import DaoAoiPoi
import datetime
from src.door_naming.util import exec_shell_cmd

root_path = Path(os.path.abspath(__file__)).parents[4]
print(root_path)
current_path = Path(os.path.abspath(__file__)).parent

work_dir = current_path / 'poi_nav_pv'
work_dir.mkdir(parents=True, exist_ok=True)
today_ymd = datetime.date.today().strftime("%Y%m%d")
# 需要挂载，挂载的afs目录为  afs://kunpeng.afs.baidu.com:9902/user/lbs-traj/navi-o2d/PRODUCTIONS/DATAS/poi_500m_pv_uv
pv_target_base_path = Path('/home/<USER>/mnt')

pv_file = work_dir / 'poi_nav_pv_merge.txt'
pv_check_file = ''
import datetime
from src.door_naming.DaoTrack import DaoTrack
from src.common import common_tool


def sync_nav_pv():
    """
    保存本地数据
    :return:
    """
    # 删除旧数据
    if pv_file.exists():
        pv_file.unlink()
    if not pv_target_base_path.exists():
        raise Exception("导航pv文件夹不存在，请先挂载afs")
    cur_pv_target_path = None
    # 尝试找历史记录,保证能更新到最近的数据
    for i in range(4):
        cur_date = datetime.date.today() - datetime.timedelta(days=i)
        pv_target_path = Path(f'/home/<USER>/mnt/{cur_date.strftime("%Y%m%d")}')
        done_file = pv_target_path / '_done'
        if not done_file.exists():
            continue
        cur_pv_target_path = pv_target_path
        break
    if cur_pv_target_path is None:
        raise Exception("没有找到有效的pv文件，请检查")
    print(f"开始合并文件: {cur_pv_target_path}")
    cmd = f'cat {cur_pv_target_path.as_posix()}/part* > {pv_file.as_posix()}'
    exec_shell_cmd(cmd)
    if not pv_file.exists():
        raise Exception('复制文件失败，结束')
    file_len = 0
    with open(pv_file) as f:
        for i in f:
            file_len += 1
    if file_len < 15000000:
        raise Exception('文件行数小于1000w，请检查')
    print("合并后的文件行数：", file_len)


def sync_to_db():
    """
    同步到数据库
    :return:
    """

    today = datetime.date.today().strftime('%Y%m%d')
    table_name = f'poi_nav_500m_pv_{today}'
    print(f"开始创建数据表:{table_name}")
    with DaoAoiPoi(with_road=False) as dao_aoi_poi:
        sql = f'drop table if exists {table_name}'
        dao_aoi_poi.cursor_poi.execute(sql)
        dao_aoi_poi.conn_poi.commit()
        # 创建数据表
        sql = f'create table {table_name}' \
              f' (bid varchar(50) primary key , pv int default 0, ' \
              f'uv int default 0)'
        dao_aoi_poi.cursor_poi.execute(sql)
        dao_aoi_poi.conn_poi.commit()

        with open(pv_file) as f:
            dao_aoi_poi.cursor_poi.copy_from(f, table_name, sep='\t', columns=['bid', 'pv', 'uv'])
        dao_aoi_poi.conn_poi.commit()

        # 切换view
        view_name = 'poi_nav_500m_pv'
        sql = f'create or replace view {view_name} as select * from {table_name} '
        dao_aoi_poi.cursor_poi.execute(sql)
        dao_aoi_poi.conn_poi.commit()


def clean_old():
    """
    删除旧数据
    :return:
    """
    with DaoAoiPoi(with_road=False) as dao_dest_traj:
        for i in range(2, 10):
            old_data = datetime.date.today() - datetime.timedelta(days=i)
            table_name = f'poi_nav_500m_pv_{old_data.strftime("%Y%m%d")}'
            print("开始清理旧的数据", table_name)
            sql = f'drop table if exists {table_name}'
            dao_dest_traj.cursor_poi.execute(sql)
            dao_dest_traj.conn_poi.commit()


def run():
    """
    主任务
    :return:
    """
    sync_nav_pv()
    sync_to_db()
    clean_old()


if __name__ == '__main__':
    try:
        run()
    except Exception as e:
        common_tool.send_hi(f"同步导航最后500米的pv失败:{e.args}, 请及时恢复,代码位置在de16:{current_path}",
                            atuserids=['chenbaojun_cd'])
