# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDD商单数据输出
"""
import os
import psycopg2

pdd_sh_conf = {"db": "pdd_sd", "user": "pdd_sd_se_rw", "pwd": "i<PERSON><PERSON><PERSON><PERSON>",
               "host": "**************",
               "port": 6432}

pdd_sd_conn = psycopg2.connect(database=pdd_sh_conf["db"],
                               user=pdd_sh_conf["user"],
                               password=pdd_sh_conf["pwd"],
                               host=pdd_sh_conf["host"],
                               port=pdd_sh_conf["port"])
pdd_sd_dao = pdd_sd_conn.cursor()

root_dir = "/home/<USER>/gengyong/pdd_sd/data"
bud_path = root_dir + "/background/00-building"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-divison"
bad_county_path = root_dir + "/background/04-divison/county"
bad_town_path = root_dir + "/background/04-divison/town"
province_list = ['anhui', 'aomen', 'beijing', 'fujian', 'gansu', 'guangdong', 'guangxi', 'guizhou',
                 'hainan', 'hebei', 'henan', 'heilongjiang', 'hubei', 'hunan', 'jilin', 'jiangsu',
                 'jiangxi', 'liaoning', 'neimenggu', 'ningxia', 'qinghai', 'shandong', 'shanxi',
                 'shaanxi', 'shanghai', 'sichuan', 'tianjin', 'xizang', 'xianggang', 'xinjiang',
                 'yunnan', 'zhejiang', 'chongqing']
province_list = ['shandong']


def run():
    """
    成果输出
    Returns:

    """
    print(f"开始bud导出")
    bud_export()
    print(f"开始blu导出")
    blu_export()
    print(f"开始blc导出")
    blc_export()
    print(f"开始brw导出")
    brw_export()
    print(f"开始bad导出")
    bad_export()
    print(f"导出完成")


def bud_export():
    """
    建筑物数据输出
    Returns:

    """
    for province in province_list:
        export_dir = bud_path + "/" + province
        sql = f"""
            select 
                face_id,struct_id,poi_id,poi_bid,aoi_id,roof_style,wall_material,area,perimeter,bf.geom 
            from bud_face bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where province_en = '{province}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/bud_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)


def blu_export():
    """
    AOI数据输出
    Returns:

    """
    for province in province_list:
        export_dir = blu_path + "/" + province
        sql = f"""
            select 
                face_id,kind,area,perimeter,name_ch,name_ph,name_en,admin_id,aoi_level,city_name,poi_id,poi_bid,bf.geom 
            from blu_face bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where province_en = '{province}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)


def blc_export():
    """
    BLC数据输出
    Returns:

    """
    for province in province_list:
        export_dir = blc_path + "/" + province
        sql = f"""
            select 
                face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,bf.geom 
            from blc_green bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where province_en = '{province}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_lvdi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        sql = f"""
                    select 
                        face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,bf.geom 
                    from blc_water bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
                    where province_en = '{province}'
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_shuixi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def brw_export():
    """
    BRW数据输出
    Returns:

    """
    for province in province_list:
        export_dir = brw_path + "/" + province
        sql = f"""
            select 
                link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,bf.geom 
            from brw_link bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where province_en = '{province}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/brw_link.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        sql = f"""
                    select 
                        node_id,kind,form
                    from brw_node bf inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
                    where province_en = '{province}'
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/brw_node.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def bad_export():
    """
    行政区划数据输出
    Returns:

    """
    for province in province_list:
        export_dir = bad_county_path + "/" + province
        sql = f"""
            select 
                countycode,countyname,countyenname,citycode,cityname,cityenname,procode,proname,proenname,(case when st_isempty(geom)='t' then Null else geom end) as geom  
            from bad_county where province_py_name = '{province}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/division_county.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        if province in ['xianggang', 'aomen']:
            continue
        export_dir = bad_town_path + "/" + province
        sql = f"""
                    select 
                        towncode,townname,townenname,countycode,countyname,countyenname,citycode,cityname,cityenname,procode,proname,proenname,(case when st_isempty(geom)='t' then Null else geom end) as geom 
                    from bad_town where province_py_name = '{province}'
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/division_town.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def update_bad_name_sql():
    """
    更新bad_town表中的province_py_name字段
    Returns:
    """
    test_sql = f"""
        update bad_town set province_py_name='hebei' where proname='河北省';
        update bad_town set province_py_name='aomen' where proname='澳门';
        update bad_town set province_py_name='jilin' where proname='吉林省';
        update bad_town set province_py_name='yunnan' where proname='云南省';
        update bad_town set province_py_name='sichuan' where proname='四川省';
        update bad_town set province_py_name='guangdong' where proname='广东省';
        update bad_town set province_py_name='hainan' where proname='海南省';
        update bad_town set province_py_name='gansu' where proname='甘肃省';
        update bad_town set province_py_name='xizang' where proname='西藏自治区';
        update bad_town set province_py_name='henan' where proname='河南省';
        update bad_town set province_py_name='fujian' where proname='福建省';
        update bad_town set province_py_name='shanxi' where proname='山西省';
        update bad_town set province_py_name='xianggang' where proname='香港';
        update bad_town set province_py_name='xinjiang' where proname='新疆维吾尔自治区';
        update bad_town set province_py_name='jiangxi' where proname='江西省';
        update bad_town set province_py_name='guizhou' where proname='贵州省';
        update bad_town set province_py_name='ningxia' where proname='宁夏回族自治区';
        update bad_town set province_py_name='shandong' where proname='山东省';
        update bad_town set province_py_name='tianjin' where proname='天津市';
        update bad_town set province_py_name='jiangsu' where proname='江苏省';
        update bad_town set province_py_name='zhejiang' where proname='浙江省';
        update bad_town set province_py_name='beijing' where proname='北京市';
        update bad_town set province_py_name='shaanxi' where proname='陕西省';
        update bad_town set province_py_name='hunan' where proname='湖南省';
        update bad_town set province_py_name='anhui' where proname='安徽省';
        update bad_town set province_py_name='chongqing' where proname='重庆市';
        update bad_town set province_py_name='neimenggu' where proname='内蒙古自治区';
        update bad_town set province_py_name='shanghai' where proname='上海市';
        update bad_town set province_py_name='heilongjiang' where proname='黑龙江省';
        update bad_town set province_py_name='hubei' where proname='湖北省';
        update bad_town set province_py_name='liaoning' where proname='辽宁省';
        update bad_town set province_py_name='guangxi' where proname='广西壮族自治区';
        update bad_town set province_py_name='qinghai' where proname='青海省';
    """


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/background")
    make_dir(bud_path)
    make_dir(blu_path)
    make_dir(blc_path)
    make_dir(brw_path)
    make_dir(bad_path)
    make_dir(bad_county_path)
    make_dir(bad_town_path)
    for province in province_list:
        make_dir(bud_path + "/" + province)
        make_dir(blu_path + "/" + province)
        make_dir(blc_path + "/" + province)
        make_dir(brw_path + "/" + province)
        make_dir(bad_county_path + "/" + province)
        make_dir(bad_town_path + "/" + province)


def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


if __name__ == '__main__':
    init_dir()
    print("目录初始化完成,开始数据导出")
    run()
