# -*- coding: utf-8 -*-
"""
地下停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
import math
import re
import tqdm
import requests
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.affinity import translate
from shapely import wkt
from loguru import logger
from rapidfuzz import fuzz

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from common import pipeline
from aoi.park_access.mistake import context
from aoi import utils
from aoi.park_access import park_utils
from aoi.park_access.mistake import park_access_qc as qc
from collections import defaultdict, deque


COORDINATE_FACTOR = 110000
DEAD_LINK_DISTANCE_LIMIT = 100


# logger.remove()
log_path = "./log/access_out_add_intelligence.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
NODE_DISTANCE_IN_PARK_LIMIT = 20


def create_intelligence(ctx: context.Context):
    """
    门前出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        get_park_access_list,

        check_by_complete,

        is_strategy_already_recall,
        park_access_filter,
        save_park_to_db,
    )
    pipe_list(ctx)



def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_out_qc")
    qc.park_access_out_qc(ctx)
    return proceed()


def is_strategy_already_recall(ctx: context.Context, proceed):
    """
    当前策略召回过不投放
    """
    for park_access in ctx.parking_access_list:
        park_intelligence = ctx.dao.poi_online_query.get_park_intelligence_by_access_bid(
            park_access.bid, park_access.strategy)
        if not park_intelligence:
            continue
        park_access.is_valid = False
        park_access.filter_reason = f"情报id:{park_intelligence[0]},{park_access.strategy}"
        logger.info(f"end is_strategy_already_recall,{park_access.filter_reason}")
    return proceed()


def get_park_std_tag(ctx: context.Context, proceed):
    """
    获取标准标签
    """
    park_category_data = ctx.dao.poi_online_query.get_park_category(ctx.parking_info.bid)
    if park_category_data:
        ctx.parking_info.park_category = park_category_data[0]
    logger.info(f"park_category:{ctx.parking_info.park_category}")
    return proceed()


def get_park_access_list(ctx: context.Context, proceed):
    """
    获取停车场出入口
    Returns:
    """
    logger.info(f"star-park_id：{ctx.parking_info.bid}")
    sub_pois = ctx.dao.poi_online_query.get_sub_pois(ctx.parking_info.bid, "出口")
    if not sub_pois:
        logger.error(f"{ctx.parking_info.bid}没有子poi")
        return
    for sub_poi in sub_pois:
        if not sub_poi[3] or sub_poi[4] != 1:
            continue
        park_sub_poi = context.ParkingSubPOI()
        park_sub_poi.bid = sub_poi[0]
        park_sub_poi.point_gcj = sub_poi[2]
        road_relation = sub_poi[3]
        park_sub_poi.road_relation = road_relation
        park_sub_poi.name = sub_poi[5]
        park_sub_poi.open_limit_new = int(sub_poi[6]) if sub_poi[6] else 0
        if road_relation is not None and "link_info" in road_relation:
            # 关联node
            link_info_list = road_relation["link_info"]
            if len(link_info_list) == 0:
                continue
            link_info = link_info_list[0]
            if 'type' in link_info and link_info["type"] == 1:
                short_node_ids = link_info['node_id']
                long_node_list = ctx.dao.trans_dao.get_long_node_by_short_node_ids([short_node_ids])
                long_node_ids = [item[0] for item in long_node_list if long_node_list]
                if len(long_node_ids) == 0:
                    continue
                park_sub_poi.relation_node_id = long_node_ids[0]
                park_sub_poi.relation_short_node_id = short_node_ids
                park_sub_poi.type = 'NODE'
            # 关联link
            if 'type' in link_info and link_info["type"] == 2:
                short_link_ids = link_info['link_id']
                long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids([short_link_ids])
                long_link_ids = [item[0] for item in long_link_list if long_link_list]
                if len(long_link_ids) == 0:
                    continue
                park_sub_poi.relation_link_id = long_link_ids[0]
                park_sub_poi.type = 'LINK'
            ctx.sub_pois.append(park_sub_poi)
    if len(ctx.sub_pois) == 0:
        logger.error(f"{ctx.parking_info.bid}没有有效子poi")
        return
    return proceed()


def get_build_compete_lists(ctx):
    """
    竞品出入口匹配
    """
    park_access_navi_competitor_list = ctx.dao.poi_online_query.get_competitor_access_nav_by_park_bids(\
        [ctx.parking_info.bid], "出口")
    if not park_access_navi_competitor_list:
        logger.info(f"小区竞品未匹配到出入口:{ctx.parking_info.bid}")
        return False
    return park_access_navi_competitor_list


def competitor_diff_by_park_bid(ctx, park_bid, access_geom_wkt):
    """
    0, "小区竞品数据不存在"
    1, "竞品与出入口距离小于20米"
    2, "小区竞品未匹配到停车场"
    3, "小区竞品未匹配到出入口"
    4, "停车场存在且距离大于20"
    """
    park_access_navi_competitor_list = get_build_compete_lists(ctx)
    return competitor_diff_by_park_bid_with_access(park_access_navi_competitor_list, access_geom_wkt)


def get_competitor_diff_by_park_bid_with_access_min_dis(park_access_navi_competitor_list, access_geom_sp):
    """
    竞品出入口距离
    """
    min_dis = 100000
    if not park_access_navi_competitor_list:
        return min_dis
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        now_dis = poi_navi_sp.distance(access_geom_sp) * COORDINATE_FACTOR
        min_dis = min(min_dis, now_dis)
    return min_dis


def competitor_diff_by_park_bid_with_access(park_access_navi_competitor_list, access_geom_wkt, distance=20):
    """
    出入口diff
    """
    if not park_access_navi_competitor_list:
        return 0, "小区竞品数据不存在"
    # print(f"competitor_diff_by_park_bid_with_access_node_geom\t{access_geom_wkt}")
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        # print(poi_navi_sp.wkt)
        if poi_navi_sp.distance(wkt.loads(access_geom_wkt)) < distance / COORDINATE_FACTOR:
            return 1, f"竞品与出入口距离小于{distance}m"
    return 4, f"停车场存在且距离大于{distance}m"

    
def check_by_complete(ctx: context.Context, proceed):
    """
    检查是否完成
        Args:
            ctx:
            proceed:

    Returns:
    """
    logger.info(f"check_by_complete-start:park_bid\t{ctx.parking_info.bid}")
    park_bid = ctx.parking_info.bid
    parent_id = ctx.parking_info.parent_id
    show_tag = ctx.parking_info.show_tag
    logger.info(f"parent_id: {parent_id},\tshow_tag{ctx.parking_info.show_tag}\t{ctx.parking_info.name}")
    park_area_gcj_wkt = ctx.parking_info.park_area_gcj_wkt \
        if ctx.parking_info.park_area_gcj_wkt else ctx.parking_info.park_area_aoi_30m_wkt
    
    # 出口竞品
    park_access_navi_competitor_list = get_build_compete_lists(ctx)
    # if park_access_navi_competitor_list:
    #     for a in park_access_navi_competitor_list:
    #         print(a[0])
    if not park_access_navi_competitor_list:
        logger.info(f"竞品数据不存在\t{ctx.parking_info.bid}")
        return proceed()
    access_jp_num = len(park_access_navi_competitor_list) if park_access_navi_competitor_list else 0
    parent_id = ctx.parking_info.parent_id
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        min_dis = 100000
        for sub_park_access in ctx.sub_pois:
            access_geom_sp = wkt.loads(sub_park_access.point_gcj)
            now_dis = poi_navi_sp.distance(access_geom_sp) * COORDINATE_FACTOR
            min_dis = min(min_dis, now_dis)
            logger.info(f"access_geom_sp:{access_geom_sp}min_dis{min_dis},access_bid:{sub_park_access.bid}")
        if min_dis < 50:
            logger.info(f"access_min_dis\t{now_dis}\t jp_competitor_diff过滤")
            continue
        logger.info(f"poi_navi_sp:{poi_navi_sp}\taccess_min_dis:{min_dis}\t jp_diff_add")
        park_access = context.ParkingAccess()
        park_access.bid = park_bid
        park_access.parent_id = parent_id
        park_access.node_geom = poi_navi_sp.wkt
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_ADD
        park_access.strategy_value = min_dis
        ctx.parking_access_list.append(park_access)
        logger.info(f"PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_ADD\t{park_bid}\t{poi_navi_sp}\t{min_dis}")
    return proceed()


def main(bid_list: list):
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场', '地下停车场', '立体停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    logger.info(f"开始挖掘关联错误出口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        ctx = context.Context(parking)
        logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
        create_intelligence(ctx)
        # try:
        #     ctx = context.Context(parking)
        #     logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
        #     create_intelligence(ctx)
        # except Exception as e:
        #     print(f"情报挖掘失败:{e}{parking}\n{traceback.format_exc()}")


def dead_traj_and_dead_link(ctx, geom_wkt, aoi_geom_wkt, dbscan_eps_num, dbscan_min_sample_num, distance_min=10):
    """
    断头轨迹聚类 + 断头路
    Args:
        ctx:
        proceed:
    Returns:

    """
    logger.info(f"begin dead_traj_and_dead_link")
    if aoi_geom_wkt == "":
        logger.info(f"dead_link_traj_list aoi_geom_wkt is none")
        return False
    # 查找断头路轨迹点
    dead_link_traj_list = ctx.dao.traj_feature_dao.get_broken_points_by_geom(aoi_geom_wkt)
    if not dead_link_traj_list:
        logger.info(f"dead_link_traj_list is none")
        return False
    broken_points = [item['geom'] for item in dead_link_traj_list]
    # logger.info(f"聚类轨迹终点: {broken_points}")
    clusters = park_utils.clusters_by_point_list(broken_points, dbscan_eps=dbscan_eps_num, \
        dbscan_min_sample=dbscan_min_sample_num)
    if not clusters:
        logger.info(f"dead_link_traj_list 无 clusters 点")
        return False
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        multipoint = MultiPoint(points)
        # logger.error(multipoint.wkt)
        # logger.info(f"断头轨迹终点聚类: {multipoint.wkt}")
        convex_hull_sp = multipoint.convex_hull.buffer(3 / COORDINATE_FACTOR)
        # logger.error(convex_hull_sp)
        if convex_hull_sp.distance(wkt.loads(geom_wkt)) < distance_min / COORDINATE_FACTOR:
            return True
    logger.info(f"dead_link_traj_list clusters 点 未命中")
    return False


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type},bid:{item.bid}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 是否有效:{item.is_valid},过滤原因:{item.filter_reason}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type},bid:{park_access.bid}, node_id位置\t{park_access.node_id}, \
                    策略:{park_access.strategy}, "
                    f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        jdq_batch = 'JQDCWP2024101810'
        resp = ''
        guid = str(uuid.uuid4()).replace("-", "")
        park_access.type = "park_access_overground_out_gate_add"
        if ctx.parking_info.show_tag in ["地下停车场", "立体停车场"]:
            park_access.type = "park_access_underground_out_gate_add"
        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': park_access.type,
            'batch_number': f"{park_access.type}_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': f"{park_access.type}_{guid}",
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'road_relation': '',
            'park_access_bid': ''
        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)
    proceed()


def get_bid_lists():
    """
    获取停车场列表
    """
    poi_online_query = PoiOnlineQuery()
    bid_list = []
    parking_4cate_lists = poi_online_query.get_4categories_park_bids()
    bid_set = set()
    if parking_4cate_lists:
        for cate in parking_4cate_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
    park_4cate_near_lists = poi_online_query.get_4categories_near_park_bids()
    if park_4cate_near_lists:
        for cate in park_4cate_near_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
    logger.info(f"总停车场任务数：{len(bid_list)}")
    
    return bid_list


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        bid_list = get_bid_lists()
        # with open(bid_file, "r") as f:
            # bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)

