# -*- coding: utf-8 -*-
"""
新增大门与精准AOI关联关系监控
"""
import os
import sys
import datetime
import time

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))

sys.path.append("../..")
sys.path.append("..")
from aoi import PgQuery
from aoi import PgUpdate
from aoi import PgRoadQuery
from aoi import CommTool

DB_DOMAIN_ROAD = "toll"

dao = PgQuery.PgQuery()
update_dao = PgUpdate.PgUpdate()
road_dao = PgRoadQuery.PgRoadQuery()


def road_gate_statistics():
    """
    道路大门统计
    :return:
    """
    print("开始执行统计")
    # 获取统计时间
    day, week, origin_week = get_cal_date_day_week()
    if day is None:
        return
    print("day:{}, week:{}, origin_week:{}".format(day, week, origin_week))
    print("开始创建发布库索引")
    road_dao.create_road_release_db_index()
    print("新增大门统计")
    add_gate_calculate(day, week)
    print("删除大门统计")
    delete_gate_calculate(day, week)
    print("大门总量统计")
    total_gate_statistics(day, week)
    update_pre_week_data(week, origin_week)
    print("统计完成")

def update_pre_week_data(current_week, pre_week):
    """
    更新前一周week
    :param current_week:
    :param pre_week:
    :return:
    """
    current_week_begin_date = current_week.split("~")[0]
    pre_week_begin_date = pre_week.split("~")[0]
    if current_week_begin_date != pre_week_begin_date:
        return
    update_dao.update_gate_week_data(pre_week, current_week)


def total_gate_statistics(day, week):
    """
    大门总量统计
    :param day:
    :param week:
    :return:
    """
    total_gate_list = road_dao.get_new_gate_group_gate_type_total()
    if total_gate_list is None:
        return
    print("大门总量统计：{}".format(total_gate_list))
    for total_gate in total_gate_list:
        record = {
            "node_id": total_gate[1],
            "date_day": day,
            "date_week": week,
            "kind": "TOTAL",
            "num": total_gate[0],
            "gate_type": total_gate[1],
            "face_id": "",
            "bid": "",
            "aoi_level": 0
        }
        update_dao.insert_or_update_monitor_gate_num(record)


def delete_gate_calculate(day, week):
    """
    删除大门统计
    :return:
    """
    node_id_cur = ''
    page_num = 1000
    num = 0
    while True:
        print("当前进度：{}".format(num))
        num += page_num
        # 查询最新发布库大门Node
        old_gate_list = road_dao.get_pre_gate_by_node_id_sort(node_id_cur, page_num)
        if old_gate_list is None or len(old_gate_list) == 0:
            break
        node_id_list = [old_gate[0] for old_gate in old_gate_list]
        node_id_cur = old_gate_list[len(old_gate_list) - 1][0]
        node_id_to_old_gate_map = {old_gate[0]: old_gate for old_gate in old_gate_list}

        # 查找最新发布库Node大门，diff出删除大门
        new_node_id_list = []
        new_gate_list = road_dao.get_new_gate_by_node_id(node_id_list)
        if new_gate_list is not None and len(new_gate_list) > 0:
            new_node_id_list = [last_gate[0] for last_gate in new_gate_list]
        delete_node_id_list = list(set(node_id_list) - set(new_node_id_list))
        if len(delete_node_id_list) == 0:
            continue

        # 删除大门入库
        for delete_node_id in delete_node_id_list:
            print("删除大门入库：{}".format(delete_node_id))
            record = {
                "node_id": delete_node_id,
                "date_day": day,
                "date_week": week,
                "num": 1,
                "kind": "DELETE",
                "gate_type": node_id_to_old_gate_map[delete_node_id][1],
                "face_id": "",
                "bid": "",
                "aoi_level": 0
            }
            res = update_dao.insert_or_update_monitor_gate_num(record)
    print("更新完成")


def add_gate_calculate(day, week):
    """
    新增大门统计
    :return:
    """
    node_id_cur = ''
    page_num = 1000
    num = 0
    while True:
        print("当前进度：{}".format(num))
        num += page_num
        # 查询最新发布库大门Node
        new_gate_list = road_dao.get_nav_gate_by_node_id_sort(node_id_cur, page_num)
        if new_gate_list is None or len(new_gate_list) == 0:
            break
        node_id_list = [new_gate[0] for new_gate in new_gate_list]
        node_id_cur = new_gate_list[len(new_gate_list) - 1][0]
        node_id_to_new_gate_map = {new_gate[0]: new_gate for new_gate in new_gate_list}
        # 查找上一次发布库Node大门，diff出新增大门
        old_node_id_list = []
        pre_gate_list = road_dao.get_nav_gate_by_node_id(node_id_list)
        if pre_gate_list is not None and len(pre_gate_list) > 0:
            old_node_id_list = [last_gate[0] for last_gate in pre_gate_list]
        add_node_id_list = list(set(node_id_list) - set(old_node_id_list))
        if len(add_node_id_list) == 0:
            continue

        # 查询新增Node与AOI关联关系
        node_relation_aoi_list = dao.get_aoi_info_by_node(add_node_id_list)
        node_to_aoi_map = {node_relation_aoi[0]: node_relation_aoi for node_relation_aoi in node_relation_aoi_list}

        # 新增大门入库
        for add_node_id in add_node_id_list:
            print("新增大门入库：{}".format(add_node_id))
            record = {"node_id": add_node_id, "face_id": "", "bid": "", "date_day": day, "date_week": week,
                      "aoi_level": 0, "gate_type": node_id_to_new_gate_map[add_node_id][1], "num": 1, "kind": "ADD"}
            if add_node_id in node_to_aoi_map and node_to_aoi_map[add_node_id][1] is not None:
                record['face_id'] = node_to_aoi_map[add_node_id][1]
                record['bid'] = node_to_aoi_map[add_node_id][2]
                record['aoi_level'] = node_to_aoi_map[add_node_id][3]
            res = update_dao.insert_or_update_monitor_gate_num(record)
    print("更新完成")


def get_cal_date_day_week():
    """
    查询统计的天跟周
    :return: 统计天|统计周|原始周
    """
    # 查询最新发布库信息
    road_release_db_list = CommTool.get_release_road_db()
    print("道路发布库信息：{}".format(road_release_db_list))
    if road_release_db_list is None or len(road_release_db_list) < 2:
        print("道路发布库查询异常：{}".format(road_release_db_list))
        return None, None, None
    pre_day = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    last_road_release_db_create_time = datetime.datetime.strptime(road_release_db_list[0]['create_time'],
                                                                  "%Y-%m-%d %H:%M:%S")
    if last_road_release_db_create_time.strftime("%Y-%m-%d") < pre_day:
        print("道路发布库未更新：{}".format(last_road_release_db_create_time))
        return None, None, None
    # 查询最新一条新增大门记录
    last_gate_info = dao.get_last_monitor_gate()
    last_gate_record_time = datetime.datetime.strptime(last_gate_info[0][0:10], "%Y-%m-%d")
    if pre_day == last_gate_record_time:
        print("最新记录已更新：{}".format(last_gate_record_time))
        return None, None, None
    # 计算week
    week_list = last_gate_info[1].split("~")
    week1 = datetime.datetime.strptime(week_list[0], "%Y%m%d")
    week2 = datetime.datetime.strptime(week_list[1], "%Y%m%d")
    diff_day = (datetime.datetime(week2.year, week2.month, week2.day) - datetime.datetime(week1.year, week1.month,
                                                                                          week1.day)).days
    if diff_day < 6:
        week2 = (week2.date() + datetime.timedelta(days=1)).strftime("%Y%m%d")
        current_week = week1.strftime("%Y%m%d") + "~" + week2
    else:
        week1 = (week2.date() + datetime.timedelta(days=1)).strftime("%Y%m%d")
        week2 = week1
        current_week = week1 + "~" + week2
    return pre_day, current_week, last_gate_info[1]


if __name__ == '__main__':
    road_gate_statistics()
