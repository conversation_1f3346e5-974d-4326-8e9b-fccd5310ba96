#!/bin/bash
CUR_DIR=$(readlink -f $(dirname $BASH_SOURCE[0]))
source "$CUR_DIR"/../conf/globalConf.sh
date=$(date +"%Y%m%d")

message=$1
if [ ! "$1" ]; then
  echo "消息不能为空"
  exit 1
fi


if [[ -z "$2" && "$2" == "warning" ]]; then
  type="【警告】"
  at_all=",{\"atall\":true, \"type\":\"AT\"}"
else
  type="【通知】"
  at_all=""
fi

now_time=$(date +"%F %T")
content="${type}${message}，时间: ${now_time}"

curl -X POST -H "Content-Type:application/json" \
-d '{"message":{"body":[{"content":"'"${content}"'","type":"TEXT"}'"${at_all}"']}}' \
'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token='"${INFOFLOW_ROBOT_TOKEN}"
