"""
将每日diff blu_record产出的aoi、gate变更信息表，拷贝到关联策略情报表，作为情报源触发关联策略
"""
import datetime
import time
import os
import sys
import traceback

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from pg_tool import PgTool
from common import common_tool
import aoi_v1_protected

today = datetime.date.today().strftime('%Y%m%d')


class BatchCopyChangeInfo(PgTool):
    """
    关联策略批处理-拷贝情报源
    """

    def __init__(self, road_dsn=None, exec_date=None):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        self.exec_day = today
        if exec_date:
            self.exec_day = exec_date
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        enter
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        exit
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        数据提交
        """
        PgTool.commit(self)

    def copy_change_info(self):
        """
        拷贝每日产出的gate_trigger_ aoi_trigger_表情报信息，到关联策略情报表
        """
        # date = time.strftime('%Y%m%d', time.localtime(time.time()))
        gate_table_name = f"gate_trigger_{self.exec_day}_{self.env}"
        aoi_table_name = f"aoi_trigger_{self.exec_day}_{self.env}"
        gate_sql = f"select to_regclass('{gate_table_name}')"
        aoi_sql = f"select to_regclass('{aoi_table_name}')"

        retry = 0
        while retry < 50:
            self.cursor_trajectory.execute(gate_sql)
            res_gate = self.cursor_trajectory.fetchone()
            self.cursor_trajectory.execute(aoi_sql)
            res_aoi = self.cursor_trajectory.fetchone()
            if res_gate[0] == "" or res_aoi[0] == "":
                retry = retry + 1
                time.sleep(300)
                continue
            break

        if retry >= 50:
            raise Exception('get bid change info failed')
        self.reconnect()
        protected_main_bids = aoi_v1_protected.load_protected_main_bids(self)

        sql = f"""
           select 
               bid, 
               node_id, 
               face_id, 
               type 
           from 
               {gate_table_name}
           where 
               bid != ''
        """
        self.cursor_trajectory.execute(sql)
        data = self.cursor_trajectory.fetchall()
        for v in data:
            bid = v[0]
            if bid in protected_main_bids:
                continue
            node_id = v[1]
            face_id = v[2]
            type = v[3]
            sql = f"""
               insert into 
                   gate_match_intel_src(
                       bid,
                       face_id, 
                       node_id,
                       src
                   )
               values(
                   '{bid}',
                   '{face_id}',
                   '{node_id}',
                   'node-{type}'
               )
            """
            self.cursor_poi_write.execute(sql)

        sql = f"""
           select 
               bid, 
               face_id, 
               type 
           from 
               {aoi_table_name}
           where 
               bid != ''
        """
        self.cursor_trajectory.execute(sql)
        data = self.cursor_trajectory.fetchall()
        for v in data:
            bid = v[0]
            face_id = v[1]
            type = v[2]
            sql = f"""
               insert into 
                   gate_match_intel_src(
                       bid,
                       face_id, 
                       src
                   )
               values(
                   '{bid}',
                   '{face_id}',
                   'aoi-{type}'
               )
            """
            self.cursor_poi_write.execute(sql)
        self.conn_poi_write.commit()

    def run(self):
        """
        run
        """
        print("copy_change_info start：", self.get_now())
        self.copy_change_info()
        print("copy_change_info end：", self.get_now())

    @staticmethod
    def get_now():
        """
        获取当前时间戳
        """
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


if __name__ == "__main__":
    """
    main
    """
    yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y%m%d')
    with BatchCopyChangeInfo(exec_date=yesterday) as batch_copy:
        try:
            batch_copy.run()
        except Exception as e:
            common_tool.send_hi("例行拷贝blu_record变更失败-de16-xxl-job-165", ['chenbaojun_cd'])
            traceback.print_exc()
