"""
抓取大门任务和建筑物任务
"""
import dataclasses
import os
import sys
import datetime
from pathlib import Path
import pandas as pd
import shapely.wkt
import tqdm
import uuid

# 设置root_path
root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())

from src.common import pgsql

TYPE_BUILD = 'build'
TYPE_DOOR = 'door'
PRIORITY_COMMON = 0
PRIORITY_HIGH = 999

# afs 文件存放的位置
AFS_BASE_PATH = Path('/home/<USER>/mnt/aoi/apartment/work_daily')

TABLE = 'apartment_intelligence_task'
BUILD_TASK_COUNT = 7000
ALL_COUNT = 10000
BUFFER_SIEZ = 20 * 1.1 * 1e-5

# 竞品入库的表
TABLE_INTELLIGENCE = 'apartment_intelligence'

intelligence_save_dir = Path(os.path.abspath(__file__)).parents


def bool_task_exists(bid):
    """
    检查是否存在, 主要是防止重复投放
    :param bid:
    :return:
    """
    week_before = datetime.date.today() - datetime.timedelta(days=60)
    sql = f'select * from {TABLE} where bid=:d1 and created_at>:d2'
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"d1": bid, "d2": week_before})
    return ret


def add_task(bid, _type, priority=0):
    """
    添加任务
    :param bid:
    :param _type:
    :param priority: 优先级
    :return:
    """
    if bool_task_exists(bid):
        print(f"近期已经投放过:{bid}")
        return
    sql = f'insert into {TABLE} (bid, type, priority) values (:d1, :d2, :d3)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"d1": bid, "d2": _type, "d3": priority})
    return True


def multi_create_aoi(bid_list, task_file):
    """
    批量创建任务
    :param bid_list:
    :param task_file:
    :return:
    """
    bid_data = []
    sql = 'select b.poi_bid, st_astext(a.geom) as geom' \
          ' from blu_face a inner join blu_face_poi b on a.face_id=b.face_id' \
          ' where b.poi_bid in :d1'
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"d1": tuple(bid_list)})
    if len(ret) > 0:
        for item in ret:
            bid_data.append({
                "bid": item.poi_bid,
                "geom": shapely.wkt.loads(item.geom).buffer(BUFFER_SIEZ).wkt
            })
        pd.DataFrame(bid_data).to_csv(AFS_BASE_PATH / task_file, sep='\t', index=False)
    return True


def create_task():
    """
    创建抓取任务
    :return:
    """
    if not AFS_BASE_PATH.is_dir():
        print("找不到挂载目录,无法下发任务,请重新处理")
        return
    # 20250311.csv
    task_file = (datetime.date.today() + datetime.timedelta(days=1)).strftime("%Y%m%d") + ".csv"
    if (AFS_BASE_PATH / task_file).exists():
        print("已经下发过, 可明天再下发")
        return
    bid_set = set()
    sql = f'select bid from apartment_intelligence_task where type = :d1 and status = 0 order by priority desc' \
          f' limit {BUILD_TASK_COUNT} '
    build_ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": TYPE_BUILD})
    print(f"建筑物类型的任务数量:{len(build_ret)}")
    door_count = ALL_COUNT - len(build_ret)
    sql = f'select bid from apartment_intelligence_task where type = :d2 and status = 0 order by priority desc' \
          f' limit {door_count} '
    door_ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d2": TYPE_DOOR})
    print(f"大门任务类型的梳理:{len(door_ret)}")
    for item in build_ret:
        bid_set.add(item.bid)
    for item in door_ret:
        bid_set.add(item.bid)
    bid_list = list(bid_set)
    if (len(bid_list)) < 1:
        print("无任务可下发")
        return

    if multi_create_aoi(bid_list, task_file):
        sql = 'update apartment_intelligence_task set status=1, task_file = :d2 where bid in :d3 and status=0 '
        pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"d2": task_file, "d3": tuple(bid_list)})
        print("下发成功")
    else:
        print("下发失败")


# def add_multi():
#     """
#     批量添加
#     :return:
#     """
#     df = pd.read_csv('all_v1.tsv', converters={"main_bid": str, 'city': str}, sep='\t')
#     df_sh = df[df['city'] == '上海市']
#     df_bj = df[df['city'] == '北京市']
#     df_other = df[~((df['city'] == '上海市') & (df['city'] == '北京市'))]
#     for _, row in tqdm.tqdm(df_sh.iterrows(), total=len(df_sh), desc='添加上海任务'):
#         add_task(row['main_bid'], TYPE_DOOR, 0)
#     for _, row in tqdm.tqdm(df_bj.iterrows(), total=len(df_bj), desc='添加北京任务'):
#         add_task(row['main_bid'], TYPE_DOOR, 0)
#     for _, row in tqdm.tqdm(df_other.iterrows(), total=len(df_other), desc='添加其他城市任务'):
#         add_task(row['main_bid'], TYPE_DOOR, 0)


@dataclasses.dataclass
class ApartmentIntelligence:
    """
    竞品数据保存对象
    """
    aoi_bid: str
    gid: str
    name: str
    city: str
    tag: str
    address: str
    x: str
    y: str
    plan: str
    uuid: str
    notknown: str = dataclasses.field(default='')
    created_at: str = dataclasses.field(default=datetime.datetime.now())


def insert_data_create(lines: list, check_date: str):
    """
    保存任务
    :param lines:
    :param check_date:
    :return:
    """
    # 删除历史数据
    print("开始创建任务")
    if not check_date:
        return
    print(f"{check_date}, 共{len(lines)}条数据")
    sql = 'delete from apartment_intelligence where plan=:plan'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"plan": check_date})
    pd.DataFrame(lines).to_sql('apartment_intelligence', con=pgsql.get_engine(pgsql.ENGINE_POI_ONLINE),
                               index=False, if_exists='append', chunksize=10000)


def save_intelligence_data(check_date):
    """
    保存竞品数据
    :param check_date:
    :return:
    """
    path = AFS_BASE_PATH / f'{check_date}.csv.ret'
    print(f"开始解析文件:{path.as_posix()}")
    lines: list = []
    # 用于去重
    keys = {}
    with open(path, 'r') as f:
        for item in f:
            line = item.strip().split('\t')
            if line[0] == '情报id':
                continue
            if len(line) != 13:
                print('数据错误', line)
                continue
            d = ApartmentIntelligence(
                aoi_bid=line[0],
                gid=line[2],
                name=line[3],
                city=line[4],
                tag=line[5],
                address=line[6],
                x=line[7],
                y=line[8],
                plan=check_date,
                uuid=str(uuid.uuid4())
            )
            key = f"{d.aoi_bid}_{d.gid}_{check_date}"
            if key in keys:
                continue
            keys[key] = 1
            lines.append(dataclasses.asdict(d))
    insert_data_create(lines, check_date)


if __name__ == '__main__':
    # add_by_file()
    # add_multi()
    # clear_multi()
    if len(sys.argv) > 1:
        action = sys.argv[1]
        if action == 'create_task':
            # 生成任务
            create_task()
        elif action == 'save_intelligence':
            # 保存竞品数据
            if len(sys.argv) > 2:
                _check_date = sys.argv[2]
            else:
                _check_date = datetime.date.today().strftime("%Y%m%d")
            print(f"开始处理:{_check_date}")
            save_intelligence_data(_check_date)
