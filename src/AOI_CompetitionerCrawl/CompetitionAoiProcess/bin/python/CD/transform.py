"""
transform the input data format
transform the coordinate format
"""

import sys
import util
import shapely.wkt


def main():
    """
    :return: void
    """
    file = open(sys.argv[1])

    for line in file:
        line = line.strip("\n")
        items = line.split(",")
        out = []
        temp = []
        poly = False

        for i in range(len(items)):
            if 'POLYGON' in items[i]:
                temp.append(items[i])
                poly = True
                continue

            if poly:
                temp.append(items[i])
                if '))' in items[i]:
                    poly = False

                if poly == False and len(temp) >= 2:
                    shape = ",".join(temp)
                    out.append(shape)
                    continue

                continue

            out.append(items[i])

        coordinates = out[1].strip("\"").strip("POLYGON((").strip(")").split(",")
        polygon = "POLYGON (("
        for xy in coordinates:
            [x, y] = util.coordtrans("gcj02ll", "bd09mc", float(xy.split(" ")[0]), float(xy.split(" ")[1]))
            polygon += str(x) + " " + str(y) + ", "
        polygon = polygon[:-2] + "))"
        shape = shapely.wkt.loads(polygon)

        out[1] = str(shape)
        out[2] = ""
        name = out[3]
        if name == "" or name is None or name == "\"\"":
            continue
        out[6] = ""
        out[11] = "TJ"
        out[13] = ""
        print "\t".join(out)


main()
