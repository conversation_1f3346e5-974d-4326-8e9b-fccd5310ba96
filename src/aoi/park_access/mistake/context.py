# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场出入口上下文信息
"""
from datetime import datetime, timedelta
import sys
import os
from loguru import logger
from dataclasses import dataclass, field
from mapio.utils import bid2uid
from shapely import wkt

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.dest_traj_to_aoi import DestTrajToAOI
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.aoi_resume_query import AoiResumeQuery
from src.aoi.BFQuery import BFQuery
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi.dao.traj_feature import TrajFeatureDao
from src.aoi import utils

log_path = "./log/underground.log"
logger.add(log_path, rotation="10 MB")



# 出入口错误关联策略
PARK_ACCESS_MISTAKE_BY_DISTANCE = "PARK_ACCESS_MISTAKE_DISTANCE_GT_200M"
PARK_ACCESS_MISTAKE_BY_LINK_NEAR_CLOSE_GATE = "PARK_ACCESS_MISTAKE_BY_LINK_NEAR_CLOSE_GATE"
PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE = "PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE"
PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE_ONE_ACCESS = "PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE_ONE_ACCESS"
PARK_ACCESS_MISTAKE_BY_YAW_EXP_TRAJ = "PARK_ACCESS_MISTAKE_BY_YAW_EXP_TRAJ"

PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_DEAD_ROAD = "PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_DEAD_ROAD"
PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD = "PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD"
PARK_ACCESS_MISTAKE_BY_ACCESS_BY_LOW_TRAJ = "PARK_ACCESS_MISTAKE_BY_ACCESS_BY_LOW_TRAJ"
PARK_ACCESS_MISTAKE_BY_NODE_RELATION_BY_OTHER_PARK = "PARK_ACCESS_MISTAKE_BY_NODE_RELATION_BY_OTHER_PARK"
PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK = "PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK"
PARK_ACCESS_MISTAKE_BY_LINK_ON_URGENT_GATE = "PARK_ACCESS_MISTAKE_BY_LINK_ON_URGENT_GATE"
PARK_ACCESS_MISTAKE_BY_DIFF_MAIN_BID_FROM_NODE_AND_PARK = "PARK_ACCESS_MISTAKE_BY_DIFF_MAIN_BID_FROM_NODE_AND_PARK"

PARK_ACCESS_MISTAKE_BY_OVERGROUND_DEAD_ROAD = "PARK_ACCESS_MISTAKE_BY_OVERGROUND_DEAD_ROAD"
PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE = "PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE"
PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_LINK = "PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_LINK"
PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_NODE = "PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_NODE"
PARK_ACCESS_MISTAKE_BY_NODE_LINE_HAS_OTHER_NODE = "PARK_ACCESS_MISTAKE_BY_NODE_LINE_HAS_OTHER_NODE"
PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF"
# PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OFFLINE = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OFFLINE"  
# strategy改成上面的，strategy_value = 'complete_diff_offline'
PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_LESS_100 = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_LESS_100"
PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_MISTAKE = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_MISTAKE"
PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_ADD = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OUT_ADD"
PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE = "PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE"
PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_ON_DEAD_ROAD_BUT_NEAR = \
    "PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_ON_DEAD_ROAD_BUT_NEAR"
PARK_ACCESS_MISTAKE_BY_PASS_HIGH_ROAD = "PARK_ACCESS_MISTAKE_BY_PASS_HIGH_ROAD"
PARK_ACCESS_MISTAKE_BY_EXP_TRAJ_NOT_ARRIVE = "PARK_ACCESS_MISTAKE_BY_EXP_TRAJ_NOT_ARRIVE"
PARK_ACCESS_MISTAKE_BY_DEL_RECALL = "PARK_ACCESS_MISTAKE_BY_DEL_RECALL"
PARK_ACCESS_PLAN_AND_PARENT_EXP_TRAJ_DIFF = "PARK_ACCESS_PLAN_AND_PARENT_EXP_TRAJ_DIFF"
PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD_MIN_AREA = "PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD_MIN_AREA"
PARK_ACCESS_MISTAKE_RONGYU_AUTO = "PARK_ACCESS_MISTAKE_RONGYU_AUTO"



@dataclass
class Traj:
    """
    轨迹信息
    """
    dest_traj_list: list = field(default_factory=list)
    park_dest_traj_list: list = field(default_factory=list)
    aoi_exp_traj_list: list = field(default_factory=list)
    park_exp_traj_list: list = field(default_factory=list)


@dataclass
class Dao:
    """
    数据库操作对象
    """
    road_dao = Road()
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    aoi_resume_query = AoiResumeQuery()
    trans_dao = Trans()
    beeflow_dao = BFQuery()
    master_back_dao = MasterBackDao()
    dest_traj_dao = DestTraj()
    traj_feature_dao = TrajFeatureDao()
    dest_traj_to_aoi = DestTrajToAOI()


@dataclass
class ParkingAccess:
    """
    停车场出入口挖掘成果
    """
    park_bid: str = field(default="", init=False)  # 类型, 关联Node还是link
    type: str = field(default="", init=False)  # 类型, 关联Node还是link
    node_id: str = field(default="", init=False)
    short_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    node_geom_mc: str = field(default="", init=False)
    node_passage: str = field(default="", init=False)
    link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    link_geom_mc: str = field(default="", init=False)
    strategy: str = field(default="", init=False)  # 挖掘策略
    is_valid: bool = field(default=True, init=False)  # 关联是否合法
    filter_reason: str = field(default="", init=False)  # 过滤原因
    road_relation: str = field(default="", init=False)
    name: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    confidence: int = field(default=0, init=False)


@dataclass
class ParkingInfo:
    """"
    停车场信息
    """
    bid: str = field(default="", init=False)
    name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    park_wkt: str = field(default="", init=False)
    park_area_wkt: str = field(default="", init=False)
    park_area_aoi_30m_wkt: str = field(default="", init=False)
    park_area_gcj_wkt: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    road_relation_childrens: str = field(default="", init=False)
    traj_dbname: str = field(default="", init=False)
    address: str = field(default="", init=False)
    park_category: str = field(default="", init=False)


@dataclass
class ParkingAccess:
    """
    停车场出入口挖掘成果
    """
    bid: str = field(default="", init=False)
    type: str = field(default="", init=False)  # 类型, 关联Node还是link
    node_id: str = field(default="", init=False)
    short_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    node_geom_mc: str = field(default="", init=False)
    node_passage: str = field(default="", init=False)
    link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    link_geom_mc: str = field(default="", init=False)
    strategy: str = field(default="", init=False)  # 挖掘策略
    strategy_value: str = field(default="", init=False)  # 挖掘策略
    is_valid: bool = field(default=True, init=False)  # 关联是否合法
    filter_reason: str = field(default="", init=False)  # 过滤原因
    road_relation: str = field(default="", init=False)
    name: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    confidence: int = field(default=0, init=True)
    open_limit_new: int = field(default=0, init=True)
    remark: str = field(default="", init=False)


COORDINATE_FACTOR = 110000

# 出入口关联类型
PARKING_ACCESS_TYPE_NODE = "NODE"
PARKING_ACCESS_TYPE_LINK = "LINK"
PARKING_ACCESS_TYPE_POINT = "POINT"


@dataclass
class ParkingSubPOI:
    """
    停车场子POI信息
    """
    bid: str = field(default="", init=False)
    point_gcj: str = field(default="", init=False)
    relation_short_node_id: str = field(default="", init=False),
    relation_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    relation_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    type: str = field(default="", init=False)
    name: str = field(default="", init=False)
    open_limit_new: int = field(default=0, init=True)

@dataclass
class AOIInfo:
    """
    停车场父AOI信息
    """
    bid: str = field(default="", init=False)
    wkt: str = field(default="", init=False)
    area: str = field(default="", init=False)
    std_tag: str = field(default="", init=False)
    aoi_complete: int = field(default=0, init=False)
    aoi_level: int = field(default=0, init=False)
    park_distance: float = field(default=0, init=False)
    blu_access_list: list = field(default_factory=list, init=False)


class Context:
    """
    建筑物后处理上下文
    """

    def __init__(self, parking: list):
        self.origin_parking_list = parking
        self.parking_info = ParkingInfo()
        self.sub_pois = list()
        self.aoi_info = AOIInfo()
        self.parking_access_list = list()
        self.traj = Traj()
        self.dao = Dao()
        self.init_parking_info()
        self.init_aoi_info()
        self.debug = True

    def init_parking_info(self):
        """
        初始化停车场信息
        Args:
            ctx:
            parking:

        Returns:

        """
        paring_info = ParkingInfo()
        parking = self.origin_parking_list
        paring_info.name = parking[0]
        paring_info.show_tag = parking[4]
        paring_info.parent_id = parking[5] if parking[5] and parking[5] != '0' else ""
        paring_info.bid = parking[7]
        paring_info.park_wkt = parking[10]
        paring_info.park_area_wkt = parking[6]
        paring_info.road_relation = parking[9]
        paring_info.address = parking[11]
        paring_info.open_limit_new = parking[12]
        paring_info.road_relation_childrens = ""
        if len(parking) > 13:
            paring_info.road_relation_childrens = parking[13]
        if parking[6]:
            paring_info.park_area_gcj_wkt = utils.mc_to_gcj(parking[6])
        self.parking_info = paring_info

    def init_aoi_info(self):
        """
        初始化AOI信息
        Returns:

        """
        if self.parking_info.parent_id == "" or self.parking_info.parent_id == "0":
            return
        aoi_res = self.dao.master_back_dao.get_aoi_info_by_bid(self.parking_info.parent_id)
        if not aoi_res:
            return
        aoi_info = AOIInfo()
        aoi_info.bid = aoi_res[2]
        aoi_info.area = aoi_res[0]
        aoi_info.wkt = aoi_res[1]
        aoi_info.aoi_complete = aoi_res[4]
        aoi_info.aoi_level = aoi_res[3]
        self.aoi_info = aoi_info
        # 计算停车场与AOI距离
        aoi_sp = wkt.loads(aoi_info.wkt)
        park_sp = wkt.loads(self.parking_info.park_wkt)
        self.aoi_info.park_distance = aoi_sp.distance(park_sp)
        # 查询AOI垂类
        poi_info = self.dao.poi_online_query.get_poi_by_bids([aoi_res[2]])
        if not poi_info:
            logger.info(f"{self.parking_info.bid}POI失效")
            return
        self.aoi_info.std_tag = poi_info[0]['std_tag']

    def init_traj(self):
        """
        获取轨迹
        Args:
            ctx:

        Returns:

        """
        traj = Traj()
        # 经验轨迹
        if self.parking_info.parent_id:
            parent_uid = bid2uid(int(self.parking_info.parent_id))
            aoi_exp_traj_list = self.dao.dest_traj_to_aoi.get_exp_traj_by_bid_new(parent_uid)
            traj.aoi_exp_traj_list = aoi_exp_traj_list if aoi_exp_traj_list else []
        park_uid = bid2uid(int(self.parking_info.bid))
        park_exp_traj_list = self.dao.dest_traj_to_aoi.get_exp_traj_by_bid_new(park_uid)
        traj.park_exp_traj_list = park_exp_traj_list if park_exp_traj_list else []

        # 20天终点轨迹
        date_15_days_ago = (datetime.now() - timedelta(days=20)).strftime('%Y-%m-%d %H:%M:%S')
        if self.aoi_info.wkt:
            traj_geom = self.aoi_info.wkt
            traj_buffer_geom = wkt.loads(traj_geom).buffer(10 / COORDINATE_FACTOR).wkt
            park_traj_list = utils.get_traj_list_by_bid_and_geom(self.parking_info.parent_id, traj_buffer_geom,
                                                                 date_15_days_ago)
            traj.dest_traj_list = [wkt.loads(item[0]) for item in park_traj_list] if park_traj_list else []
        park_area_gcj_wkt = self.parking_info.park_area_gcj_wkt \
            if self.parking_info.park_area_gcj_wkt else wkt.loads(self.parking_info.park_wkt).buffer(
            30 / COORDINATE_FACTOR).wkt
        traj_buffer_geom = wkt.loads(park_area_gcj_wkt).buffer(10 / COORDINATE_FACTOR).wkt
        park_traj_list = utils.get_traj_list_by_bid_and_geom(self.parking_info.parent_id, traj_buffer_geom,
                                                             date_15_days_ago)
        traj.park_dest_traj_list = [wkt.loads(item[0]) for item in park_traj_list] if park_traj_list else []
        self.traj = traj


