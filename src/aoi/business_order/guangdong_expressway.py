# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDD商单数据输出
"""
import os
import sys

import psycopg2

root_dir = "/home/<USER>/gengyong/business_order/guangdong_expressway"
bud_path = root_dir + "/background/00-building"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-division"
bad_county_path = root_dir + "/background/04-division/county"
bad_city_path = root_dir + "/background/04-division/city"
bad_town_path = root_dir + "/background/04-division/town"
bad_province_path = root_dir + "/background/04-division/province"
province = "广东省"
mesh_ids = ""


def run():
    """
    成果输出
    Returns:

    """
    print(f"开始blu导出")
    blu_export()
    print(f"开始bud导出")
    bud_export()
    print(f"开始blc导出")
    blc_export()
    print(f"开始brw导出")
    brw_export()
    print(f"开始bad导出")
    bad_export()
    print(f"导出完成")


def blu_export():
    """
    AOI数据输出
    Returns:

    """
    export_dir = blu_path
    sql = f"""
        with tmp as (select geom wkt from bad_province where proname='{province}')
        select 
            face_id,poi_bid,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,name_ch as name,bf.geom
        from tmp, blu_face bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
        where mc.mesh_id in('{mesh_ids}') and st_intersects(bf.geom, tmp.wkt)
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)

def bud_export():
    """
    建筑物数据输出
    Returns:

    """
    export_dir = bud_path
    sql = f"""
        with tmp as (select geom wkt from bad_province where proname='{province}')
        select 
            bf.face_id,bf.struct_id,case when bs.name_ch='虚拟POI' then '' else bs.name_ch end as name_ch,bf.aoi_id,bf.height,
            0.0 as floors,0.0 as sub_floors,bf.area,bf.perimeter,bf.geom  
        from tmp, bud_face bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            left join bud_struct_muku bs on bf.struct_id=bs.struct_id 
        where mc.mesh_id in('{mesh_ids}') and st_intersects(bf.geom, tmp.wkt)
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/bud_face.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """

    os.system(cmd)


def blc_export():
    """
    BLC数据输出
    Returns:

    """
    export_dir = blc_path
    sql = f"""
        with tmp as (select geom wkt from bad_province where proname='{province}')
        select 
            face_id,name_ch,name_ph,kind,admin_id,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,bf.geom 
        from tmp, blc_green bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
        where mc.mesh_id in('{mesh_ids}') and st_intersects(bf.geom, tmp.wkt)
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_lvdi.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)
    sql = f"""
        with tmp as (select geom wkt from bad_province where proname='{province}')
        select 
            face_id,name_ch,name_ph,kind,admin_id,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,bf.geom  
        from tmp, blc_water bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
        where mc.mesh_id in('{mesh_ids}') and st_intersects(bf.geom, tmp.wkt)
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_shuixi.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)


def brw_export():
    """
    BRW数据输出
    Returns:

    """
    export_dir = brw_path
    sql = f"""
        with tmp as (select geom wkt from bad_province where proname='{province}')
        select 
            link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,bf.geom 
        from  tmp, brw_link bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
        where mc.mesh_id in('{mesh_ids}')  and st_intersects(bf.geom, tmp.wkt)
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/brw_link.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)
    sql = f"""
        select 
            node_id,kind,form
        from brw_node bf 
            inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
        where mc.mesh_id in('{mesh_ids}')
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/brw_node.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)


def bad_export():
    """
    行政区划数据输出
    Returns:

    """
    export_dir = bad_province_path
    sql = f"""
                select 
                     guid as face_id,procode as admin_id,proname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
                from bad_province where proname = '{province}'
            """
    cmd = f"""
                ogr2ogr -f 'MapInfo File' {export_dir}/division_province.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
            """
    os.system(cmd)
    export_dir = bad_city_path
    sql = f"""
        select 
             guid as face_id,citycode as admin_id,cityname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
        from bad_city where proname = '{province}'
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/division_city.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)
    export_dir = bad_county_path
    sql = f"""
        select 
             guid as face_id,countycode as admin_id,countyname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
        from bad_county where proname = '{province}'
    """
    cmd = f"""
        ogr2ogr -f 'MapInfo File' {export_dir}/division_county.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
    """
    os.system(cmd)
    export_dir = bad_town_path
    sql = f"""
                select 
                     guid as face_id,towncode as admin_id,townname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
                from bad_town where proname = '{province}'
            """
    cmd = f"""
                ogr2ogr -f 'MapInfo File' {export_dir}/division_town.mif -dsco farmat=mif PG:"host='**************' dbname='business_order' port='7432' user='business_order_se_rw' password='kpigzear'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
            """
    os.system(cmd)


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/background")
    make_dir(bud_path)
    make_dir(blu_path)
    make_dir(blc_path)
    make_dir(brw_path)
    make_dir(bad_path)
    make_dir(bad_county_path)
    make_dir(bad_city_path)
    make_dir(bad_province_path)
    make_dir(bad_town_path)

def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)

def get_mesh_ids_by_file(mesh_id_file):
    """
    获取图幅信息
    Args:
        mesh_id_file:

    Returns:

    """
    mesh_ids = []
    with open(mesh_id_file, 'r') as f:
        for line in f.readlines():
            line = line.strip()
            if len(line) > 0:
                mesh_ids.append(line)
    return mesh_ids


if __name__ == '__main__':
    data_version = sys.argv[1]
    mesh_conf_file = sys.argv[2]
    cmd = "rm -rf ./background"
    os.system(cmd)
    init_dir()
    print("目录初始化完成,开始数据导出")
    mesh_id_list = get_mesh_ids_by_file(mesh_conf_file)
    mesh_ids = "','".join(mesh_id_list)
    run()
    zip_cmd = f"zip -r {data_version} background"
    os.system(zip_cmd)