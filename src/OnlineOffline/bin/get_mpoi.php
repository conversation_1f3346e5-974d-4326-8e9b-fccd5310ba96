<?php

require_once(dirname(__FILE__).'/CNsHead.class.php');

/**
  * @param: nshead, arr
  * @return: res
  **/
function send_to_diwu($nshead, $arr)    
{
	$di_req = mc_pack_array2pack($arr, PHP_MC_PACK_V2);
	$ret_arr['body_len'] = strlen($di_req);
	$ret_arr['log_id'] = 123;
	$ret_arr['provider'] = "script";

    //var_dump($di_req);
	$socket = fsockopen("*************", 8887);
    $num = 0;
    while($socket == false  && $num < 3) {
    	$socket = fsockopen("*************", 8887);
        sleep(1);
        $num++;
    } 
    if($socket == false) {
        return 0;
    }
	$res = $nshead->nshead_write($socket, $ret_arr, $di_req);
	$di_res = $nshead->nshead_read($socket);
	fclose($socket);
    if($di_res == false) {
        return 0;
    }
	$res = mc_pack_pack2array($di_res['buf']);
    return $res;
}

$nshead = new NsHead();
$mid=$argv[1];


    $send_arr = array("action" => "get");
    $send_arr["query_type"] = "dw_mpoi_upd";
    
    $data = array();
    array_push($data, $mid);
    $send_arr["mid"] = $data;
    var_dump($send_arr);
    $res = send_to_diwu($nshead, $send_arr);
    var_dump($res);
