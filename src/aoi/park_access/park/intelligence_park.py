# -*- coding: utf-8 -*-
"""
停车场情报挖掘, 根据新增停车场情报挖掘出入口情报下发
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
from math import ceil

import tqdm
import requests
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from loguru import logger
from shapely import wkt
from sklearn.cluster import DBSCAN

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from shapely import wkt
from loguru import logger
from common import pipeline
from aoi.park_access.park.context import Context
from aoi.park_access.park import context
from aoi.park_access.park import park_access_qc as qc
from aoi.park_access.overground import context as overground_context
from aoi.park_access.overground import intelligence_overground as overground_intelligence
from aoi.park_access.underground import context as underground_context
from aoi.park_access.underground import intelligence_underground as underground_intelligence

COORDINATE_FACTOR = 110000

# logger.remove()
log_path = "./log/intelligence_park.log"
logger.add(log_path, rotation="10 MB")
IS_TEST = True


def create_park_intelligence(ctx: Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        create_parking_info,
        overground_park_access_intelligence,
        underground_park_access_intelligence,
        park_access_filter,
        save_park_to_db
    )
    pipe_list(ctx)


def create_parking_info(ctx: Context, proceed):
    """
    构造停车场信息
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin create_parking_info")
    # 构造停车场信息
    parking_info = []
    if ctx.parking_info.bid:
        parking_list = ctx.dao.poi_online_query.get_parking_list(bids=[ctx.parking_info.bid])
        if parking_list:
            parking_info = parking_list[0]
    if not parking_info:
        parking_map = {
            "name": ctx.parking_info.name,
            "point_x": 0,
            "point_y": 0,
            "std_tag": "交通设施;停车场",
            "show_tag": ctx.parking_info.show_tag,
            "parent_id": ctx.parking_info.parent_id,
            "area": "",
            "bid": ctx.parking_info.bid,
            "show_area": "",
            "road_relation": "",
            "gcj_geom": ctx.parking_info.park_wkt,
        }
        parking_info = list(parking_map.values())
    ctx.virtual_park_info = parking_info
    return proceed()


def overground_park_access_intelligence(ctx: Context, proceed):
    """
    地上停车场出入口情报挖掘
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin overground_park_access_intelligence")
    if ctx.parking_info.show_tag == '地下停车场':
        return proceed()
    # 获取出入口情报
    overground_ctx = overground_context.Context(ctx.virtual_park_info)
    overground_ctx.init_context()
    overground_ctx.get_park_sub_pois()
    # AOI 场景
    if overground_ctx.aoi_info.bid:
        # 获取出入口信息
        overground_ctx.get_aoi_blu_access_list()
        # 轨迹过滤AOI范围外的
        overground_ctx.filter_traj_by_aoi()
        # 计算AOI出入口轨迹通量
        overground_ctx.cal_dest_traj_pass_blu_access()
    overground_ctx.traj.filter_aoi_dest_traj_list = overground_ctx.traj.dest_traj_list
    ctx.parking_access_list = overground_intelligence.get_park_access_intelligence(overground_ctx)
    return proceed()


def underground_park_access_intelligence(ctx: Context, proceed):
    """
    地下停车场出入口情报挖掘
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin underground_park_access_intelligence")
    if ctx.parking_info.show_tag == '地上停车场':
        return proceed()
    # 获取出入口情报
    underground_ctx = underground_context.Context(ctx.virtual_park_info)
    underground_ctx.init_context()
    underground_ctx.get_park_sub_pois()
    # AOI 场景
    if underground_ctx.aoi_info.bid:
        # 获取出入口信息
        underground_ctx.get_aoi_blu_access_list()
        # 轨迹过滤AOI范围外的
        underground_ctx.filter_traj_by_aoi()
        # 计算AOI出入口轨迹通量
        underground_ctx.cal_dest_traj_pass_blu_access()
    underground_ctx.traj.filter_aoi_dest_traj_list = underground_ctx.traj.dest_traj_list
    ctx.parking_access_list += underground_intelligence.get_park_access_intelligence(underground_ctx)
    return proceed()


def park_access_filter(ctx: Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_filter(ctx)
    return proceed()


def save_park_access_intelligence_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_access_intelligence_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 是否有效:{item.is_valid},过滤原因:{item.filter_reason}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        data = {
            "qb_id": str(uuid.uuid4()).replace("-", ""),
            "model": f"strategy_park_access_parking_add",
            "recall_desc": "strategy_park_access",
            "parent_id": ctx.parking_info.parent_id,
            "parking_bid": ctx.parking_info.bid,
            "geom": park_access.node_geom,
            "strategy": park_access.strategy,
            "dt": datetime.datetime.now().strftime("%Y%m%d"),
            "batch": f"strategy_park_access_parking_add_{time.strftime('%Y%m%d', time.localtime())}",
            "node_id": f"{park_access.node_id}|{park_access.link_id}",
            "outside_id": ctx.park_intelligence_id,
        }
        ctx.dao.poi_online_rw.insert_parking_manual_prepush(data)

    proceed()


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 是否有效:{item.is_valid},过滤原因:{item.filter_reason}")
        return proceed()
    exist_valid_park_access = False
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        exist_valid_park_access = True
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"new_park_intelligence_add_{ctx.guid}"
        tcq_batch = 'TCQXZP2024102801'
        data = {
            'bid': ctx.parking_info.bid,
            'strategy': park_access.strategy,
            'type': 'new_park_intelligence_add',
            'batch_number': f"new_park_intelligence_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': ctx.park_intelligence_id,
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': tcq_batch,
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
        }
        ctx.dao.poi_online_rw.insert_gate_front_intelligence(data)
    if exist_valid_park_access:
        ctx.dao.poi_online_rw.update_effect_raising_create_park_status_by_qb_id(ctx.park_intelligence_id, 1)
    else:
        # 没有匹配到有效出入口, 修改无效,直接推送停车场作业
        ctx.dao.poi_online_rw.update_effect_raising_create_park_project_by_qb_id(ctx.park_intelligence_id, 'TCZ')
        # qb_id = push.park_intelligence_push(ctx.park_intelligence_id)
        # ctx.dao.poi_online_rw.update_effect_raising_create_park_qb_id_by_id(ctx.park_intelligence_id, qb_id)
    proceed()


def main(park_intelligence_ids=list):
    """
    出入口停车场情报产出
    Args:
        park_intelligence_ids:

    Returns:

    """
    # 获取停车场信息
    park_intelligence_ids = park_intelligence_ids.tolist() if len(park_intelligence_ids) > 0 else None
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_park_add_intelligence([0], park_intelligence_ids, 'TCZ')
    if not parking_list:
        logger.info("没有需要挖掘的停车场")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"开始挖掘停车场出入口:{parking['id']}")
            ctx = Context(parking)
            create_park_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
