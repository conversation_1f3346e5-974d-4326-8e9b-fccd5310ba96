"""
自动下发任务
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())
from src.common import pgsql
import requests
import json
import pandas as pd
import datetime
import tqdm

work_dir = Path(os.path.abspath(__file__)).parent / 'v2_preview'
work_dir.mkdir(exist_ok=True, parents=True)

PRE_BASE_API_PATH = "https://mapde-poi.baidu-int.com/prod"
DE_OSS_BASE_PATH = "http://mapde-poi.baidu-int.com/beefs/get?uuid="
today = datetime.datetime.now().strftime('%Y%m%d')


def add_manual_wait(bid, node_id, source):
    """
    下发任务
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    uid = f"{node_id}_{bid}_{today}"
    sql = 'insert into gate_aoi_match_check_data (bid, node_id, uid, source, ways, wait_create)' \
          ' values (:bid, :node_id, :uid, :source, :ways, :wait_create)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql,
                      {"bid": bid, "node_id": node_id, "uid": uid, "source": source,
                       "ways": '清查提准', "wait_create": 1})


def upload_file(filename):
    """
    上传文件
    :param filename:
    :return:
    """
    req = requests.post(
        url=PRE_BASE_API_PATH + "/compat/upload",
        files={"file": open(filename, "rb")}
    )
    return req.text


def get_token():
    """
    生成token
    :return:
    """
    req = requests.post(
        url=PRE_BASE_API_PATH + "/account/login",
        data={'user_email': '<EMAIL>', 'user_password': 'pw123456'}
    )
    return req.json()['data']['access_token']


def create_aoi_pre_plan(file_path: Path, batch, strategy_type):
    """
    创建aoi预览任务
    :param file_path:
    :param batch:
    :param strategy_type: 63 方位核实, 68 关联合并
    :return:
    """
    url = upload_file(file_path)
    file_name = file_path.name
    strategy_upload_req = {
        "file_url": DE_OSS_BASE_PATH + url,
        "batch": batch,
        "strategy_type": strategy_type,  # aoi一体化
        "src": file_name,
        "src_type": 5,  # 这里只能是5
        "remove_dup": False
    }
    req = requests.post(
        url=PRE_BASE_API_PATH + "/plan/strategy/upload",
        data=json.dumps(strategy_upload_req),
        headers={'token': get_token()}
    )
    print(req.json())
    return req.json()


def create_human_handle_extra(main_bid, access_id_list, access_jp_map):
    """
    生成人工处理的工单信息
    :param main_bid: aoi的bid
    :param access_id_list: access_id列表
    :param access_jp_map: 竞品map
    :return:
    """
    if len(access_id_list) < 1:
        return None
    access_ids = access_id_list
    sql = "select *,st_astext(geom) as access_geom from blu_access where access_id in :access_ids"
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {'access_ids': tuple(access_ids)})
    extra_lists = []
    for x in ret:
        bind_node_id = x.node_id
        sql = "select node_id from blu_access_gate_rel where access_id=:access_od"
        gate_ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {'access_od': x.access_id})
        if len(gate_ret) < 1:
            continue
        may_nodes = [x.node_id for x in gate_ret]
        if bind_node_id in may_nodes:
            node_id = bind_node_id
        else:
            node_id = may_nodes[0]
        extra_lists.append({
            "trans_id": x.access_id,
            "mark_wkt": x.access_geom,
            "geom": x.access_geom,
            "access_id": x.access_id,
            "access_poi_bid": x.access_poi_bid,
            "node_id": node_id,
            'access_name': x.name,
            "gd_access_name": access_jp_map.get(x.access_id, '')
        })
    if len(extra_lists) < 1:
        return None
    return {
        "bid": main_bid,
        "aoi_src_data_extra": extra_lists,
    }


def create_jp_json(file_name, df):
    """
    生成json文件
    :param file_name:
    :param df:
    :return:
    """
    lines = []
    for bid, sub_df in tqdm.tqdm(df.groupby('main_bid'), desc='加载access_id数据'):
        access_jp_map = {}
        access_id_List = []
        for _, row in sub_df.iterrows():
            work_info = row['work_info']
            gd_access_name = work_info['gd_access_name']
            access_id = row['access_id']
            access_id_List.append(access_id)
            access_jp_map[access_id] = gd_access_name
        human_handle_extra = create_human_handle_extra(bid, access_id_List, access_jp_map)
        if human_handle_extra is None:
            continue
        lines.append(human_handle_extra)
    with open(file_name, 'w') as f:
        json.dump(lines, f, ensure_ascii=False, indent=2)
    return True


def run_jp():
    """
    例行化下发2.0任务, access_intelligence_new 的数据存储
    work_status=1 待下发
    work_status=2 下发完成
    :return:
    """
    file_name = work_dir / f'tmp_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}.json'
    sql = 'select batch_id from access_intelligence_new where work_status = 1 and type=:type limit 1'
    batch = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"type": 'access_name'})
    if not batch:
        print("无数据需要处理")
        return
    print(f'开始处理:{batch.batch_id}')
    source = batch.batch_id
    sql = 'select id, main_bid, work_info, batch_id, uuid as access_id ' \
          ' from access_intelligence_new ' \
          ' where batch_id=%(source)s and work_status  = 1 and type=%(type)s'
    df = pd.read_sql(sql, con=pgsql.get_engine(pgsql.ENGINE_POI_ONLINE),
                     params={'source': source, 'type': 'access_name'})
    id_list = df['id'].tolist()
    if create_jp_json(file_name, df) and file_name.exists():
        create_aoi_pre_plan(file_name, source, 80)
    sql = 'update access_intelligence_new set work_status=2 where id in :ids'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {'ids': tuple(id_list)})


if __name__ == '__main__':
    args = sys.argv
    if len(args) > 1:
        act = sys.argv[1]
        if act == 'run_jp':
            # 添加竞品任务
            run_jp()
    else:
        print('请输入参数')
