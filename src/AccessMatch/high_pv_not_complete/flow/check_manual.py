"""
检查是否人工核实
"""
import datetime
from src.common import mysql, pgsql
import pandas as pd
import tqdm
from src.door_naming.strategy_v2 import common

DOOR_ADDED = '新增挖掘任务下发完成'
GATE_MATCHED = '关联关系匹配完成'
IN_GATE_MATCH_MANUAL = '关联关系人工作业中'

WHITE_TYPE_V1 = 'v1'
WHITE_TYPE_V2 = 'v2'

from src.AccessMatch.high_pv_not_complete.flow import utils


def check_in_add_new(bid):
    """
    是否在人工新增的流程
    :return:
    """
    # 新增大门,判断是否下发过人工
    day_before = datetime.datetime.now() - datetime.timedelta(days=30)
    sql = 'select * from qb_gate_prepush_preprocess  where item_id=:bid and create_time > :create_time'
    ret = pgsql.query_one(pgsql.ENGINE_DEST_TRAJ_TO_AOI, sql, {"bid": bid, "create_time": day_before})
    if ret:
        return DOOR_ADDED
    return None


def check_in_v1_strategy(bid):
    """
    判断是否在关联关系的匹配策略中
    :param bid:
    :return:
    """
    day_before = datetime.datetime.now() - datetime.timedelta(days=30)
    sql = 'select bid from gate_match_intel_src where bid=:bid and update_time > :update_time '
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid": bid, "update_time": day_before})
    if ret:
        return GATE_MATCHED
    return None


def get_nav_pv(bid_list: list):
    """
    poi的pv
    :param bid_list:
    :return:
    """
    pv_dict = {}
    sql = 'select bid, pv from poi_nav_500m_pv where bid in :bid_list'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid_list": tuple(bid_list)})
    if ret:
        for row in ret:
            pv_dict[row.bid] = row.pv
    return pv_dict


def check_in_v1_manual(bid):
    """
    检测v1人工作业
    :param bid:
    :return:
    """
    day_before = datetime.datetime.now() - datetime.timedelta(days=30)
    sql = 'select bid,work_status, create_time, batch_id  from strategy_feature_list where bid=:bid ' \
          ' and strategy_type = 62 and work_status in  (1,2) ' \
          ' and create_time > :create_time and std_tag not in :std_tag '
    batch2 = ('交通设施', '交通设施;其他',
              '交通设施;飞机场', '交通设施;火车站',
              '交通设施;地铁站', '旅游景点',
              '旅游景点;其他', '旅游景点;公园',
              '旅游景点;动物园', '旅游景点;植物园'
              )
    ret = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql,
                          {"bid": bid, "std_tag": batch2, "create_time": day_before})
    if ret:
        return f"{IN_GATE_MATCH_MANUAL}, {ret.batch_id}"
    return ""


def check_common_by_bid(bid):
    """
    检查情报状态
    :param bid:
    :return:
    """
    add_gate = check_in_add_new(bid)
    v1_strategy = check_in_v1_strategy(bid)
    v1_manual = check_in_v1_manual(bid)
    return add_gate, v1_strategy, v1_manual


def run_common():
    """
    测试脚本
    :return:
    """
    file = 'common.tsv'
    df = pd.read_csv(file, converters={"main_bid": str})
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df)):
        main_bid = row['main_bid']
        df.loc[idx, 'add_gate'] = check_in_add_new(main_bid)
        df.loc[idx, 'v1_strategy'] = check_in_v1_strategy(main_bid)
        df.loc[idx, 'v1_manual'] = check_in_v1_manual(main_bid)
    df.to_csv("checked_" + file, sep='\t', index=False)


def check_common_can_complete(bid, aoi_level, auto_add_to_flow=False):
    """
    检查基础院落是否可闭环
    :param bid:
    :param aoi_level:
    :param auto_add_to_flow:
    :return:
    """
    if aoi_level != 2:
        raise Exception("check_common_can_complete 不支持aoi_level 非2的数据")
    # 当前aoi_complete >=3 直接闭环
    current_level = common.get_aoi_complete_level(bid)
    if current_level >= 3:
        return True
    # 在白名单的数据,直接闭环
    sql = 'select bid from aoi_whitelist where bid=:bid and type=:type'
    in_white = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, "type": WHITE_TYPE_V1})
    if in_white:
        return True
    # 生成挖掘任务,1.0批处理任务,并且无人工作业任务, 直接闭环
    # 查询是否在闭环流程里面
    sql = 'select id from aoi_white_list_flow where bid=:bid and is_door_add_complete=1 and is_door_batch_complete=1'
    flow_ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid})
    # 查询是否有人工作业的清单,如果没有直接闭环
    if not flow_ret:
        # 不在流程里面的,尝试添加
        if auto_add_to_flow:
            utils.add_to_flow(bid, aoi_level)
        return False
    else:
        if check_in_v1_manual(bid):
            return False
        else:
            return True


if __name__ == '__main__':
    run_common()
