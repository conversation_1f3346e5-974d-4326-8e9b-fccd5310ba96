<?php
$bid = $argv[1];
$city = $argv[2];
$file = $argv[3];
$error_output = "aoi_rate_error.info";
$username = "map";
$clusterIp = "************";
// $clusterIp = "cp01-map-ssd-2016-13.epc.baidu.com";
$clusterPort = "5432";
$defaultDb = "postgres";
// $defaultDb = "test";
$conn_string = "host={$clusterIp} port={$clusterPort} dbname={$defaultDb} user={$username}";


try{
    $dbconn = pg_connect($conn_string);
    if(!empty($bid)){
    	$sql = "select aoi_test.bid as bid1,blu_face_from_se.bid as bid2,blu_face_from_se.cp,ST_Area(ST_Intersection(aoi_test.geom,blu_face_from_se.geom))/ST_Area(aoi_test.geom) as rate,ST_Area(ST_Intersection(aoi_test.geom,blu_face_from_se.geom))/ST_Area(blu_face_from_se.geom) as rate2 from aoi_test,blu_face_from_se where aoi_test.bid='{$bid}' and aoi_test.city_name='$city' and blu_face_from_se.city_name='{$city}' and blu_face_from_se.cp not in ('WQT','CJH') and ST_Area(ST_Intersection(aoi_test.geom,blu_face_from_se.geom))/ST_Area(aoi_test.geom) BETWEEN 0.5 AND 1 and ST_Area(ST_Intersection(aoi_test.geom,blu_face_from_se.geom))/ST_Area(blu_face_from_se.geom) BETWEEN 0.5 AND 1";
        $result = pg_query($dbconn, $sql);
        
        if (pg_num_rows($result) == 0) {
            echo "[INFO] ".$city." 不存在压盖 bid:".$bid."\n";
        } else {
            echo "有".pg_num_rows($result)."条压盖信息\n";
            echo $bid . "压盖\n";
            $bid = $bid . "\n";
            file_put_contents($file,$bid ,FILE_APPEND);
        }
    } else{
        echo "bid is empty" . "\n";
    }
    pg_close($dbconn);
}catch(Exception $e){
    $err = '[WARNING] 数据库连接错误:'.$e->getMessage()."\n";
    file_put_contents($error_output, $err,FILE_APPEND);
    echo $err;
}
