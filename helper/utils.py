# !/usr/bin/env python3
"""
工具
"""
import itertools
import uuid
import requests
import subprocess
from helper.db.poi import BluAccessBidForYuYi
from helper.db.back import BluFaceComplete, BluFacePoi


def download_file(filename, _file_url):
    """
    文件下载
    Args:
        filename:
        _file_url:

    Returns:

    """
    r = requests.get(_file_url, stream=True)
    with open(filename, 'wb') as f:
        for chunk in r.iter_content(chunk_size=1024):
            f.write(chunk)
    return filename


def access_release(base_url, data):
    req = requests.post(
        base_url + "/api/accessRelease",
        data=data,
        timeout=10.0
    )
    return req


def parking_limit_release(token, base_url, data):
    req = requests.get(
        base_url + "/release/parkingLimitRelease",
        params=data,
        timeout=10.0,
        headers={'token': token}
    )
    return req


def poi_gate_release_by_mid(token, base_url, data):
    req = requests.post(
        base_url + "/release/poiGateReleaseByMid",
        data=data,
        timeout=10.0,
        headers={'token': token}
    )
    return req


def get_token(base_url):
    req = requests.post(
        url=base_url + "/account/login",
        data={'user_email': '<EMAIL>', 'user_password': 'pw123456'}
    )
    return req.json()['data']['access_token']


def release_poi_navi_std(token, base_url, data):
    req = requests.get(
        base_url + "/api/releasePoiNaviStd",
        params=data,
        timeout=10.0,
        headers={'token': token}
    )
    return req


def agg_release(base_url, bid, need_aggregated):
    url = base_url + '/api/trafficRelease'
    req = requests.get(url, params={'bid': bid, 'need_aggregated': need_aggregated})
    return req.json()


class UsefulTools:
    def __init__(self, **kwargs):
        if "poi_online_session" in kwargs:
            self.poi_online_session = kwargs['poi_online_session']
        if "master_back_session" in kwargs:
            self.master_back_session = kwargs['master_back_session']

    def add_bid_to_white_list(self, bid):
        """
        增加bid到2.0白名单
        Args:
            bid:

        Returns:

        """
        yuyi_record = self.poi_online_session.query(BluAccessBidForYuYi).filter_by(main_bid=bid).scalar()
        if yuyi_record:
            yuyi_record.status = 8
        else:
            self.poi_online_session.add(BluAccessBidForYuYi(main_bid=bid, status=8))

        blu_face_poi = self.master_back_session.query(BluFacePoi).filter_by(poi_bid=bid).first()
        if not blu_face_poi:
            return

        complete_record = self.master_back_session.query(BluFaceComplete).filter_by(main_bid=bid).scalar()
        if complete_record:
            complete_record.aoi_complete = 4
            complete_record.is_white = 1
            complete_record.version = "1.0"
        else:
            self.poi_online_session.add(BluFaceComplete(
                complete_info_id=uuid.uuid4().hex,
                main_bid=bid,
                face_id=blu_face_poi.face_id,
                aoi_complete=4,
                is_white=1,
                version="1.0"))
        self.poi_online_session.commit()
        self.master_back_session.commit()


def stream_batch_update(api_url, entity, body):
    """
    批量提交修改
    Returns:
    """
    req = requests.put(api_url + f"/{entity}", json=body)
    return req.json()


def send_hi(msg, atuserids=None, token="d7b137e827d9a9d3e18faef6dd8a7b3c1", group_id=None):
    """发送如流消息
    Args:
        msg：消息内容
        atuserids: at群的某个人
        token: 群机器人token
        group_id: 必须是int形式, 群号,如果为空则忽略
    """
    if atuserids is None:
        atuserids = ['zhangcong_cd']
    url = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=' + token
    # 如流消息的header信息
    header = {}
    # 如果的body信息
    if atuserids is not None and len(atuserids) != 0:
        body = [
            {
                "type": "TEXT",
                "content": msg
            }, {
                "atuserids": atuserids,
                "atall": False,
                "type": "AT"
            }
        ]
    else:
        body = [
            {
                "type": "TEXT",
                "content": msg
            }
        ]
    # 指定群号发送
    if group_id is not None:
        header['toid'] = [group_id]
    data = {
        "message": {
            "header": header,
            "body": body,
        }
    }
    requests.post(url, json=data)


def split_list_itertools(big_list, sublist_size=5000):
    """
    分割列表
    Args:
        big_list:
        sublist_size:

    Returns:

    """
    it = iter(big_list)
    while True:
        chunk = list(itertools.islice(it, sublist_size))
        if not chunk:
            break
        yield chunk


class AfsClient:
    """
    AfsClient类，用于与afs交互。
    """

    def __init__(self, afs_bin, username, password, prefix):
        """
        初始化类对象。

        Args:
            afs_bin (str): afs_bin的路径。
            username (str): 用户名。
            password (str): 密码。
            prefix (str): 前缀。

        Returns:
            None

        """
        self.afs_bin = afs_bin
        self.username = username
        self.password = password
        self.prefix = prefix

    @classmethod
    def _execute(cls, cmd):
        """
        执行命令
        Args:
            cmd:

        Returns:

        """
        subproc = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess_return = subproc.stdout.read()
        return subprocess_return

    def get(self, remote_file, local_file):
        """
        获取远程文件到本地
        Args:
            remote_file:
            local_file:

        Returns:

        """
        cmd = (
            f"{self.afs_bin} "
            f"--username={self.username} "
            f"--password={self.password} "
            f"get {self.prefix}{remote_file} {local_file}"
        )
        return self._execute(cmd)

    def put(self, local_file, remote_file):
        """
        上传本地文件到远程
        Args:
            local_file:
            remote_file:

        Returns:

        """
        cmd = (
            f"{self.afs_bin} "
            f"--username={self.username} "
            f"--password={self.password} "
            f"put {local_file} {self.prefix}/{remote_file}"
        )
        return self._execute(cmd)

    def ls(self, remote_dir):
        """
        列出远程目录下的所有文件
        Args:
            remote_dir:

        Returns:

        """
        cmd = (
            f"{self.afs_bin} "
            f"--username={self.username} "
            f"--password={self.password} "
            f"ls {self.prefix}/{remote_dir}"
        )
        return self._execute(cmd).decode().split('\n')
