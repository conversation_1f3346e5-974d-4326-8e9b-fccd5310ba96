#!/bin/bash

CUR_DIR=$(dirname "$0")
POI_NAVI_NAME_FILE="$CUR_DIR/poi_navi.name"
AFS_POI_NAVI_DIR="/home/<USER>/intelligence/poi_navi/data"
new_file_name=`ls $AFS_POI_NAVI_DIR | grep 'internet_qingbao_all_' | sort -rn | head -n 1`
old_file_name=`cat $POI_NAVI_NAME_FILE`

if [ $new_file_name == $old_file_name ];then
  echo "no new file"
  exit 0
fi
python $CUR_DIR/data_update.py $AFS_POI_NAVI_DIR/$new_file_name
