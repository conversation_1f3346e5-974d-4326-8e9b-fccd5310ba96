"""
自动下发任务
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())
from src.common import pgsql
import requests
import json
import pandas as pd
import datetime
import tqdm

work_dir = Path(os.path.abspath(__file__)).parent / 'v1_preview'
work_dir.mkdir(exist_ok=True, parents=True)

PRE_BASE_API_PATH = "https://mapde-poi.baidu-int.com/prod"
DE_OSS_BASE_PATH = "http://mapde-poi.baidu-int.com/beefs/get?uuid="
today = datetime.datetime.now().strftime('%Y%m%d')


def add_manual_wait(bid, node_id, source):
    """
    下发任务
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    if check_handled(bid, node_id, source):
        print(f"已处理:{bid}_{node_id}_{source}")
        return
    uid = f"{node_id}_{bid}_{today}"
    sql = 'insert into gate_aoi_match_check_data (bid, node_id, uid, source, ways, wait_create)' \
          ' values (:bid, :node_id, :uid, :source, :ways, :wait_create)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql,
                      {"bid": bid, "node_id": node_id, "uid": uid, "source": source,
                       "ways": '清查提准', "wait_create": 1})


def check_handled(bid, node_id, source):
    """
    检查是否下发过
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    sql = 'select id from gate_aoi_match_check_data where bid=:bid and node_id=:node_id and source=:source '
    return pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, 'node_id': node_id, "source": source})


def upload_file(filename):
    """
    上传文件
    :param filename:
    :return:
    """
    req = requests.post(
        url=PRE_BASE_API_PATH + "/compat/upload",
        files={"file": open(filename, "rb")}
    )
    return req.text


def get_token():
    """
    生成token
    :return:
    """
    req = requests.post(
        url=PRE_BASE_API_PATH + "/account/login",
        data={'user_email': '<EMAIL>', 'user_password': 'pw123456'}
    )
    return req.json()['data']['access_token']


def create_aoi_pre_plan(file_path: Path, batch):
    """
    创建aoi预览任务
    :param file_path:
    :param batch:
    :return:
    """
    url = upload_file(file_path)
    file_name = file_path.name
    strategy_upload_req = {
        "file_url": DE_OSS_BASE_PATH + url,
        "batch": batch,
        "strategy_type": 62,  # aoi一体化
        "src": file_name,
        "src_type": 3,  # 这里只能是3
        "remove_dup": False
    }
    req = requests.post(
        url=PRE_BASE_API_PATH + "/plan/strategy/upload",
        data=json.dumps(strategy_upload_req),
        headers={'token': get_token()}
    )
    print(req.json())
    return req.json()


def create_json(file_name, df):
    """
    生成json文件
    :param file_name:
    :param df:
    :return:
    """
    lines = []
    for bid, sub_df in tqdm.tqdm(df.groupby('bid'), desc='加载node数据'):
        aoi_src_data = []
        #  根据bid+node 去重
        bid_node_set = set()
        for index, row in sub_df.iterrows():
            uid = str(row['uid'])
            if uid == '':
                continue
            if len(uid.split('_')) != 3:
                continue
            node_id = str(row['node_id'])
            key = f'{bid}_{node_id}'
            if key in bid_node_set:
                continue
            sql = 'select st_astext(geom) as geom from nav_node where node_id=:node_id'
            ret = pgsql.query_one(pgsql.ENGINE_ROAD, sql, {'node_id': node_id})
            if not ret:
                continue
            node_wkt = ret.geom
            bid_node_set.add(key)
            aoi_src_data.append({
                "trans_id": uid,
                "mark_wkt": node_wkt,
            })
        if len(aoi_src_data) > 0:
            lines.append({
                "bid": bid,
                "aoi_src_data_extra": aoi_src_data,
            })
    if len(lines) == 0:
        return False
    with open(file_name, 'w') as f:
        json.dump(lines, f, ensure_ascii=False, indent=2)
    return True


def run():
    """
    例行化下发1.0任务, 基于gate_aoi_match_check_data 的数据存储
    :return:
    """
    file_name = work_dir / f'tmp_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}.json'
    sql = 'select source from gate_aoi_match_check_data where wait_create = 1 limit 1'
    batch = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql)
    if not batch:
        print("无数据需要处理")
        return
    print(f'开始处理:{batch.source}')
    source = batch.source
    sql = 'select id, bid, node_id, uid, source ' \
          ' from gate_aoi_match_check_data ' \
          ' where source=%(source)s and wait_create = 1'
    df = pd.read_sql(sql, con=pgsql.get_engine(pgsql.ENGINE_POI_ONLINE), params={'source': source})
    id_list = df['id'].tolist()
    if create_json(file_name, df) and file_name.exists():
        create_aoi_pre_plan(file_name, source)
    sql = 'update gate_aoi_match_check_data set wait_create=2 where id in :ids'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {'ids': tuple(id_list)})


if __name__ == '__main__':
    run()
