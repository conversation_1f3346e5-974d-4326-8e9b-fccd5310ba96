"""
检查是否人工核实
"""
import json

from src.common import mysql, pgsql
import pandas as pd
import tqdm
from src.door_naming.strategy_v2 import common
import dataclasses
from pathlib import Path
import os
from typing import Optional
from src.AccessMatch.high_pv_not_complete.flow import check_manual

MANUAL_TASK_UGC = 'ugc作业'
MANUAL_TASK_PLATFORM = '已下发平台作业'
MANUAL_TASK_NO_TASK = '需要作业但是未下发人工'
MANUAL_COMPLETE = '已经闭环了'

ugc_file = Path(os.path.abspath(__file__)).parent / 'ugc_gather_completed.txt'

with open(ugc_file) as f:
    ugc_gather_bids = [x.strip() for x in f]

SUCC_RATE1 = 75
SUCC_RATE2 = 70


@dataclasses.dataclass
class SubAoi:
    """
    子aoi
    """
    sub_face_id: str
    sub_poi: str
    sub_name: str
    sub_std_tag: str
    sub_area: float
    sub_aoi_complete: str
    sub_aoi_level: str
    sub_src: str
    sub_area_rate: float
    sub_manual_complete: bool = False


@dataclasses.dataclass
class Aoi:
    """
    aoi
    """
    bid: str
    face_id: str
    area: float
    name: str
    aoi_level: int
    std_tag: str
    main_city: str
    nav_pv: str
    aoi_complete: int
    src: str
    manual_task: str = ''
    manual_task_complete: bool = False
    sub_aoi_count: int = 0
    sub_aoi_common_count: int = 0
    same_tag_l1: int = 0
    same_tag_l2: int = 0
    tag_all_area_rate: float = 0
    tag_l1_area_rate: float = 0
    all_sub_aoi_complete: bool = False
    msg: str = ''
    aoi_can_complete_v1: bool = False
    sub_aoi: list[SubAoi] = dataclasses.field(default_factory=list)


def get_nav_pv(bid_list: list):
    """
    获取导航pv
    :param bid_list:
    :return:
    """
    pv_dict = {}
    sql = 'select bid, pv from poi_nav_500m_pv where bid in :bid_list'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid_list": tuple(bid_list)})
    if ret:
        for row in ret:
            pv_dict[row.bid] = row.pv
    return pv_dict


def in_gather_task(bid):
    """
    是否在聚合区域的任务中
    :param bid:
    :return:
    """
    sql = 'select id from strategy_feature_list where bid=:bid and strategy_type=37 and  work_status in (1,2,3)'
    ret = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, {"bid": bid})
    return ret is not None


def get_gather_common(bid, lines_all):
    """
    获取聚合院落下的基础院落,以及是否人工作业
    :param bid:
    :param lines_all:
    :return:
    """
    main_poi = common.get_poi_info_by_bid(bid)
    if main_poi:
        main_name = main_poi.name
        main_std_tag = main_poi.std_tag
        main_city = main_poi.city
    else:
        return 0
    pv_dict = get_nav_pv([bid])
    if bid in pv_dict:
        pv = pv_dict[bid]
    else:
        pv = -1
        # main_name = '无效poi'
        # main_std_tag = ''
        # main_city = ''
    sql = ' select a.face_id, a.area, c.aoi_complete from blu_face a' \
          ' inner join blu_face_poi b on a.face_id=b.face_id' \
          ' left join blu_face_complete c on b.poi_bid=c.main_bid where b.poi_bid=:bid  '
    ret = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"bid": bid})
    if not ret:
        return 0
    face_id = ret.face_id
    area = ret.area
    main_aoi_complete = ret.aoi_complete
    sql = 'select b.face_id, b.poi_bid, c.area, c.aoi_level from blu_aoi_rel a ' \
          ' inner join blu_face_poi b on a.face_id2=b.face_id ' \
          ' inner join blu_face c on b.face_id=c.face_id ' \
          ' where a.face_id1=:face_id and c.src != :src '
    sub_face = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"face_id": face_id, 'src': "SD"})

    main_tag_prefix = main_std_tag.split(";")[0]
    if not sub_face:
        lines_all.append({
            "bid": bid,
            "face_id": face_id,
            "area": area,
            "name": main_name,
            "std_tag": main_std_tag,
            "main_city": main_city,
            "main_aoi_complete": main_aoi_complete,
            '导航pv': pv,
            "msg": '无子区域',
            "是否人工作业": in_gather_task(bid),
        })
        return 0
    else:
        # 子区域面积计算只计算 一级tag相同的数据
        area_all = sum([x.area for x in sub_face])
        sub_rate = (area_all / area * 100.0)
        valid_area = 0
        for face in sub_face:
            sub_bid = face.poi_bid
            sub_poi = common.get_poi_info_by_bid(sub_bid)
            if sub_poi:
                sub_name = sub_poi.name
                sub_std_tag = sub_poi.std_tag
                if main_tag_prefix in sub_std_tag:
                    valid_area += face.area
            else:
                sub_name = '无效poi'
                sub_std_tag = ''

            lines_all.append({
                "bid": bid,
                "face_id": face_id,
                "area": area,
                "name": main_name,
                "std_tag": main_std_tag,
                "main_aoi_complete": main_aoi_complete,
                "main_city": main_city,
                '导航pv': pv,
                "msg": '',
                "是否人工作业": in_gather_task(bid),
                "子点面积占比: %": round(sub_rate, 4),
                "sub_face_id": face.face_id,
                "sub_poi:": face.poi_bid,
                "sub_name": sub_name,
                "sub_std_tag": sub_std_tag,
                "sub_area": face.area,
                "sub_aoi_level": face.aoi_level,
            })
        return valid_area / area * 100.0


def fill_manual_task(aoi: Aoi):
    """
    检查人工状态
    :param aoi:
    :return:
    """
    if aoi.aoi_complete and aoi.aoi_complete >= 3:
        aoi.manual_task = MANUAL_COMPLETE
        aoi.manual_task_complete = True
    elif aoi.bid in ugc_gather_bids:
        aoi.manual_task = MANUAL_TASK_UGC
        aoi.manual_task_complete = True
    else:
        date_start = '2025-02-01'
        sql = 'select bid, work_status, update_time from strategy_feature_list' \
              ' where strategy_type=37 and create_time >:ct1 and bid=:bid '
        manual_data = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, {"ct1": date_start, 'bid': aoi.bid})
        if manual_data:
            aoi.manual_task = MANUAL_TASK_PLATFORM
            if manual_data.work_status == 3:
                aoi.manual_task_complete = True


def aoi_can_complete_v1(aoi: Aoi):
    """
    判断aoi是否可满足1.0闭环

1、对于人工未维护的清单：
（1）无需边框人工核实场景：
    子区域面积相加（一级tag和聚合一致相加），相加后面积≥75%聚合院落的，分别下发关联关系和大门，闭环子区域。
    ①若子区域全闭环则聚合院落和关联的基础院落全闭环。
    ②若有子区域无法闭环，聚合院落转精准，已经精准的基础院落推sug，无法精准的基础院落闭环但不转精准也不推送（标记）。
（2）需边框人工核实场景：
    子区域面积相加（一级tag和聚合一致相加），相加后面积＜75%聚合院落的，下发聚合院落新增计划类型（37-聚合院落新增）先由边框环节维护。
    ①若人工做完后子区域面积相加≥75%（此时不判断tag，避免tag错误），按照边框环节已经闭环处理，下发关联的子区域基础院落给大门和关联关系作业，
    作业完后闭环。
        A、若子区域全闭环则聚合院落和关联的基础院落全闭环。
        B、若有子区域无法闭环，聚合院落转精准，已经精准的基础院落推sug，无法精准的基础院落闭环但不转精准也不推送（标记）。

    2. 若人工做完一轮后面积≤70%，单独回捞单独处理闭环。
    :param aoi:
    :return:
    """
    if aoi.aoi_level != 1:
        return
    if aoi.aoi_complete >= 3:
        aoi.aoi_can_complete_v1 = True
        aoi.msg = '历史: 已经转化为精准1.0'
        return
    if aoi.manual_task in [MANUAL_TASK_PLATFORM, MANUAL_TASK_UGC]:
        if len(aoi.sub_aoi) < 1:
            aoi.msg = '人工已核实,但是没有子区域'
            return
        # 人工已经做过
        if aoi.manual_task_complete:
            # 人工处理过,面积大于70%
            if aoi.tag_all_area_rate >= SUCC_RATE2:
                if aoi.all_sub_aoi_complete:
                    aoi.aoi_can_complete_v1 = True
                    aoi.msg = '人工已核实:子区域全部闭环,可自动转化为精准1.0'
                    return
                else:
                    aoi.msg = '人工已核实:不缺框,但是子区域还没闭环'
                    return
            else:
                aoi.msg = '人工已核实: 子域还不满足70%,疑似缺框'
                return
        else:
            aoi.msg = '聚合区域人工核实中'
            return
    else:
        # 人工没做过
        if len(aoi.sub_aoi) < 1:
            aoi.msg = '人工未核实: 没有子区域, 需下发人工'
            return
        if aoi.tag_all_area_rate >= SUCC_RATE1:
            if aoi.tag_l1_area_rate:
                aoi.aoi_can_complete_v1 = True
                aoi.msg = '人工未核实: 子区域已全部完成, 可自动转化1.0'
                return
            else:
                aoi.msg = '人工未核实: 不缺框,但是子区域还没闭环'
                return
        else:
            aoi.msg = '人工未核实: 子区域还不够70%,需要下发人工处理'
            return


def get_gather_common_detail(bid) -> Optional[Aoi]:
    """
    获取聚合院落下的基础院落,以及人工作业情况和闭环情况
    :param bid:
    :return: 返回当前处理的聚合院落和基础院落的检测详情, 是一个aoi的结构
    """
    main_poi = common.get_poi_info_by_bid(bid)
    if main_poi:
        main_name = main_poi.name
        main_std_tag = main_poi.std_tag
        main_city = main_poi.city
        if '旅游' in main_std_tag or '交通' in main_std_tag:
            return None
    else:
        return None
    pv_dict = get_nav_pv([bid])
    if bid in pv_dict:
        pv = pv_dict[bid]
    else:
        pv = -1
    sql = ' select a.face_id, a.area, c.aoi_complete, a.src, a.aoi_level from blu_face a' \
          ' inner join blu_face_poi b on a.face_id=b.face_id' \
          ' left join blu_face_complete c on b.poi_bid=c.main_bid where b.poi_bid=:bid  '
    ret = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"bid": bid})
    if not ret:
        return None
    face_id = ret.face_id
    area = ret.area
    # 获取子区域
    sql = 'select b.face_id, b.poi_bid, c.area, c.aoi_level, d.aoi_complete, c.src from blu_aoi_rel a ' \
          ' inner join blu_face_poi b on a.face_id2=b.face_id ' \
          ' inner join blu_face c on b.face_id=c.face_id ' \
          ' left join blu_face_complete d on d.main_bid=b.poi_bid ' \
          ' where a.face_id1=:face_id and c.src != :src '
    sub_face = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"face_id": face_id, 'src': "SD"})
    main_tag_prefix = main_std_tag.split(";")[0]
    # 子区域面积计算只计算 一级tag相同的数据
    valid_area = 0
    # 所有aoi面积
    all_sub_area = 0
    a = Aoi(
        bid=bid,
        face_id=face_id,
        area=area,
        name=main_name,
        std_tag=main_std_tag,
        main_city=main_city,
        aoi_complete=ret.aoi_complete,
        nav_pv=pv,
        sub_aoi_count=len(sub_face),
        src=ret.src,
        aoi_level=ret.aoi_level,
    )
    fill_manual_task(a)
    if len(sub_face) < 1:
        a.msg = '[无子aoi区域]'
    if ret.aoi_level != 1:
        a.msg = f'[已经非聚合区域,当前{ret.aoi_level}]'
    sub_face_set = set()

    for face in sub_face:
        # 根据face_id 去重, 发现好重复的
        sub_face_id = face.face_id
        if sub_face_id in sub_face_set:
            continue
        sub_face_set.add(sub_face_id)
        sub_bid = face.poi_bid
        sub_poi = common.get_poi_info_by_bid(sub_bid)
        if sub_poi:
            # 同tag的任务
            sub_name = sub_poi.name
            sub_std_tag = sub_poi.std_tag
            all_sub_area += face.area
            if main_tag_prefix in sub_std_tag:
                valid_area += face.area
                a.same_tag_l1 += 1
            if main_std_tag == sub_std_tag:
                a.same_tag_l2 += 1
        else:
            sub_name = '[poi失效]'
            sub_std_tag = '--'
        if face.aoi_level == 2:
            a.sub_aoi_common_count += 1
        sub_aoi = SubAoi(
            sub_face_id=face.face_id,
            sub_poi=face.poi_bid,
            sub_name=sub_name,
            sub_std_tag=sub_std_tag,
            sub_area=face.area,
            sub_aoi_complete=face.aoi_complete,
            sub_aoi_level=face.aoi_level,
            sub_src=face.src,
            sub_area_rate=round(face.area / area * 100, 4),
            sub_manual_complete=check_manual.check_common_can_complete(face.poi_bid, 2,
                                                                       True) if face.aoi_level == 2 else True
        )
        a.sub_aoi.append(sub_aoi)
    a.tag_l1_area_rate = round(valid_area / area * 100.0, 4)
    a.tag_all_area_rate = round(all_sub_area / area * 100.0, 4)
    a.all_sub_aoi_complete = len(a.sub_aoi) > 0 and len([x for x in a.sub_aoi if not x.sub_manual_complete]) < 1
    # 计算是否满足1.0的标准
    aoi_can_complete_v1(a)
    return a


def run_gather():
    """
    测试入口
    :return:
    """
    file = 'data_bids/house_bid_0424.txt'
    df = pd.read_csv(file, converters={"main_bid": str})
    df = df.head(1000)
    lines_all = []
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df)):
        main_bid = row['main_bid']
        aoi = get_gather_common_detail(main_bid)
        if not aoi:
            continue
        if len(aoi.sub_aoi) > 0:
            # 存在子区域.按照子区域输出
            for item in aoi.sub_aoi:
                aoi_dict = dataclasses.asdict(aoi)
                del aoi_dict['sub_aoi']
                tmp = dataclasses.asdict(item)
                aoi_dict.update(tmp)
                lines_all.append(aoi_dict)
        else:
            aoi_dict = dataclasses.asdict(aoi)
            del aoi_dict['sub_aoi']
            lines_all.append(aoi_dict)
    pd.DataFrame(lines_all).to_csv('data_bids/house_bid_0424_check.csv', index=False, sep='\t')


if __name__ == '__main__':
    run_gather()
