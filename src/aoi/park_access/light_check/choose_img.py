"""
轻核实选图 step 1
"""
from distutils.log import info
import sys
import os
import time
import hashlib 
import tqdm
import requests
import math
import json
import copy 
import heapq
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.light_check import context
from src.aoi.park_access.light_check import config
from src.aoi.park_access.light_check import common_func
from collections import defaultdict, deque
import logging as log 
log.basicConfig(level=log.ERROR, format='%(asctime)s %(filename)s[line:%(lineno)d] %(message)s')

light_status = config.light_status

common_config = {
    "max_img_num_single": 5,    # 单个轨迹召回图片数
    "min_img_num_single": 4,    # 单个轨迹最少召回图片数
    "max_img_num": 1,           # 最多轨迹召回图片数 有图就退出
}


def start_choose_img(ctx):
	"""
	开始选图
	"""
	pipe_list = pipeline.Pipeline(
		# 获取link_id和范围框
		init_link_info,
		# 获取轨迹
		get_tracks,
		# 保存到数据
		save_to_db,
	)
	pipe_list(ctx)



def find_nearest_high_level_link(links, node_id):
    # Step 1: 构建图和link字典
    link_dict = {}  # {link_id: {s_node, e_node, dir, kind, length}}
    graph = {}      # {node: [(neighbor, link_id, length)]}

    for link in links:
        l_id, s_node, e_node, direction, geom, kind = link
        points = []
        # 提取坐标部分（去掉 "LINESTRING(" 和 ")"）
        coordinates = geom[geom.find("(") + 1:geom.rfind(")")]
        # 按逗号分割多个坐标对
        coord_pairs = coordinates.split(",")
        # 解析每个坐标对
        for pair in coord_pairs:
            x, y = map(float, pair.split())
            points.append((x, y))
 
        length = wkt.loads(geom).length * 110000
       
        link_dict[l_id] = {
            's_node': s_node,
            'e_node': e_node,
            'dir': direction,
            'kind': kind,
            'length': length
        }
        
        # 根据方向添加边到图中
        if direction == 1:  # 双向
            graph.setdefault(s_node, []).append((e_node, l_id, length))
            graph.setdefault(e_node, []).append((s_node, l_id, length))
        elif direction == 2:  # s->e
            graph.setdefault(s_node, []).append((e_node, l_id, length))
        elif direction == 3:  # e->s
            graph.setdefault(e_node, []).append((s_node, l_id, length))

    # Step 2: 检查node_id是否直接位于高级道路上
    for l_id, link in link_dict.items():
        if (node_id == link['s_node'] or node_id == link['e_node']) and link['kind'] <= 7:
            return {
                'original_node_id': node_id,
                'high_level_link_id': l_id,
                'total_length': 0,
                'path_link_ids': []
            }

    # Step 3: Dijkstra算法搜索最短路径
    min_heap = [(0.0, node_id, [], set())]  # (累计长度, 当前节点, path_link_ids, visited_nodes)
    visited = set()  # 全局已访问节点，避免环路

    while min_heap:
        current_dist, current_node, path_link_ids, visited_nodes = heapq.heappop(min_heap)
        
        if current_node in visited:
            continue
        visited.add(current_node)

        # 检查当前节点的邻居
        for neighbor, link_id, length in graph.get(current_node, []):
            if neighbor in visited_nodes:
                continue  # 避免环路

            new_visited = set(visited_nodes)
            new_visited.add(current_node)
            new_path = list(path_link_ids)
            new_path.append(link_id)
            new_dist = current_dist + length

            # 检查邻居是否是高级道路
            link = link_dict[link_id]
            if link['kind'] <= 7:
                return {
                    'original_node_id': node_id,
                    'high_level_link_id': link_id,
                    'total_length': new_dist,
                    'path_link_ids': new_path
                }
            
            # 否则继续探索
            heapq.heappush(min_heap, (new_dist, neighbor, new_path, new_visited))

    return None  # 没有找到高级道路


def calculate_bearing(point_sp1, point_sp2):
    """
    计算从 point_sp1 到 point_sp2 的偏北角（0~360°）
    :param point_sp1: 起点，格式 (x1, y1)
    :param point_sp2: 终点，格式 (x2, y2)
    :return: 偏北角（单位：度）
    """
    x1, y1 = point_sp1.coords[0]
    x2, y2 = point_sp2.coords[0]
    dx = x2 - x1
    dy = y2 - y1
    # 计算角度（弧度），atan2(dx, dy) 表示从正北方向开始计算
    angle_rad = math.atan2(dx, dy)
    # 转换为角度并调整到 0~360 范围
    angle_deg = math.degrees(angle_rad)
    bearing = angle_deg if angle_deg >= 0 else angle_deg + 360.0
    return bearing


def cal_link_north_angle(ctx, buffer_meter=100):
    """
    计算link的偏航角
    long_link_id: 长linkid
    access_point_sp: 出入口坐标的Point对象
    return: 偏北角, msg
    """
    long_link_id = ctx.intelligence_info.link_id
    access_point_sp = wkt.loads(ctx.intelligence_info.node_geom)
    north_angle = None
    link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([long_link_id])
    link_geom = link_info[0][0]
    s_node = link_info[0][1]
    e_node = link_info[0][2]
    s_node_info = ctx.dao.road_dao.get_node_geom([s_node])
    e_node_info = ctx.dao.road_dao.get_node_geom([e_node])
    if not s_node_info or not e_node_info:
        return None, f'Node {s_node} or {e_node} does not exist.'

    link_all = ctx.dao.road_dao.get_nav_link_by_geom(wkt.loads(link_geom).buffer(buffer_meter / 110000).wkt, form=None, kind=None)
    # logger.info(len(link_all))
    s_node_res = find_nearest_high_level_link(link_all, s_node)
    e_node_res = find_nearest_high_level_link(link_all, e_node)

    if s_node_res is None and e_node_res is None:
        return north_angle, f'Link {long_link_id} does not connect to any high-level road.'

    s_node_sp = wkt.loads(s_node_info[0][0])
    e_node_sp = wkt.loads(e_node_info[0][0])
    if s_node_res['total_length'] == 0 and e_node_res['total_length'] == 0:
        # 都在高等级道路时，计算从远到近的偏航角
        s_node_point_dis = access_point_sp.distance(s_node_sp)
        e_node_point_dis = access_point_sp.distance(e_node_sp)
        if s_node_point_dis <= e_node_point_dis:
            north_angle = calculate_bearing(e_node_sp, s_node_sp)
        else:
            north_angle = calculate_bearing(s_node_sp, e_node_sp)
        return north_angle, f"link {long_link_id} on high-level"
    # 2个node都有到高等级道路的路径和距离，方向为距离近的到距离远的
    if s_node_res['total_length'] <= e_node_res['total_length']:
        # logger.info(f"进入方向为:{s_node} -> {e_node}")
        north_angle = calculate_bearing(s_node_sp, e_node_sp)
    else:
        # logger.info(f"进入方向为:{e_node} -> {s_node}")
        north_angle = calculate_bearing(e_node_sp, s_node_sp)
    return north_angle, f"link {long_link_id} not on high-level"

# # 数据库
# def get_nav_link_by_link_ids(self, link_id_list: list):
#     """
#     获取nav_link
#     :param link_id_list:
#     :return:
#     """
#     sql = f"select st_astext(geom),s_nid,e_nid,link_id,kind,len,dir from nav_link where link_id=ANY(%s)"
#     return self.queryall(sql, [link_id_list])

# def get_node_geom(self, node_ids: list):
#     """
#     获取Node的几何信息
#     Args:
#         node_ids:

#     Returns:

#     """
#     sql = "select st_astext(geom),node_id from nav_node where node_id=ANY(%s)"
#     return self.queryall(sql, [node_ids])

# def get_nav_link_by_geom(self, geom, form='52', kind=7):
#     """
#     获取指定范围内的道路大门L
#     :param geom:
#     :return:
#     dir 1 双向，2顺，3逆
#     """
#     args = [geom]
#     sql = f"""select link_id, s_nid, e_nid, dir, st_astext(geom), kind
#         from nav_link where st_intersects(geom,st_geomfromtext(%s, 4326))"""
#     if form:
#         sql += " and form = %s"
#         args.append(form)
#     if kind:
#         sql += " and kind >= %s"
#         args.append(kind)
#     nav_links = self.queryall(sql, args)
#     return nav_links


	
def save_to_db(ctx: context.Context, proceed):
    """
    入库
    """
    if len(ctx.intelligence_info.track_traj_list) > 1:
        # 按 字典time从小到大重新排序
        ctx.intelligence_info.track_traj_list = sorted(ctx.intelligence_info.track_traj_list, key=lambda d: d['time'])
    img_content = []
    if len(ctx.intelligence_info.track_traj_list) > 0:
        ctx.intelligence_info.result = 'success'
        ctx.intelligence_info.light_status = light_status['light_check_wait']
        ctx.intelligence_info.filter_reason = ''
        img_content = format_img(ctx.intelligence_info.track_traj_list)
        print("\n")
        print("target_access" + "\t" + str(ctx.intelligence_info.id) + "\t" + 
            str(ctx.parking_info.bid) + "\t" + str(ctx.parking_info.show_tag) + "\t" + 
            str(ctx.intelligence_info.link_id) + "\t" + str(ctx.intelligence_info.node_id) + "\t" + 
            ctx.intelligence_info.node_geom + "\t" + json.dumps(ctx.intelligence_info.track_traj_img_list) + "\t" + json.dumps(ctx.intelligence_info.track_traj_list))
        print("\n")
    else:
        if ctx.intelligence_info.filter_reason == '':
            ctx.intelligence_info.filter_reason = '无有效轨迹'
            ctx.intelligence_info.light_status = light_status['no_valid_track']
        if ctx.intelligence_info.result == '':
            ctx.intelligence_info.result = 'error'
        print("\n")
        print("filter_data" + "\t" + str(ctx.intelligence_info.id) + "\t" + 
            str(ctx.parking_info.bid) + "\t" + str(ctx.parking_info.show_tag) + "\t" + 
            str(ctx.intelligence_info.link_id) + "\t" + str(ctx.intelligence_info.node_id) + "\t" + 
            ctx.intelligence_info.node_geom + "\t" + ctx.intelligence_info.result + "\t" + 
            ctx.intelligence_info.filter_reason)

    data = {
        'intelligence_id': ctx.intelligence_info.id,
        'park_bid': ctx.intelligence_info.park_bid,
        'parent_bid': ctx.intelligence_info.parent_id,
        'node_geom': ctx.intelligence_info.node_geom,
        'long_node_id': ctx.intelligence_info.node_id,
        'long_link_id': ctx.intelligence_info.link_id,
        'status': ctx.intelligence_info.light_status,
        'msg': ctx.intelligence_info.filter_reason,
        'img_content': json.dumps(img_content),
        'type': ctx.intelligence_info.intelligence_type,
        'conclusion_txt': '',
        'conclusion': 0,
        'img_num': len(ctx.intelligence_info.track_traj_list)
    }

    old_light_check = ctx.dao.poi_online_query.get_light_check(ctx.intelligence_info.id)
    is_same = False
    if old_light_check:
        new_img_num = len(ctx.intelligence_info.track_traj_img_list)
        old_imgs = old_light_check.get('img_content')
        old_img_lists = [item['bos_key'] for item in old_imgs]
        logger.info(old_img_lists)
        if new_img_num > 0 and new_img_num == len(old_img_lists):
            is_same = True
            logger.info(ctx.intelligence_info.track_traj_img_list)
            for bos_key in ctx.intelligence_info.track_traj_img_list:
                if bos_key not in old_img_lists:
                    is_same = False
                    break
    # logger.info(is_same)
    # logger.info(old_light_check)
    # logger.info(ctx.intelligence_info.track_traj_img_list)
    if is_same:
        same_update_data = {'msg': '选图不变'}
        logger.info("和原来选图一样，无需更新")
        res = ctx.dao.poi_online_rw.update_park_access_light_check(same_update_data, condition={'id': ctx.intelligence_info.light_check_id})
        logger.info(res)
        return proceed()
    if ctx.intelligence_info.is_re_choose_img == 1:
        data['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        res = ctx.dao.poi_online_rw.update_park_access_light_check(data, condition={'id': ctx.intelligence_info.light_check_id})
        logger.info(res)
    else:
        res = ctx.dao.poi_online_rw.insert_light_check_list(data)
        logger.info(res)
    return proceed()


def format_img(track_traj_list):
    """
    格式化图片数据
    """
    temp_arr = []
    for track_traj in track_traj_list:
        temp_dict = {
            'bos_key': track_traj['bos_key'],
            'cdid': track_traj['cdid'],
            'time': track_traj['time'],
            'speed': track_traj['speed'],
            'x': track_traj['x'],
            'y': track_traj['y'],
        }
        temp_arr.append(temp_dict)
    return temp_arr



def get_tracks(ctx: context.Context, proceed):
    """
    获取众源轨迹
    """
    if ctx.intelligence_info.link_id == '':
        logger.info('link为空，暂不处理')
        ctx.intelligence_info.result = 'no_valid_link'
        ctx.intelligence_info.filter_reason = 'link为空，暂不处理'
        ctx.intelligence_info.light_status = light_status['no_valid_link']
        return proceed()

    if ctx.intelligence_info.link_north is None:
        logger.info(f"link:{ctx.intelligence_info.link_id},无法计算角度，暂不处理")
        ctx.intelligence_info.result = 'no_valid_link_north'
        ctx.intelligence_info.filter_reason = 'link角度未知，暂不处理'
        ctx.intelligence_info.light_status = light_status['no_valid_link_north']
        return proceed()

    # 量少去掉，后处理再做过滤
    # if ctx.aoi_info.wkt != '':
    #     park_name = ctx.parking_info.name
    #     if ctx.parking_info.parent_name != park_name.split("-", 1)[0]:
    #         if ctx.aoi_info.park_distance_boundary is None or ctx.aoi_info.park_distance_boundary < 20:
    #             logger.info('park到aoi距离小于20m')
    #             ctx.intelligence_info.result = 'not_normal_park'
    #             ctx.intelligence_info.filter_reason = '疑似非常规停车场'
    #             ctx.intelligence_info.light_status = light_status['not_normal_park']
    #             return proceed()
    check_geom(ctx)
    return proceed()


def init_link_info(ctx: context.Context, proceed):
    """
    初始化link信息
    """
    access_qb = ctx.intelligence_info
    # 只有坐标尝试找3m内的node
    if access_qb.link_id == '' and access_qb.node_id == '':
        access_qb.node_id = common_func.get_near_node(ctx, access_qb)
        logger.info(f"3m内找node:{access_qb.node_id}")

    # 有link检验link是否有效
    if access_qb.link_id != '':
        link_info = ctx.dao.road_dao.get_nav_link(link_ids=[access_qb.link_id])
        if link_info is None:
            access_qb.link_id == common_func.get_near_link(ctx, access_qb)
            logger.info(f"link失效5m内找link:{access_qb.link_id}")
    else:
        # 只有坐标且找不到node, 尝试找5m内的link
        if access_qb.node_id == '':
            access_qb.link_id = common_func.get_near_link(ctx, access_qb)
            logger.info(f"5m内找link:{access_qb.link_id}")

    # 只有node没有link,选link
    if access_qb.link_id == '' and access_qb.node_id !='':
        node_arr = [access_qb.node_id]
        links = ctx.dao.road_dao.get_nav_link(s_nodes=node_arr)
        if links:
            ctx.intelligence_info.link_id = links[0]['link_id']
    if ctx.intelligence_info.link_id == '':
        return proceed()
    link_info = ctx.dao.road_dao.get_nav_link(link_ids=[ctx.intelligence_info.link_id])
    if link_info is None:
        return proceed()
    ctx.intelligence_info.link_geom = link_info[0]['geom']
    ctx.intelligence_info.buffer_geom = wkt.loads(ctx.intelligence_info.link_geom).buffer(60 / 110000).wkt
    # 门的in_link的正北角
    link_sp = wkt.loads(ctx.intelligence_info.link_geom)


    ctx.intelligence_info.link_north, res = cal_link_north_angle(ctx)
    if ctx.intelligence_info.link_north is None:
        logger.info(f"link_north_100-fial:{res},try-one")
        ctx.intelligence_info.link_north, res = cal_link_north_angle(ctx, 300)
        if ctx.intelligence_info.link_north is None:
            logger.info(f"link_north_300-fial:{res}")
            ctx.intelligence_info.link_north, res = cal_link_north_angle(ctx, 500)
            if ctx.intelligence_info.link_north is None:
                logger.info(f"link_north_500-fial:{res}")
                return proceed()

    print(ctx.intelligence_info.intelligence_type)
    if ctx.intelligence_info.intelligence_type in ['park_access_overground_out_add', 'park_access_underground_out_add']:
        # 出口情报
        logger.info(f"出口情报，link_id：{ctx.intelligence_info.link_id}, old_link_north:{ctx.intelligence_info.link_north}")
        ctx.intelligence_info.link_north = (ctx.intelligence_info.link_north + 180) % 360
    logger.info(f"link_north-final:{ctx.intelligence_info.link_north}")
    # logger.info(res)
    # exit(111)
    # link_north = calculate_bearing(Point(link_sp.coords[0]), Point(link_sp.coords[-1])) 
    # ctx.intelligence_info.link_north = link_north

    # 获取link_id的近邻link
    node_arr = [link_info[0]['s_nid'], link_info[0]['e_nid']]
    near_link_info1 = ctx.dao.road_dao.get_nav_link(s_nodes=node_arr)
    near_link_info2 = ctx.dao.road_dao.get_nav_link(e_nodes=node_arr)
    link_near_list = []
    for link in near_link_info1:
        link_near_list.append(link['link_id'])
    for link in near_link_info2:
        if link['link_id'] not in link_near_list:
            link_near_list.append(link['link_id'])
    ctx.intelligence_info.link_near_list = link_near_list
    return proceed()

# def calculate_bearing(point1, point2):
#     """ 
#     计算正北角
#     """
#     lon1, lat1 = point1.x, point1.y
#     lon2, lat2 = point2.x, point2.y 
#     # 将纬度和经度从度数转换为弧度
#     lat1 = math.radians(lat1)
#     lon1 = math.radians(lon1)
#     lat2 = math.radians(lat2)
#     lon2 = math.radians(lon2)
#     # 计算经度差
#     d_lon = lon2 - lon1
#     # 计算方位角
#     x = math.sin(d_lon) * math.cos(lat2)
#     y = math.cos(lat1) * math.sin(lat2) - (math.sin(lat1) * math.cos(lat2) * math.cos(d_lon))
#     initial_bearing = math.atan2(x, y)
#     # 将方位角转换为 0 到 360 度
#     initial_bearing = math.degrees(initial_bearing)
#     logger.info(f"initial_bearing:{initial_bearing}")
#     compass_bearing = (initial_bearing + 360) % 360
#     return compass_bearing


def check_geom(ctx):
    """
    check_geom
    """
    geom = wkt.loads(ctx.intelligence_info.buffer_geom)
    t_time = int(time.time())
    months = 12
    # 1.2 查询源: 指定6个源  众源区域更新  众源高频
    track_source_list = ['vnet', 'varea', 'vmonitor', 'point', 'qblutao', 'qbother', 'qbpoi', 'qbrobot'] 
    # track_source_list = ['varea', 'vmonitor', 'point', 'qbother', 'qbpoi', 'qbrobot'] 
    # 2.查询
    t_time = int(time.time())
    # 最长12个月 
    for i in range(months):
        s_time = t_time - 3600 * 24 * 30 * (i+1)
        e_time = t_time - 3600 * 24 * 30  * i 
        time_box = [s_time, e_time]
        for source in track_source_list:
            # print(f"source:{source}\tmonth:{i},\ttime_box:{time_box}")
            if len(ctx.intelligence_info.track_traj_list) >= common_config['min_img_num_single']: 
                logger.info(f"有效图片超过4张退出循环-inner")
                break
            # 获取采集图片信息 
            tracks = get_tracks_by_geom(source, geom, time_box)
            # 对采集图片进行过滤 
            filter_track_pics(ctx, tracks, months)
        if len(ctx.intelligence_info.track_traj_list) >= common_config['min_img_num_single']:  
            logger.info(f"有效图片超过4张退出循环-outer")
            break
    # logger.info(ctx.intelligence_info.track_traj_list)


def get_tracks_by_geom(source, polygon, time_box):
    """查询指定geo范围内 指定时间范围内，指定源的采集轨迹信息
    """

    time.sleep(0.03)
    # url 拼接
    left, bottom, right, top = polygon.bounds 
    s_time, e_time = time_box 
    
    appkey = 'zhongye_mark'
    sc = 'ecf4f403b1a0f6b61314190d6794277d'
    sc_str = '%sappkey%sbottom%fetime%sleft%fright%fsource%sstime%stop%ftype%s%s' % \
             (sc, appkey, bottom, e_time, left, right, source, s_time, top, 10, sc) 
    sign = hashlib.md5(sc_str.encode('utf-8')).hexdigest() 
    track_url_v2 = 'http://m.map.baidu.com:8014/api/v2/track'

    url = track_url_v2 + '?left=%f&right=%f&top=%f&bottom=%f&' \
          'appkey=%s&source=%s&stime=%s&etime=%s&sign=%s&type=10' % \
          (left, right, top, bottom, appkey, source, s_time, e_time, sign)
    log.info("url: {}".format(url))
    res = http_get(url) 
    if res is None:
        return  
    return res.get('tracks')


def http_get(url, try_cnt=3, spl_t=0.5):
    """get 请求
    """
    time.sleep(0.05)
    while try_cnt > 0:
        try:
            response = requests.get(url, timeout=5000)
            res = response.json()
            if res.get('errno') == 0:
                return res 
        except Exception as e:
            #log.error("HTTP GET request failed: {} \n{}".format(e, url))
            time.sleep(spl_t)
            print(f"http_get_error:{e}")
        try_cnt -= 1
        if try_cnt <= 0:
            print(f"HTTP GET request failed: {url}")
    return None


def filter_track_pics(ctx, tracks, month):
    """ 过滤轨迹图片 
    {track_id: {"ra": {"image_list": [], "pic_list": [], cd_id_list: []}, "vof": {同ra}}
    需要记录下每个bos_key 对应的车天  
    需要返回该事件区间内的轨迹形态数据 
    ### 未绑在门进入退出link 上的轨迹条数 
    ### 绑在门上的轨迹条数   用于控制时间区间长短 
    """

    # 按track_id 进行分组
    grouped_data = defaultdict(list)
    if tracks is None or len(tracks) == 0:
        return []
    

    bos_key_set = set()
    for track in tracks:
        # logger.info(track.get('bos_key'))
        bos_key = track.get('bos_key')
        if bos_key in bos_key_set:
            # logger.info(f"重复的bos_key:{bos_key}")
            continue
        bos_key_set.add(bos_key)
        bos_key_arr =bos_key.split("_")
        track_id = ''
        if len(bos_key_arr) == 3: 
            track_id = bos_key_arr[1]
        # elif len(bos_key_arr) == 4: 
            # P_lxt 开头的不是一个轨迹过滤掉 不能聚合
            # track_id = bos_key_arr[2]
        else:
            # log.error("bos_key format error: {}".format(bos_key_arr))
            continue
        grouped_data[track_id].append(track)

    # 每个track_id 按时间进行排序 
    tmp_grouped_data = copy.deepcopy(grouped_data)
    for track_id, track_list in tmp_grouped_data.items():
        sorted_track_list = sorted(track_list, key=lambda x: datetime.strptime(x.get('time'), '%Y-%m-%d %H:%M:%S').timestamp())
        grouped_data[track_id] = sorted_track_list 


    # 轨迹过滤
    res = {} 
    
    # 形态统计 
    pass_cnt, forward_cnt = 0, 0 

    node_point_sp = wkt.loads(ctx.intelligence_info.node_geom)
    link_near_list = ctx.intelligence_info.link_near_list
    # aoi_geom = wkt.loads(ctx.aoi_info.wkt)

    for track_id, track_list in grouped_data.items():
        try:
        # if True:
            # 剔除out方向的采集结果 # in / out /unkonow 
            # track_dir = get_track_dir(track_list, node_point_sp, aoi_geom)
            # if track_dir == 'out':
            #     log.warning("track_id: {} 不是in方向".format(track_id)) 
            #     continue  

            # 计算图片点与门的距离 同时记录每个点的前后boskey
            # 记录boskey 前后的boskey
            track_dict =  {}
            for idx, track in enumerate(track_list):
                bos_key = track.get('bos_key')
                if bos_key not in track_dict:
                    track_dict[bos_key] = {}
                # if bos_key == 'P_090201210700013212_1727685016522':
                #     logger.info(track_list[idx-1])
                #     logger.info(track_list[idx+1])
                track_dict[bos_key]['pre'] = track_list[idx-1] if idx > 0 else None
                track_dict[bos_key]['next'] = track_list[idx+1] if idx < len(track_list)-1 else None
                track_point = Point(float(track.get('x')), float(track.get('y'))) 
                track['node_dis'] = node_point_sp.distance(track_point) * 100000

            # logger.info(track_list)
            # logger.info(track_dict)

            # # 判断轨迹形态
            # track_type = get_track_type(track_list, node_point_sp)
            # if track_type == 'pass':
            #     pass_cnt += 1
            # if track_type == 'forward': 
            #     forward_cnt += 1
            track_list = sorted(track_list, key=lambda x: (x.get('node_dis'))) 
            # 同一条轨迹中最多保留5张
            # 剔除夜间图片 
            # 剔除速度过快的照片 > 10km/h  
            # ra 车天的如何过滤？
            temp_track_traj_list = []
            temp_track_traj_img_list = []
            cnt = 0 
            for i, track in enumerate(track_list):
                track_time = datetime.strptime(track.get('time'), '%Y-%m-%d %H:%M:%S')
                if track_time.hour < 6 or track_time.hour > 20:
                    # logger.info(f"夜图过滤:{track_time.hour},{track.get('bos_key')}")
                    continue  
                if track.get('speed') > 10:
                    # logger.info(f"超速过滤:{track.get('speed')},{track.get('bos_key')}")
                    continue 
                if track.get('node_dis') > 50:
                    # logger.info(f"过远过滤:{track.get('node_dis')},{track.get('bos_key')}")
                    continue
                if track.get('link_id') not in link_near_list:
                    # logger.info(f"非有效link过滤:{track.get('link_id')},{link_near_list},{track.get('bos_key')}")
                    continue
                angle_diff = abs(track.get('north') - ctx.intelligence_info.link_north)
                if angle_diff > 35:
                    # logger.info(f"角度偏离大过滤:{track.get('north')},{ctx.intelligence_info.link_north},{track.get('bos_key')}")
                    # 非平行的过滤掉
                    continue
                if track.get('bos_key') in temp_track_traj_img_list:
                    continue
                cnt = cnt + 1
                temp_track_traj_list.append(track)
                temp_track_traj_img_list.append(track.get('bos_key'))
                # print(f"cnt：{cnt}, temp_track_traj_list:{temp_track_traj_list}")
                if cnt >= common_config['max_img_num_single'] - 1:
                    # 单个轨迹超过退出
                    print(f"cnt：{cnt}, 单个轨迹超过退出")
                    break
            # 单条轨迹少,获取前后的轨迹
            if len(temp_track_traj_list) > 0:
                temp_track_traj_list = sorted(temp_track_traj_list, key=lambda d: d['time'])
                # logger.info(temp_track_traj_list)
                if cnt < common_config['max_img_num_single']:
                    # 获取前后的轨迹 第一次外扩
                    # logger.info(track_dict)
                    expent_img_list(track_dict, temp_track_traj_list, temp_track_traj_img_list)
                    # logger.info(len(temp_track_traj_list))
                    # logger.info(f"第一次外扩,cnt:{cnt},max_img_num_single:{common_config['max_img_num_single']}")
                    # logger.info(temp_track_traj_list)
                if len(temp_track_traj_list) < common_config['max_img_num_single']:
                    # 获取前后的轨迹 第二次外扩  数量不够时
                    expent_img_list(track_dict, temp_track_traj_list, temp_track_traj_img_list)
                    # logger.info(len(temp_track_traj_list))
                    # logger.info(f"第二次外扩,cnt:{len(temp_track_traj_list)},max_img_num_single:{common_config['max_img_num_single']}")
                    # logger.info(temp_track_traj_list)
                if len(temp_track_traj_list) < 4:
                    # 获取前后的轨迹 第三次外扩，扩2次都少于4张，再扩一次
                    expent_img_list(track_dict, temp_track_traj_list, temp_track_traj_img_list)
                    # logger.info(len(temp_track_traj_list))
                    # logger.info(f"第三次外扩,cnt:{len(temp_track_traj_list)},max_img_num_single:{common_config['max_img_num_single']}")
                    # logger.info(temp_track_traj_list)
                # 第一次找到，或者后面找到的图片数量多就替换掉前面的
                if len(ctx.intelligence_info.track_traj_list) == 0 or \
                    len(temp_track_traj_list) > len(ctx.intelligence_info.track_traj_list):
                    # logger.info("final  temp_track_traj_list")
                    # logger.info(temp_track_traj_list)
                    ctx.intelligence_info.track_traj_list = temp_track_traj_list
                    ctx.intelligence_info.track_traj_img_list = temp_track_traj_img_list
            if len(ctx.intelligence_info.track_traj_list) >= common_config['min_img_num_single']:
                # 总数量超过退出最小满足的量就退出
                break
        except Exception as e:
            log.error("track_id: {} filter_track_pics error: {}".format(track_id, e)) 
            continue 
    return res, pass_cnt, forward_cnt 


def expent_img_list(track_dict, temp_track_traj_list, temp_track_traj_img_list):
    """
    扩充选取前后的图片
    """
    bos_key_start = temp_track_traj_list[0]['bos_key']
    if track_dict[bos_key_start]['pre'] is not None:
        pre_track = track_dict[bos_key_start]['pre']
        if pre_track.get('bos_key') not in temp_track_traj_img_list:
            temp_track_traj_list.insert(0, pre_track)
            temp_track_traj_img_list.append(pre_track.get('bos_key'))
    bos_key_end = temp_track_traj_list[-1]['bos_key']
    if track_dict[bos_key_end]['next'] is not None:
        next_track = track_dict[bos_key_end]['next']
        if next_track.get('bos_key') not in temp_track_traj_img_list:
            temp_track_traj_list.append(next_track)
            temp_track_traj_img_list.append(next_track.get('bos_key'))


def get_track_type(track_list, node_info):
    """ 获取轨迹形态 (link_id 不能表征)
    1. 门外主路轨迹 pass(经过轨迹)
    2. 门前轨迹 (forward)(轨迹上距离门最近的两个点的正北角和门的in_link的正北角角度差在30度以内)
    """
    
    if track_list is None or len(track_list) == 0:
        return 'unknow'

    # 门的in_link的正北角
    inlink_geom = node_info[0].get('inlink_geom') #  LINESTRING(114.9466342 26.8498369,114.9455858 26.8497788) 
    inlink_geom = inlink_geom.replace("LINESTRING(", "").replace(")", "")
    inlink_geom = inlink_geom.split(",")
    inlink_points = [Point(float(elem.split(" ")[0]), float(elem.split(" ")[1])) for elem in inlink_geom]
    inlink_angle = calculate_bearing(inlink_points[0], inlink_points[1]) 

    
    # 轨迹正北角, 只有一个点时使用自带的正北角, 否则计算距离门最近的两个点的正北角
    track_geom = LineString([[elem['x'], elem['y']] for elem in track_list]) if len(track_list) > 1 else Point(float(track_list[0].get('x')), float(track_list[0].get('y')))  
    if len(track_list) == 1:
        track_angle = track_list[0].get('north')
    else:
        track_list = sorted(track_list, key=lambda x: x['node_dis'])
        track_points = track_list[0:2]
        track_points = [Point(float(elem.get('x')), float(elem.get('y'))) for elem in track_points]
        track_angle = calculate_bearing(track_points[0], track_points[1])  

    # 角度差
    angle_diff = abs(track_angle - inlink_angle)

    if angle_diff <= 30 or (angle_diff > 150 and angle_diff < 210):
        return 'forward'
    else:
        return 'pass'



def get_track_dir(track_list, node_point_sp, aoi_geom):
    """获取轨迹方向
    in: 进入aoi 
    out: 离开aoi 
    unknow: 未知方向 

    aoi内的轨迹:通过计算门到轨迹起终点的距离差来判断 进入/推出
    aoi相交的轨迹: 判断起终点是否在aoi内
    """
    # aoi 边框
    aoi_boundary = aoi_geom.exterior

    # 计算门到aoi 距离最近的点 
    nearest_point = aoi_boundary.interpolate(aoi_boundary.project(node_point_sp)).coords.xy 
    nearest_point = Point(nearest_point[0][0], nearest_point[1][0])

    # 采集轨迹 
    if len(track_list) == 1:
        # 轨迹只有一个点 
        return 'unknow' 
    
    track_geom = LineString([[elem['x'], elem['y']] for elem in track_list])
    
    s_point0 = Point(float(track_list[0].get('x')), float(track_list[0].get('y')))
    s_point1 = Point(float(track_list[1].get('x')), float(track_list[1].get('y'))) 

    e_point0 = Point(float(track_list[-2].get('x')), float(track_list[-2].get('y')))
    e_point1 = Point(float(track_list[-1].get('x')), float(track_list[-1].get('y')))

    if track_geom.within(aoi_geom):
        start_diff = (s_point1.distance(nearest_point) - s_point0.distance(nearest_point))
        end_diff = (e_point1.distance(nearest_point) - e_point0.distance(nearest_point))

        if start_diff > 0 and end_diff > 0:
            return 'in'
        elif start_diff <= 0 and end_diff <= 0:
            return 'out'
        else:
            return 'unknow' 
    else:
        if not aoi_geom.contains(s_point0) and aoi_geom.contains(e_point1):
            return 'in'
        elif aoi_geom.contains(s_point0) and not aoi_geom.contains(e_point1):
            return 'out'
        else:
            return 'unknow'


def check_is_exit_light(ctx, id):
    """
    检查是否已存在
    """
    light_check = ctx.dao.poi_online_query.get_light_check(intelligence_id=id)
    return light_check


def main():
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    bids_arr = None

    bid_file = sys.argv[1]
    bid = sys.argv[2]
    if bid != '0':
        bids_arr = [bid]
    if bid_file != '0':
        with open(bid_file, "r") as f:
            bids_arr = f.read().replace("\n", ",").strip(",").split(",")
    limit = None
    qb_lists = poi_online_query.get_access_add_intelligence(bids=bids_arr, 
        # intelligence_type=['park_access_overground_add', 'park_access_underground_add', 'park_access_overground_out_add']
        intelligence_type=['park_access_overground_add', 'park_access_overground_out_add', 'park_access_underground_add'],
        # id=3833587,
        limit=limit)
    if not qb_lists:
        logger.error("No qb_lists found")
        return
    logger.info(f"新增情报{len(qb_lists)}")
    for access_qb in tqdm.tqdm(qb_lists):
        # qb_id = access_qb['id']
        # logger.error(f"start-----\t{access_qb}")
        # parking_list = poi_online_query.get_parking_list(bids=[access_qb['bid']], 
        #     show_tags=['地上停车场', '立体停车场', '停车场', '地下停车场'])
        # if not parking_list:
        #     logger.error(f"No parking found\t{access_qb}")
        #     continue
        # ctx = context.Context(parking_list[0], access_qb)
        # light_check = check_is_exit_light(ctx, qb_id)
        # if light_check and len(light_check) > 1:
        #     ctx.intelligence_info.is_re_choose_img = 1
        #     ctx.intelligence_info.light_check_id = int(light_check['id'])
        #     # recall_status = [light_status['init'], light_status['no_valid_link'], \
        #     #     light_status['no_valid_link_north'], \
        #     #     light_status['not_normal_park'], light_status['no_valid_track']]
        #     # if light_check['status'] not in recall_status:
        #     #     logger.info(f"already exist in light list\t{qb_id}")
        #     #     continue
        # start_choose_img(ctx)
        # exit(1)
        try:
            qb_id = access_qb['id']
            logger.error(f"start-----\t{access_qb}")
            parking_list = poi_online_query.get_parking_list(bids=[access_qb['bid']], 
                show_tags=['地上停车场', '立体停车场', '停车场'])
            if not parking_list:
                logger.error(f"No parking found\t{access_qb}")
                continue
            ctx = context.Context(parking_list[0], access_qb)
            light_check = check_is_exit_light(ctx, qb_id)
            if light_check and len(light_check) > 1:
                continue
                ctx.intelligence_info.is_re_choose_img = 1
                ctx.intelligence_info.light_check_id = int(light_check['id'])
                # 可重选图的状态
                recall_status = [
                    light_status['init'], 
                    light_status['no_valid_link'], 
                    light_status['not_normal_park'], 
                    light_status['no_valid_track'], 
                    # light_status['light_check_synced'],
                    light_status['wait_rechoose_img'] 
                    ]
                if light_check['status'] not in recall_status:
                    logger.info(f"already exist in light list\t{qb_id}")
                    continue
            start_choose_img(ctx)
        except Exception as e:
            #log.error("HTTP GET request failed: {} \n{}".format(e, url))
            logger.info(f"access_qb start-error:{e}")
            logger.info(access_qb)



if __name__ == '__main__':
	main()
# aq_py3 choose_img.py 0 0 
