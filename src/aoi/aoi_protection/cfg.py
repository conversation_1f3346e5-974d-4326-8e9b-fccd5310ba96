"""
一些常规的配置
"""
# 测试环境手动改pre
# ENV = "pre"
# 上线后改成prod
ENV = "prod"

# hi推送的数据
GROUP_TOKEN = 'd15c6ca35bb6a3d794954e99685478fd3'
GROUP_ID = 10514116

PRE_BASE_API_PATH = "http://mapde-poi.baidu-int.com/pre"
PROD_PRE_BASE_API_PATH = "http://mapde-poi.baidu-int.com/prod"
DE_OSS_BASE_PATH = "http://mapde-poi.baidu-int.com/beefs/get?uuid="

BASE_API_PATH = PRE_BASE_API_PATH if ENV == 'pre' else PROD_PRE_BASE_API_PATH

HIGH_LEVEL_TAG = (
    'basic.ganyu',
    'basic.security',
    'layer.vip_ganyu',
    'jinji.ganyu',
    'dataop.photo',
    'wanneng_touchuan.mingdeng_bianji',
    'dataop.bianji_ugc_xiugai_auto',
    'dataop.bianji_ugc_shangxian_auto',
    'dataop.ugc_phone_sh',
    'dataop.bianji_ugc_delete_auto',
    'dataop.bianji_ugc_new_auto',
    'dataop.ugc_tag_auto',
    'dataop.bianji_ugc_revise_auto',
    'dataop.ugc_phone_auto',
    'dataop.bianji_ugc_xiaxian_auto',
    'dataop.ugc_dianhua_auto',
    'dataop.bianji_ugc_revise',
    'dataop.bgc_tag',
    'dataop.ugc_phone_bai',
    'dataop.bianji_ugc_revise_light',
    'dataop.bianji_ugc_delete',
    'dataop.bianji_ugc_new_light',
    'dataop.bianji_ugc_new',
    'dataop.ugc_tag',
    'lbc.brand_normal_bid',
    'dataop.bianji_ugc_revise_important',
    'dataop.bianji_ugc_new_important',
    'dataop.bianji_ugc_del_important',
    'dataop.ugc_phone_rg',
    'special.bianji_ugc_new_special',
    'special.bianji_ugc_delete_special',
    'special.bianji_ugc_revise_special',
    'lbc.brand_normal',
    'dataop.bianji_ugc_delete_light',
    'dataop.bianji_ugc_new_direct',
    'dataop.bianji_ugc_delete_direct',
    'dataop.bianji_ugc_revise_direct',
    'lbc.new_biaozhu_bid',
    'lbc.new_biaozhu',
)

PARK_TAG = [
    "aoi_boundary_tag_change",
    "parking.attribute_integrate",
    "chuilei_scope.rg",
    # 新增新能源车企来源数据自动下线
    'zt_car.xnycheqi_car',
]

#  白名单的tag,直接放行
WHITE_TAG = PARK_TAG

MANUAL_HIGH = 310  # 高优处理
MANUAL_COMMON = 311  # 低优处理

WORK_TYPE_STRATEGY = 1  # 策略处理
WORK_TYPE_MANUAL = 2  # 人工处理
WORK_TYPE_REPEAT = 3  # 策略判重

HANDLE_STATUS_DEFAULT = 0  # 默认
HANDLE_STATUS_STRATEGY = 1  # 策略处理中
HANDLE_STATUS_WAIT_PREVIEW = 2  # 等待创建计划
HANDLE_STATUS_WAIT_HUMAN_HANDLE = 3  # 等待人工作业
HANDLE_STATUS_SUCCESS = 99  # 处理完成
HANDLE_STATUS_FAIL = -1

# 策略自动化处理的相关操作, 这部分代码在 aoi-ml代码库: src/aoi_protect_strategy/auto_aoi_protect_handle.py
STRATEGY_REPEAT_CHANGE_MAIN_BID = 'repeat_change_main_bid'  # 换绑主点
STRATEGY_B_TO_C = 'repeat_b_to_c'  # 策略b转c
STRATEGY_NOTHING = 'nothing'  # 无需处理

CAN_OFFLINE = 1  # 可下线
CANNOT_OFFLINE = 2  # 不可下线

# 推送状态
CALLBACK_STATUS_DEFAULT = 0  # 未处理
CALLBACK_STATUS_PUSHED = 1  # 推送完成
CALLBACK_STATUS_CHECKED = 2  # 推送检查完成
CALLBACK_STATUS_FAILED = -1  # 推送失败, 需要重新推

# 可建边框的垂类
ENABLE_TAGS = [
    '旅游景点',
    '旅游景点;博物馆',
    '旅游景点;动物园',
    '旅游景点;风景区',
    '旅游景点;公园',
    '旅游景点;海滨浴场',
    '旅游景点;教堂',
    '旅游景点;景点',
    '旅游景点;其他',
    '旅游景点;水族馆',
    '旅游景点;寺庙',
    '旅游景点;文物古迹',
    '旅游景点;游乐园',
    '旅游景点;植物园',
    '交通设施',
    '交通设施;飞机场',
    '交通设施;服务区',
    '交通设施;港口',
    '交通设施;火车站',
    '交通设施;加油加气站',
    '交通设施;长途汽车站',
    '购物',
    '购物;百货商场',
    '购物;超市',
    '购物;购物中心',
    '购物;家居建材',
    '购物;其他',
    '购物;市场',
    '教育培训',
    '教育培训;成人教育',
    '教育培训;高等院校',
    '教育培训;科技馆',
    '教育培训;科研机构',
    '教育培训;培训机构',
    '教育培训;其他',
    '教育培训;特殊教育学校',
    '教育培训;图书馆',
    '教育培训;小学',
    '教育培训;幼儿园',
    '教育培训;中学',
    '医疗',
    '医疗;疗养院',
    '医疗;其他',
    '医疗;专科医院',
    '医疗;综合医院',
    '运动健身;体育场馆',
    '文化传媒;广播电视',
    '文化传媒;美术馆',
    '文化传媒;文化宫',
    '文化传媒;展览馆',
    '休闲娱乐;电影院',
    '休闲娱乐;度假村',
    '休闲娱乐;剧院',
    '休闲娱乐;农家院',
    '休闲娱乐;休闲广场',
    '休闲娱乐;游戏场所',
    '房地产',
    '房地产;其他',
    '房地产;写字楼',
    '房地产;住宅区',
    '公司企业',
    '公司企业;厂矿',
    '公司企业;公司',
    '公司企业;农林园艺',
    '公司企业;其他',
    '公司企业;园区',
    '政府机构',
    '政府机构;党派团体',
    '政府机构;福利机构',
    '政府机构;各级政府',
    '政府机构;公检法机构',
    '政府机构;居民委员会',
    '政府机构;民主党派',
    '政府机构;其他',
    '政府机构;社会团体',
    '政府机构;涉外机构',
    '政府机构;行政单位',
    '政府机构;政治教育机构',
    '政府机构;中央机构',
    '酒店',
    '酒店;公寓式酒店',
    '酒店;快捷酒店',
    '酒店;民宿',
    '酒店;其他',
    '酒店;星级酒店',
    '生活服务;房产中介机构',
    '生活服务;家政服务',
    '生活服务;物流公司',
    '汽车服务',
    '汽车服务;其他',
    '汽车服务;汽车检测场',
    '汽车服务;汽车美容',
    '汽车服务;汽车配件',
    '汽车服务;汽车维修',
    '汽车服务;汽车销售',
]
