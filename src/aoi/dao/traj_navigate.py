# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
长短Node/link转换库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class TrajNavigate(PgDao):
    """
    导航轨迹
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "navigate_traj" in conf:
            poi_online_r_conf = conf['navigate_traj']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("trans 配置不存在")

    def get_traj_navigate(self, bids=None, geom=None, status=None):
        """
        获取导航轨迹
        Returns:

        """
        sql = f"""
            select bid,traj_info from traj_navigate where 1=1
        """
        args = []
        if bids:
            sql += f" and bid=ANY(%s)"
            args.append(bids)
        if geom:
            sql += f" and st_intersects(geom,ST_GeomFromText(%s,4326))"
            args.append(geom)
        if status:
            sql += f" and status=%s"
            args.append(status)
        return self.queryall_by_kv_cursor(sql, args)

    def insert_traj_navigate_list(self, data_list):
        """
        导航轨迹成果
        Args:
            data_list:

        Returns:

        """
        sql = """
            INSERT INTO traj_navigate (bid, traj_info) VALUES (%s, %s)
        """
        self.batch_insert(sql, data_list)

    def update_traj_navigate_status(self, bid):
        """
        bid
        Args:
            bid:

        Returns:

        """
        sql = "update traj_navigate set status='SUCCESS' where bid=%s"
        self.execute(sql, [bid])
