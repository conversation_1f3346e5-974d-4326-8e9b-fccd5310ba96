#!/usr/bin/env bash
# 启动命令  bash multi.sh 

mkdir -p /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/mistake/bids
rm -rf /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/mistake/bids/*

# 路侧集合
psql -Upoi_aoi_rw -Wpoi_aoi_rw -p 8032 -h 10.56.135.223 -dpoi_online_rw -c "\copy (SELECT bid FROM park_luce_list where  root_nav_pv > 30 or root_pv > 10000) to '/home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/mistake/bids/new_bids.csv' with csv"


cd /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/mistake/bids
# 要分割的文件
INPUT_FILE="new_bids.csv"
# 获取文件的总行数
TOTAL_LINES=$(wc -l < "$INPUT_FILE")
# 计算每个分割文件大致应该包含的行数（注意：这里不处理余数）
LINES_PER_SPLIT=$(( (TOTAL_LINES + 29) / 30 ))  # 向上取整到最近的整数（加9再除以10相当于向上取整，当TOTAL_LINES能被10整除时结果不变）
# 使用 split 按行数分割文件，并指定数字格式的输出文件名
split -d -l "$LINES_PER_SPLIT" "$INPUT_FILE" bids

cd ../
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids00 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids01 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids02 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids03 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids04 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids05 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids06 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids07 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids08 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids09 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids10 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids11 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids12 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids13 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids14 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids15 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids16 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids17 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids18 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids19 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids20 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids21 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids22 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids23 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids24 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids25 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids26 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids27 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids28 0 0 1 1 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids29 0 0 1 1 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 run_test.py bids/bids30 0 0 1 1 &