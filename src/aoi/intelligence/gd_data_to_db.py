"""
高德数据入库
"""

import datetime
import pandas as pd
from pathlib import Path

import shapely.wkt
import tqdm
import os
import sys

root_path = (Path(os.path.abspath(__file__))).parents[3]
sys.path.insert(0, root_path.as_posix())

from src.common.common_tool import get_mesh_id
from src.common import pgsql

today = datetime.date.today().strftime("%Y%m%d")
work_dir = Path(os.path.abspath(__file__)).parent / 'intelligence' / today
work_dir.mkdir(parents=True, exist_ok=True)


def run():
    """
    主入口
    :return:
    """
    ori_data = []
    for i in range(0, 6):

        current_date = (datetime.date.today() - datetime.timedelta(days=i)).strftime("%Y%m%d")
        # 这个是需要在de16上执行,并且挂载响应的数据
        file_name = f"/home/<USER>/mnt/aoi-ml/crawl_gd/output_{current_date}.tsv.ret"
        p_file = Path(file_name)
        if Path(file_name).exists():
            print(f"开始处理文件：{p_file.stem}")
            with open(p_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '情报id' in line:
                        continue
                    line_item = line.strip().split('\t')
                    if len(line_item) < 9:
                        continue
                    ori_data.append({
                        "情报id": line_item[0],
                        "gid": line_item[1],
                        "名称": line_item[2],
                        "polyline": line_item[3],
                        "分类": line_item[4],
                        "地址": line_item[5],
                        "x": line_item[6],
                        "y": line_item[7],
                        "时间戳": line_item[8],
                        "batch": p_file.stem
                    })
        lines = []
        for row in ori_data:
            bid = row['情报id']
            intel_id = row['gid']
            name = row['名称']
            polygon = "POLYGON(({}))".format(row['polyline'].replace(',', ' ').replace('_', ','))
            address = row['地址']
            x = row['x']
            y = row['y']
            lines.append(
                {"bid": bid, "intel_id": intel_id, "name": name, "geom": polygon, "address": address, 'display_x': x,
                 'display_y': y,
                 'mesh_id': get_mesh_id(f"POINT({x} {y})"),
                 "batch": row["batch"]})
        pd.DataFrame(lines).to_csv(work_dir / 'jp_all.csv', sep='\t', index=False)


def check_in_db(bid, batch):
    """
    这个批次是之前直接加入的,可以直接使用
    :param bid:
    :param batch:
    :return:
    """
    other_batch = 'dtc_2025_02_19.tsv'
    sql = 'select id from aoi_intelligence_history where bid=:bid and batch in :batch'
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, "batch": tuple([batch, other_batch])})
    return ret


def to_db():
    """
    保存到数据库
    :return:
    """
    df = pd.read_csv(work_dir / "jp_all.csv", converters={"bid": str}, sep='\t')
    for _, row in tqdm.tqdm(df.iterrows(), total=len(df)):
        bid = row['bid']
        batch = row['batch']
        # 这个是multigeom， 暂时还不支持，先跳过
        if '|' in row['geom']:
            continue
        try:
            # 检查是否是合法的wkt
            shapely.wkt.loads(row['geom'])
        except:
            continue
        dict_data = {
            "bid": row['bid'],
            "intel_id": row['intel_id'],
            "name": row['name'],
            "address": row['address'],
            "display_x": row['display_x'],
            "display_y": row['display_y'],
            "mesh_id": str(row['mesh_id']),
            "geom": row['geom'],
            "batch": batch,
        }
        if check_in_db(bid, batch):
            print("bid", bid, '已经入库')
            continue
        try:
            sql = "insert into aoi_intelligence_history" \
                  " (bid, intel_id,name,address,display_x,display_y,mesh_id,geom,batch) values " \
                  "(:bid, :intel_id, :name, :address,:display_x, :display_y, :mesh_id," \
                  " st_geomfromtext(:geom, 4326), :batch)"
            pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql, dict_data)
        except Exception as e:
            # 插入出现错误,例如首位不一致,这部分暂时先不处理了
            print(e.args)
            print(bid, '||', row['geom'])
    # aoi_intelligence_history 字段
    #   id,bid,intel_id,name,address,display_x,display_y,mesh_id,geom,batch,crawl_time,create_time


if __name__ == '__main__':
    run()
    to_db()
