# -*- coding: UTF-8 -*-
################################################################################
"""
轨迹策略提准调研
"""

import psycopg2
import ConfigParser
import os
import urllib2
import json


def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../../conf/config.ini')

dbname = "poi_online"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()

conn_tra = psycopg2.connect(database = "eq_carpark", user = "eq_carpark_se_rw", password = "cwuhkvwq", \
            host = "************", port = 5432)
cursor_tra = conn_tra.cursor()

def main():
    """
    轨迹策略V2,提高了有效率,但仍需进一步优化，需进一步优化
    """
    bid_set = set()
    with open("bidList", "r") as f:
        lines = f.readlines()
        for line in lines:
            bid_set.add(line.strip())
    
    # bid_fail_node_dict = dict()
    # with open("bid_node_list", "r") as f:
    #     lines = f.readlines()
    #     for line in lines:
    #         bid = line.strip().split('\t')[0]
    #         if bid in bid_fail_node_dict:
    #             bid_fail_node_dict[bid].append(line.strip().split('\t')[3])
    #         else:
    #             bid_fail_node_dict[bid] = [line.strip().split('\t')[3]]
    # bid_node_output_dict = dict()
    for bid in bid_set:
        sql = "select success_node_id from aoi_gate_match_batch_result where bid = %s and valid = 1 and \
            level = 3 and ((length(success_node_id) - length(replace(success_node_id,',','')))  = 1 or \
            (length(success_node_id) - length(replace(success_node_id,',',''))) = 2 )"
        cursor_poi.execute(sql, [bid])
        res = cursor_poi.fetchone()
        if res is None:
            continue
        #过滤紧急大门
        sql = "select string_agg(node_id, ',') from nav_gate where node_id in ('%s') \
                and type != 0" % (res[0].replace(",", "','"))
        cursor_road.execute(sql)
        origin_node = cursor_road.fetchone()[0]
        if origin_node is None:
            print(bid + "\t只有紧急大门")
            continue
        origin_node_list = origin_node.split(',')
       
        sql = "select gate_id1, pv_ratio, total from inter_gate_point_cluster_sep_zc where bid = '%s' \
                and gate_id1 != '' and pv_ratio > 0.03" % (bid)
        cursor_tra.execute(sql)
        res_gate_tra = cursor_tra.fetchall()
        if res_gate_tra is None or len(res_gate_tra) == 0:
            continue

        node_tra_dict = dict()
        addition_list = []
        gate_tra = ""
        for res_gate_tra_tmp in res_gate_tra:
            if res_gate_tra_tmp[1] * res_gate_tra_tmp[2] > 1.5 or res_gate_tra_tmp[1] > 0.1:
                gate_tra = gate_tra + "','" + res_gate_tra_tmp[0]
        gate_tra = gate_tra.strip("','")
        if len(gate_tra) > 0:
            sql = "select distinct a.node_id, st_astext(geom) from nav_gate a inner join nav_node b on a.node_id\
                 = b.node_id where gate_id in ('%s')" % (gate_tra)
            cursor_road.execute(sql)
            res_node_tra = cursor_road.fetchall()
            for node_tra_tmp in res_node_tra:
                node_tra_dict[node_tra_tmp[0]] = node_tra_tmp[1]

            for node_id_tmp in node_tra_dict.keys():
                sql = """
                select 
                    string_agg(node_id, ''',''') 
                from nav_node 
                where 
                    st_dwithin(geom, st_geomfromtext(%s, 4326), 0.0004) 
                    and node_id != %s"""
                cursor_road.execute(sql, [node_tra_dict[node_id_tmp], node_id_tmp])
                res_node_nearby = cursor_road.fetchone()
                if res_node_nearby is None:
                    continue
                sql = "select distinct node_id from nav_gate where node_id in ('%s')" % (res_node_nearby[0])
                cursor_road.execute(sql)
                res_gate_nearby = cursor_road.fetchall()
                for node_nearby_tmp in res_gate_nearby:
                    addition_list.append(node_nearby_tmp[0])
        for node_tmp in set(origin_node_list).difference(set(node_tra_dict.keys() + addition_list)):
            sql = "select st_astext(geom) from nav_node where node_id = %s"
            cursor_road.execute(sql, [node_tmp])
            geom_node = cursor_road.fetchone()
            if geom_node is None:
                continue
            sql = """
                select 
                    count(*) 
                from poi 
                where 
                    st_dwithin(st_geomfromtext('%s', 4326), geometry, 0.00020) """ % (geom_node[0])
            sql = sql + """and (name like '%门' 
                        or name like '%门)' 
                        or name like '%门岗' 
                        or name like '%出口' 
                        or name like '%入口' 
                        or std_tag = '出入口;门')"""
            cursor_poi.execute(sql)
            if cursor_poi.fetchone()[0] > 0:
                continue
            print(bid + "\t" + node_tmp)
    

if __name__ == "__main__":
    main()

