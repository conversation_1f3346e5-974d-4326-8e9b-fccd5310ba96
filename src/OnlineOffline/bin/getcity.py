"""
get city
"""
import sys

faceid2city = {}
f = open(sys.argv[1])
for line in f:
	data = line.strip('\n').split('\t')
	faceid = data[4]
	city = data[-1]
	if not '|' in faceid:
		faceid2city[faceid] = city
	else:
		for item in faceid.split('|'):
			faceid2city[item] = city
f.close()


f = open(sys.argv[2])
fout = open(sys.argv[3], 'w')
for line in f:
	data = line.strip().split('\t')
	faceid = data[0]
	poly = data[2]
	city = 'nocity'
	if faceid in faceid2city:
		city = faceid2city[faceid] 
	fout.write(faceid + '\t' + poly + '\t' + city + '\n')
f.close()
fout.close()

