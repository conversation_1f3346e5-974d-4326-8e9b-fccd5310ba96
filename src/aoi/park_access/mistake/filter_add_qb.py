# -*- coding: utf-8 -*-
"""
出入口新增情报过滤
"""
import datetime
import multiprocessing
from operator import is_
import sys
import os
import math
from tkinter import W

import tqdm
import requests
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.affinity import translate
from shapely import wkt
from datetime import datetime
from datetime import timedelta

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from aoi.dao.poi_online_rw import PoiOnlineRW
from aoi.dao.master_back import MasterBackDao
from aoi.dao.road import Road
from aoi import utils
from shapely import wkt
from loguru import logger

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
road_dao = Road()


# logger.remove()
log_path = "./log/filter_qb.log"
logger.add(log_path, rotation="10 MB")


def filter_add_qb(qb, res):
    """
    过滤出入口新增情报
    conclusion 0 不变，1 删除
    """
    id = qb['id'] 
    res[id] = {
        "id": id,
        "park_bid": qb["bid"],
        "geom": wkt.loads(qb["geom"]),
        "status": "INIT",
        "msg": "",
        "type": qb["type"],
        "strategy": qb["strategy"],
        "conclusion": 0,
        "confidence_dis": 0,
        "confidence_low_traj": 0
    }
    logger.info(f"start_filter,bid:{qb['bid']},id:{qb['id']}")
    # 准备数据
    get_prepare_data(qb, res)
    if res[qb['id']]['conclusion'] == 1:
        return
    # 已有出入口距离过滤
    filter_by_distance(qb, res, max_dis=50)
    if res[qb['id']]['conclusion'] == 1:
        return
    # 轨迹通量较大，但是口的通量低,且数量少
    filter_by_low_traj(qb, res, min_traj_num=20, min_pass_percent=0.1, min_pass_num=50)


def get_traj_list_by_bid_and_geom(geom_wkt, parent_id):
    """
    获取轨迹列表
    """
    date_30_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')
    dest_traj_list = []
    park_traj_list = utils.get_traj_list_by_bid_and_geom(parent_id, geom_wkt, date_30_days_ago)
    if park_traj_list:
        for item in park_traj_list:
            tmp_sp = wkt.loads(item[0])
            last_point_geom = Point(tmp_sp.coords[-1]) 
            if last_point_geom.within(wkt.loads(geom_wkt)):
                dest_traj_list.append(tmp_sp)
    return dest_traj_list


def calculate_linestring_angle(line1, line2):
    """
    计算两条线路的夹角
    """
    # 确保输入是有效的 LineString
    if not isinstance(line1, LineString) or not isinstance(line2, LineString):
        logger.info("输入必须是 LineString 对象")
        return 90, 90
    # 提取方向向量
    x1, y1 = line1.coords[0]
    x2, y2 = line1.coords[-1]
    x3, y3 = line2.coords[0]
    x4, y4 = line2.coords[-1]
    # 计算方向向量
    u = (x2 - x1, y2 - y1)
    v = (x4 - x3, y4 - y3)
    # 计算点积
    dot_product = u[0] * v[0] + u[1] * v[1]
    # 计算向量的模长
    norm_u = math.sqrt(u[0] ** 2 + u[1] ** 2)
    norm_v = math.sqrt(v[0] ** 2 + v[1] ** 2)
    # 防止除以零的情况
    if norm_u == 0 or norm_v == 0:
        logger.info("线段的长度不能为零")
        return 90, 90
    # 计算夹角的余弦值
    cos_theta = dot_product / (norm_u * norm_v)
    # 将余弦值限定在 [-1, 1] 范围内，避免数值误差
    cos_theta = max(-1, min(1, cos_theta))
    # 计算夹角（弧度制）
    angle_rad = math.acos(cos_theta)
    # 如果需要角度制，转换为角度
    angle_deg = math.degrees(angle_rad)
    return angle_rad, angle_deg


def is_cross_node(traj, node_buffer, link_geom):
    """
    判断轨迹是否穿过 node
    """
    intersection = traj.intersection(node_buffer)
    if isinstance(intersection, LineString) and intersection.length > 0:
        angle_rad, angle_deg = calculate_linestring_angle(intersection, link_geom)
        return angle_deg <= 70 or angle_deg >= 110
    return False


def get_dest_traj_pass_node_num(node_buffer_sp, link_geom_sp, dest_traj_list):
    """
    获取轨迹通过节点的数量
    """
    traj_pass_node_num = 0
    if not dest_traj_list:
        return 0 
    
    for sp in dest_traj_list:
        res = is_cross_node(sp, node_buffer_sp, link_geom_sp)
        if res:
            traj_pass_node_num += 1
    return traj_pass_node_num


def filter_by_low_traj(qb, res, min_traj_num=10, min_pass_percent=0.1, min_pass_num=50):
    """
    根据轨迹数量过滤出入口新增情报
    """
    logger.info(f"filter_by_distance-start")
    id = qb['id']

    if "area_sp" not in res[id] or res[id]['area_sp'] == "":
        res[id]["status"] = "AREA_SP_NOT_EXISTS"
        res[id]["msg"] = "面不存在"
        return
    

    if not res[id]['link_geom_sp']:
        res[id]["status"] = "LINK_GEOM_SP_NOT_EXISTS"
        res[id]["msg"] = "link_geom_sp不存在"
        return

    # 获取轨迹
    end_trajs = get_traj_list_by_bid_and_geom(res[id]['area_sp'].wkt, res[id]['park_info'][1])
    end_trajs_num = len(end_trajs)
    node_buffer_sp = wkt.loads(qb['geom']).buffer(30 / 110000)

    # 终点轨迹轨迹数量经过node的数量
    pass_num = get_dest_traj_pass_node_num(node_buffer_sp, res[id]['link_geom_sp'], end_trajs)
    # 终点数量过少 跳过
    if end_trajs_num < min_traj_num:
        res[id]['status'] = 'TRAJ_NUM_TOO_LOW'
        res[id]['msg'] = f"终点轨迹数量过少, 数量为{end_trajs_num}"
        return 

    # 计算占比 和数量
    percent = pass_num / end_trajs_num
    # 终点通量过低标记无效
    confidence = 0
    if percent < min_pass_percent and pass_num < min_pass_num:
        confidence = 90
        res[id]['status'] = 'TRAJ_NUM_PERCENT_TOO_LOW'
        res[id]['msg'] = f"终点轨迹数量占比少，轨迹数量:{end_trajs_num},通过node数量为:\
            {pass_num},百分比为:{round(percent*100)},不超过数量{min_pass_num}"
        res[id]['conclusion'] = 1
    elif percent < 0.2 and pass_num < 50:
        confidence = 80
        res[id]['status'] = 'TRAJ_NUM_PERCENT_TOO_LOW'
        res[id]['msg'] = f"终点轨迹数量占比少，轨迹数量:{end_trajs_num},通过node数量为:\
            {pass_num},百分比为:{round(percent*100)},不超过数量{min_pass_num}"
        res[id]['conclusion'] = 1
    elif percent < 0.2 and pass_num < 100:
        confidence = 70
        res[id]['status'] = 'TRAJ_NUM_PERCENT_TOO_LOW'
        res[id]['msg'] = f"终点轨迹数量占比少，轨迹数量:{end_trajs_num},通过node数量为:\
            {pass_num},百分比为:{round(percent*100)},不超过数量{min_pass_num}"
        res[id]['conclusion'] = 1
    else:
        res[id]['status'] = 'TRAJ_NUM_PERCENT_MORE'
        res[id]['msg'] = f"终点轨迹数量占比少，轨迹数量:{end_trajs_num},通过node数量为:\
            {pass_num},百分比为:{round(percent*100)},不超过数量{min_pass_num}"
    res[id]['confidence_low_traj'] = confidence
    return


def filter_by_distance(qb, res, max_dis=50):
    """
    根据距离过滤出入口新增情报
    """
    logger.info(f"filter_by_distance-start")
    id = qb['id']
    if 'access_info' not in res[id]:
        return
    for access_info in res[id]['access_info']:
        distance = wkt.loads(access_info['gcj_geom']).distance(wkt.loads(qb['geom'])) * COORDINATE_FACTOR
        logger.info(f"距离过远, 距离为{distance}米, 出入口bid:{access_info['bid']}, \
出入口坐标：{access_info['gcj_geom']}, 情报坐标：{qb['geom']}")
        if distance < max_dis:
            if distance <= 30:
                confidence = 90
            elif distance <= 50:
                confidence = 80
            elif distance <= 100:
                confidence = 70
            res[id]['conclusion'] = '1'
            res[id]['confidence_dis'] = confidence
            res[id]['status'] = 'DISTANCE_FILTERED'
            res[id]['msg'] = f"距离过近, 距离为{distance}米, 出入口bid:{access_info['bid']}, \
出入口坐标：{access_info['gcj_geom']}, 情报坐标：{qb['geom']}"
            break


def get_prepare_data(qb, res):
    """
    准备数据
    """
    park_bid = qb["bid"]
    id = qb['id']
    node_id = qb['node_id']
    res[id]['link_geom_sp'] = None
    if "node_id" in qb and qb['node_id']:
        link_infos = road_dao.get_nav_link_by_node(node_ids=[qb["node_id"]], form=None)
        if link_infos:
            res[id]['link_geom_sp'] = wkt.loads(link_infos[0][6])

    park_info = poi_online_query.get_park_by_bid(park_bid)
    if not park_info:
        res[id]["status"] = "PARK_DEL"
        res[id]["msg"] = "停车场不存在"
        res[id]["conclusion"] = 1
        return
    access_info = poi_online_query.get_access_by_bid(park_bid)
    if not access_info:
        res[id]["status"] = "INIT"
        res[id]["msg"] = "出入口不存在"

    aoi_info = master_back_dao.get_aoi_info_by_bid(park_info[0][1])
    res[id]["park_info"] = park_info[0]
    res[id]["access_info"] = access_info
    res[id]["aoi_info"] = aoi_info

    if not aoi_info:
        res[id]["status"] = "AOI_DEL"
        res[id]["msg"] = "AOI不存在"

    if park_info[0]['point_wkt']:
        res[id]["park_point_sp"] = wkt.loads(park_info[0]['point_wkt'])
    
    if aoi_info and aoi_info[1]:
        res[id]["aoi_area_sp"] = wkt.loads(aoi_info[1])
    
    if park_info and park_info[0]['area_wkt']:
        res[id]["park_area_sp"] = wkt.loads(utils.mc_to_gcj(park_info[0]['area_wkt']))
    
    if "park_area_sp" not in res[id]:
        res[id]["park_area_sp"] = res[id]["park_point_sp"].buffer(100 / 110000)

    is_intersects = True
    if "aoi_area_sp" in res[id] and res[id]["park_area_sp"]:
        #判断是否相交
        if not res[id]["aoi_area_sp"].intersects(res[id]["park_area_sp"]):
            is_intersects = False
    if "aoi_area_sp" in res[id] and is_intersects:
        res[id]["area_sp"] = res[id]["aoi_area_sp"]
    elif "park_area_sp" in res[id]:
        res[id]["area_sp"] = res[id]["park_area_sp"]
    else:
        res[id]["area_sp"] = res[id]["park_point_sp"].buffer(100 / 110000)
    

def main(bid, strategy, limit):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:
    """
    bid_lists = None
    if bid:
        bid_lists = [bid]
    # 获取待过滤的情报
    qb_lists = poi_online_query.get_park_access_intelligence_add_by_strategy(strategy, bid_lists, limit)
    logger.info(f"开始过滤出入口新增情报, 策略：{strategy}, 停车场：{bid}, 任务数{len(qb_lists)}")
    res = {}
    # 过滤情报
    for i in tqdm.tqdm(range(len(qb_lists))):
        qb = qb_lists[i]
        filter_add_qb(qb, res)
        if res[qb['id']]['conclusion'] == 1:
            logger.error(f"需要过滤出入口新增情报: {res[qb['id']]['status']},{res[qb['id']]['msg']}")
            continue    
        # try:
        #     filter_add_qb(qb, res)
        #     if res[qb['id']]['conclusion'] == 1:
        #         logger.error(f"需要过滤出入口新增情报: {res[qb['id']]['status']},{res[qb['id']]['msg']}")
        #         continue 
        # except Exception as e:
        #     print(e)
    conclusion_res = {
        'unchange': 0,
        'update': 0,
    }
    for id, item in res.items():
        if item['status'] not in conclusion_res:
            conclusion_res[item['status']] = 0
        conclusion_res[item['status']] += 1
        if item['conclusion'] == 1:
            conclusion_res['update'] += 1
        else:
            conclusion_res['unchange'] += 1
        print(f"pinggu|{item['conclusion']}|{item['park_bid']}|{item['geom']}|\
{item['id']}|{item['status']}|{item['confidence_dis']}|{item['confidence_low_traj']}|\
{item['msg']}|{item['type']}|{item['strategy']}")
    logger.info(conclusion_res)
    
if __name__ == '__main__':
    bid = sys.argv[1] if sys.argv[1] != '0' else None
    strategy = sys.argv[2] if sys.argv[2] != '0' else None
    limit = sys.argv[3]
    main(bid, strategy, limit)
