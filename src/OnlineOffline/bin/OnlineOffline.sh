#!/bin/bash

CUR_DIR=$(readlink -f $(dirname $BASH_SOURCE[0]))
source $CUR_DIR"/conf.sh"

function SplitCity() {
  rm ${SplitPath}/*
  local INFILE=$1
  split_line_cnt=$(wc -l <${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}')
  split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

  for ONE_PART in $(ls ${SplitPath}/part.input.a?); do
    ${PYTHONBIN} ${BinPath}/CityMap.py ${ONE_PART} ${CITYFILE} ${ONE_PART}.out >${ONE_PART}.log &
  done
  wait
}

function MidOnline() {
  >ok
  local INFILE=$1
  rm ${SplitPath}/*

  split_line_cnt=$(wc -l <${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}')
  split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

  for ONE_PART in $(ls ${SplitPath}/part.input.a?); do
    ${PHPBIN} ${BinPath}/MidOnline.php ${ONE_PART} &
  done
  wait
}

function MidOffline() {
  >ok
  local INFILE=$1
  rm ${SplitPath}/*

  split_line_cnt=$(wc -l <${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}')
  split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

  for ONE_PART in $(ls ${SplitPath}/part.input.a?); do
    ${PHPBIN} ${BinPath}/MidOffline.php ${ONE_PART} &
  done
  wait
}

function BidOnline() {
  >ok
  local INFILE=$1
  rm ${SplitPath}/*

  split_line_cnt=$(wc -l <${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}')
  split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

  for ONE_PART in $(ls ${SplitPath}/part.input.a?); do
    ${PHPBIN} ${BinPath}/BidUpdate.php ${ONE_PART} "jichudiwu" "changdi_zhudian" &
  done
  wait
}

function BidOffline() {
  >ok
  local INFILE=$1
  rm ${SplitPath}/*

  split_line_cnt=$(wc -l <${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}')
  split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

  for ONE_PART in $(ls ${SplitPath}/part.input.a?); do
    ${PHPBIN} ${BinPath}/BidUpdate.php ${ONE_PART} "jichudiwu" "changdi_zhudian" &
  done
  wait
}

function ClearEnv() {
  rm ${OnlinePath}/*
  rm ${OfflinePath}/*
  rm ${FinalPath}/*

  mkdir ${OnlinePath}
  mkdir ${OfflinePath}
  mkdir ${FinalPath}
  mkdir ${SplitPath}
}

function GetData() {
  cp ${DATA_PATH}/aoi/online/mid.aoi.online ${OnlinePath}
  cp ${DATA_PATH}/aoi/online/bid.aoi.online ${OnlinePath}
  cp ${DATA_PATH}/aoi/online/mid.aoi.offline ${OfflinePath}
  cp ${DATA_PATH}/aoi/online/bid.aoi.offline ${OfflinePath}

  cat ${SplitPath}/part.input.a?.out | awk -F "\t" '
    ARGIND==1{
        mid = $1;
        city = "";

        if(NF == 10)
            city = $NF;
        map[mid] = city;
    }
    ARGIND==2{
        mid = $1;
        if(mid in map)
            print $0 "\t" map[mid];
        else
            print $0 "\t" "";
    }' - ${DATA_PATH}/building/part/GetFinalResult/online/mid.building.online >${OnlinePath}/mid.building.online

  cp ${DATA_PATH}/building/part/GetFinalResult/online/bid.building.online ${OnlinePath}
  cp ${DATA_PATH}/building/part/GetFinalResult/online/mid.building.offline ${OfflinePath}
  cp ${DATA_PATH}/building/part/GetFinalResult/online/bid.building.offline ${OfflinePath}

  cat ${OnlinePath}/mid.*.online >${FinalPath}/mid.online.all
  cat ${OfflinePath}/mid.*.offline | awk -F "\t" '{print $1}' >${FinalPath}/mid.offline.all
  cat ${OnlinePath}/bid.*.online | awk -F "\t" '{print $1 "\t" $2}' >${FinalPath}/bid.online.all
  cat ${OfflinePath}/bid.*.offline | awk -F "\t" '{print $1 "\t" 0}' >${FinalPath}/bid.offline.all
}

function AllWork() {
  BidOffline ${FinalPath}/bid.offline.all
  MidOffline ${FinalPath}/mid.offline.all
  BidOnline ${FinalPath}/bid.online.all
  MidOnline ${FinalPath}/mid.online.all
}

AllWork
