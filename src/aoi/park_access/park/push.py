# -*- coding: utf-8 -*-
"""
推送情报到人工作业
"""
import datetime
import multiprocessing
import sys
import os
import uuid
from collections import defaultdict

import tqdm
import requests

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from aoi.dao.poi_online_rw import PoiOnlineRW
from shapely import wkt
from loguru import logger
from aoi.park_access.mistake import park_access_qc as qc
from aoi import utils

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()

# logger.remove()
log_path = "./log/underground_intelligence.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
TCQ_BATCH_ID = "TCQXZP2024103101"
TCZ_BATCH_ID = "TCZXZP2024103101"


def park_access_intelligence_push(push_number=1000, strategy=None):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:

    """
    # 获取待推送情报主点
    park_access_intelligence_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
        'new_park_intelligence_add', status='INIT', strategy=strategy)
    if not park_access_intelligence_list:
        logger.info("无待推送情报")
        return
    park_access_intelligence_map = {}
    for item in park_access_intelligence_list:
        if item['outside_id'] not in park_access_intelligence_map:
            park_access_intelligence_map[item['outside_id']] = [item]
            continue
        park_access_intelligence_map[item['outside_id']].append(item)
    # 开始推送
    pushed_park_access_list = []
    for outside_id, park_access in tqdm.tqdm(park_access_intelligence_map.items()):
        if len(pushed_park_access_list) >= push_number:
            logger.info(f"本次推送结束,共推送:{len(pushed_park_access_list)}")
            return
        intelligence_ids = [item['id'] for item in park_access]
        # 查询停车场情报信息
        park_intelligence_list = poi_online_query.get_park_add_intelligence([], [outside_id])
        if not park_intelligence_list:
            logger.error(f"停车场信息查询失败:{park_access['outside_id']}")
            poi_online_rw.update_park_access_intelligence_status_by_id(intelligence_ids, status='ERROR',
                                                                       resp='停车场信息查询失败')
            continue
        park_intelligence = park_intelligence_list[0]
        push_park_access_list = []
        valid_intelligence_ids = []
        for item in park_access:
            park_access_name, show_tag = get_park_access_name(park_intelligence['name'], item['node_id'],
                                                              item['link_id'])
            if not park_access_name:
                poi_online_rw.update_park_access_intelligence_status_by_id([item['id']], status='ERROR', resp='大门不可通行')
                continue
            push_park_access_list.append({
                'bid': '',
                'name': park_access_name,
                'wkt': item['geom'],
                'show_tag': f"停车场{show_tag}",
                "status": 1,
                "parent_bid": "",
                'node_id': item['node_id'],
                'link_id': item['link_id'],
            })
            valid_intelligence_ids.append(item['id'])
        if len(valid_intelligence_ids) > 0:
            source_id = f"new_park_intelligence_add_{outside_id}"
            qb_id = park_access_request(park_intelligence, push_park_access_list, source_id)
            poi_online_rw.update_park_access_intelligence_status_by_id(valid_intelligence_ids, status='PUSHED',
                                                                       resp=qb_id)
            poi_online_rw.update_effect_raising_create_park_qb_id_by_id(outside_id, qb_id)
            pushed_park_access_list.append(park_intelligence)


def park_intelligence_push(park_intelligence_id):
    """
    停车场情报推送
    Args:
        park_intelligence_id:

    Returns:

    """
    park_intelligence_list = poi_online_query.get_park_add_intelligence([], [park_intelligence_id])
    if not park_intelligence_list:
        logger.error(f"停车场信息查询失败:park_intelligence_ids{park_intelligence_id}")
        return
    park_intelligence = park_intelligence_list[0]
    source_id = f"new_park_intelligence_add_{park_intelligence['id']}"
    qb_id = park_push_request(park_intelligence, source_id)
    return qb_id


def get_park_access_name(park_name, long_node_id, lone_link_id):
    """
    获取停车场出入口名称
    Args:
        park_name:
        long_node_id:
        lone_link_id:

    Returns:

    """
    park_access_name = park_name.replace("-地下停车场", "地下停车场").replace("-地上停车场", "地上停车场").replace(
        "-停车场", "停车场").replace("-立体停车场", "立体停车场")
    if lone_link_id:
        return f"{park_access_name}-入口", '入口'
    # 查大门通行性
    nav_gate_semantic = utils.get_nav_gate_semantic(long_node_id)
    if nav_gate_semantic not in ['入口', '出入口']:
        logger.error(f"大门通行性查询失败:{park_access_name}-{long_node_id}-{lone_link_id}-{nav_gate_semantic}")
        return False, False
    return f"{park_access_name}-{nav_gate_semantic}", nav_gate_semantic


def park_access_request(parking_info, park_access_list, source_id):
    """
    出入口情报推送推送
    Args:
        parking_info:
        park_access_list:
        source_id:

    Returns:

    """
    data = {
        "src": 12,  # 必填，9:停车场, 10:停车场出入口;12 停车场整体制作
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": TCQ_BATCH_ID,  # 必填，批次号
        "main_poi_bid": '15731802024032445438',  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "from_src": "new_park_intelligence",
        "qb_type": 0,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "TCQ",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                # "callback": "http://mapde-poi.baidu-int.com/pre/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "核实全部出入口",
                "park_list": [{
                    "park": {
                        "bid": "",
                        "name": parking_info['name'],
                        "wkt": parking_info['geom'],
                        "show_tag": parking_info['show_tag'],
                        "status": 1,
                        "parent_bid": parking_info['parent_bid'],
                    },
                    "park_gate_list": park_access_list,
                }],
            }
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        # url='http://mapde-poi.baidu-int.com/pre/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


def park_push_request(parking_info, source_id):
    """
    停车场推送
    Args:
        parking_info:
        park_access_list:
        source_id:

    Returns:

    """
    data = {
        "src": 12,  # 必填，9:停车场, 10:停车场出入口;12 停车场整体制作
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": TCZ_BATCH_ID,  # 必填，批次号
        "main_poi_bid": '15731802024032445438',  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "from_src": "new_park_intelligence",
        "qb_type": 1,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "TCQ",  # 必填
                "priority": 10,  # 必填
                # "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "callback": "http://mapde-poi.baidu-int.com/pre/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "核实全部出入口",
                "park_list": [{
                    "park": {
                        "bid": "",
                        "name": parking_info['name'],
                        "wkt": parking_info['geom'],
                        "show_tag": parking_info['show_tag'],
                        "status": 1,
                        "parent_bid": parking_info['parent_bid'],
                    },
                }],
            }
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        # url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        url='http://mapde-poi.baidu-int.com/pre/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


if __name__ == '__main__':
    push_number = sys.argv[1]
    pv = sys.argv[2]
    strategy = sys.argv[3] if sys.argv[3] != '0' else None
    park_access_intelligence_push(int(push_number), strategy)
