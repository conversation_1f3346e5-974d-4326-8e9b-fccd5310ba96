LocalPath=`dirname $0`
source ${LocalPath}/conf.sh

date=$(date +'%Y%m%d')
now_date=$(date --date='3 days ago' +%Y%m%d)
rm "$DATAPATH/gaode.aoi.utf8";
rm "$DATAPATH/gaode.aoi.filter.utf8"
rm "$DATAPATH/gaode.aoi"
$HADOOP dfs -getmerge "$HadoopInputPath/$now_date/result" "$DATAPATH/gaode.aoi.utf8"
cat "$DATAPATH/gaode.aoi.utf8" | sh "$LocalPath"/parseData.sh > "$DATAPATH/gaode.aoi.filter.utf8"
iconv -f utf8 -t gb18030 "$DATAPATH/gaode.aoi.filter.utf8"  > "$DATAPATH/gaode.aoi"

