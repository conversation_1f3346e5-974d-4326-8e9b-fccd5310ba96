# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
停车场采纳轨迹库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class TrajDB(PgDao):
    """
    采纳轨迹
    """

    def __init__(self, shared_pool=True, pool=None):
        conf = CommTool.get_config("pg").get("trj_db")
        if not conf:
            raise Exception("trj_db 配置不存在")
        self.db_conf = conf

        # 传递配置和连接池给父类，由父类统一管理连接池
        super().__init__(pg_conf=conf, pool=pool, use_shared_pool=shared_pool)

    def get_park_accept_traj(self, geom=None, accept_time=None):
        """
        获取采纳轨迹
        Returns:

        """
        sql = f"""
            select st_astext(arrived_traj) as arrived_traj from park_accept_data where 1=1
        """
        args = []
        if geom:
            sql += f" and st_intersects(arrived_traj,ST_GeomFromText(%s,4326))"
            args.append(geom)
        if accept_time:
            sql += f" and accept_time >= %s"
            args.append(accept_time)
        return self.queryall_by_kv_cursor(sql, args)

    def get_gj_traj(self, geom=None):
        """
        获取采纳轨迹
        Returns:

        """
        sql = f"""
            select st_astext(geom) as geom from tmp_byd_traj where 1=1
        """
        args = []
        if geom:
            sql += f" and st_intersects(geom,ST_GeomFromText(%s,4326))"
            args.append(geom)
        return self.queryall_by_kv_cursor(sql, args)

    def get_hd_traj(self, geom=None, src=None, mesh_ids=None, intercept_geom=None):
        """
        获取高精轨迹
        Returns:

        """
        sql = f"""
            select st_astext(geom) as geom,src from hd_traj where 1=1
        """
        args = []
        if intercept_geom:
            sql = f"""
                select st_astext(st_intersection(geom, ST_GeomFromText(%s,4326))) as geom,src from hd_traj where 1=1
            """
            args.append(intercept_geom)
        if geom:
            sql += f" and st_intersects(geom,ST_GeomFromText(%s,4326))"
            args.append(geom)
        if src:
            sql += f" and src = %s"
            args.append(src)
        if mesh_ids:
            sql += f" and mesh_id=ANY (%s)"
            args.append(mesh_ids)
        return self.queryall_by_kv_cursor(sql, args)
