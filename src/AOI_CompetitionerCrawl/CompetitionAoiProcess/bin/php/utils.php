<?php

require_once('CNsHead.class.php');

/**
 * @param 请求数据
 * @param 结果数组
 * @return 无
 */
function ss_send($req, &$res)
{
	$raw_uid="null";
	if( isset($req['list'][0]['id'])){
		$raw_uid=$req['list'][0]['id'];
	}
    $ip = '************';
    $port = 10108;
    $socket = fsockopen($ip, $port, $errno, $error, 1);
    if(empty($socket)){
		$socket = fsockopen($ip, $port, $errno, $error, 1);
	}
    if(empty($socket)){
		$socket = fsockopen($ip, $port, $errno, $error, 1);
	}
	if(empty($socket)){
		write_msg("error_socket\t$raw_uid\n", "stdout");
		return false;
	}
    static $nshead;
    if (empty($nshead))
    {
		$nshead = new NsHead();
    }
	
	$req = mc_pack_array2pack($req, PHP_MC_PACK_V2);
    if(is_null($req)){
		write_msg("error_null_req\t$raw_uid\n", "stdout");
		return false;
	}
    $req_arr['body_len'] = strlen($req);
    $nshead->nshead_write($socket, $req_arr, $req);
    $res = $nshead->nshead_read($socket);
    fclose($socket);
	if ($res == false) 
	{
		write_msg("error_nshead_read\t$raw_uid\n", "stdout");
		return false;
	}
	else
	{
		$res = mc_pack_pack2array($res['buf']);
	}
}

/**
 * @param 日志信息
 * @param 日志类型
 * @return 无
 */
function write_msg($str, $type)
{
	if ($type == "stdout")
	{
		fwrite(STDOUT, $str);
	}
	else if ($type == "stderr")
	{
		fwrite(STDERR, $str);
	}
}

/**
 * @param 请求数据
 * @param 结果数组
 * @return 无
 */
function run_add($data, &$res)
{
	$arr = array(
		'type' => 1,
	);

	$list[0] = $data;
	$arr['list'] = $list;
	ss_send($arr, $res);
}

/**
 * @param 请求数据
 * @param 结果数组
 * @param 置信度
 * @return 无
 */
function run_gl($data, &$res, $th)
{
	$arr = array(
		'type' => 14,
		'ret_num' => 10,
		'confidence' => $th,
	);

	$list[0] = $data;
	unset($list[1]);
	$arr['list'] = $list;
	ss_send($arr, $res);
}

/**
 * @param 请求数据
 * @param 结果数组
 * @return 无
 */
function run_del($data, &$res)
{
	$arr = array(
		'type' => 2,
	);

	$list[0] = $data;
	$arr['list'] = $list;
	ss_send($arr, $res);
}

/**
 * @param 原始输出
 * @param 结果数组
 * @return 无
 */
function parser_line($line, &$data)
{
	$line  = rtrim($line, "\n\r");
	$array = explode("\t", $line);
	if ( count($array) != 10 )
	{
		write_msg("input file count = ".count($array)."\n", "stderr");
		return array('errno' => 1, 'error' => 'fail');
	}

	$data['id'] = $array[0];
	$data['name'] = $array[1];
	$data['city'] = $array[2];
	$data['address'] = $array[3];
	$data['point_x'] = intval($array[4]);
	$data['point_y'] = intval($array[5]);
	$data['phone'] = $array[6];
	$data['tag'] = $array[7];
	$data['catalog'] = $array[8];
	if (!isset($array[9])) 
	{
		$data['src_type'] = '';
	} 
	else 
	{
		$data['src_type'] = $array[9];
	}

	$data['alias'] = '';

	return array('errno'=>0, 'error'=>'OK');
}
?>
