#!/bin/bash

CUR_DIR=$(readlink -f $(dirname $BASH_SOURCE[0]))
source "$CUR_DIR"/../conf/globalConf.sh
date=$(date +"%Y%m%d")

RESULT_PATH="$DATA_PATH/poi"
mkdir -p "$RESULT_PATH"
AFS_COMMAND="$AFS_SHELL --username=mapdata-dw-poi-output-cd --password=faA15C83c04f"
POI_AFS_PATH="afs://kunpeng.afs:9902/user/mapdata-dw-poi/output/changdi/poi_res/data"

rm -rf "$RESULT_PATH/poi_res.md5"
$AFS_COMMAND get "$POI_AFS_PATH/poi_res.md5" "$RESULT_PATH"
md5=$(awk '{print $1}' "$RESULT_PATH/poi_res.md5")
cur_md5=$(md5sum "$RESULT_PATH/poi_res" | awk '{print $1}')
if [ "$md5" == "$cur_md5" ]; then
  exit 0
fi

### start copy new poi
originCount=$(wc -l "$RESULT_PATH/poi_res")
mv "$RESULT_PATH/poi_res" "$TMP_PATH/poi_res.bak.$date"
$AFS_COMMAND get "$POI_AFS_PATH/poi_res" "$RESULT_PATH"
count=$(wc -l "$RESULT_PATH/poi_res")
diff_count=$[$originCount - $count]
if [[ ${diff_count} -gt 1000000 || $diff_count -lt -10000000 ]]; then
  sh "{$LIB_PATH}sendNotice.sh" "poi全量数据差异过大，当前数量${count}, 昨天数量${originCount}" "warning"
fi
iconv -c -f gb18030 -t utf8 "$RESULT_PATH/poi_res" >"$RESULT_PATH/poi_res.utf8"
