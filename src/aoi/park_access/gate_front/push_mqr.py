# -*- coding: utf-8 -*-
"""
推送情报到人工作业
"""
import datetime
import multiprocessing
import sys
import os
import uuid
from collections import defaultdict
import psycopg2
from time import sleep
import autocomplete

import tqdm
import requests
from shapely import wkt
from loguru import logger
from shapely.geometry import Point

poiOnlineConf = {"db": "poi_online", "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
                 "host": "gzbh-ns-map-de16.gzbh.baidu.com",
                 "port": 8532}
# 连接线上库
poiOnlineDbConn = psycopg2.connect(database=poiOnlineConf["db"],
                                   user=poiOnlineConf["user"],
                                   password=poiOnlineConf["pwd"],
                                   host=poiOnlineConf["host"],
                                   port=poiOnlineConf["port"])
poiOnlineDao = poiOnlineDbConn.cursor()

buConf = {"db": "business_order", "user": "business_order_se_rw", "pwd": "kpigzear",
          "host": "**************",
          "port": 7432}
# 连接线上库
buDbConn = psycopg2.connect(database=buConf["db"],
                            user=buConf["user"],
                            password=buConf["pwd"],
                            host=buConf["host"],
                            port=buConf["port"])
buDao = buDbConn.cursor()

transConf = {"databases": "trans_id",
             "user": "trans_id_se_rw", "pwd": "cpmkukky",
             "host": "*************", "port": 5432}
transDbConn = psycopg2.connect(host=transConf['host'],
                               port=transConf['port'],
                               user=transConf['user'],
                               password=transConf['pwd'],
                               database=transConf['databases'])
transDao = transDbConn.cursor()

COORDINATE_FACTOR = 110000

# logger.remove()
log_path = "./log/push_mqt.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
MQR_BATCH_ID_NO_PARK_ACCESS = "MQRMQP2024112701"
MQR_BATCH_ID_EXIST_PARK_ACCESS = "MQRMQP2024112702"


def push_park_access(push_number=1000, strategy=None):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:

    """
    batch_id = MQR_BATCH_ID_NO_PARK_ACCESS if strategy == 'NO_PARK_ACCESS' else MQR_BATCH_ID_EXIST_PARK_ACCESS
    # 获取待推送情报主点
    park_access_intelligence_list = get_gate_front_park_access_intelligence('park_access_gate_front', status='INIT',
                                                                            strategy=strategy)
    if not park_access_intelligence_list:
        logger.info("无待推送情报")
        return
    park_access_intelligence_map = {}
    for item in park_access_intelligence_list:
        unique_id = f"{item[1]}_{item[9]}"
        if unique_id not in park_access_intelligence_map:
            park_access_intelligence_map[unique_id] = [item]
            continue
        park_access_intelligence_map[unique_id].append(item)
    # 开始推送
    pushed_park_access_list = []
    for unique_id, park_access_list in tqdm.tqdm(park_access_intelligence_map.items()):
        logger.info(f"开始推送:{unique_id}")
        face_id = park_access_list[0][1]
        if len(pushed_park_access_list) >= push_number:
            logger.info(f"本次推送结束,共推送:{len(pushed_park_access_list)}")
            return
        # 获取门前停车场面信息
        # park_area_list = get_park_intelligence(face_ids=[face_id])
        park_area_list = get_park_intelligence_v2(face_ids=[face_id], task_ids=[park_access_list[0][9]])
        if not park_area_list:
            logger.error(f"无门前停车场出入口情报推送:{face_id}")
            continue

        # 停车场信息查询
        gate_front_park_geom = park_area_list[0][1]
        poi_info = autocomplete.try_get_primary_poi(gate_front_park_geom)
        if not poi_info:
            logger.error(f"门前停车场未获取到名称地址信息:{face_id}")
            # continue
        name = address = poi_geom = ''
        if poi_info and poi_info.name:
            name = autocomplete._normalize_park_name(poi_info.name, 0)
            address = poi_info.address
            poi_geom = poi_info.point.wkt
        park_access_name, show_tag = get_park_access_name(name)
        # 出入口信息拼接
        data_list = []
        intelligence_ids = []
        park_area_list = get_mc_park_area_list(gate_front_park_geom)
        for park_access in park_access_list:
            link_id = park_access[8]
            node_id = park_access[7]
            park_access_geom = park_access[4]
            park_access_mc_sp = wkt.loads(gcj2mc(park_access_geom))
            source_id = park_access[5]
            poinx_x = park_access_mc_sp.coords[0][0]
            poinx_y = park_access_mc_sp.coords[0][1]
            # 大门推送
            direction_geom = gcj2mc(poi_geom) if poi_geom else gcj2mc(park_access[4])
            if link_id == '':
                data_list.append({
                    # suggest_* 开头的字段，新增时灌入建议值；纠错时，如果有建议值，则灌入建议值，没有可灌入线上值。
                    "data": {
                        # 透传字段可以放data下，不要和文档定义的key重复
                        "aoi": park_area_list,
                        "address": address,
                        # AOI面
                        "direction": direction_geom,  # 参考方位
                        "bid": "",  # 纠错点BID，新增可为空
                        "suggest_name": park_access_name,  # 名称建议值
                        "suggest_point": f"{poinx_x},{poinx_y}",  # 坐标建议值
                        "suggest_related_type": "DE大门",
                        # 关联类型，可选值：DE大门、LINK。值为DE大门时，灌入DE大门建议值；值为LINK时，灌入link建议值。
                        "suggest_gate": [{
                            "node_id": get_short_node_id_by_long_node_ids(node_id),
                            "point": gcj2mc(park_access_geom)
                        }],  # 大门建议值
                        "suggest_link_id": "",  # link建议值
                        "suggest_is_open": "开放"  # 开放属性建议值，可选范围：开放、不开放
                    },
                    "$tpl": "parking_entrance"  # 表示使用的标注模板，固定
                })
            else:
                data_list.append({
                    # suggest_* 开头的字段，新增时灌入建议值；纠错时，如果有建议值，则灌入建议值，没有可灌入线上值。
                    "data": {
                        # 透传字段可以放data下，不要和文档定义的key重复
                        "aoi": park_area_list,
                        "address": address,
                        # AOI面
                        "direction": direction_geom,  # 参考方位
                        "bid": "",  # 纠错点BID，新增可为空
                        "suggest_name": park_access_name,  # 名称建议值
                        "suggest_point": f"{poinx_x},{poinx_y}",  # 坐标建议值
                        "suggest_related_type": "DE大门",
                        # 关联类型，可选值：DE大门、LINK。值为DE大门时，灌入DE大门建议值；值为LINK时，灌入link建议值。
                        "suggest_gate": [],  # 大门建议值
                        "suggest_link_id": get_short_link_id_by_long_link_ids(link_id),  # link建议值
                        "suggest_is_open": "开放"  # 开放属性建议值，可选范围：开放、不开放
                    },
                    "$tpl": "parking_entrance"  # 表示使用的标注模板，固定
                })
            intelligence_ids.append(park_access[0])
        if len(data_list) == 0:
            return
        resp = push(source_id, batch_id, data_list)
        update_park_access_intelligence_status_by_id(intelligence_ids, status='PUSHED', resp=resp)
        # update_park_storefront_strategy_diff_status_by_face_id([face_id], 'GATE_DIFFED_REUSE')
        logger.info(f"推送成功:push_data:{data_list},resp:{resp}")
        pushed_park_access_list.append(data_list)


def get_mc_park_area_list(park_area_geom):
    """
    获取门前停车场面mc
    Args:
        park_area_geom:
    """
    park_mc_geom_list = []
    park_sp = wkt.loads(gcj2mc(park_area_geom))
    if park_sp.geom_type == "MultiPolygon":
        for park_area in park_sp.geoms:
            park_mc_geom_list.append(gcj2mc(park_area.wkt))
    else:
        park_mc_geom_list.append(gcj2mc(park_area_geom))
    return park_mc_geom_list


def gcj2mc(gcj_geom):
    """
    gcj 转mc
    Args:
        gcj_geom:

    Returns:

    """
    sql = f"""
        select st_astext(gcj2mc(st_geomfromtext('{gcj_geom}',4326))) from mesh_conf limit 1
    """
    buDao.execute(sql)
    return buDao.fetchone()[0]


def get_short_link_id_by_long_link_ids(long_link_id):
    """

    Args:
        long_link_id:

    Returns:

    """
    sql = f"select tid from image_r where sid = '{long_link_id}'"
    transDao.execute(sql)
    long_link_info = transDao.fetchone()
    if not long_link_info:
        print(f"未获取到长link信息:{long_link_id}")
        return None
    return long_link_info[0]


def get_short_node_id_by_long_node_ids(long_node_id):
    """

    Args:
        long_node_id:

    Returns:

    """
    sql = f"select tid from image_n where sid = '{long_node_id}'"
    transDao.execute(sql)
    long_node_info = transDao.fetchone()
    if not long_node_info:
        print(f"未获取到长link信息:{long_node_id}")
        return None
    return long_node_info[0]


def get_park_access_name(park_name):
    """
    获取停车场出入口名称
    Args:
        park_name:

    Returns:

    """
    park_access_name = park_name.replace("-门前停车场", "门前停车场")
    return f"{park_access_name}-入口", '入口'


def push(source_id, batch_id, park_access_list):
    """
    一体化下发
    Args:
        push_data_list:
        park_access_list:

    Returns:

    """
    data = {
        "src": 12,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": batch_id,  # 必填，批次号
        "main_poi_bid": '15731802024032445438',  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "from_src": "storefront_access",
        "qb_type": 2,
        "extra": {
            "detail": {
                'project': 'MQR',
                "inst_list": [{
                    "project": "MQR",  # 必填，批次类型，固定
                    "creator": "<EMAIL>",  # 必填，灌入人邮箱，如***********************
                    "priority": 5,  # 必填，重要度，和生产确认
                    "batch_id": batch_id,  # 必填，批次号，格式：项目简称+灌入人简称(3位)+日期+编号，编号支持2~11位，所有英文字母均为大写，如MQRZQP202405281035
                    "source_id": source_id,  # 必填，批次号，格式：项目简称+灌入人简称(3位)+日期+编号，编号支持2~11位，所有英文字母均为大写，如MQRZQP202405281035
                    "callback": "{\"method\":\"post\",\"url\":\""
                                "http://mapde-poi.baidu-int.com/prod/parking/manualCallback\"}",
                    # 回调地址示例，需灌入实际地址
                    "detail": {
                        "mark_id": source_id,  # 可选，可用来追踪case，支持类型： 1、bid:<$bid> 2、guid:<$guid> 3、自定义的id串
                        "items": {
                            "item_type": 4,  # 表示标注平台，固定
                            "contents": park_access_list,
                            "reference": {
                                "message": {
                                    "content": "根据情报点位核实门前停车场入口"  # 工艺提示，和生产确认
                                }
                            }
                        }
                    }
                }]
            }
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        # url='http://mapde-poi.baidu-int.com/pre/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


def get_gate_front_park_access_intelligence(type: str, status=None, face_ids=None, strategy=None):
    """
    获取的停车场情报
    Args:
        type:
        status:
        bids:

    Returns:

    """
    args = [type]
    sql = f"""
        select a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom),a.source_id,a.park_access_bid,a.node_id,a.link_id,
        a.outside_task_id
        from park_access_intelligence a
            left join park_storefront_intelligence b on a.outside_id = b.face_id
        where a.type = %s
    """
    if status:
        sql += f" and a.status = %s"
        args.append(status)
    if face_ids:
        sql += f" and a.outside_id = ANY(%s)"
        args.append(face_ids)
    if strategy:
        sql += f" and a.strategy = %s"
        args.append(strategy)
    poiOnlineDao.execute(sql, args)
    return poiOnlineDao.fetchall()


def get_park_intelligence(status=None, face_ids=None, batch=None):
    """
    获取已推送的停车场情报
    Args:
        status:
        face_ids:

    Returns:

    """
    args = []
    sql = f"""
        select face_id, st_astext(geom) from park_storefront_intelligence where 1=1
    """
    if status:
        sql += " and status = ANY(%s)"
        args.append(status)
    if face_ids:
        sql += " and face_id = ANY(%s)"
        args.append(face_ids)
    if batch:
        sql += " and batch = %s"
        args.append(batch)
    poiOnlineDao.execute(sql, args)
    return poiOnlineDao.fetchall()


def get_park_intelligence_v2(face_ids=None, task_ids=None):
    """
    获取已推送的停车场情报
    Args:
        status:
        face_ids:

    Returns:

    """
    args = []
    sql = f"""
        select face_id, st_astext(geom) from park_storefront_strategy where 1=1
    """
    if face_ids:
        sql += " and face_id = ANY(%s)"
        args.append(face_ids)
    if task_ids:
        sql += " and task_id = ANY(%s)"
        args.append(task_ids)
    poiOnlineDao.execute(sql, args)
    return poiOnlineDao.fetchall()


def update_park_access_intelligence_status_by_id(intelligence_ids, status, resp=''):
    """
    停车场出入口下线状态变更
    Args:
        intelligence_ids:
        resp:

    Returns:

    """
    sql = f"update park_access_intelligence set status=%s,resp=%s where id=ANY (%s)"
    poiOnlineDao.execute(sql, [status, resp, intelligence_ids])
    return poiOnlineDbConn.commit()


def update_park_storefront_strategy_diff_status_by_face_id(face_ids, status):
    """
    更新差分成果状态
    :param face_id:
    :param status:
    :return:
    """
    sql = f"update park_storefront_strategy_diff set status=%s where face_id=ANY(%s)"
    poiOnlineDao.execute(sql, [status, face_ids])
    return poiOnlineDbConn.commit()


def export_no_gate_park():
    """
    导出无出入口门前停车场的数据
    Returns:

    """
    sql = f"""
        select a.face_id,a.name,a.task_id,st_astext(b.geom) from park_storefront_access a 
            inner join park_storefront_strategy b on a.face_id=b.face_id and a.task_id=b.task_id 
        where a.created_at>='2024-12-17 10:00:00' 
            and a.conclusion='nogate' 
            and a.task_id in(select task_id from park_storefront_task 
                where batch in('beijing_haidian_20241206','beijing_except_haidian_20241208'))
    """
    poiOnlineDao.execute(sql)
    no_gate_park_list = poiOnlineDao.fetchall()
    with open('no_gate_park.csv', 'w', newline='') as f:
        for park in tqdm.tqdm(no_gate_park_list):
            face_id, name, task_id, geom = park
            link_list = autocomplete.get_road_relation(geom)
            if not link_list:
                continue
            for link_info in link_list:
                link_id, link_geom, center_point = link_info
                f.write(f"{face_id},{name},{task_id},{link_id},{center_point},{geom}\n")


if __name__ == '__main__':
    push_number = sys.argv[1]
    pv = sys.argv[2]
    strategy = sys.argv[3] if sys.argv[3] != '0' else None
    push_park_access(int(push_number), 'NO_PARK_ACCESS')
    push_park_access(int(push_number), strategy)
