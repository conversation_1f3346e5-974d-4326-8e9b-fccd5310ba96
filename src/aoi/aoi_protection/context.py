"""
context
"""
import dataclasses
from typing import Dict, List


@dataclasses.dataclass
class Data:
    """
    失效保护情报类
    """
    id: int
    bid: str
    src: str
    pv: int
    created_at: str
    updated_at: str
    std_tag: str
    memo: Dict = dataclasses.field(default_factory=dict)


@dataclasses.dataclass
class HumanHandleData:
    """
    人工作业结论, 目前只需要处理这些数据
    """
    wpf_task_id: str
    bid: str
    wpf_human_status: int
    qb_id: List[int] = dataclasses.field(default_factory=list)
