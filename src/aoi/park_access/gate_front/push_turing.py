# -*- coding: utf-8 -*-
"""
地上推送情报到人工作业
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/SWyXWRxQ4s5LK8
"""
import sys
import os
import time
import uuid
import shapely
from mapio.utils import bid2uid
import tqdm
import requests
from datetime import datetime
from shapely.geometry import Point, Polygon, mapping, shape, LineString
from shapely import wkt
from loguru import logger
from collections import defaultdict
import numpy as np

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi import utils
from src.aoi.park_access import park_utils
from src.aoi import PgQuery
import turing_work_ref
import math
import psycopg2
import pymysql

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
road_dao = Road()
trans_dao = Trans()
dao = PgQuery.PgQuery()

# logger.remove()
log_path = "./log/overground_intelligence_push.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
TCQ_BATCH_ID = "TCQDSH2024123001"


def push_add_intelligence(push_number=10, strategy=None, parent_pv=0, limit_collect=True):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:
    """
    # 如果策略不为空,获取策略所有BID聚合推送
    park_bids = None
    # park_bids = [
    #     '6670149030014311292',
    # ]
    if strategy:
        park_access_intelligence_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
            'park_access_gate_front', status='INIT', strategy=strategy, pv=parent_pv)
        if not park_access_intelligence_list:
            logger.info("无待推送情报")
            return
        park_bids = [item['bid'] for item in park_access_intelligence_list]

    # 获取待推送情报主点
    park_access_intelligence_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
        'park_access_gate_front', status='INIT', bids=park_bids, pv=parent_pv)

    # 获取更新的停车场
    update_parks = poi_online_query.get_park_storefront_result(type='update', status='PARK_ACCESS_INTELLIGENCE')
    print(f"更新停车场：{len(update_parks)}")

    if not update_parks:
        update_parks = []
    if not park_access_intelligence_list and not update_parks:
        logger.info("无待推送情报")
        return

    park_access_intelligence_map = {}
    for park_access_intelligence in park_access_intelligence_list:
        if park_access_intelligence['bid'] not in park_access_intelligence_map:
            park_access_intelligence_map[park_access_intelligence['bid']] = [park_access_intelligence]
        else:
            park_access_intelligence_map[park_access_intelligence['bid']].append(park_access_intelligence)

    for an_update_park in update_parks:
        # 如果更新没有新挖掘到情报，也把现有的成果推送到平台核实
        if an_update_park['bid'] in park_access_intelligence_map:
            continue
        park_access_intelligence_map[an_update_park['bid']] = []

    # 开始推送
    pushed_park_access_list = []
    for park_bid, park_access_list in tqdm.tqdm(park_access_intelligence_map.items()):
        if len(pushed_park_access_list) >= push_number:
            logger.info(f"本次推送结束,共推送:{len(pushed_park_access_list)}")
            return

        # 获取门前停车场面信息
        park_area_list = poi_online_query.get_park_storefront_result(bids=[park_bid])
        if not park_area_list:
            logger.error(f"无门前停车场出入口情报推送:{park_bid}")
            continue

        park_info = park_area_list[0]
        if _is_test_park(park_info):
            logger.info(f"{park_info['bid']} 是试生产停车场数据，不在正式环境推送")
            continue

        if park_info['track_url'] == '':
            logger.error(f"还没有挖掘轨迹，先不推:{park_bid}")
            continue
        if limit_collect and not park_info['zhongyuan_complete']:
            logger.error(f"采集资料未准备就绪，先不推:{park_bid}")
            continue

        track_uids = park_info['track_ids']
        # 获取不包括 下/将下 线的出入口
        old_accesses = poi_online_query.get_park_storefront_prod_access_list(
            parent_bids=[park_bid], not_statuses=['READY_OFFLINE', 'ALREADY_OFFLINE'])

        if len(old_accesses) + len(park_access_list) == 0:
            logger.error(f"没有旧成果，也没有新增情报:{park_bid}")
            continue

        if len(old_accesses) > 0:
            # 已有出入口成果，投更新
            print(f"{park_bid}, 已有出入口成果：{len(old_accesses)}: {len(park_access_list)}，投更新")
            resp = push_update(park_info, park_access_list, old_accesses)
        else:
            # 获取行政区划信息
            admin = get_admin_by_geom(park_info["geom"])
            # 获取推送出入口信息
            park_access_list = _sort_access_infos(park_info, park_access_list)
            push_park_access_list = get_add_park_access_geojson(park_access_list)
            # 开始推送
            source_id = f"park_storefront_141011_strategy_{park_access_list[0]['source_id']}"
            # 历史出入口成果
            old_park_access_list = poi_online_query.get_park_storefront_prod_access_list(parent_bids=[park_bid])
            refgeo = _gen_refgeo(old_park_access_list) if old_park_access_list else []
            resp = push(source_id, admin['city_name'], admin['province_ch'], park_info["geom"], park_info["name"],
                        park_bid,
                        push_park_access_list, refgeo=refgeo, track_uids=track_uids, url=park_info['track_url'])

        pushed_park_access_list.append(park_bid)
        intelligence_ids = [item['id'] for item in park_access_list]
        if len(intelligence_ids) > 0:
            poi_online_rw.update_park_access_intelligence_outside_id_by_id(intelligence_ids, resp, 'turing')
        poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status(
            from_status='PARK_ACCESS_INTELLIGENCE',
            to_status='PARK_ACCESS_INTELLIGENCE_PUSHED',
            park_bids=[park_bid])


def _is_test_park(park_info: dict) -> bool:
    """
    是否是测试，试生产停车场，是返回 True
    """
    return any(x in park_info['bid'] for x in ['bak', 'test'])


def _sort_access_infos(park_info: dict, access_infos: list) -> list:
    """
    为出入口情报排序
    """
    line = shapely.wkt.loads(park_info['central_line'])
    line = extend_linestring(line, 50 * 1e-5)

    def point_position_ratio(item):
        pt = shapely.wkt.loads(item['geom'])
        proj_dist = line.project(pt)
        return proj_dist / line.length if line.length > 0 else 0

    return sorted(access_infos, key=point_position_ratio)


def extend_linestring(line: LineString, dist: float) -> LineString:
    """
    延长 LineString 的两端，根据指定的距离
    :param line: 待延长的 LineString
    :param dist: 需要延长的距离
    :return: 返回延长后的 LineString
    """

    def extend_point(p0: Point, p1: Point):
        vec = np.array([p1.x - p0.x, p1.y - p0.y])
        vec = vec / np.linalg.norm(vec)
        vec = vec * dist
        return Point(p1.x + vec[0], p1.y + vec[1])

    points = [Point(x, y) for x, y in line.coords]
    points = [extend_point(points[1], points[0]), *points[1:-1], extend_point(points[-2], points[-1])]
    return LineString(points)


def push_update(park_info: dict, intelligences: list, old_accesses: list) -> str:
    """
    推送更新情报
    """
    park_bid = park_info['bid']
    push_park_access_list = get_update_park_access_geojson_by_accesses(old_accesses)
    push_park_access_list += get_update_park_access_geojson_by_intelligences(intelligences)

    # 开始推送
    admin = get_admin_by_geom(park_info["geom"])
    source_id = f"park_storefront_update_{uuid.uuid4().hex}_{park_bid}"
    return push(source_id, admin['city_name'], admin['province_ch'], park_info["geom"], park_info["name"], park_bid,
                push_park_access_list, data_type=1213, track_uids=park_info['track_ids'])


def get_update_park_access_geojson_by_accesses(accesses: list) -> list:
    """
    组装更新的出入口信息，已有的出入口成果
    """
    push_park_access_list = []
    for item in accesses:
        push_park_access_map = {
            'bid': item['bid'],
            'unique_id': str(item['id']),
            "entry_verification_conclusion": None,
            "relation_type": None,
            "suggest_is_open": item['suggest_is_open'],
            "expanded": True,
            "link_info": [],
            "gate_info": [],
            "point_info": [],
            "entry_verify_type": 2,
            "entry_check_memo": item['check_memo'] if item['check_memo'] is not None else '',
        }

        park_access_sp = wkt.loads(utils.gcj_to_bd09(item['geom']))
        push_park_access_map['point_info'] = [park_access_sp.x, park_access_sp.y]

        road_relation = item['road_relation']
        if road_relation and "link_info" in road_relation and road_relation['link_info']:
            # 推送LINK
            if road_relation['link_info'][0]['type'] == 2 and road_relation['link_info'][0]['link_id'] != '':
                short_link_id = road_relation['link_info'][0]['link_id']
                long_link_list = trans_dao.get_long_link_by_short_link_ids([short_link_id])
                if long_link_list:
                    long_link_id = long_link_list[0][0]
                    link_info_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
                    if link_info_list:
                        link_bd09_sp = wkt.loads(utils.gcj_to_bd09(link_info_list[0]['geom']))
                        link_info_geojson_list = mapping(link_bd09_sp),
                        link_info_map = dict(link_info_geojson_list[0])
                        link_info_map['properties'] = {}
                        link_info_map['properties']['id'] = long_link_id
                        link_info_map['properties']['link_id'] = long_link_id
                        push_park_access_map['link_info'] = [link_info_map]
                        push_park_access_map['relation_type'] = 2
                        push_park_access_list.append(push_park_access_map)
                        continue
            # 推送Node
            if road_relation['link_info'][0]['type'] == 1 and road_relation['link_info'][0]['node_id'] != '':
                short_node_id = road_relation['link_info'][0]['node_id']
                long_node_list = trans_dao.get_long_node_by_short_node_ids([short_node_id])
                if long_node_list:
                    node_list = road_dao.get_node_geom([long_node_list[0][0]])
                    gate_list = road_dao.get_nav_gate_by_node_id(long_node_list[0][0])
                    if node_list and gate_list:
                        node_bd09_sp = wkt.loads(utils.gcj_to_bd09(node_list[0][0]))
                        node_info_geojson_list = mapping(node_bd09_sp),
                        node_info_map = dict(node_info_geojson_list[0])
                        node_info_map['properties'] = {}
                        node_info_map['properties']['id'] = long_node_list[0][0]
                        node_info_map['properties']['node_id'] = long_node_list[0][0]
                        node_info_map['properties']['gate_id'] = gate_list[0]['gate_id']
                        push_park_access_map['gate_info'] = [node_info_map]
                        push_park_access_map['relation_type'] = 1
                        push_park_access_list.append(push_park_access_map)
                        continue

        # 缺少关联关系 推送点位
        push_park_access_map['relation_type'] = 3
        push_park_access_list.append(push_park_access_map)
    return push_park_access_list


def get_update_park_access_geojson_by_intelligences(intelligences: list) -> list:
    """
    组装更新的出入口信息，根据情报
    """
    resp = get_add_park_access_geojson(intelligences)
    for item in resp:
        item['entry_verification_conclusion'] = 0
        item['entry_verify_type'] = 1
        item['entry_check_memo'] = ''
    return resp


def get_add_park_access_geojson(park_access_list):
    """
    组装推送的出入口信息
    Args:
        park_access_list:
    Returns:

    """
    # 组装出入口信息
    push_park_access_list = []
    for item in park_access_list:
        push_park_access_map = {
            'bid': '',
            'unique_id': item['source_id'],
            "entry_verification_conclusion": None,
            "relation_type": None,
            "suggest_is_open": 0,
            "expanded": True,
            "link_info": [],
            "gate_info": [],
            "point_info": [],
        }
        # 推送LINK
        link_list = road_dao.get_nav_link_by_link_ids_new([item['link_id']]) if item['link_id'] else None
        if item['link_id'] != '' and link_list:
            link_res = link_list[0]
            link_bd09_sp = wkt.loads(utils.gcj_to_bd09(link_res['geom']))
            link_info_geojson_list = mapping(link_bd09_sp),
            link_info_map = dict(link_info_geojson_list[0])
            link_info_map['properties'] = {}
            link_info_map['properties']['id'] = link_res['link_id']
            link_info_map['properties']['link_id'] = link_res['link_id']
            push_park_access_map['link_info'] = [link_info_map]
            push_park_access_map['relation_type'] = 2

        # 推送大门
        node_list = road_dao.get_node_geom([item['node_id']]) if item['node_id'] else None
        if item['link_id'] == '' and item['node_id'] != '' and node_list:
            node_res = node_list[0]
            gate_list = road_dao.get_nav_gate_by_node_id(node_res[1])
            node_bd09_sp = wkt.loads(utils.gcj_to_bd09(node_res[0]))
            node_info_geojson_list = mapping(node_bd09_sp),
            node_info_map = dict(node_info_geojson_list[0])
            node_info_map['properties'] = {}
            node_info_map['properties']['id'] = node_res[1]
            node_info_map['properties']['node_id'] = node_res[1]
            node_info_map['properties']['gate_id'] = gate_list[0]['gate_id']
            push_park_access_map['gate_info'] = [node_info_map]
            push_park_access_map['relation_type'] = 1

        # 推送点位
        park_access_sp = wkt.loads(utils.gcj_to_bd09(item['geom']))
        point_info = [park_access_sp.x, park_access_sp.y]
        push_park_access_map['point_info'] = point_info
        push_park_access_list.append(push_park_access_map)
    return push_park_access_list


def get_check_park_access_geojson(park_access_list):
    """
    组装清查推送的出入口信息
    Args:
        park_access_list:

    Returns:

    """
    # 组装出入口信息
    push_park_access_list = []
    for item in park_access_list:
        push_park_access_map = {
            'bid': item['bid'],
            'unique_id': item['source_id'],
            "entry_verification_conclusion": None,
            "relation_type": None,
            "suggest_is_open": 0,
            "expanded": True,
            "link_info": [],
            "gate_info": [],
            "point_info": [],
        }
        # 获取出入口关联关系
        park_access_list = master_back_dao.get_parking_by_bids(item['bid'])
        if not park_access_list:
            logger.error(f"母库不存在出入口数据:{item['bid']}")
            continue
        if park_access_list[0][2] == 1:
            push_park_access_map['suggest_is_open'] = 1
        road_relation = park_access_list[0][1]
        if road_relation and "link_info" in road_relation and road_relation['link_info']:
            # 推送LINK
            if road_relation['link_info'][0]['type'] == 2 and road_relation['link_info'][0]['link_id'] != '':
                short_link_id = road_relation['link_info'][0]['link_id']
                long_link_list = trans_dao.get_long_link_by_short_link_ids([short_link_id])
                if long_link_list:
                    long_link_id = long_link_list[0][0]
                    link_info_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
                    if link_info_list:
                        link_bd09_sp = wkt.loads(utils.gcj_to_bd09(link_info_list[0]['geom']))
                        link_info_geojson_list = mapping(link_bd09_sp),
                        link_info_map = dict(link_info_geojson_list[0])
                        link_info_map['properties'] = {}
                        link_info_map['properties']['id'] = long_link_id
                        link_info_map['properties']['link_id'] = long_link_id
                        push_park_access_map['link_info'] = [link_info_map]
                        continue
            # 推送Node
            if road_relation['link_info'][0]['type'] == 1 and road_relation['link_info'][0]['node_id'] != '':
                short_node_id = road_relation['link_info'][0]['node_id']
                long_node_list = trans_dao.get_long_node_by_short_node_ids([short_node_id])
                if long_node_list:
                    node_list = road_dao.get_node_geom([long_node_list[0][0]])
                    gate_list = road_dao.get_nav_gate_by_node_id(long_node_list[0][0])
                    if node_list and gate_list:
                        node_bd09_sp = wkt.loads(utils.gcj_to_bd09(node_list[0][0]))
                        node_info_geojson_list = mapping(node_bd09_sp),
                        node_info_map = dict(node_info_geojson_list[0])
                        node_info_map['properties'] = {}
                        node_info_map['properties']['id'] = long_node_list[0][0]
                        node_info_map['properties']['node_id'] = long_node_list[0][0]
                        node_info_map['properties']['gate_id'] = gate_list[0]['gate_id']
                        push_park_access_map['gate_info'] = [node_info_map]
                        push_park_access_map['relation_type'] = 1
                        continue
        # 缺少关联关系 推送点位
        park_access_sp = wkt.loads(utils.gcj_to_bd09(item['geom']))
        point_info = [park_access_sp.x, park_access_sp.y]
        push_park_access_map['point_info'] = point_info
        push_park_access_list.append(push_park_access_map)
    return push_park_access_list


def get_bd09_park_area_json_list(park_area_geom):
    """
    获取门前停车场面mc
    Args:
        park_area_geom:
    """
    park_mc_geom_list = []
    park_sp = wkt.loads(park_area_geom)
    if park_sp.geom_type == "MultiPolygon":
        for park_area in park_sp.geoms:
            bd09_park_area = utils.gcj_to_bd09(park_area.wkt)
            bd09_park_area_sp = wkt.loads(bd09_park_area)
            park_mc_geom_list.append(area_geo2map(Polygon(bd09_park_area_sp)))
    else:
        bd09_park_area = utils.gcj_to_bd09(park_area_geom)
        bd09_park_area_sp = wkt.loads(bd09_park_area)
        park_mc_geom_list.append(area_geo2map(Polygon(bd09_park_area_sp)))
    return park_mc_geom_list


def area_geo2map(geom) -> dict:
    """
    Args:
        geom:

    Returns:

    """
    return {
        "type": "Feature",
        "geometry": mapping(geom),
        "properties": {
            'color': '#33FF00'
        },
    }


def get_admin_by_geom(geom: str) -> dict:
    """
    获取行政区划
    """
    res = master_back_dao.get_bad_admin_by_geom(geom)
    if not res:
        raise Exception(f"获取行政区划失败")
    return {
        'province_ch': res[0],
        'city_name': res[1],
    }


def push(source_id, city, province, park_area, park_name, park_bid, park_access_list, data_type=1210, refgeo=[],
         track_uids=[], url=None):
    """
    推送门前停车场入口核实
    Args:
        source_id:
        city:
        province:
        park_area:

    Returns:

    """
    sign = 'd33d20fb851173a80b2e571fd5828668'
    if int(data_type) == 1213:
        sign = 'da8ada28a286ab02fa4140028d186834'

    uids = get_top_pv50_street_uids(park_area)
    # end_traj_list = get_end_traj_list(park_area)

    collect_tasks = _get_collect_data_info(park_bid, park_area)
    if collect_tasks is None or len(collect_tasks) <= 0:
        logger.info(f"{park_bid},无采集数据")
    else:
        logger.info(f"{park_bid},有采集数据")

    param = {
        "sourceid": source_id,
        "cityname": city,
        "sign": sign,
        "source": data_type,
        "platform": 0,
        "content": "门前停车场入口核实",
        "_signkeys": "platform,source",
        "subbusinesstype": 408,
        "provname": province,
        "problem": 0,
        "occurtime": int(time.time()),
        "coordsys": "bd09ll",
        "geo": get_bd09_park_area_json_list(park_area),
        "refgeo": refgeo,
        "properties": {
            "need_giving_back": {
                "data_type": data_type
            },
            "uids": uids if uids else [],
            "main_poi_bid": park_bid,
            "parking_lot_name": park_name,
            "sub_info": park_access_list,
            # "user_geo": end_traj_list,
            "tasks": collect_tasks,
        },
    }
    if url is not None:
        param['url'] = url
    if track_uids is not None:
        param['properties']['track_uids'] = track_uids
    # url = "http://10.169.24.138:80/stargaze/api/addintelligence" # 测试环境
    url = "http://10.169.24.137:80/stargaze/api/addintelligence"  # 正式环境
    # print(url)
    # print(json.dumps(param, ensure_ascii=False))
    # exit()

    try:
        logger.info(f"推送门前停车场入口核实:{param}")
        res = requests.post(url, json=param)
        """
        {'errno': 0, 'errmsg': 'success', 'data': {'id': '676cc6e22d4c5da4e56c4d51', 'duplicate': 0}}
        """
        data = res.json()
        logger.info(f"推送门前停车场入口核实结果:{data}")
        if data['errno'] == 0:
            return data['data']['id']
        return ''
    except Exception as e:
        raise Exception(f"推送失败:{e}")


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    return pymysql.connect(host='*************', port=int(5730), user='bee_flow',
                           password='nl4c/mqeTcsgpH', db='bee_flow', charset="utf8mb4")


def _get_callback_count(task_ids):
    beeflow_connection = _create_beeflow_connection()
    sql = f"select count(distinct task_id) from collect_task_callback_records where task_id in ({task_ids})"
    with beeflow_connection.cursor() as cursor:
        cursor.execute(sql)
        res = cursor.fetchone()
        return int(res[0])


def _get_callback_info(task_id):
    beeflow_connection = _create_beeflow_connection()
    sql = f"select task_id, content from collect_task_callback_records where task_id='{task_id}' group by task_id"
    with beeflow_connection.cursor() as cursor:
        cursor.execute(sql)
        res = cursor.fetchone()
        return res


def _sample_pics_by_time_and_distance(collected_pics, time_interval=2, distance_threshold=5):
    if not collected_pics:
        return []

    # 先按时间排序
    collected_pics.sort(key=lambda x: x['time'])

    # 步骤1：按时间2秒筛选
    time_filtered = []
    last_time = None
    for pic in collected_pics:
        if last_time is None or pic['time'] - last_time >= time_interval:
            time_filtered.append(pic)
            last_time = pic['time']

    # 步骤2：再按距离筛选
    distance_filtered = []
    last_pic = None
    for pic in time_filtered:
        if last_pic is None:
            distance_filtered.append(pic)
            last_pic = pic
        else:
            dist = _haversine(last_pic['my_x'], last_pic['my_y'], pic['my_x'], pic['my_y'])
            if dist >= distance_threshold:
                distance_filtered.append(pic)
                last_pic = pic

    return distance_filtered


def _haversine(lon1, lat1, lon2, lat2):
    """
    使用 haversine 公式计算地球上两点之间的距离（单位：米）。

    参数:
        lon1 (float): 第一个点的经度。
        lat1 (float): 第一个点的纬度。
        lon2 (float): 第二个点的经度。
        lat2 (float): 第二个点的纬度。

    返回:
        float: 两点之间的距离（单位：米）。
    """
    # 地球半径（单位：米）
    R = 6371000

    # 将经纬度转换为弧度
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

    # 计算差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1

    # haversine 公式
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    # 计算距离
    distance = R * c
    return distance


def _get_collect_data_info(park_bid: str, area: str):
    try:
        poiOnlineConf = {"db": "poi_online_rw", "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
                         "host": "*************",
                         "port": 8032}

        poiOnlineDbConn = psycopg2.connect(database=poiOnlineConf["db"],
                                           user=poiOnlineConf["user"],
                                           password=poiOnlineConf["pwd"],
                                           host=poiOnlineConf["host"],
                                           port=poiOnlineConf["port"])
        poiOnlineDao = poiOnlineDbConn.cursor()
        result = []
        grouped_pics = defaultdict(list)

        sql = f"select data from parking_collect_data_history where source_id = '{park_bid}'"
        poiOnlineDao.execute(sql)
        history = poiOnlineDao.fetchall()
        if len(history) > 0:
            for item in history:
                # 获取第一个item中的时间戳
                first_timestamp = item[0][0]['time'] // 1000
                source = 'newest'
                if int(datetime.now().timestamp()) - first_timestamp > 30 * 24 * 3600:
                    source = 'secnew'

                for pic in item[0]:
                    cdid = pic['cdid']
                    point_bd09 = utils.gcj_to_bd09(f"POINT({pic['x']} {pic['y']})")
                    point_bd09_geo = shapely.wkt.loads(point_bd09)
                    x, y = point_bd09_geo.x, point_bd09_geo.y
                    bos_key = pic['bos_key']
                    pic_url = f"http://m.map.baidu.com:8011/{bos_key}"
                    one_pic = {
                        "north": pic['north'],
                        "cdid": cdid,
                        "time": pic['time'] // 1000,  # timestamp
                        "x": x,  # 坐标经度（BD09）
                        "y": y,  # 坐标经度（BD09）
                        "pic_url": pic_url,
                        "selftag": "history",
                        "bos_key": bos_key,
                        "source": source,
                    }
                    grouped_pics[cdid].append(one_pic)

        for cdid, pics in grouped_pics.items():
            pics.sort(key=lambda x: x['time'])

            collected_pics = [p for p in pics if p['selftag'] == 'collected']
            history_pics = [p for p in pics if p['selftag'] == 'history']

            sampled_collected = []
            if len(collected_pics) > 0:
                points = [(coord['my_x'], coord['my_y']) for coord in collected_pics]
                # points = [(coord['x'], coord['y']) for coord in collected_pics]
                lines = LineString(points)
                geom = wkt.loads(area)
                if not geom.intersects(lines):
                    # print(f"不在区域内, {geom}, {lines}")
                    continue

                sampled_collected.append(collected_pics[0])
                if len(collected_pics) > 1:
                    sampled_collected = _sample_pics_by_time_and_distance(collected_pics)

            combined_pics = sampled_collected + history_pics
            combined_pics.sort(key=lambda x: x['time'])

            result.append({
                "pics": combined_pics
            })
        return result
    except Exception as e:
        print("Error occurred while fetching data:", e)
        return []


def get_top_pv50_street_uids(park_area):
    """
    获取toppv50的uid
    Returns:

    """
    uids = []
    bids = park_utils.get_near_street_poi_by_area(park_area)
    if not bids:
        return uids
    poi_list = poi_online_query.get_poi_by_bids_kv(bids)
    if not poi_list:
        return uids
    bids = [poi['bid'] for poi in sorted(poi_list, key=lambda x: x['click_pv'], reverse=True)][:50]
    uids = [bid2uid(int(bid)) for bid in bids]
    return uids


def push_repaired(park_info, repaired_accesses: list, effected_accesses: list) -> str:
    """
    推送待修复的数据
    """
    push_accesses = get_update_park_access_geojson_by_accesses(repaired_accesses)
    refgeo = _gen_refgeo(effected_accesses)

    park_bid = park_info['bid']
    # 开始推送
    admin = get_admin_by_geom(park_info["geom"])
    source_id = f"park_storefront_repaired_{uuid.uuid4().hex}_{park_bid}"
    return push(source_id, admin['city_name'], admin['province_ch'], park_info["geom"], park_info["name"], park_bid,
                push_accesses, data_type=1213, refgeo=refgeo)


def _gen_refgeo(old_accesses: list):
    refgeo = []
    for access in old_accesses:
        road_relation = access['road_relation']
        _wkt = access['geom']
        if 'link_info' in road_relation and road_relation['link_info'][0]['type'] == 2:
            long_link_ids = trans_dao.get_long_link_by_short_link_ids([road_relation['link_info'][0]['link_id']])
            nav_links = dao.get_nav_link_by_link_id_form([item[0] for item in long_link_ids])
            if nav_links and len(nav_links[0]) > 3:
                _wkt = nav_links[0][3]
            else:
                print(f"不符合预期, link 失效：{access}")
        bd09_wkt = utils.gcj_to_bd09(_wkt)
        refgeo.append(area_geo2map(shapely.wkt.loads(bd09_wkt)))
    return refgeo


def push_wait_repaired_access():
    """
    推送待修复的出入口数据
    """
    wait_repaired = poi_online_query.get_repaired(
        status='INIT', reason='Repeated_Access', prev_tab='park_storefront_prod_parking')
    for item in tqdm.tqdm(wait_repaired):
        args = item['args']
        if 'Repeated_Access' not in args:
            continue
        data = args['Repeated_Access']

        park = poi_online_query.get_park_storefront_result(id=int(item['prev_id']))[0]
        error = []
        if len(data['err_ids']) > 0:
            error = poi_online_query.get_park_storefront_prod_access_list(ids=data['err_ids'])
        right = []
        if len(data['eff_ids']) > 0:
            right = poi_online_query.get_park_storefront_prod_access_list(ids=data['eff_ids'])
        repaired_ids = args['repaired_ids'] + [item['id']]

        if len(error) == 0:
            print(f"{item['prev_id']} 没有异常的出入口")
            continue

        resp = push_repaired(park, error, right)
        if resp == '':
            print(f"{item['prev_id']} 推送失败")
            continue

        poi_online_rw.update_repaired_user_and_status(
            repaired_ids=repaired_ids,
            user_id=resp,
            user_name='turing',
            status='ING',
        )
        poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status(
            from_status='WAIT_REPAIR',
            to_status='REPAIRING',
            park_bids=[park['bid']]
        )


def push_access_checked_failed():
    """
    推送出入口质检失败的数据
    """
    wait_repaired = poi_online_query.get_repaired(
        status='INIT',
        reason='ACCESS_CHECK_FAILED',
        prev_tab='park_storefront_prod_parking',
        username='turing',
    )
    for item in tqdm.tqdm(wait_repaired):
        park = poi_online_query.get_park_storefront_result(id=int(item['prev_id']))[0]
        args = item['args']
        error = []
        if len(args['err_ids']) > 0:
            error = poi_online_query.get_park_storefront_prod_access_list(ids=args['err_ids'])
        right = []
        if len(args['eff_ids']) > 0:
            right = poi_online_query.get_park_storefront_prod_access_list(ids=args['eff_ids'])
        repaired_ids = args['repaired_ids'] + [item['id']]

        if len(error) == 0:
            print(f"{item['prev_id']} 没有异常的出入口")
            continue

        resp = push_repaired(park, error, right)
        if resp == '':
            print(f"{item['prev_id']} 推送失败")
            continue

        poi_online_rw.update_repaired_user_and_status(
            repaired_ids=repaired_ids,
            user_id=resp,
            user_name='turing',
            status='ING',
        )
        poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status(
            from_status='WAIT_REPAIR',
            to_status='REPAIRING',
            park_bids=[park['bid']]
        )


def get_end_traj_list(park_area):
    """
    获取终点轨迹
    Args:
        park_area:

    Returns:

    """
    end_taj_geojson_list = []
    bids = park_utils.get_near_street_poi_by_area(park_area)
    if not bids:
        return end_taj_geojson_list
    end_traj_list = turing_work_ref.end_traj_ref(bids, park_area)
    for traj_sp in end_traj_list:
        if traj_sp.geom_type not in ['MultiLineString', 'LineString']:
            logger.error(f"终点轨迹类型错误:{traj_sp.geom_type}")
            continue
        if traj_sp.geom_type == "MultiLineString":
            for line in traj_sp.geoms:
                end_taj_geojson_list.append({
                    "type": "Feature",
                    "geometry": mapping(utils.gcj_to_bd09(line)),
                    "properties": {},
                })
            continue
        end_taj_geojson_list.append({
            "type": "Feature",
            "geometry": mapping(utils.gcj_to_bd09(traj_sp.wkt)),
            "properties": {},
        })
    return end_taj_geojson_list


if __name__ == '__main__':
    push_number = sys.argv[1]
    pv = sys.argv[2]
    strategy = sys.argv[3] if sys.argv[3] != '0' else None
    limit_collect = True
    if len(sys.argv) > 5 and sys.argv[5] == '0':
        limit_collect = False
    print(f"是否限制采集资料：{limit_collect}")

    push_add_intelligence(int(push_number), strategy, pv, limit_collect)
    # push_check_intelligence(int(push_number), strategy, pv)
    if len(sys.argv) > 4 and sys.argv[4] == 'repeated_access':
        # 推重复出入口
        push_wait_repaired_access()
    if len(sys.argv) > 4 and sys.argv[4] == 'check_failed_access':
        # 推质检失败的出入口
        push_access_checked_failed()
