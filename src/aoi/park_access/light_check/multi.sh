#!/usr/bin/env bash
# 启动命令  bash multi.sh 

mkdir -p /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids
#rm -rf /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids/*

# 路侧集合
export PGPASSWORD="poi_aoi_rw"  # 设置密码
# 投放情报
# psql -Upoi_aoi_rw -p 8032 -h 10.56.135.223 -dpoi_online_rw -c "\copy (select distinct(a.bid) from park_access_intelligence a  left join park_online_data b on a.bid = b.bid  left join park_access_light_check c on a.id = c.intelligence_id  where  b.std_tag not like '%服务区%' and a.status = 'INIT' and a.type = 'park_access_overground_add' and c.id is null) to '/home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids/bids.csv' with csv"
# 已投放情报 重跑
# psql -Upoi_aoi_rw -p 8032 -h 10.56.135.223 -dpoi_online_rw -c "\copy ( select distinct(park_bid) from park_access_light_check where  status < 11) to '/home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids/bids.csv' with csv"
# psql -Upoi_aoi_rw -p 8032 -h 10.56.135.223 -dpoi_online_rw -c "\copy ( select distinct(park_bid) from park_access_intelligence where status = ‘INIT’ and type = 'park_access_underground_add' limit 10000) to '/home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids/bids.csv' with csv"
psql -Upoi_aoi_rw -p 8032 -h 10.56.135.223 -dpoi_online_rw -c "\copy (select distinct(bid) from park_access_intelligence where status = 'INIT' and type in ('park_access_overground_out_add', 'park_access_underground_add')) to '/home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids/bids.csv' with csv"


unset PGPASSWORD  # 执行后清除环境变量（可选）


cd /home/<USER>/chengqiong/www/aoi-strategy-master/aoi-strategy/src/aoi/park_access/light_check/bids
# 要分割的文件
INPUT_FILE="bids.csv"
# 获取文件的总行数
TOTAL_LINES=$(wc -l < "$INPUT_FILE")
# 计算每个分割文件大致应该包含的行数（注意：这里不处理余数）
LINES_PER_SPLIT=$(( (TOTAL_LINES + 19) / 20))  # 向上取整到最近的整数（加9再除以10相当于向上取整，当TOTAL_LINES能被10整除时结果不变）
# 使用 split 按行数分割文件，并指定数字格式的输出文件名
split -d -l "$LINES_PER_SPLIT" "$INPUT_FILE" bids

cd ../
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids00 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids01 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids02 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids03 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids04 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids05 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids06 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids07 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids08 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids09 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids10 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids11 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids12 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids13 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids14 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids15 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids16 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids17 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids18 0 &
nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids19 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids20 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids21 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids22 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids23 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids24 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids25 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids26 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids27 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids28 0 &
# nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids29 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids30 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids31 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids32 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids33 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids34 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids35 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids36 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids37 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids38 0 &
# # nohup /home/<USER>/chengqiong/conda_py39/bin/python3 choose_img.py bids/bids39 0 &
