[loggers]
keys=root, err

[handlers]
keys=<PERSON><PERSON><PERSON><PERSON>,file<PERSON><PERSON><PERSON>,err<PERSON><PERSON><PERSON>

[formatters]
keys=fmt

[logger_root]
level=INFO
handlers=fileHandler

[logger_err]
level=ERROR
handlers=errHandler
qualname=errlog

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=fmt
args=(sys.stdout,)

[handler_fileHandler]
class=logging.handlers.TimedRotatingFileHandler
level=DEBUG
formatter=fmt
args=('/home/<USER>/aoi-strategy/log/release/aoi-to-pds.log',)

[handler_errHandler]
class=logging.handlers.TimedRotatingFileHandler
level=ERROR
formatter=fmt
args=('/home/<USER>/aoi-strategy/log/release/err.log',)

[formatter_fmt]
format=%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
datefmt=%Y-%m-%d %H:%M:%S
