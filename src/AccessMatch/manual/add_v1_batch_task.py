"""
添加1.0的策略匹配任务
"""
from src.common import pgsql
from src.door_naming.strategy_v2 import common


def add_task(bid):
    """
    添加1.0自动批处理任务
    :param bid:
    :return:
    """
    aoi_info = common.get_aoi_by_main_bid(bid)
    if not aoi_info:
        print(f'not found aoi info for {bid}')
        return
    face_id = aoi_info.face_id

    sql = 'insert into gate_match_intel_src( face_id, bid, src ) values (:d1, :d2, :d3)'
    params = {"d1": face_id, "d2": bid, "d3": '手动插入触发'}
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, params, try_times=5)
