# -*- coding: utf-8 -*-
"""
出入口失效LINK
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
from math import ceil

import tqdm
import requests
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi import utils
from src.aoi.park_access import park_utils

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()
traj_dao = DestTraj()

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_relatedness.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_relatedness.txt"


def main(bids: list):
    """
    失效高通量Link处理
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids.tolist())
    for park in tqdm.tqdm(park_list):
        try:
            if park['park_spec'] == 1 or park['show_tag'] == '门前停车场':
                logger.info(f"{park['bid']} 临街场景不处理")
                continue
            logger.info(f"\n{park['bid']} 开始处理")
            road_relation_json = park['road_relation']
            if not road_relation_json or road_relation_json['link_info'][0]['type'] != 1:
                continue
            point_mc_xy = road_relation_json['link_info'][0]['point'].split(",")
            point_gcj_xy = utils.mc_to_gcj(f"point({point_mc_xy[0]} {point_mc_xy[1]})")
            point_gcj_sp = wkt.loads(point_gcj_xy)
            # 找到距离最近的大门
            node_gate_list = road_dao.get_nav_gate_by_geom(point_gcj_sp.buffer(10 / 100000).wkt)
            if not node_gate_list:
                logger.warning(f"{park['bid']} 附近没有大门")
                continue
            node_ids = []
            if park['name'].endswith('出口') or park['name'].endswith('出入口'):
                node_ids = [x[0] for x in node_gate_list if utils.is_out_gate(x[0])]
            if park['name'].endswith('入口') or park['name'].endswith('出入口'):
                node_ids = [x[0] for x in node_gate_list if utils.is_enter_gate(x[0])]
            if len(node_ids) == 0:
                logger.warning(f"{park['bid']} 附近没有通行性契合的大门")
                continue
            node_list = road_dao.get_node_geom(node_ids)
            node_res = min(node_list, key=lambda x: point_gcj_sp.distance(wkt.loads(x[0])))
            road_relation, park_access_mc_geom = park_utils.get_road_relation_by_node_id(node_res[1])
            push(park['bid'], park_access_mc_geom, road_relation)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"{park['bid']} 异常:{e}")


def push(bid, park_wkt, road_relation):
    """
    更新park_wkt
    Args:
        bid:
        road_relation:

    Returns:

    """
    # 写入成果数据
    with open("./invalid_gate.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{park_wkt}\t{road_relation}\n")
        f.flush()
        logger.info(f"{bid} 写入成果数据:{park_wkt}")
    logger.info(f"{bid} 开始推送:{park_wkt},{road_relation}")
    park_mc_sp = wkt.loads(park_wkt)
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_INVALID_GATE",
        "source_id": f"STRATEGY_INVALID_GATE_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_INVALID_GATE_1214",
        "road_relation": road_relation,
        "point_x": park_mc_sp.x,
        "point_y": park_mc_sp.y,
    }
    logger.info(f"失效gate推送:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in tqdm.tqdm(f):
            data_list = line.strip().split("\t")
            if (data_list[1] == '' or data_list[1] == '其他') and "R6011" in data_list[8]:
                bids.append(data_list[2])

    return bids


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if len(bid_list) == 0:
        bid_list = get_need_deal_bids_by_afs()
    if len(bid_list) == 0:
        exit(f"没有可执行BID")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
