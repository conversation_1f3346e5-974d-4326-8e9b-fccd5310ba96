# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地自动化 识别机器单独执行  代码暂时放这里
"""

import os

from pathlib import Path

import pymysql

expimpMysqlConf = {"databases": "bee_flow", "user": "bee_flow", "pwd": "nl4c/mqeTcsgpH", "host": "*************",
                   "port": 5730}

# 连接MySQL
expimpDbConn = None
aoiDao = None

GREEN_BASE_DIR = Path("/home/<USER>/green")  # 绿地数据基础目录
AFS_SHELL = "/home/<USER>/afs/bin/afsshell"
AFS_USERNAME = "map_data_aoi"
AFS_PASSWORD = "DKGjrtAiXi2r"
# 模型识别成果AFS路径
AFS_GREEN_DISCERN_RESULT_DIR = "afs://fenghuang.afs:9902/user/map_data_aoi/green/discern_result"
GREEN_MODEL_SHELL = "/home/<USER>/chenlong22/green-land-sdk-v1.1/run.sh"
GREEN_MODEL_CONFIG = "/home/<USER>/chenlong22/green-land-sdk-v1.1/config/green_land"


def init_beeflow():
    """
    初始化bee_flow
    :return:
    """
    global expimpDbConn, aoiDao
    try:
        expimpDbConn.ping()
    except Exception as e:
        expimpDbConn = pymysql.connect(host=expimpMysqlConf['host'],
                                       port=expimpMysqlConf['port'],
                                       user=expimpMysqlConf['user'],
                                       password=expimpMysqlConf['pwd'],
                                       database=expimpMysqlConf['databases'],
                                       charset='utf8')
        aoiDao = expimpDbConn.cursor()



def run():
    pre_plan = get_green_pre_plan()
    if pre_plan is None or len(pre_plan) == 0:
        print("没有待识别任务")
        return
    print(f"获取预览计划：{pre_plan}")
    batch = str(pre_plan[0])
    tile_map_afs_filepath = pre_plan[1]  # 瓦片图AFS路径
    current_batch_dir = GREEN_BASE_DIR / batch  # 当前批次的目录
    headmap_dir = current_batch_dir / "headmap"  # 热力图目录
    output_result_dir = current_batch_dir / "output_result"  # 识别成果目录
    make_dir(current_batch_dir)
    make_dir(headmap_dir)
    make_dir(output_result_dir)

    # 影像图下载
    tile_map_file_dir = download_tile_map_from_afs(current_batch_dir, tile_map_afs_filepath)
    print(f"获取瓦片图目录：{tile_map_file_dir}")

    # 开始执行识别任务
    discern_cmd = f"sh {GREEN_MODEL_SHELL} {GREEN_MODEL_CONFIG} {tile_map_file_dir} {output_result_dir}"
    ret = os.system(discern_cmd)
    if ret != 0:
        raise ValueError(f'识别任务执行失败: {discern_cmd}')
    # 成果上传AFS
    upload_discern_result(batch, current_batch_dir, output_result_dir)
    print(f"成果上传AFS完成")
    # 更新预览计划状态
    res = update_image_vector_file_by_id(batch)
    print(f"预览计划更新完成:{res}")


def upload_discern_result(batch, current_batch_dir, output_result_dir):
    """
    压缩上传识别成果
    :param output_result_dir:
    :return:
    """
    output_result_file = current_batch_dir / f"{batch}_output.zip"
    zip_cmd = f"zip -q -rj {output_result_file} {output_result_dir}/*"
    ret = os.system(zip_cmd)
    if ret != 0:
        raise ValueError(f'识别成果压缩失败: {zip_cmd}')
    # 上传AFS
    upload_afs_cmd = f"{AFS_SHELL} --username={AFS_USERNAME} --password={AFS_PASSWORD} put --override {output_result_file} {AFS_GREEN_DISCERN_RESULT_DIR}"
    ret = os.system(upload_afs_cmd)
    if ret != 0:
        raise ValueError(f'识别成果上传失败: {upload_afs_cmd}')
    return


def download_tile_map_from_afs(current_batch_dir: str, tile_map_filepath: str):
    """
    影像图下载
    :param batch:
    :param tile_map_filepath:
    :return:
    """
    download_cmd = f"{AFS_SHELL} --username={AFS_USERNAME} --password={AFS_PASSWORD} get {tile_map_filepath} {current_batch_dir}"
    ret = os.system(download_cmd)
    if ret != 0:
        raise ValueError(f'影像图下载失败: {tile_map_filepath}')
    tile_map_filename = tile_map_filepath.rsplit("/", 1)[1]

    # 解压
    unzip_dir = f"{current_batch_dir}/{tile_map_filename.replace('.zip', '')}"
    unzip_cmd = f"unzip -q -o -d {unzip_dir}  {current_batch_dir}/{tile_map_filename}"
    ret = os.system(unzip_cmd)
    if ret != 0:
        raise ValueError(f'解压失败: {unzip_cmd}')
    print(f"解压成功：{unzip_cmd}")
    return current_batch_dir / tile_map_filename.replace(".zip", "")


def make_dir(dir):
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


def get_green_pre_plan():
    """
    获取待识别的绿地预览计划
    :return:
    """
    sql = "select id, result_url from basic_feature_pre_plan where status='TILE_MERGED' order by id limit 1"
    aoiDao.execute(sql)
    return aoiDao.fetchone()


def update_image_vector_file_by_id(id):
    """
    更新预览计划状态
    :param vector:
    :return:
    """
    init_beeflow()
    sql = f"update basic_feature_pre_plan set status='IDENTIFIED' where id={id} and status='TILE_MERGED'"
    aoiDao.execute(sql)
    return expimpDbConn.commit()


if __name__ == '__main__':
    init_beeflow()
    run()
