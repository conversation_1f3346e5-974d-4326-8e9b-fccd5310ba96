<?php
/**
 * @file releaseBidHandle.php
 * @auther gengyong_cd
 * @date 2022/4/14
 * @brief 判重Bid入库
 */
class ReleaseBidHandle {
    /**
     * 推送判重文件
     */
    public function pushReleaseBidData () {
        $conf = parse_ini_file('../../../conf/config.ini');
        if (!file_exists($conf['release_bid_path'])) {
            echo $conf['release_bid_path'] . "判重文件不存在\n";
            return;
        }
        $success = 0;
        $i = 0;
        echo "开始从文件读取判重数据\n";
        $fp = fopen($conf['release_bid_path'], "r");
        $repeatBidList = array();
        while (!feof($fp)) {
            $i++;
            $currentRowStr = fgets($fp);
            if ($i == 1) {
                continue;
            }
            $repeatDataArr = explode("\t", trim($currentRowStr));
            $releaseArr = [
                'invalid_bid' => $repeatDataArr[0] ?? '',
                'release_bid' => $repeatDataArr[1] ?? '',
                'release_time' => $repeatDataArr[2] ?? '',
                'origin_bid' => $repeatDataArr[3] ?? '',
            ];
            $repeatBidList[] = $releaseArr;
            if (count($repeatBidList) > 1000 || feof($fp)) {
                $url = $conf['aoi_api_save_bid_repeat'];
                $data  = json_encode($repeatBidList);
                $headerArray =array("Content-type:application/json;charset='utf-8'","Accept:application/json");
                $curl = curl_init($url);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER,false);
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST,false);
                curl_setopt($curl, CURLOPT_POST, 1);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
                curl_setopt($curl,CURLOPT_HTTPHEADER,$headerArray);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
                curl_exec($curl);
                curl_close($curl);
                $repeatBidList = [];
                $success+=1000;
            }
        }
        fclose($fp);
        echo "数据导入完成:$i,success:$success\n";
    }

}

$obj = new ReleaseBidHandle();
$obj->pushReleaseBidData();