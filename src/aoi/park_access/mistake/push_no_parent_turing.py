
"""
推送情报到人工作业
"""
import datetime
import multiprocessing
import sys
import os

import tqdm
import requests

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from shapely import wkt
from loguru import logger
from src.aoi.park_access.mistake import park_access_qc as qc

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()

# logger.remove()
log_path = "./log/mistake_intelligence.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
same_access_type_arr = ['PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK', \
                        'PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE', \
                        'PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE']


batch_dict = {
    "MISTAKE": "JQDGLC2025052301",  
    "RONGYU": "JQDRYH2025060401"
}


def main(push_number=1000, pv=0, strategy=None, work_type="MISTAKE"):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:
    """
    if work_type == 'MISTAKE':
        strategy = ['PARK_ACCESS_MISTAKE_BY_COMPLETE_CHARGE_DIFF_OFFLINE',
                  'PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_LINK',
                  'PARK_ACCESS_MISTAKE_BY_LINK_ON_URGENT_GATE',
                  'PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE',
                  'PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OFFLINE',
                  'PARK_ACCESS_MISTAKE_BY_PASSAGE_OFFLINE',
                  'PARK_ACCESS_MISTAKE_BY_OVERGROUND_DEAD_ROAD',
                  'COMMON_POI_NAVI_COMPETITOR_DIFF',
                  'PARK_ACCESS_MISTAKE_BY_EXP_TRAJ_NOT_ARRIVE',
                  'PARK_ACCESsS_MISTAKE_BY_LINK_TO_MULTI_NODE',
                  'PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD_MIN_AREA',
                  'PARK_ACCESS_MISTAKE_DISTANCE_GT_200M']
    else:
        strategy = None
    # 获取待推送情报主点
    strategy = None
    access_type = "park_access_mistake"
    src = 18
    if work_type == 'RONGYU':
        access_type = "park_access_rongyu"
        src = 17
    park_access_intelligence_list = poi_online_query.get_park_access_intelligence_mistake(
        access_type, status='INIT', strategy=strategy, pv=pv, work_type=work_type)    
    if not park_access_intelligence_list:
        logger.info("无待推送情报")
        return
    strategy = None
    pushed_park_access_list = []
    pushed_park_bid_list = []
    jqd_batch_id = batch_dict[work_type]
    # 获取所有停车场
    parking_list = [item[1] for item in park_access_intelligence_list]
    if not parking_list:
        logger.info(f"无停车场")
        return
    for park_bid in tqdm.tqdm(list(set(parking_list))):
        if park_bid in pushed_park_bid_list:
            logger.info(f"停车场已推送:{park_bid}")
            continue
        # 获取停车场下出入口是否都是暂停营业
        park_access = poi_online_query.get_access_by_bid(park_bid, '入口')
        if park_access and len(park_access) > 0:
            # 有出入口 
            for access in park_access:
                print(access)
                if access['status'] != 3:
                    logger.info(f"有非暂停营业出入口:{park_bid}")
                    break
            logger.info("有出入口且都暂停营业，不下发")
        # 获取停车场下情报
        logger.info(f"start-获取停车场下情报:{park_bid}")
        park_access_intelligence_list = poi_online_query.get_park_access_intelligence_mistake(
            access_type, status='INIT', bids=[park_bid], strategy=None, work_type=work_type, is_get_all_access=True)
        if not park_access_intelligence_list:
            logger.info(f"无停车场出入口情报:{park_bid}")
            continue

        tips = "核实灌入出入口是否为停车场的出入口，不是则修正为该停车场出入口"
        # 增加冗余类型
        if work_type == 'RONGYU':
            tips = "重点核实下出入口是否冗余"
        push_data = []
        logger.info(f"process-下发出入口情报")
        for park_access_intelligence in park_access_intelligence_list:
            intelligence_id = park_access_intelligence[0]
            park_bid = park_access_intelligence[1]
            park_access_sp = wkt.loads(park_access_intelligence[3])
            guid = park_access_intelligence[4]
            park_access_bid = park_access_intelligence[5]
            # 过滤已下线停车场出入口
            if not qc.filter_offline_park_access(park_bid, park_access_sp.buffer(2 / COORDINATE_FACTOR).wkt):
                logger.info(f"过滤已下线停车场出入口:{intelligence_id}")
                poi_online_rw.update_park_access_intelligence_status_by_id([intelligence_id],
                                                                            status='ALREADY_OFFLINE')
                logger.info(f"过滤已下线停车场出入口:{intelligence_id}")
                continue
            if access_type == 'park_access_mistake':
                if qc.filter_pushed_park_access_strict(park_bid, park_access_bid, "park_access_mistake"):
                    poi_online_rw.update_park_access_intelligence_status_by_id([intelligence_id],
                                                                            status='PUSHED_BEFORE')
                    logger.info(f"过滤已推送停车场出入口:{intelligence_id}")
                    continue

            # 开始推送
            source_id = f"{access_type}_check_{guid}"
            push_data.append({'source_id': source_id,   'jqd_batch_id': jqd_batch_id, 'park_bid': park_bid,
                                'park_access_bid': park_access_bid, 'intelligence_id': intelligence_id})
        if len(push_data) == 0:
            logger.info(f"无停车场出入口情报推送:{park_bid}")
            continue
        qb_id = park_access_request_turing(push_data, source_id, jqd_batch_id, src)
        logger.info(f"推送成功:push_data:{push_data},resp:{qb_id}")
        intelligence_ids = [item['intelligence_id'] for item in push_data]
        poi_online_rw.update_park_access_intelligence_status_batch_by_id(intelligence_ids,
            status='PUSHED', batch=jqd_batch_id, resp=qb_id)
        logger.info(f"推送成功:push_data:{push_data},resp:{qb_id}")
        pushed_park_access_list.append(push_data)
        pushed_park_bid_list.append(park_bid)   
        if len(pushed_park_bid_list) >= push_number:
            exit(f"本次推送结束,共推送停车场:{len(pushed_park_bid_list)}，共推送情报：{len(pushed_park_access_list)}")


def park_access_request_turing(park_access_list, source_id, jqd_batch_id, src):
    """
    出入口情报推送推送图灵
    Args:
        parking_info:
        park_access_list:
        source_id:

    Returns:

    """
    access_info = []
    park_bid = park_access_list[0]['park_bid']
    for park_access in park_access_list:
        access_info.append({
            'access_bid': park_access['park_access_bid'],
            'node_id': "",
            'link_id':  "",
            'gate_wkt': "",
        })
    data = {
        "park_bid": park_bid,
        "access": access_info,
        'pic_urls': []
    }
        #     'pic_urls': [
        #     {
        #         'x': 115.818208,
        #         'y': 32.92387,
        #         'pic_url': 'http://turing.map.baidu.com/picpre/bos_key'
        #     }
        # ]
    res = push_turing(src, jqd_batch_id, source_id, park_bid, 1, **data)
    logger.info(f"推送结果:{res}")
    return res
    

def push_turing(src, batch_id, source_id, bid, qb_type, **data):
    """
    推送图灵
    """
    data = {
        "src": src,  # 必填，15 出入口  16 停车场
        "ref_qb_id": source_id,
        "ref_qb_batch_id": batch_id,  # 必填，批次号
        "main_poi_bid": bid,
        "qb_type": qb_type, # 必填，1出入口新增 2关联错  3 冗余
        "extra": {
            "detail": data
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        # 正式地址
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        # 测试地址
        # url='http://mapde-poi.baidu-int.com/pre/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


def push(push_data: list, tips):
    """
    一体化下发
    Args:
        point_xy:
        source_id:

    Returns:

    """
    data = {
        "src": 10,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": push_data[0]['source_id'],  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": push_data[0]['jqd_batch_id'],  # 必填，批次号
        "main_poi_bid": push_data[0]['park_bid'],  # 停车场bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "qb_type": 2,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": push_data[0]['jqd_batch_id'][:3],  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": tips,
                "gates": [{'wkt': '', 'desc': '', 'gate_bid': item['park_access_bid']} for item in push_data]
            }
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


if __name__ == '__main__':
    push_number = sys.argv[1]
    pv = sys.argv[2]
    strategy = sys.argv[3] if sys.argv[3] != '0' else None
    work_type = sys.argv[4] if len(sys.argv) > 4 else "NORNAL"
    logger.info(f"开始推送,策略:{strategy},pv:{pv},work_type:{work_type}")
    main(int(push_number), int(pv), strategy, work_type)
