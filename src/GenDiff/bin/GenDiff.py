#encoding=gbk
"""
Generate the Difference between Last and Recent Database

zhu<PERSON><PERSON><PERSON>@baidu.com at 20171012
"""

import sys
import os
import shapely.wkt
import shapely.geometry

def Main():
    """
    Main Function
    """
    infile = open(sys.argv[1], "r")
    outfile = open(sys.argv[2], "w")

    while 1:
        lines = infile.readlines(10000)
        if not lines:
            break
        for line in lines:
            try:
                line = line.strip('\n\r')
                mid = line.split('\t')[0]
                old_shape = shapely.wkt.loads(line.split('\t')[1])
                new_shape = shapely.wkt.loads(line.split('\t')[2])
                if old_shape.centroid.almost_equals(new_shape.centroid) and \
                abs((old_shape.area - new_shape.area)) <= 1:
                    continue
                print >> outfile, "%s\t%s\t%s" % (line, str(old_shape.centroid) + \
                    " " + str(new_shape.centroid), str(abs((old_shape.area - new_shape.area))))
            except Exception as e:
                continue

if __name__ == '__main__':
    Main()