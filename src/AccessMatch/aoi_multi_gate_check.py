# -*- coding: UTF-8 -*-
################################################################################
"""
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
每月触发一次
重要清单buffer40米,下发所有未关联大门
"""
import collections
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
import sys
from pypika import Query, Table, Field

std_tag_list = ['', '', '', '']

class AoiMultiGateCheckStrategy(PgTool):
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        提交事务。
        
        Args:
            无参数。
        
        Returns:
            无返回值。
        
        """
        PgTool.commit(self)
            
    def process_manu_node(self, node_manu, bid, src='', type=''):
        """
        人工作业的关联关系入库
        """
        for node_id in node_manu:
            sql = """
                insert into 
                    gate_aoi_match_check_data_tmp(
                        node_id, 
                        bid, 
                        source, 
                        ways
                    )
                    values(
                        %s, 
                        %s, 
                        %s, 
                        %s
                    )
            """
            self.cursor_poi_write.execute(sql, [node_id, bid, src, type])
    
            
    
    def find_L2_aoi(self):
        """
        获取精准aoi的sbid
        """
        sql = " select main_bid from blu_Face_complete where aoi_complete > 2"
        self.cursor_aoi.execute(sql)

        return self.cursor_aoi.fetchall() 

    def filt_with_tag(self, bid):
        """
        判断是否属于需要人工作业的特定垂类
        """
        sql = '''
            select count(*) from poi 
            where bid = %s and (
                std_tag ~ '医疗' or 
                std_tag ~ '高等院校' or 
                std_tag ~ '住宅区' or 
                std_tag ~ '公司企业;公司' or
                std_tag ~ '政府机构')
        '''
        self.cursor_poi.execute(sql, [bid])

        return self.cursor_poi.fetchone()[0] > 0
    
    def get_matched_node_by_bid(self, bid):
        """
        returns: set()
        """
        node_set = set()
        sql = f"""
            select 
                b.node_id 
            from 
                blu_access a 
                inner join 
                blu_access_gate_rel b 
            on 
                a.access_id = b.access_id
            where main_bid = '{bid}'
        """
        self.cursor_aoi.execute(sql)
        for node_id, in self.cursor_aoi.fetchall():
            node_set.add(node_id)

        sql = '''
            select 
                node_id, 
                action
            from gate_bid_change_history
            where 
                bid = %s
                and imp_state = 0 
            order by 
                id 
        '''
        self.cursor_poi.execute(sql, [bid])
        res = self.cursor_poi.fetchall()
        for v in res:
            if v[1] == "add":
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def get_checked_node_by_bid(self, bid):
        """
        根据bid获取已作业过的大门数据
        """
        sql = "select distinct node_id from gate_aoi_match_check_data where bid = %s" 
        self.cursor_poi.execute( sql, [bid])

        return set(x for x, in self.cursor_poi.fetchall())

    def run(self):
        """
        运行函数，对重要垂类的aoi清单进行四门清查策略。
        Args:
            无
        Returns:
            无
        """

        # 精准AOI垂类例行下发
        for bid, in self.find_L2_aoi():
            if self.filt_with_tag(bid):
                node_set = self.get_matched_node_by_bid(bid)
                if len(node_set) < 4:
                    continue
                node_manued_set = self.get_checked_node_by_bid(bid)
                self.process_manu_node(node_set.intersection(node_manued_set), bid, '关联关系例行批处理-特定垂类精准AOI四门清查', '提准')
        self.commit()

if __name__ == "__main__":
    obj = AoiMultiGateCheckStrategy()
    obj.run()