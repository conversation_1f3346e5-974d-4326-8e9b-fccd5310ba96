#!/usr/bin/env python
# coding=utf-8

"""
update AOI
"""

import sys
import os
import json
import itertools
from query import *
import logging
import logging.config
import time


class Service(object):
    """ Service
    """
    def __init__(self, ip, port):
        """ conn
        """
        self._conn = connect(ip, port)

    def __del__(self):
        """ disconn
        """
        self._conn.close()

    def _make_mcpack_data(self, bid, geo_str):
        """ pack data
        """
        info = {
            "transfer": 1,
            "src_type": "wanneng_touchuan",
            "sub_src": "guoke",
            "geo": geo_str,
        }
        details = {
            "detail_type": "guoke_geo",
            "detail_info": json.dumps(info),
            "status": 1,
        }
        query = {
            "_product": "map",
            "_topic": "access",
            "_cmd": "mid",
            "query_type": "baidu_poi_update",
            "src_type": "wanneng_touchuan",
            "sub_src": "guoke",
            "details": {0: details},
            "bid": bid,
        }
        #logging.info("send: {}".format(json.dumps(query)))
#print json.dumps(query)
        print query
        return mcpack.dumps(query, 1024 * 1024)

    # def request(self, bid, geo_str):
    #     """ request
    #     """
    #     if not self._conn:
    #         return None
    #     mcpack_data = self._make_mcpack_data(bid, geo_str)

        # json_result = None
        # return mcpack.dumps(query, 1024 * 1024)

    def request(self, bid, geo_str):
        """ request
        """
        if not self._conn:
            return None
        mcpack_data = self._make_mcpack_data(bid, geo_str)

        json_result = None
        try:
            json_result = query(self._conn, mcpack_data)
        except Exception as e:
            logging.error(e)

        return json_result


def text_file_reader(file_name, fieldnames=None, delimiter='\t',
        chunk_size=32 * 1024 * 1024):
    """读取首行带header的文本文件
    """
    with open(file_name, "rb") as fin:
        keys = fieldnames
        if fieldnames is None:
            line = fin.readline()
            keys = line.rstrip('\r\n').split(delimiter)

        while 1:
            chunk = fin.readlines(chunk_size)
            if not chunk:
                break
            for row in chunk:
                values = row.rstrip('\r\n').split(delimiter)
                yield (row, dict(itertools.izip(keys, values)))


def sender(ip, port, bid, geo_str):
    """ send data
    """
    service = Service(ip, port)
    json_result = service.request(bid, geo_str)
    return json_result


def process(input_file, output_file, ip, port):
    """ process
    """
    HEADER = ["bid", "name", "city_id", "area_id", "level", "string"]
    nn = 0
    with open(output_file, "w") as fout:
        for row, items in text_file_reader(input_file, HEADER):
            bid = items["bid"]
            geo_str = items["string"]
            time.sleep(0.1)
            nn += 1
            # short connect, construct and delete connect each time
            json_result = sender(ip, port, bid, geo_str)
            logging.info(
                "file [{}], row [{}], receive: {}".format(input_file, nn, str(json_result)))
            fout.write(row)


if __name__ == "__main__":
#    ip = '*************'   # online-1,广州机房
#    port = 2000
    ip = '*************'   # online-1,广州机房
    port = 8899
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    dir_path = os.path.dirname(os.path.realpath(__file__))
    log_file = os.path.realpath("%s/../../../conf/logging.conf" % dir_path)
    logging.config.fileConfig(log_file)
    process(input_file, output_file, ip, port)
