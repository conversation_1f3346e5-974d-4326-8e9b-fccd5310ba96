"""
地上新增出口后处理 step 4
"""
from distutils.log import info
import sys
import os
import time
import hashlib
import tqdm
import requests
import math
import json
import copy 
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import nearest_points
from shapely.affinity import translate
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.common import pipeline
from src.aoi.park_access.light_check import context
from src.aoi.park_access.light_check import config
from src.aoi.park_access.light_check import common_func
from collections import defaultdict, deque
import logging as log 
log.basicConfig(level=log.ERROR, format='%(asctime)s %(filename)s[line:%(lineno)d] %(message)s')
from src.aoi import utils
from src.aoi.park_access import park_utils

light_status = config.light_status


def start_filter_access(ctx):
	"""
	开始选图
	"""
	pipe_list = pipeline.Pipeline(
        # 获取停车场精准化及pv信息
        init_park_info,
		# 获取link_id和范围框
		init_link_info,
        # 过滤低置信
		# filter_low_confidence,
        # 无父点过滤压高等级道路
        filter_on_high_link,

        # 30m内有aoi封门，且自己不是aoi大门
        filter_not_aoi_gate_and_aoi_has_close_gate_near,
        
        # 过滤aoi外
        # filter_out_park,
        # # 过滤长link
        # filter_long_link,
        # 过滤断头路
        # filter_dead_road,
        # 疑似门前路侧场景过滤
        filter_store_front,  
        # 过滤线上已存在出入口
        filter_exist_access,
        # 过滤link周围有出入口场景  过滤封门 
        filter_close_gate,
        # 过滤聚合院落
        filter_group_aoi,
        # 无父点低置信
        # filte_no_parent_low_confidence,
        # 临街
        filter_store_front_park,

        # 路线规划过滤
        # filter_by_route_plan_exist_park_access,

        # 地上过滤地下入口 todo
        # filter_underground_access,
		# 保存到数据

		save_to_db,
	)
	pipe_list(ctx)


def filter_not_aoi_gate_and_aoi_has_close_gate_near(ctx: context.Context, proceed):
    """
    30m内有aoi 非可出口，且自己不是aoi大门
    """

    logger.info("start-filter_not_aoi_gate_and_aoi_has_close_gate_near")
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()

    if ctx.intelligence_info.long_node_id in [item.node_id for item in ctx.aoi_info.blu_access_list]:
        return proceed()

    for item in ctx.aoi_info.blu_access_list:
        if item.is_outer:
            continue
        dis = wkt.loads(item.node_geom).distance(wkt.loads(ctx.intelligence_info.node_geom)) * 110000
        if dis < 30:
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'filter_not_aoi_gate_and_aoi_has_close_gate_near'
            return proceed()
    return proceed()


def filter_by_route_plan_exist_park_access(ctx: context.Context, proceed):
    """
    过滤路线规划已存在其余出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    
    relation_node_sp_list = park_utils.get_relation_out_park_access(ctx.parking_info.bid, orientation=1)
    # for item in relation_node_sp_list:
        # logger.info(f"relation_node_sp_list:{item}")
    node_sp = wkt.loads(ctx.intelligence_info.node_geom)
    link_sp = wkt.loads(ctx.intelligence_info.link_geom)
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    # 如果100m范围内不存在已关联出入口, 则直接过滤
    if len([item for item in relation_node_sp_list if item.distance(link_sp) < 100 / 110000]) == 0:
        return proceed()
    # 获取路线规划轨迹
    route_plan_traj_sp_list = park_utils.get_route_plan_traj_by_four_direction(park_sp)
    # for traj in route_plan_traj_sp_list:
    #     print(traj)
    if not route_plan_traj_sp_list:
        return proceed()
    # 如果轨迹穿过已存在出入口, 则直接过滤
    for traj_sp in route_plan_traj_sp_list:
        pass_park_access = [node_sp for node_sp in relation_node_sp_list if
                            node_sp.intersects(traj_sp.buffer(5 / 110000))]
        if not pass_park_access:
            traj_end_point_sp = Point(traj_sp.coords[-1])
            pass_park_access = [node_sp for node_sp in relation_node_sp_list if
                            node_sp.intersects(traj_end_point_sp.buffer(20 / 110000))]
        if pass_park_access:
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'filter_by_route_plan_exist_park_access'
            return proceed()
    return proceed()


def filter_store_front_park(ctx: context.Context, proceed):
    """
    filter_store_front_park
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    is_near_street_park_res = is_near_street_park(ctx)
    if is_near_street_park_res:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_store_front_park'
    return proceed()


def is_near_street_park(ctx):
    """
    判断是否是临街场景停车场,距离边框较近,存在门前停车场,范围内存在临街POI
    Args:
        park_bid:

    Returns:

    """
    if ctx.aoi_info.wkt == '':
        return False
    park_bid, aoi_area = ctx.parking_info.bid, ctx.aoi_info.wkt
    aoi_sp = wkt.loads(aoi_area)
    parking_list = ctx.dao.poi_online_query.get_parking_list_by_kv(bids=[park_bid])
    if not parking_list:
        raise Exception(f"{park_bid} 不存在")
    park_info = parking_list[0]
    park_sp = wkt.loads(park_info["geom"])
    if park_sp.distance(aoi_sp.boundary) > 50 / 110000:
        logger.info(f"停车场距离边界较远,非临街场景")
        return False
    park_area = utils.mc_to_gcj(park_info["area"]) if park_info["area"] else None
    if not park_area:
        park_area = park_sp.buffer(10 / 110000).wkt
    park_area_sp = wkt.loads(park_area)
    # 查询停车场附近是否存在临街POI
    street_poi_list = ctx.dao.poi_online_query.get_near_street_poi_by_area(park_area_sp.buffer(10 / 110000).wkt)
    if not street_poi_list:
        return False
    # 查询附近是否存在门前停车场
    near_gate_front_park_list = ctx.dao.poi_online_query.get_parking_list_by_kv(gcj_geom=park_area, show_tags=['门前停车场'],
                                                                        park_spec=1)
    # 如果附近存在门前停车场,且存在临街POI,且距离边框较近,则认为临街场景
    if near_gate_front_park_list and street_poi_list and park_sp.distance(aoi_sp.boundary) < 20 / 110000:
        logger.info(f"附近存在门前停车场, 存在临街POI,且距离边框较近,认为临街场景")
        return True
    # 不存在门前停车场,但范围内临街POI较多且距离边框较近,也认为临街场景
    if len(street_poi_list) > 10 and park_sp.distance(aoi_sp.boundary) < 40 / 110000:
        logger.info(f"不存在门前停车场,但范围内临街POI较多且距离边框较近,也认为临街场景")
        return True
    return False


def filter_group_aoi(ctx: context.Context, proceed):
    """
    过滤聚合院落
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    if ctx.aoi_info.aoi_level == 1:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_group_aoi'
    return proceed()

def filter_on_high_link(ctx: context.Context, proceed):
    """
    filter_on_high_link
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    # 停车场和 node_geom 连线  buffer 10m
    parking_geom = wkt.loads(ctx.parking_info.park_wkt)
    node_geom = wkt.loads(ctx.intelligence_info.node_geom)
    line = LineString([parking_geom, node_geom])
    buffer_geom = shorten_line_simple(line)
    buffer_wkt = buffer_geom.wkt
    links = ctx.dao.road_dao.get_high_nav_link_by_geom(geom=buffer_wkt, form=None, kind=8)
    if len(links) > 0:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_on_high_link'
    return proceed()


def shorten_line_simple(line, ratio=0.01):
    """
    让 LINESTRING 两端各缩进一小段距离（按比例计算，不涉及投影）
    :param line: LineString (GCJ-02 或 WGS84 坐标均可)
    :param ratio: 缩进比例（默认 0.01，即 1%）
    :return: 缩进后的 LineString
    """
    if line.is_empty or len(line.coords) < 2:
        return line  # 空线段或单点无法缩进

    coords = list(line.coords)
    start_x, start_y = coords[0]
    end_x, end_y = coords[-1]

    # 计算起点缩进后的新位置（向第二个点方向移动一小段）
    if len(coords) >= 2:
        next_x, next_y = coords[1]
        dx = next_x - start_x
        dy = next_y - start_y
        new_start = (start_x + dx * ratio, start_y + dy * ratio)
    else:
        new_start = (start_x, start_y)

    # 计算终点缩进后的新位置（向倒数第二个点方向移动一小段）
    if len(coords) >= 2:
        prev_x, prev_y = coords[-2]
        dx = prev_x - end_x
        dy = prev_y - end_y
        new_end = (end_x + dx * ratio, end_y + dy * ratio)
    else:
        new_end = (end_x, end_y)

    # 构建缩进后的线段
    if len(coords) == 2:
        # 只有 2 个点的情况
        shortened_coords = [new_start, new_end]
    else:
        # 多个点的情况（保留中间点）
        shortened_coords = [new_start] + coords[1:-1] + [new_end]

    return LineString(shortened_coords)



def filter_inner_park(ctx: context.Context, proceed):
    """
    filter_inner_park
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    is_precise10 = ctx.parking_info.is_precise10
    if ctx.parking_info.open_limit_new == '1' and is_precise10:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_inner_park'
    return proceed()


def init_park_info(ctx: context.Context, proceed):
    """
    判断是否精准
    """
    park = ctx.dao.poi_online_query.get_park_statics_data(ctx.parking_info.bid)
    if park and len(park) > 0:
        ctx.parking_info.is_precise10 = int(park[0][1])
        ctx.parking_info.root_pv = int(park[0][0])
        if park[0][2] is not None:
            ctx.parking_info.root_nav_pv = int(park[0][2])\
        # intelligence_type = ['park_access_overground_add', 'park_access_underground_add', 'park_access_overground_out_add']
    if ctx.intelligence_info.intelligence_type in ['park_access_overground_out_add']:
        # 是否是地上出口
        if ctx.parking_info.show_tag not in ['地上停车场', '停车场']:
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'filter_show_tag_not_overground'
            return proceed()
    return proceed()


def filter_by_exp_traj(ctx: context.Context, proceed):
    """
    父点经验轨迹未到达过滤  
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    is_exp_traj_arrive = check_is_exp_traj_arrive(ctx)
    if not is_exp_traj_arrive:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_by_exp_traj'
    
    # if len(ctx.parking_info.parent_id) < 2:
    #     ctx.intelligence_info.result = 'error'
    #     ctx.intelligence_info.light_status = light_status['intelligence_filter']
    #     ctx.intelligence_info.filter_reason = 'filter_by_no_parent'
    #     return proceed()
    return proceed()


def check_is_exp_traj_arrive(ctx):
    """
    检查是否经验轨迹到达
    """
    parent_id = ctx.parking_info.parent_id
    bid = ctx.parking_info.bid
    exp_traj = []
    if len(parent_id) > 2:
        park_parent_exp_traj = utils.get_exp_traj_and_number_track_by_bid(parent_id, 3, 0.8)
        exp_traj.extend(park_parent_exp_traj)
        # for traj in park_parent_exp_traj:
        #     print(traj['traj'])
        if len(park_parent_exp_traj) == 0:
            park_parent_exp_traj = utils.get_exp_traj_and_number_track_by_bid(parent_id, 1, 0.6)
            exp_traj.extend(park_parent_exp_traj)

    park_exp_traj = utils.get_exp_traj_lists_by_bid(bid, 1, 0.9)
    exp_traj.extend(park_exp_traj)
    link_sp = wkt.loads(ctx.intelligence_info.link_geom).buffer(30 / 110000)
    for exp_traj_dict in exp_traj:
        exp_traj_sp = exp_traj_dict['traj']
        # 相交
        exp_traj_end_point_sp = Point(exp_traj_sp.coords[-1])
        if link_sp.intersects(exp_traj_end_point_sp):
            return True
    return False

def filte_no_parent_low_confidence(ctx: context.Context, proceed):
    """
    无父点，低置信
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    if len(ctx.parking_info.parent_id) < 2 and ctx.intelligence_info.traj_flux == 0:
        logger.info(f"无父点，低置信过滤")
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'no_parent_and_low_confidence'
    return proceed()
    

def filter_dead_road(ctx: context.Context, proceed):
    """
    过滤断头路
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    is_dead_node, is_dead_link = False, False
    if ctx.intelligence_info.long_node_id != '':
        is_dead_node, _, _ = is_node_on_dead_road(ctx, node_id=ctx.intelligence_info.long_node_id, form='52')
    if ctx.intelligence_info.long_link_id != '':
        is_dead_link, _, _ = is_link_on_dead_road(ctx, link_id=ctx.intelligence_info.long_link_id, form='52')
    if is_dead_node or is_dead_link:
        logger.info(f"断头路，过滤,{is_dead_node},{is_dead_link}")
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'is_dead_end'
        return proceed()
    return proceed()

def filter_low_confidence(ctx: context.Context, proceed):
    """
    过滤低置信
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    # if ctx.intelligence_info.traj_flux < 70:
    #     logger.info(f"低置信度「{ctx.intelligence_info.traj_flux}」，过滤")
    #     ctx.intelligence_info.result = 'error'
    #     ctx.intelligence_info.light_status = light_status['intelligence_filter']
    #     ctx.intelligence_info.filter_reason = 'low_confidence'
    #     return proceed()
    return proceed()


def filter_long_link(ctx: context.Context, proceed):
    """
    过滤长link
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    if ctx.intelligence_info.link_length > 100:
        logger.info(f"link长度「{ctx.intelligence_info.link_length}」米，过滤")
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'too_long_link'
        return proceed()
    return proceed()


def filter_store_front(ctx: context.Context, proceed):
    """
    过滤门前路侧场景
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()

    # 疑似门前路侧 增加距离限制
    park_name = ctx.parking_info.name
    is_near_street_park = '东侧' in park_name or '南侧' in park_name or '西侧' in park_name or '北侧' in park_name
    # 计算link到停车场面的距离
    link_sp = wkt.loads(ctx.intelligence_info.link_geom)
    distance = 0
    if ctx.parking_info.park_area_gcj_wkt:
        distance = link_sp.distance(wkt.loads(ctx.parking_info.park_area_gcj_wkt)) * 110000
        if distance > 100 and is_near_street_park:
            logger.info(f"门前路侧，距离park_area「{distance}」米，过滤")
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'store_front_access_faraway_area'    
            return proceed()
    elif ctx.parking_info.park_wkt:
        distance = link_sp.distance(wkt.loads(ctx.parking_info.park_wkt)) * 110000
        if distance > 150 and is_near_street_park:
            logger.info(f"门前路侧，距离park_point「{distance}」米，过滤")
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'store_front_access_faraway_point'    
            return proceed()
    if distance > 500:
        logger.info(f"距离park_point「{distance}」米，过滤")
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_access_faraway_park'    
        return proceed()
    # 和门前入口过滤
    buffer_wkt = wkt.loads(ctx.intelligence_info.link_geom).buffer(20/110000).wkt
    store_front_access = ctx.dao.poi_online_query.get_access_by_buffer(buffer_wkt, parent_show_tag=['门前停车场'], access_type=None)
    if store_front_access is not None and len(store_front_access) > 0:
        logger.info("附近10m存在门前停车场出入口")
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'exists_access_online_by_street'    
        return proceed()
    return proceed()


def filter_exist_access(ctx: context.Context, proceed):
    """
    过滤线上已存在
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    sub_access_info = ctx.dao.poi_online_query.get_sub_pois(ctx.intelligence_info.park_bid, access_type='出口')
    # 和当前停车场子点入口过滤
    if sub_access_info is not None and len(sub_access_info) > 0:
        node_sp = wkt.loads(ctx.intelligence_info.node_geom)
        logger.info(ctx.intelligence_info.node_geom)
        access_min_dis = min(node_sp.distance(wkt.loads(access_info[2])) for access_info in sub_access_info) * 110000
        if access_min_dis < 50:
            logger.info(f"{ctx.intelligence_info.park_bid} 存在线上已存在，距离node「{access_min_dis}」米")
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'exists_access_online_by_node'
            return proceed()
        link_sp = wkt.loads(ctx.intelligence_info.link_geom)
        access_min_dis = min(link_sp.distance(wkt.loads(access_info[2])) for access_info in sub_access_info) * 110000
        if access_min_dis < 20:
            logger.info(f"{ctx.intelligence_info.park_bid} 存在线上已存在，距离link「{access_min_dis}」米")
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'exists_access_online_by_link'
            return proceed()
    
    if ctx.aoi_info.std_tag == '交通设施;长途汽车站':
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'filter_bus_station'    
        return proceed()
    return proceed()


def filter_close_gate(ctx: context.Context, proceed):
    """
    过滤封门
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    if ctx.intelligence_info.long_node_id != "":
        passage = common_func.get_nav_gate_semantic(ctx, ctx.intelligence_info.node_id)
        ctx.intelligence_info.node_passage = passage
        if passage == "不可入":
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'close_gate'
        elif passage == "入口":
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'in_gate'
    return proceed()


def filter_out_park(ctx: context.Context, proceed):
    """
    过滤封门
    """
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        return proceed()
    if ctx.aoi_info.wkt != '' and ctx.parking_info.park_area_gcj_wkt != '':
        aoi_sp = wkt.loads(ctx.aoi_info.wkt)
        park_sp = wkt.loads(ctx.parking_info.park_wkt)
        park_area_buffer_wkt = wkt.loads(ctx.parking_info.park_area_gcj_wkt).buffer(50 / 110000).wkt
        if aoi_sp.contains(park_sp):
            return proceed()
        store_std_tag = [    
                # 购物类
                '购物;家居建材', '购物;便利店', '购物;超市', '购物;烟酒店', '购物;商铺',
                # 美食餐饮类
                '美食;小吃快餐店', '美食;中餐厅', '美食;其他', '美食;蛋糕甜品店',
                # 生活服务类
                '生活服务;宠物服务', '生活服务;洗衣店', '生活服务;其他', '生活服务;图文打印', '生活服务;家政服务', '生活服务;物流公司',
                # 丽人类
                '丽人;美容', '丽人;美体', '丽人;美发', '丽人;美甲',
                # 教育培训类
                '教育培训;幼儿园', '教育培训;托管班', '教育培训;兴趣班', '教育培训;培训机构',
                # 汽车服务类
                '汽车服务;汽车美容', '购物;商铺',
                # 金融类
                '金融;银行', '金融;atm',
                # 医疗健康类
                '医疗;药店', '医疗;诊所', '休闲娱乐;洗浴按摩',
                # 休闲娱乐类
                '休闲娱乐;游戏场所', '生活服务;彩票销售点'
            ]
        store_pois = ctx.dao.poi_online_query.get_store_poi_by_geom(
                park_area_buffer_wkt, std_tag=store_std_tag,
            )
        if len(store_pois) >= 5:
            logger.info(f"附近临街商铺较多，疑似门前，「{len(store_pois)}」")
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'store_poi_many'
            return proceed()
    return proceed()
    
	
def save_to_db(ctx: context.Context, proceed):
    """
    入库
    """
    if ctx.parking_info.root_pv > 5000:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'high_root_pv'
    if ctx.parking_info.root_nav_pv > 500:
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'high_root_nav_pv'
    logger.info(f"root_pv{ctx.parking_info.root_pv},nav_pv:{ctx.parking_info.root_nav_pv}")
    
    if ctx.intelligence_info.light_status != light_status['light_check_synced']:
        print("\ntarget\t" + f"{ctx.aoi_info.area}\t{ctx.intelligence_info.link_and_park_distance}\t{ctx.intelligence_info.id}\t{ctx.parking_info.bid}\t{ctx.intelligence_info.long_node_id}\t{ctx.intelligence_info.long_link_id}\t{ctx.intelligence_info.link_length}\t{ctx.intelligence_info.node_geom}\t{ctx.intelligence_info.filter_reason}")
        return proceed()
    ctx.intelligence_info.light_status = light_status['access_need_add']
    ctx.intelligence_info.filter_reason = 'filter_pass'
    print("\ntarget\t" + f"{ctx.aoi_info.area}\t{ctx.intelligence_info.link_and_park_distance}\t{ctx.intelligence_info.id}\t{ctx.parking_info.bid}\t{ctx.intelligence_info.long_node_id}\t{ctx.intelligence_info.long_link_id}\t{ctx.intelligence_info.link_length}\t{ctx.intelligence_info.node_geom}\t{ctx.intelligence_info.filter_reason}")
    
    
    # data = {
    #     'intelligence_id': ctx.intelligence_info.id,
    #     'park_bid': ctx.intelligence_info.park_bid,
    #     'parent_bid': ctx.intelligence_info.parent_id,
    #     'node_geom': ctx.intelligence_info.node_geom,
    #     'long_node_id': ctx.intelligence_info.node_id,
    #     'long_link_id': ctx.intelligence_info.link_id,
    #     'status': ctx.intelligence_info.light_status,
    #     'msg': ctx.intelligence_info.filter_reason,
    #     'img_num': len(ctx.intelligence_info.track_traj_list)
    # }
    # logger.info(data)
    # res = ctx.dao.poi_online_rw.insert_light_check_list(data)
    # logger.info(res)
    return proceed()


def init_link_info(ctx: context.Context, proceed):
    """
    初始化link信息
    """
    ctx.intelligence_info.result = 'success'
    ctx.intelligence_info.light_status = light_status['light_check_synced']
    ctx.intelligence_info.filter_reason = ''
    link_info = ctx.dao.road_dao.get_nav_link(link_ids=[ctx.intelligence_info.long_link_id])
    # logger.info(link_info)
    if link_info is None or len(link_info) == 0:
        # 无效link过滤
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'no_valid_link'
        return proceed()
    if ('52' not in link_info[0]['form'] and '53' not in link_info[0]['form']) or int(link_info[0]['kind']) <= 7:
        logger.info(f"link 无效，尝试寻找有效link")
        if ctx.intelligence_info.long_node_id != '':
            new_link_info = ctx.dao.road_dao.get_nav_link_by_node(
                node_ids=[ctx.intelligence_info.long_node_id], 
                form='52', 
                exclude_link_id=ctx.intelligence_info.long_link_id
                )     
            if new_link_info and len(new_link_info) > 0:
                logger.info(f"link_id无效，旧link「{ctx.intelligence_info.long_link_id}」，\
                    新link:{new_link_info[0][0]}")
                link_info = ctx.dao.road_dao.get_nav_link(link_ids=[new_link_info[0][0]])

    # if '52' not in link_info[0][5]:
    #     # 非内部路过滤
    #     ctx.intelligence_info.result = 'error'
    #     ctx.intelligence_info.light_status = light_status['intelligence_filter']
    #     ctx.intelligence_info.filter_reason = 'not_inner_road'
    #     return proceed()
    
    if int(link_info[0]['kind']) <= 7:
        # 主干道过滤
        ctx.intelligence_info.result = 'error'
        ctx.intelligence_info.light_status = light_status['intelligence_filter']
        ctx.intelligence_info.filter_reason = 'main_road_filter'
        return proceed()
    
    temp_geom = wkt.loads(ctx.intelligence_info.node_geom)
    # 如果是 MULTIPOINT，提取第一个点
    if temp_geom.geom_type == 'MultiPoint':
        if len(temp_geom.geoms) == 1:
            first_point = temp_geom.geoms[0]  # 获取第一个点
            point = Point(first_point.x, first_point.y)  # 创建新的 POINT
            ctx.intelligence_info.node_geom = point.wkt
        else:
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'node_geom_multipoint'
            return proceed()

    ctx.intelligence_info.link_geom = link_info[0]['geom']
    ctx.intelligence_info.buffer_geom = wkt.loads(ctx.intelligence_info.link_geom).buffer(30 / 110000).wkt
    ctx.intelligence_info.link_length = round(wkt.loads(ctx.intelligence_info.link_geom).length * 110000, 2)
    link_sp = wkt.loads(ctx.intelligence_info.link_geom)
    link_north = calculate_bearing(Point(link_sp.coords[0]), Point(link_sp.coords[-1])) 
    ctx.intelligence_info.link_north = link_north
    if ctx.aoi_info.wkt != '':
        aoi_geom = wkt.loads(ctx.aoi_info.wkt)
        aoi_boundary = aoi_geom.boundary
        if aoi_geom.intersects(link_sp):
            ctx.intelligence_info.is_link_in_aoi = "1"
        else:
            ctx.intelligence_info.is_link_in_aoi = "0"
        # 计算距离
        ctx.intelligence_info.link_and_aoi_distance = link_sp.distance(aoi_boundary) * 110000
        park_and_aoi_distance = wkt.loads(ctx.parking_info.park_wkt).distance(aoi_boundary) * 110000
        # logger.info(ctx.intelligence_info.link_and_aoi_distance)
        # logger.info(ctx.intelligence_info.is_link_in_aoi)
        if ctx.intelligence_info.is_link_in_aoi == "1" and \
            ctx.intelligence_info.link_and_aoi_distance > 75:
            # aoi内且距离aoi边框远过滤
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'link_in_aoi_inner'
            return proceed()
        
        if ctx.parking_info.park_area_gcj_wkt:
            ctx.intelligence_info.link_and_park_distance  = link_sp.distance(wkt.loads(ctx.parking_info.park_area_gcj_wkt)) * 110000
            if aoi_geom.intersects(wkt.loads(ctx.parking_info.park_area_gcj_wkt)):
                ctx.intelligence_info.is_park_in_aoi = "1"
            else:
                ctx.intelligence_info.is_park_in_aoi = "0"
        elif ctx.parking_info.park_wkt:
            ctx.intelligence_info.link_and_park_distance  = link_sp.distance(wkt.loads(ctx.parking_info.park_wkt)) * 110000
            if aoi_geom.intersects(wkt.loads(ctx.parking_info.park_wkt)):
                ctx.intelligence_info.is_park_in_aoi = "1"
            else:
                ctx.intelligence_info.is_park_in_aoi = "0"

        if ctx.aoi_info.area > 200000 and ctx.intelligence_info.link_and_park_distance > 270:
            ctx.intelligence_info.result = 'error'
            ctx.intelligence_info.light_status = light_status['intelligence_filter']
            ctx.intelligence_info.filter_reason = 'big_area_and_link_faraway_park'
            return proceed()


        # temp del
        # if ctx.intelligence_info.is_link_in_aoi == "0" and ctx.intelligence_info.is_park_in_aoi == "1":
        #     ctx.intelligence_info.result = 'error'
        #     ctx.intelligence_info.light_status = light_status['intelligence_filter']
        #     ctx.intelligence_info.filter_reason = 'link_not_in_aoi_and_park_in_aoi'
        #     return proceed()

        # if ctx.parking_info.is_precise10 == 1 and ctx.aoi_info.std_tag == '房地产;住宅区' and \
        #     (ctx.intelligence_info.is_park_in_aoi == "0" or park_and_aoi_distance < 22):
        #     ctx.intelligence_info.result = 'error'
        #     ctx.intelligence_info.light_status = light_status['intelligence_filter']
        #     ctx.intelligence_info.filter_reason = 'park_not_in_aoi_or_too_close_aoi_boundary'
        #     return proceed()
    return proceed()


def calculate_bearing(point1, point2):
    """ 
    计算正北角
    """
    lon1, lat1 = point1.x, point1.y
    lon2, lat2 = point2.x, point2.y 
    # 将纬度和经度从度数转换为弧度
    lat1 = math.radians(lat1)
    lon1 = math.radians(lon1)
    lat2 = math.radians(lat2)
    lon2 = math.radians(lon2)
    # 计算经度差
    d_lon = lon2 - lon1
    # 计算方位角
    x = math.sin(d_lon) * math.cos(lat2)
    y = math.cos(lat1) * math.sin(lat2) - (math.sin(lat1) * math.cos(lat2) * math.cos(d_lon))
    initial_bearing = math.atan2(x, y)
    # 将方位角转换为 0 到 360 度
    initial_bearing = math.degrees(initial_bearing)
    compass_bearing = (initial_bearing + 360) % 360
    return compass_bearing


def http_get(url, try_cnt=3, spl_t=0.5):
    """get 请求
    """
    time.sleep(0.05)
    while try_cnt > 0:
        try:
            response = requests.get(url, timeout=5000)
            res = response.json()
            if res.get('errno') == 0:
                return res 
        except Exception as e:
            #log.error("HTTP GET request failed: {} \n{}".format(e, url))
            time.sleep(spl_t)
            print(f"http_get_error:{e}")
        try_cnt -= 1
        if try_cnt <= 0:
            print(f"HTTP GET request failed: {url}")
    return None


def expent_img_list(track_dict, temp_track_traj_list, temp_track_traj_img_list):
    """
    扩充选取前后的图片
    """
    bos_key_start = temp_track_traj_list[0]['bos_key']
    if track_dict[bos_key_start]['pre'] is not None:
        pre_track = track_dict[bos_key_start]['pre']
        if pre_track.get('bos_key') not in temp_track_traj_img_list:
            temp_track_traj_list.insert(0, pre_track)
            temp_track_traj_img_list.append(pre_track.get('bos_key'))
    bos_key_end = temp_track_traj_list[-1]['bos_key']
    if track_dict[bos_key_end]['next'] is not None:
        next_track = track_dict[bos_key_end]['next']
        if next_track.get('bos_key') not in temp_track_traj_img_list:
            temp_track_traj_list.append(next_track)
            temp_track_traj_img_list.append(next_track.get('bos_key'))


def get_track_type(track_list, node_info):
    """ 获取轨迹形态 (link_id 不能表征)
    1. 门外主路轨迹 pass(经过轨迹)
    2. 门前轨迹 (forward)(轨迹上距离门最近的两个点的正北角和门的in_link的正北角角度差在30度以内)
    """
    
    if track_list is None or len(track_list) == 0:
        return 'unknow'

    # 门的in_link的正北角
    inlink_geom = node_info[0].get('inlink_geom') #  LINESTRING(114.9466342 26.8498369,114.9455858 26.8497788) 
    inlink_geom = inlink_geom.replace("LINESTRING(", "").replace(")", "")
    inlink_geom = inlink_geom.split(",")
    inlink_points = [Point(float(elem.split(" ")[0]), float(elem.split(" ")[1])) for elem in inlink_geom]
    inlink_angle = calculate_bearing(inlink_points[0], inlink_points[1]) 

    
    # 轨迹正北角, 只有一个点时使用自带的正北角, 否则计算距离门最近的两个点的正北角
    track_geom = LineString([[elem['x'], elem['y']] for elem in track_list]) if len(track_list) > 1 else Point(float(track_list[0].get('x')), float(track_list[0].get('y')))  
    if len(track_list) == 1:
        track_angle = track_list[0].get('north')
    else:
        track_list = sorted(track_list, key=lambda x: x['node_dis'])
        track_points = track_list[0:2]
        track_points = [Point(float(elem.get('x')), float(elem.get('y'))) for elem in track_points]
        track_angle = calculate_bearing(track_points[0], track_points[1])  

    # 角度差
    angle_diff = abs(track_angle - inlink_angle)

    if angle_diff <= 30 or (angle_diff > 150 and angle_diff < 210):
        return 'forward'
    else:
        return 'pass'



def get_track_dir(track_list, node_point_sp, aoi_geom):
    """获取轨迹方向
    in: 进入aoi 
    out: 离开aoi 
    unknow: 未知方向 

    aoi内的轨迹:通过计算门到轨迹起终点的距离差来判断 进入/推出
    aoi相交的轨迹: 判断起终点是否在aoi内
    """
    # aoi 边框
    aoi_boundary = aoi_geom.exterior

    # 计算门到aoi 距离最近的点 
    nearest_point = aoi_boundary.interpolate(aoi_boundary.project(node_point_sp)).coords.xy 
    nearest_point = Point(nearest_point[0][0], nearest_point[1][0])

    # 采集轨迹 
    if len(track_list) == 1:
        # 轨迹只有一个点 
        return 'unknow' 
    
    track_geom = LineString([[elem['x'], elem['y']] for elem in track_list])
    
    s_point0 = Point(float(track_list[0].get('x')), float(track_list[0].get('y')))
    s_point1 = Point(float(track_list[1].get('x')), float(track_list[1].get('y'))) 

    e_point0 = Point(float(track_list[-2].get('x')), float(track_list[-2].get('y')))
    e_point1 = Point(float(track_list[-1].get('x')), float(track_list[-1].get('y')))

    if track_geom.within(aoi_geom):
        start_diff = (s_point1.distance(nearest_point) - s_point0.distance(nearest_point))
        end_diff = (e_point1.distance(nearest_point) - e_point0.distance(nearest_point))

        if start_diff > 0 and end_diff > 0:
            return 'in'
        elif start_diff <= 0 and end_diff <= 0:
            return 'out'
        else:
            return 'unknow' 
    else:
        if not aoi_geom.contains(s_point0) and aoi_geom.contains(e_point1):
            return 'in'
        elif aoi_geom.contains(s_point0) and not aoi_geom.contains(e_point1):
            return 'out'
        else:
            return 'unknow'


def check_is_exit_light(ctx, id):
    """
    检查是否已存在
    """
    light_check = ctx.dao.poi_online_query.get_light_check(intelligence_id=id)
    return light_check


def single_process(poi_online_query, light_check):
    """
    单个处理
    """
    qb_id = light_check['intelligence_id']
    qb_lists = poi_online_query.get_access_add_intelligence(id=qb_id, limit=1)
    if not qb_lists:
        logger.error(f"error-intelligence_not_exists\t{qb_id}")
        return {'status': light_status['intelligence_not_exists'], 'msg': 'intelligence_not_exists'}
    access_qb = qb_lists[0]
    parking_list = poi_online_query.get_parking_list(bids=[access_qb['bid']], 
        show_tags=['地上停车场', '立体停车场', '停车场'])
    if not parking_list:
        logger.error(f"No parking found\t{access_qb}")
        return {'status': light_status['intelligence_park_not_exists'], 'msg': 'intelligence_park_not_exists'}
    
    show_tag = parking_list[0][4]
    if show_tag not in ['地上停车场', '停车场']:
        return {'status': light_status['intelligence_not_overground'], 'msg': 'intelligence_not_overground'}

    ctx = context.Context(parking_list[0], access_qb)
    ctx.get_aoi_blu_access_list()
    
    if light_check['long_link_id'] != '':
        ctx.intelligence_info.long_link_id = light_check['long_link_id']
    else:
        ctx.intelligence_info.long_link_id = ''
    if light_check['long_node_id'] != '':
        ctx.intelligence_info.long_node_id = light_check['long_node_id']
    else:
        ctx.intelligence_info.long_node_id = ''
    start_filter_access(ctx)
    return {'status': ctx.intelligence_info.light_status, 
            'msg': ctx.intelligence_info.filter_reason, 
            # 'link_length': ctx.intelligence_info.link_length
        }


def is_node_on_dead_road(ctx, node_id=None, node_distance_limit=100, aoi_area=None, form='52'):
    """
    判断node是否在断头路上
    判断 node 2端的link是否是断头link 是的话就返回true
    """
    if aoi_area and aoi_area < 5000:
        logger.info(f"小AOI不算断头路aoi_area:{aoi_area}\n")
        return False, None, None
    link_arr = ctx.dao.road_dao.get_nav_link_by_node([node_id], form)
    if not link_arr:
        return False, None, None
    for link in link_arr:
        link_id = link[0]
        res, res_link_id, res_node_id = is_link_on_dead_road(ctx, link_id, node_distance_limit, aoi_area, form)
        if res:
            return True, res_link_id, res_node_id
    return False, None, None


def is_link_on_dead_road(ctx, link_id=None, link_distance_limit=100, aoi_area=None, form='52'):
    """
    判断link是否在断头路上
    判断 link 2端的node 是否是断头node 是的话就返回true
    """
    if aoi_area and aoi_area < 5000:
        logger.info(f"小AOI不算断头路aoi_area:{aoi_area}\n")
        return False, None, None
    link_arr = ctx.dao.road_dao.get_nav_link_by_link_ids([link_id])

    if not link_arr:
        return False, None, None
    s_nid = link_arr[0][1]
    e_nid = link_arr[0][2]
    link_len = link_arr[0][5]
    if link_len >= link_distance_limit:
        return False, None, None

    s_ret = ctx.dao.road_dao.get_nav_link_by_node([s_nid], None)
    if not s_ret or (len(s_ret) == 1 and s_ret[0][0] == link_id):
        return True, link_id, s_nid
    e_ret = ctx.dao.road_dao.get_nav_link_by_node([e_nid], None)
    if not e_ret or (len(e_ret) == 1 and e_ret[0][0] == link_id):
        return True, link_id, e_nid
    return False, None, None


def main():
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    ids_arr = None
    bid_file = sys.argv[1]
    intelligence_id = sys.argv[2]
    if intelligence_id != '0':
        ids_arr = [int(intelligence_id)]
    if bid_file != '0':
        with open(bid_file, "r") as f:
            ids_arr = f.read().replace("\n", ",").strip(",").split(",")

    light_check_list = poi_online_query.get_light_check_list(
        intelligence_id=ids_arr, 
        status=[light_status['light_check_synced']],
        conclusion_txt='地上停车场出入口',
        intelligence_type=['park_access_overground_out_add'],
        limit=400,
        # intelligence_id='3288987',
        update_time_sort=True)
    if not light_check_list:
        logger.error("No light_check_list found")
        return
    logger.info(f"待处理任务：{len(light_check_list)}")
    for light_check in tqdm.tqdm(light_check_list):
        intelligence_id = light_check['intelligence_id']
        id = light_check['id']
        logger.error(f"start-----intelligence_id：\t{intelligence_id}")
        update_dict = single_process(poi_online_query, light_check)
        condition = {
            "id": id,
            "status": light_status['light_check_synced'],
        }
        logger.info(update_dict)
        logger.info(condition)
        # res = poi_online_rw.update_park_access_light_check(update_dict, condition)
        # logger.info(res)
        continue
        try:
            intelligence_id = light_check['intelligence_id']
            id = light_check['id']
            logger.error(f"start-----intelligence_id：\t{intelligence_id}")
            update_dict = single_process(poi_online_query, light_check)
            condition = {
                "id": id,
                "status": light_status['light_check_synced'],
            }
            logger.info(update_dict)
            logger.info(condition)
            res = poi_online_rw.update_park_access_light_check(update_dict, condition)
            logger.info(res)
        except Exception as e:
            #log.error("HTTP GET request failed: {} \n{}".format(e, url))
            logger.info(f"access_qb start-error:{e}")
            logger.info(light_check)


if __name__ == '__main__':
	main()


    