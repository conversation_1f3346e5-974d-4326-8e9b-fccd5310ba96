"""
一些公共的函数
"""
import datetime
from src.common import pgsql
from src.door_naming.strategy_v2 import common


def add_to_flow(bid, aoi_level):
    """
    加入到自动化闭环流程
    :param bid:
    :param aoi_level:
    :return:
    """
    if aoi_level not in (1, 2):
        return
    batch_id = datetime.date.today().strftime("%Y%m%d")
    sql = 'select * from aoi_white_list_flow where bid=:bid '
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid})
    if ret:
        # print(f"已经在流程中,不可重复加入:{bid}")
        return
    complete_level = common.get_aoi_complete_level(bid)
    if complete_level >= 3:
        print(f"已经闭环:{bid}")
        return
    sql = 'insert into aoi_white_list_flow (bid, aoi_level, can_complete, batch_id) values (:d1,:d2, :d3, :d4)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"d1": bid, "d2": aoi_level, "d3": 0, "d4": batch_id})
