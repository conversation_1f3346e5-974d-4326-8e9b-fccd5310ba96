# -*- coding: utf-8 -*-
"""
POI DE大门导出协作项
"""

import psycopg2

poiOnlineConf = {"db": "poi_online", "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
                 "host": "gzbh-ns-map-de16.gzbh.baidu.com",
                 "port": 8532}
masterBackConf = {"db": "master_back", "user": "master_back_se_ro", "pwd": "mapread",
                  "host": "**************",
                  "port": 5432}
roadConf = {"db": "mukucache_e91b5fb4520411eead9ce04f43dda3b4",
            "user": "mukucache_e91b5fb4520411eead9ce04f43dda3b4_se_rw", "pwd": "oscimpog",
            "host": "**************",
            "port": 8432}
transConf = {"db": "trans_id", "user": "trans_id_se_rw", "pwd": "cpmkukky",
             "host": "*************",
             "port": 5432}
# 连接线上库
poiOnlineDbConn = psycopg2.connect(database=poiOnlineConf["db"],
                                   user=poiOnlineConf["user"],
                                   password=poiOnlineConf["pwd"],
                                   host=poiOnlineConf["host"],
                                   port=poiOnlineConf["port"])
poiOnlineDao = poiOnlineDbConn.cursor()

maskBackDbConn = psycopg2.connect(database=masterBackConf["db"],
                                  user=masterBackConf["user"],
                                  password=masterBackConf["pwd"],
                                  host=masterBackConf["host"],
                                  port=masterBackConf["port"])
masterBackDao = maskBackDbConn.cursor()

roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

transDbConn = psycopg2.connect(database=transConf["db"],
                               user=transConf["user"],
                               password=transConf["pwd"],
                               host=transConf["host"],
                               port=transConf["port"])
transDao = transDbConn.cursor()


def test(result_file):
    data_list = [{"bid": "bid", "name": "name", "node_id": "node_id", "gate_id": "gate_id", "geom_x": "geom_x",
                  "geom_y": "geom_y", "inlink_id": "inlink_id", "outlink_id": "outlink_id", "parent_bid": "parent_bid",
                  "passage": "passage", "valid_obj": "valid_obj", "valid_time": "valid_time", "fee": "fee",
                  "car_restric": "car_restric", "in_or_out": "in_or_out", "source": "source"}]
    main_bid = ''
    number = 0
    print("开始查询")
    # 查询2.0AOI
    while True:
        sql = f"select main_bid from blu_face_complete where aoi_complete=4 and main_bid > '{main_bid}' order by main_bid limit 10000"
        masterBackDao.execute(sql)
        ret_list = masterBackDao.fetchall()
        if ret_list is None or len(ret_list) == 0:
            break
        for resItem in ret_list:
            number += 1
            main_bid = resItem[0]
            print(f"当前进度：{number}, 成果：{len(data_list)}")
            bid = resItem[0]
            # 查询关联POI
            sql = f"select access_poi_bid, name, access_id, st_X(geom),st_Y(geom) from blu_access where main_bid = '{bid}'"
            masterBackDao.execute(sql)
            access_poi_list = masterBackDao.fetchall()
            if access_poi_list is None or len(access_poi_list) == 0:
                continue

            for access_poi in access_poi_list:
                if access_poi[0] == "":
                    continue
                current_info = {"bid": "", "name": "", "node_id": "", "gate_id": "", "geom_x": "", "geom_y": "",
                                "inlink_id": "", "outlink_id": "", "parent_bid": "", "passage": "", "valid_obj": "",
                                "valid_time": "", "fee": "", "car_restric": "", "in_or_out": "", "source": "4"}
                current_info['parent_bid'] = bid
                current_info['bid'] = access_poi[0]
                current_info['name'] = access_poi[1]
                current_info['geom_x'] = str(access_poi[3])
                current_info['geom_y'] = str(access_poi[4])

                # 查询gate Node
                sql = f"select node_id, gate_id from blu_access_gate_rel where access_id = '{access_poi[2]}'"
                masterBackDao.execute(sql)
                gate_real_list = masterBackDao.fetchall()
                if gate_real_list is None or len(gate_real_list) == 0:
                    continue
                if gate_real_list is not None and len(gate_real_list) > 0:
                    long_node_id_list = [gate_rel[0] for gate_rel in gate_real_list]
                    gate_id_list = [gate_rel[1] for gate_rel in gate_real_list]
                    current_info["gate_id"] = ",".join(gate_id_list)

                    # 长Node转短Node
                    long_node_id_str = "','".join(long_node_id_list)
                    sql = f"select distinct tid from image_n where sid in('{long_node_id_str}')"
                    transDao.execute(sql)
                    short_node_list = transDao.fetchall()
                    if short_node_list is None or len(short_node_list) == 0:
                        continue
                    if short_node_list is not None and len(short_node_list) > 0:
                        short_node_id_list = [short_node[0] for short_node in short_node_list]
                        current_info["node_id"] = ",".join(short_node_id_list)

                    # 查询 in_link out_link
                    if len(long_node_id_list) > 0:
                        sql = f"select in_linkid,out_linkid from nav_gate where node_id in('{long_node_id_str}')"
                        roadDao.execute(sql)
                        link_info_list = roadDao.fetchall()
                        if link_info_list is not None and len(link_info_list) > 0:
                            in_link_id_list = [link_info[0] for link_info in link_info_list]
                            in_link_id_str = "','".join(in_link_id_list)
                            sql = f"select distinct tid from image_r where sid in('{in_link_id_str}')"
                            transDao.execute(sql)
                            short_in_link_list = transDao.fetchall()
                            if short_in_link_list is not None and len(short_in_link_list) > 0:
                                short_in_link_str = [short_link[0] for short_link in short_in_link_list]
                                current_info["inlink_id"] = ",".join(set(short_in_link_str))

                            out_link_id_list = [link_info[1] for link_info in link_info_list]
                            out_link_id_str = "','".join(out_link_id_list)
                            sql = f"select distinct tid from image_r where sid in('{out_link_id_str}')"
                            transDao.execute(sql)
                            short_out_link_list = transDao.fetchall()
                            if short_out_link_list is not None and len(short_out_link_list) > 0:
                                short_out_link_str = [short_link[0] for short_link in short_out_link_list]
                                current_info["outlink_id"] = ",".join(set(short_out_link_str))

                # 查询通行性
                sql = f"select valid_obj,fee,valid_time,obj_restrict,transit_type from blu_access_traffic where access_id = '{access_poi[2]}' order by valid_obj"
                masterBackDao.execute(sql)
                blu_access_traffic_list = masterBackDao.fetchall()

                if blu_access_traffic_list is not None and len(blu_access_traffic_list) > 0:
                    valid_obj_list = [str(traffic_rel[0]) for traffic_rel in blu_access_traffic_list]
                    fee_list = [str(traffic_rel[1]) for traffic_rel in blu_access_traffic_list]
                    valid_time_list = [traffic_rel[2] for traffic_rel in blu_access_traffic_list]
                    car_restric_list = [str(traffic_rel[3]) for traffic_rel in blu_access_traffic_list]
                    transit_type_list = [str(traffic_rel[4]) for traffic_rel in blu_access_traffic_list]

                    if "1" in valid_obj_list:
                        current_info['passage'] = "1"
                    else:
                        current_info['passage'] = "2"
                    valid_obj_list.sort()
                    current_info['valid_obj'] = ",".join(valid_obj_list)
                    current_info['valid_time'] = ",".join(valid_time_list)
                    current_info['fee'] = ",".join(fee_list)
                    current_info['car_restric'] = ",".join(car_restric_list)
                    current_info['in_or_out'] = ",".join(transit_type_list)
                data_list.append(current_info)
    # 成果写入
    file = open(result_file, 'w')
    for statistics_data in data_list:
        values = statistics_data.values()
        strs = '\t'.join(values) + "\n"
        file.write(strs)
    file.close()


if __name__ == '__main__':
    test("./result.txt")
