# -*- coding: utf-8 -*-
"""
出入口失效LINK
"""
import datetime
import multiprocessing
import sys
import os
import tqdm
import requests
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi import utils

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_relatedness.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_relatedness.txt"



def main(bids: list):
    """
    失效高通量Link处理
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids.tolist())
    for park in tqdm.tqdm(park_list):
        try:
            if park['park_spec'] == 1 or park['show_tag'] == '门前停车场':
                logger.info(f"{park['bid']} 临街场景不处理")
                continue
            logger.info(f"\n{park['bid']} 开始处理")
            road_relation_json = park['road_relation']
            if not road_relation_json or road_relation_json['link_info'][0]['type'] != 2:
                continue
            short_link_id = road_relation_json['link_info'][0]['link_id']
            point_mc_xy = road_relation_json['link_info'][0]['point'].split(",")
            point_gcj_xy = utils.mc_to_gcj(f"point({point_mc_xy[0]} {point_mc_xy[1]})")
            long_link_list = trans_dao.get_long_link_by_short_link_ids([short_link_id])
            long_link_id = long_link_list[0][0] if long_link_list else None
            re_match_long_link_id = re_match_link(park['geom'], point_wkt=point_gcj_xy, long_link_id=long_link_id,
                                                  short_link_id=short_link_id)
            if not re_match_long_link_id:
                logger.error(f"{park['bid']} 未匹配到有效Link")
                continue
            short_link_list = trans_dao.get_short_link_by_long_link_ids([re_match_long_link_id])
            if not short_link_list:
                logger.error(f"{park['bid']} 没有对应的短Link")
                continue
            short_link_id = short_link_list[0][0]
            road_relation_json['link_info'][0]['link_id'] = short_link_id
            release_road_relation(park["bid"], road_relation_json)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"{park['bid']} 异常:{e}")


def re_match_link(park_wkt, point_wkt=None, long_link_id=None, short_link_id=None):
    """
    失效LINK重匹配
    Returns:

    """
    if not point_wkt and not long_link_id and not short_link_id:
        return False
    re_link_id = None
    if long_link_id:
        re_link_id = re_match_high_link_by_original_geom(long_link_id=long_link_id)
    if short_link_id and not re_link_id:
        re_link_id = re_match_high_link_by_original_geom(short_link_id=short_link_id)
    if point_wkt and not re_link_id:
        re_link_id = re_match_high_link(park_wkt, point_wkt)
    return re_link_id


def re_match_high_link(park_wkt, point_wkt):
    """
    高通量Link重新匹配
    Returns:

    """
    park_sp = wkt.loads(park_wkt)
    point_sp = wkt.loads(point_wkt)
    point_buffer_5m = point_sp.buffer(0.00005)
    near_link_list = road_dao.get_intersects_nav_link_by_wkt(point_buffer_5m.wkt)
    if not near_link_list:
        logger.error(f"{point_sp} 附近没有Link")
        return False
    valid_link_list = []
    for near_link in near_link_list:
        if near_link[2] == 10:
            continue
        node_list = road_dao.get_node_list([near_link[4], near_link[5]])
        if not node_list:
            logger.error(f"{point_sp} 附近Link节点不存在")
            continue
        node_distance_park = [park_sp.distance(wkt.loads(item['geom'])) for item in node_list]
        valid_link_list.append({"link_id": near_link[0], "distance": sum(node_distance_park)})
    if not valid_link_list:
        logger.error(f"{point_sp} 附近没有有效Link")
        return False
    # 获取距离point最近的一条Link
    near_link_info = min(valid_link_list, key=lambda x: x['distance'])
    return near_link_info['link_id']


def re_match_high_link_by_original_geom(long_link_id=None, short_link_id=None):
    """
    Link重新匹配
    Returns:

    """
    if not long_link_id and not short_link_id:
        return False
    # 获取LINK失效前坐标
    if long_link_id:
        origin_list_list = poi_online_query.get_invalid_link_origin_geom(long_link_ids=[long_link_id])
    else:
        origin_list_list = poi_online_query.get_invalid_link_origin_geom(short_link_ids=[short_link_id])
    if not origin_list_list:
        logger.error(f"{long_link_id} 没有原始坐标")
        return False
    link_geom = origin_list_list[0]['geom']
    # 重新匹配合适的LINK
    link_sp = wkt.loads(link_geom)
    near_link_list = road_dao.get_intersects_nav_link_by_wkt_new(wkt=link_sp.buffer(0.00003).wkt)
    if not near_link_list:
        logger.error(f"{long_link_id} 附近没有Link")
        return False
    # buffer 3m获取与原始LINK重合度最高的一条LINK
    link_buffer_3m_sp = link_sp.buffer(0.00003)
    near_link_info = max(near_link_list, key=lambda x: link_buffer_3m_sp.intersection(
        wkt.loads(x['geom']).buffer(0.00003)).area / link_buffer_3m_sp.area)
    return near_link_info['link_id']


def release_road_relation(bid, road_relation):
    """
    更新road_relation
    Args:
        bid:
        road_relation:

    Returns:

    """
    # 写入成果数据
    with open("./invalid_park_access_link.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{road_relation}\n")
        f.flush()
        logger.info(f"{bid} 写入成果数据:{road_relation}")

    logger.info(f"{bid} 开始推送:{road_relation}")
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_INVALID_PARK_ACCESS_LINK",
        "source_id": f"STRATEGY_INVALID_PARK_ACCESS_LINK_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_INVALID_PARK_ACCESS_LINK_1214",
        "road_relation": road_relation,
        "force_update": 1,
    }
    logger.info(f"road_relation修复推送:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in tqdm.tqdm(f):
            data_list = line.strip().split("\t")
            if (data_list[1] == '' or data_list[1] == '其他') and "R6002" in data_list[8]:
                bids.append(data_list[2])
    return bids


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if len(bid_list) == 0:
        bid_list = get_need_deal_bids_by_afs()
    if len(bid_list) == 0:
        exit(f"没有可执行BID")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
