"""
aoi大门缺失的问题
1. 经验轨迹
2. 熄火点
3. 特斯拉,比亚迪轨迹
"""
import dataclasses

import shapely.wkt
from shapely.geometry import Point
import tqdm
from src.common import pgsql, dest_traj_tools
from src.door_naming.strategy_v2 import common
from mapio.utils import bid2uid
import pandas as pd

AOI_BUFFER_SIZE = 20 * 1.1 * 1e-5
CHECK_NODE_DIS = 40 * 1.1 * 1e-5

# 30米内找link对应的交割点
LINK_MOVE_DIS = 35 * 1.1

STRATEGY_EXP = '经验轨迹'
STRATEGY_HD = '高精轨迹(特斯拉/byd)'
STRATEGY_PARK = '熄火点'

'''
src = '42' # tesla
src = '48' # byd
'''
HD_SRC = ['42', '48']

# 这个策略暂时只针对这些大类
enable_tag = ['房地产', '医疗', '教育', '公司', '政府']


@dataclasses.dataclass
class SubDoorFound:
    """
    疑似缺门的位置
    """
    # 聚合点的位置
    sub_geom: str
    # 聚合点附近的aoi和link交割点的位置(30米算)
    sub_door_link_moved_geom: str = ''
    sub_door_link_id: str = ''
    # 平移距离
    move_dis: float = -1
    # has link_near
    from_strategy_list: list[str] = dataclasses.field(default_factory=list)
    # 匹配的poi的bid,没有留空
    sub_bid: str = ''
    # poi的poi的名称, 没有留空
    sub_name: str = ''


@dataclasses.dataclass
class NearbyGate:
    """
    附近的node
    """
    gate_id: str
    node_id: str
    node_geom: str


@dataclasses.dataclass
class AoiIntersectedLink:
    """
    aoi边框交割点对应的link
    """
    link_id: str
    link_geom: str
    link_kind: str
    link_form: str


@dataclasses.dataclass
class Aoi:
    """
    aoi的数据
    """
    bid: str
    name: str
    aoi_geom: str
    std_tag: str
    aoi_level: int
    click_pv: int
    # 策略匹配到的大门数据
    near_gate_list: list[NearbyGate] = dataclasses.field(default_factory=list)
    intersected_link: list[AoiIntersectedLink] = dataclasses.field(default_factory=list)
    sub_door_list: list[SubDoorFound] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class Ctx:
    """
    上下文
    """
    target_bid_list: list[str] = dataclasses.field(default_factory=list)
    taret_bid_file: str = ''
    aoi_list: list[Aoi] = dataclasses.field(default_factory=list)


def load_covered_gate(aoi: Aoi):
    """
    加载压盖的aoi,用于去重
    :param aoi:
    :return:
    """
    aoi_geom = aoi.aoi_geom
    geom_buffer = shapely.wkt.loads(aoi_geom).buffer(AOI_BUFFER_SIZE).wkt
    gate_info = common.get_covered_gate_by_geom(geom_buffer)
    if gate_info:
        for g in gate_info:
            aoi.near_gate_list.append(NearbyGate(gate_id=g['gate_id'], node_id=g['node_id'], node_geom=g['geom']))


def load_intersected_link(aoi: Aoi):
    """
    寻找附近的link与边框的交割点
    :param aoi:
    :return:
    """
    aoi_boundary = shapely.wkt.loads(aoi.aoi_geom).boundary.wkt
    sql = 'with tmp as (select st_geomfromtext(:d1, 4326) as aoi_boundary ) ' \
          ' select a.link_id, a.kind, a.form, ST_AsText(ST_Intersection(tmp.aoi_boundary, a.geom)) as geom ' \
          ' from nav_link a , tmp where kind >= 6 and st_intersects(tmp.aoi_boundary, a.geom) '
    cut_link = pgsql.query_all(pgsql.ENGINE_ROAD, sql, {"d1": aoi_boundary})
    for i in cut_link:
        if shapely.wkt.loads(i.geom).geom_type != 'Point':
            continue
        l = AoiIntersectedLink(
            link_id=i.link_id,
            link_kind=i.kind,
            link_form=i.form,
            link_geom=i.geom,
        )
        aoi.intersected_link.append(l)


def bool_enable_tag(tag):
    """
    是否是符合要求的tag
    :param tag:
    :return:
    """
    for e in enable_tag:
        if e in tag:
            return True
    return False


def load_data(ctx: Ctx):
    """
    加载数据
    :param ctx:
    :return:
    """
    # 加载基础数据
    if ctx.taret_bid_file:
        with open(ctx.taret_bid_file) as f:
            ctx.target_bid_list = [x.strip() for x in f]
    ctx.target_bid_list = ctx.target_bid_list[0:100]
    if ctx.target_bid_list:
        for bid in tqdm.tqdm(ctx.target_bid_list, desc='加载数据'):
            aoi_info = common.get_aoi_by_main_bid(bid)
            poi_info = common.get_poi_info_by_bid(bid)
            if not aoi_info or not poi_info:
                continue
            if aoi_info.src == 'SD':
                continue
            if aoi_info.aoi_level != 2:
                continue
            if not bool_enable_tag(poi_info.std_tag):
                continue
            aoi = Aoi(
                bid=bid,
                name=poi_info.name,
                aoi_geom=aoi_info.geom,
                aoi_level=aoi_info.aoi_level,
                std_tag=poi_info.std_tag,
                click_pv=poi_info.click_pv,
            )
            load_covered_gate(aoi)
            load_intersected_link(aoi)
            ctx.aoi_list.append(aoi)


def check_door_can_add(aoi: Aoi, add_geom: str, from_strategy: str):
    """
    检查是否可作为情报
    :param aoi:
    :param add_geom:
    :param from_strategy:
    :return:
    """
    nearest_link_geom = ''
    # 找到聚合点35米内最近的道路link点
    nearest_link_move_dis = LINK_MOVE_DIS
    nearest_link_id = ''
    for item in aoi.intersected_link:
        dis = shapely.wkt.loads(item.link_geom).distance(shapely.wkt.loads(add_geom)) * 1.1 * 1e5
        if dis < nearest_link_move_dis:
            nearest_link_move_dis = dis
            nearest_link_geom = item.link_geom
            nearest_link_id = item.link_id

    # 没找到,就使用add_geom
    if nearest_link_id == '':
        nearest_link_geom = add_geom
        nearest_link_move_dis = 0

    # 与线上的大门,20米去重
    for item in aoi.near_gate_list:
        if shapely.wkt.loads(item.node_geom).distance(shapely.wkt.loads(nearest_link_geom)) < CHECK_NODE_DIS:
            return
    # 已经挖掘到的门, 30米内去重, 并标记策略召回情况
    for item in aoi.sub_door_list:
        if shapely.wkt.loads(item.sub_geom).distance(shapely.wkt.loads(nearest_link_geom)) < CHECK_NODE_DIS:
            if from_strategy not in item.from_strategy_list:
                item.from_strategy_list.append(from_strategy)
            return
    # 如果待新增的点20米内有大门,放弃新增
    geom_buffer = shapely.wkt.loads(nearest_link_geom).buffer(20 * 1.1 * 1e-5).wkt
    nearby_gate = common.get_covered_gate_by_geom(geom_buffer)
    if nearby_gate:
        return
    aoi.sub_door_list.append(
        SubDoorFound(sub_geom=add_geom,
                     sub_door_link_moved_geom=nearest_link_geom,
                     sub_door_link_id=nearest_link_id,
                     move_dis=nearest_link_move_dis,
                     from_strategy_list=[from_strategy]))


def append_exp_traj(ctx: Ctx):
    """
    经验轨迹处理
    :param ctx:
    :return:
    """
    for aoi in tqdm.tqdm(ctx.aoi_list, desc='处理经验轨迹'):
        aoi: Aoi
        sql = 'select st_astext(end_track_line) as line_geom, route_end_percentage' \
              ' from exp_traj_monthly where end_poi_uid = :uid order by route_end_percentage desc limit 20'
        uid = bid2uid(int(aoi.bid))
        ret = pgsql.query_all(pgsql.ENGINE_DEST_TRAJ_TO_AOI, sql, {"uid": uid})
        if len(ret) > 10:
            ret = ret[0:10]
        for item in ret:
            # 简单策略, 对框buffer 20米(道路的距离), 过滤掉终点不在框内的数据,  取top5, 对比30米内是否有门,如果30米内没有门则认为缺门
            line_shape = shapely.wkt.loads(item.line_geom)
            last_node = line_shape.coords[-1]
            # 如果和边框没有交点,则按照最后一个点作为距离去计算
            intersection = shapely.wkt.loads(aoi.aoi_geom).boundary.intersection(line_shape)
            if intersection.geom_type == 'Point':
                check_door_can_add(aoi, intersection.wkt, STRATEGY_EXP)
            elif intersection.geom_type == 'MultiPoint':
                for sub_geom in intersection.geoms:
                    check_door_can_add(aoi, Point(sub_geom).wkt, STRATEGY_EXP)
            else:
                # 看最后一个点的位置 点在面内,无效数据
                if Point(last_node).intersects(shapely.wkt.loads(aoi.aoi_geom)):
                    continue
                # 如果最后一个点距离边框太远,忽略,否则认为那边可能是个停车点(人行门)
                if Point(last_node).distance(shapely.wkt.loads(aoi.aoi_geom)) > CHECK_NODE_DIS:
                    continue
                check_door_can_add(aoi, Point(last_node).wkt, STRATEGY_EXP)


def append_hd_traj(ctx: Ctx):
    """
    加载高精数据源
    :param ctx:
    :return:
    """
    for aoi in tqdm.tqdm(ctx.aoi_list, desc='加载高精轨迹'):
        aoi: Aoi
        sql = ' with tmp as (select st_geomfromtext(:aoi_geom, 4326) as aoi_geom) ' \
              'select st_astext(geom) as geom ' \
              ' from hd_traj,tmp where st_intersects(geom, tmp.aoi_geom) and src in :src '
        ret = pgsql.query_all(pgsql.ENGINE_TRAJ_DB, sql, {"aoi_geom": aoi.aoi_geom, "src": tuple(HD_SRC)})
        for item in ret:
            # 最后一个点不在框内的不要
            line_shape = shapely.wkt.loads(item.geom)
            last_node = line_shape.coords[-1]
            if not Point(last_node).intersects(shapely.wkt.loads(aoi.aoi_geom)):
                continue
            # 这里不用聚合,和aoi的交割点都疑似可出入
            intersection = shapely.wkt.loads(aoi.aoi_geom).boundary.intersection(shapely.wkt.loads(item.geom))
            if intersection.geom_type == 'Point':
                check_door_can_add(aoi, intersection.wkt, STRATEGY_HD)
            elif intersection.geom_type == 'MultiPoint':
                for sub_geom in intersection.geoms:
                    check_door_can_add(aoi, Point(sub_geom).wkt, STRATEGY_HD)


def append_parking_points(ctx: Ctx):
    """
    熄火点判断
    :param ctx:
    :return:
    """
    # 加载熄火点
    for aoi in tqdm.tqdm(ctx.aoi_list, desc='处理熄火点'):
        aoi: Aoi
        geom_buffer = shapely.wkt.loads(aoi.aoi_geom).buffer(AOI_BUFFER_SIZE).wkt
        # 查找终点在边框内的轨迹
        sql = 'with tmp as (select st_geomfromtext(:g1, 4326) as aoi_geom) ' \
              'select st_astext(parking_traj_geom) as track_line' \
              ' from parking_points a, tmp where st_intersects(tmp.aoi_geom, geom) and traj_src = :src'
        ret = pgsql.query_all(pgsql.ENGINE_TRAJ_FEATURE_DB, sql, {"g1": geom_buffer, "src": '31'})
        if len(ret) > 0:
            dest_traj_list = []
            for item in ret:
                # 起点在框内,忽略
                line = shapely.wkt.loads(item.track_line)
                if line and line.geom_type == 'LineString':
                    dest_traj_list.append(line)
            _, cluster_points = dest_traj_tools.get_cluster_by_cross_aoi(dest_traj_list, aoi.aoi_geom, min_samples=10)
            if len(cluster_points) > 5:
                cluster_points = cluster_points[0:5]
            for ct in cluster_points:
                check_door_can_add(aoi, ct['centroid'], from_strategy=STRATEGY_PARK)


def export(ctx: Ctx):
    """
    导出结果
    :param ctx:
    :return:
    """
    lines = []
    for a in ctx.aoi_list:
        for b in a.sub_door_list:
            tmp = dataclasses.asdict(a)
            del tmp['aoi_geom']
            del tmp['near_gate_list']
            del tmp['sub_door_list']
            del tmp['intersected_link']
            tmp.update(dataclasses.asdict(b))
            lines.append(tmp)
    pd.DataFrame(lines).to_csv("疑似缺门场景.tsv", sep='\t', index=False)


def run():
    """
    主函数
    :return:
    """
    ctx = Ctx()
    ctx.target_bid_list = ['9153276649051402994']
    # ctx.taret_bid_file = 'sh_match.txt'
    load_data(ctx)
    # 这个顺序不能换, 优先保留高精轨迹
    # 高精轨迹
    append_hd_traj(ctx)
    # 经验轨迹
    append_exp_traj(ctx)
    # 停车点轨迹
    append_parking_points(ctx)
    export(ctx)


if __name__ == '__main__':
    run()
