#!/usr/bin/env python
"""
uid get
"""
# encoding:utf-8
import ctypes
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
# from aoi import fcrypt

fcrypt = ctypes.CDLL('/root/codes/baidu_beec_aoi-strategy/baidu/beec/aoi-strategy/src/aoi/fcrypt.so')


# defaultencoding = 'utf-8'
# if sys.getdefaultencoding() != defaultencoding:
#     reload(sys)
#     sys.setdefaultencoding(defaultencoding)


class UidEncoder:
    """
    UidEncoder
    """

    def __init__(self):
        """
            init
        """
        key = "mapui.2009.04.14"
        self.m_magicNum = 0x493907bf
        self.m_handle = fcrypt.create(key)

    def close(self):
        """
        close
        """
        fcrypt.destroy(self.m_handle)

    def decode(self, encoded_str):
        """
        decode
        """
        uid_longlong = fcrypt.hstr2ulonglong(self.m_handle, encoded_str)
        uid1 = uid_longlong >> 32
        uid2 = uid_longlong & 0xFFFFFFFF
        uid = (0xFFFFFFFF - self.m_magicNum + uid2) % 0xFFFFFFFF
        uid = (uid << 32) + uid1
        return str(uid)

    def encode(self, uid):
        """
        encode
        """
        uid = int(uid)
        uid2 = uid >> 32
        uid1 = uid & 0xFFFFFFFF
        uid2 = (uid2 + self.m_magicNum + 0xFFFFFFFF) % 0xFFFFFFFF
        uid_longlong = (uid1 << 32) + uid2
        encoded_str = fcrypt.ulonglong2hstr(self.m_handle, uid_longlong)
        return encoded_str


encoder = UidEncoder()


def get_uid(bid):
    """
    get uid
    """
    return encoder.encode(bid)


def get_bid(uid):
    """
    get bid
    """
    return encoder.decode(uid)


if __name__ == '__main__':
    """"""
    # import pandas as pd
    #
    # df = pd.read_csv('/home/<USER>/wangxinzhi/utils/append_poi_uid.csv')
    # df['uid'] = df['bid'].apply(get_uid)
    # df.to_csv('/home/<USER>/wangxinzhi/utils/append_poi_uid_new.csv', index=False)

    uid = get_uid('12497821079384127997')
    print(uid)