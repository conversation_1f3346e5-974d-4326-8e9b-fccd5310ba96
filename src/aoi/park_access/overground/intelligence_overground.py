# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
import tqdm
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from loguru import logger
from shapely import wkt
from sklearn.cluster import DBSCAN

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.context import Context
from src.aoi.park_access import context
from src.aoi.park_access import park_utils
from src.aoi import utils
from src.aoi.park_access import park_access_qc as qc

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000

# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

BUILD_GATE_CATEGORY = ['医疗;综合医院', '医疗;专科医院', '教育培训;高等院校', '房地产;住宅区', '房地产;其他', '房地产', '公司企业;公司', '公司企业;园区', '公司企业',
                       '公司企业;厂矿', '公司企业;农林园艺', '教育培训;中学', '教育培训;小学', '教育培训;幼儿园', '教育培训;培训机构', '教育培训;其他', '教育培训;科研机构',
                       '教育培训;科技馆', '教育培训;成人教育', '教育培训;特殊教育学校', '教育培训', '政府机构;各级政府', '政府机构;行政单位', '政府机构;公检法机构',
                       '政府机构;政治教育机构', '政府机构;中央机构', '政府机构;涉外机构', '汽车服务;汽车销售', '汽车服务;汽车配件', '汽车服务;汽车维修', '汽车服务;汽车检测场',
                       '文化传媒;广播电视', '绿地;高尔夫球场', '医疗;疗养院', '医疗;其他', '医疗', '生活服务;物流公司']
NOT_BUILD_CATEGORY = ['交通设施;加油加气站', '交通设施;飞机场', '交通设施;火车站', '购物;购物中心']
IS_TEST = True


def create_intelligence(ctx: Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        parking_competitors_intelligence,
        # sub_poi_and_nav_gate,
        cluster_traj_list,
        traj_area_intersects,
        parking_accept_and_didi_traj_area_intersects,
        park_end_traj_area_intersects,
        exp_traj_and_competitor_parking,
        parking_traj_area_intersects,
        parking_hd_traj_area_intersects,
        exp_traj_and_gate,  # 提召
        area_intersects_link,  # 提召
        # poi_navi_competitor, 注释
        # competitor_diff, 注释
        cal_strategy_confidence,
        park_access_filter,
        save_park_access_intelligence_to_db
    )
    pipe_list(ctx)


def cal_strategy_confidence(ctx: context, proceed):
    """
    计算策略置信度
    1 轨迹相关策略, 轨迹数量大于[0, 50]置信度为60, 正常走过滤; [50, 100]置信度为80 ; [100, x] 置信度为100
    Args:
        ctx:
        proceed:

    Returns:

    """
    traj_strategy = [
        context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_GATE,
        context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_LINK,
        context.OVERGROUND_AOI_TRAJ_CLUSTER_GATE,
        context.OVERGROUND_AOI_PARK_TRAJ_AREA_INTERSECT_GATE,
        context.OVERGROUND_AOI_PARK_TRAJ_AREA_INTERSECT_LINK,
        context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_GATE,
        context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_LINK,
    ]
    # special_strategy = [
    #     context.OVERGROUND_AOI_EXP_TRAJ_AND_COMPETITOR_GATE,
    #     context.OVERGROUND_AOI_EXP_TRAJ_AND_COMPETITOR_LINK,
    # ]
    for park_access in ctx.parking_access_list:
        if park_access.strategy in traj_strategy:
            traj_num = int(park_access.strategy_value)
            if traj_num <= 50:
                park_access.confidence = 60
            elif traj_num <= 100:
                park_access.confidence = 80
            else:
                park_access.confidence = 100
        # 竞品策略置信度100
        # if park_access.strategy in special_strategy:
        #     park_access.confidence = 100
    return proceed()


def get_park_access_intelligence(ctx: Context):
    """
    出入口情报挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        parking_competitors_intelligence,
        sub_poi_and_nav_gate,
        cluster_traj_list,
        traj_area_intersects,
        exp_traj_and_competitor_parking,
        parking_traj_area_intersects,
        exp_traj_and_gate,  # 提召
        poi_navi_competitor,
        park_access_filter,
    )
    pipe_list(ctx)
    return ctx.parking_access_list


def parking_competitors_intelligence(ctx: context, proceed):
    """
    停车场竞品情报挖掘
    Args:
        ctx:
        proceed:
    """
    park_utils.parking_competitors_intelligence_overground(ctx)
    proceed()


def parking_accept_and_didi_traj_area_intersects(ctx: context.Context, proceed):
    """
    停车场终点轨迹与面交割
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info("begin parking_accept_and_didi_traj_area_intersects")
    if ctx.parking_info.park_area_gcj_wkt == "" or len(ctx.traj.park_dest_traj_list) < 10:
        logger.info(
            f"is_park_area:{not ctx.parking_info.park_area_gcj_wkt}, traj_num:{len(ctx.traj.park_dest_traj_list)}")
        return proceed()
    is_special_category = True if "旅游景点" in ctx.aoi_info.std_tag or "交通设施" in ctx.aoi_info.std_tag else False
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    # 获取轨迹和AOI交割点集
    # 取采纳和滴滴轨迹
    parking_traj_sp_list = ctx.traj.park_accept_traj_list + ctx.traj.park_didi_traj
    # parking_traj_sp_list = [wkt.loads(item) for item in parking_traj_list]
    ints_p_wkt_arr = utils.get_area_and_traj_intersects_point(ctx.aoi_info.wkt, parking_traj_sp_list, 10)
    # logger.info(f"轨迹与停车场面交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8 and len(agg_points) < 5:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(
            convex_hull.buffer(10 / COORDINATE_FACTOR).wkt)
        logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if link_sp.distance(park_area_sp) > 50 / COORDINATE_FACTOR:
                continue
            
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(park_area_sp):
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(park_area_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            max_score_result['strategy_value'] = len(points)
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    traj_sort = 0 
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.6:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            # 断头路提准
            res, _, _  = utils.is_node_on_dead_road(link_info["node_id"], 
                node_distance_limit=50, aoi_area=None, form=None)
            if res:
                logger.info(f"交割Node[{link_info['node_id']}]断头路提准失败, 跳过交割")
                continue 
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_GATE
            parking_access.strategy_value = link_info['strategy_value']
            parking_access.traj_num = len(points)
            # 每次有效的情报 排序加1 
            traj_sort = traj_sort + 1
            parking_access.traj_sort = traj_sort
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        # 断头路提准
        res, _, _  = utils.is_link_on_dead_road(link_info['link_id'], 50)
        if res:
            logger.info(f"交割Link[{link_info['link_id']}]断头路提准失败, 跳过交割")
            continue 

        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_LINK
        parking_access.strategy_value = parking_access.strategy_value = link_info['strategy_value']
        parking_access.traj_num = len(points)
        # 每次有效的情报 排序加1 
        traj_sort = traj_sort + 1
        parking_access.traj_sort = traj_sort
        ctx.parking_access_list.append(parking_access)
    proceed()
    

def parking_hd_traj_area_intersects(ctx: context.Context, proceed):
    """
    停车场高精轨迹与面交割
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info("begin parking_hd_traj_area_intersects")
    is_special_category = True if "旅游景点" in ctx.aoi_info.std_tag or "交通设施" in ctx.aoi_info.std_tag else False
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    parking_traj_sp_list = ctx.traj.aoi_hd_traj_list + ctx.traj.park_hd_traj_list
    # 获取轨迹和AOI交割点集
    ints_p_wkt_arr = utils.get_area_and_traj_intersects_point(ctx.aoi_info.wkt, parking_traj_sp_list, 30)
    logger.info(f"轨迹与停车场面交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    if ctx.parking_info.park_area_gcj_wkt == "":
        return proceed()
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8 and len(agg_points) < 2:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(
            convex_hull.buffer(3 / COORDINATE_FACTOR).wkt)
        logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            # link离停车场过远过滤
            if link_sp.distance(park_area_sp) > 400 / COORDINATE_FACTOR:
                continue
            
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(park_area_sp):
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(park_area_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            max_score_result['strategy_value'] = len(points)
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    traj_sort = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.6:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            # 断头路提准
            res, _, _  = utils.is_node_on_dead_road(link_info["node_id"], 
                node_distance_limit=50, aoi_area=None, form=None)
            if res:
                logger.info(f"交割Node[{link_info['node_id']}]断头路提准失败, 跳过交割")
                continue 
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_HD_TRAJ_AREA_INTERSECT_GATE
            parking_access.strategy_value = link_info['strategy_value']
            parking_access.traj_num = len(points)
            # 每次有效的情报 排序加1 
            traj_sort = traj_sort + 1
            parking_access.traj_sort = traj_sort
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        # is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
        #                                                            ctx.traj.park_exp_traj_list)
        # if not is_valid_link:
        #     logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
        #     continue
        # 断头路提准
        res, _, _  = utils.is_link_on_dead_road(link_info['link_id'], 50)
        if res:
            logger.info(f"交割Link[{link_info['link_id']}]断头路提准失败, 跳过交割")
            continue 

        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_HD_TRAJ_AREA_INTERSECT_LINK
        parking_access.strategy_value = parking_access.strategy_value = link_info['strategy_value']
        ctx.parking_access_list.append(parking_access)
    proceed()
   

def parking_hd_traj_area_intersects(ctx: context.Context, proceed):
    """
    停车场终点轨迹与面交割
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info("begin parking_hd_traj_area_intersects")
    if ctx.parking_info.park_area_gcj_wkt == "" or len(ctx.traj.park_dest_traj_list) < 1:
        logger.info(
            f"is_park_area:{not ctx.parking_info.park_area_gcj_wkt}, traj_num:{len(ctx.traj.park_dest_traj_list)}")
        return proceed()
    is_special_category = True if "旅游景点" in ctx.aoi_info.std_tag or "交通设施" in ctx.aoi_info.std_tag else False
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    # 获取轨迹和AOI交割点集
    # 取采纳和滴滴轨迹
    parking_traj_sp_list = ctx.traj.aoi_hd_traj_list + ctx.traj.park_hd_traj_list
    ints_p_wkt_arr = utils.get_area_and_traj_intersects_point(ctx.aoi_info.wkt, parking_traj_sp_list, 10)
    logger.info(f"轨迹与停车场面交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=1).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8 and len(agg_points) < 5:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(
            convex_hull.buffer(10 / COORDINATE_FACTOR).wkt)
        logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if link_sp.distance(park_area_sp) > 50 / COORDINATE_FACTOR:
                continue
            
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(park_area_sp):
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(park_area_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            max_score_result['strategy_value'] = len(points)
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    traj_sort = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.6:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            # 断头路提准
            res, _, _  = utils.is_node_on_dead_road(link_info["node_id"], 
                node_distance_limit=50, aoi_area=None, form=None)
            if res:
                logger.info(f"交割Node[{link_info['node_id']}]断头路提准失败, 跳过交割")
                continue 
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_GATE
            parking_access.strategy_value = link_info['strategy_value']
            parking_access.traj_num = len(points)
            # 每次有效的情报 排序加1 
            traj_sort = traj_sort + 1
            parking_access.traj_sort = traj_sort
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        # 断头路提准
        res, _, _  = utils.is_link_on_dead_road(link_info['link_id'], 50)
        if res:
            logger.info(f"交割Link[{link_info['link_id']}]断头路提准失败, 跳过交割")
            continue 

        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_ACCEPT_TRAJ_AREA_INTERSECT_LINK
        parking_access.strategy_value = parking_access.strategy_value = link_info['strategy_value']
        ctx.parking_access_list.append(parking_access)
    proceed()


def sub_poi_and_nav_gate(ctx: Context, proceed):
    """
    停车场未精准子点
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info("begin sub poi and nav gate")
    park_area_sp = wkt.loads(ctx.parking_info.park_wkt)
    for sub_poi in ctx.sub_pois:
        if sub_poi.road_relation or sub_poi.status != 1:
            continue
        sub_poi_sp = wkt.loads(sub_poi.point_gcj)
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(park_area_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            open_node_gate_list = [x[0] for x in node_gate_list if utils.is_enter_gate(x[0])]
            if open_node_gate_list:
                node_list = ctx.dao.road_dao.get_node_geom(open_node_gate_list)
                # 取距离子点最近的节点
                if node_list:
                    node_res = min(node_list, key=lambda x: sub_poi_sp.distance(wkt.loads(x[0])))
                    parking_access = context.ParkingAccess()
                    parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                    parking_access.node_id = node_res[1]
                    parking_access.node_geom = node_res[0]
                    parking_access.strategy = context.OVERGROUND_AOI_SUB_POI_GATE
                    ctx.parking_access_list.append(parking_access)
                    continue
        # 找子点附近Link
        near_link = ctx.dao.road_dao.get_near_link_by_node_geom(sub_poi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if not near_link:
            continue
        link_node_id = park_utils.get_relation_node_by_link(near_link[0], ctx.parking_info.park_wkt)
        node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_res[0][0]
        parking_access.link_id = near_link[0]
        parking_access.link_geom = near_link[3]
        parking_access.strategy = context.OVERGROUND_AOI_SUB_POI_LINK
        ctx.parking_access_list.append(parking_access)
    proceed()


def cluster_traj_list(ctx: context.Context, proceed):
    """
    根据轨迹点聚类，计算出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info("begin cluster traj list")
    if ctx.aoi_info.wkt == "":
        return proceed()
    # 根据轨迹终点聚类
    clustered_list = utils.cluster_traj_end_list(ctx.traj.filter_aoi_dest_traj_list)
    if not clustered_list:
        return proceed()
    # 聚类后轨迹过滤
    filtered_clustered_list = ctx.filter_cluster_traj_by_park_area(clustered_list)
    # AOI内部聚类的轨迹终点凸包和停车场面无交集，可能是停车场面做小了，再通过轨迹穿行停车场面提召下
    if len(filtered_clustered_list) == 0:
        # filtered_clustered_list = ctx.filter_clustered_list_by_traj_pass_park_poly()
        traj_wkt_list = ctx.filter_clustered_list_by_traj_pass_park_poly()
        traj_sp_list = [wkt.loads(x) for x in traj_wkt_list[0]['traj_list']]
        filtered_clustered_list = utils.cluster_traj_end_list(traj_sp_list) if len(traj_sp_list) > 0 else []
    # 计算聚类轨迹与AOI边框交割点
    final_clustered_list = ctx.calcul_intersect_points(filtered_clustered_list)
    # 根据聚类轨迹计算出入口
    ctx.cal_park_access_by_cluster_traj(final_clustered_list)
    proceed()


def traj_area_intersects_bak(ctx: context.Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info("begin traj area intersects")
    exp_traj_list = ctx.traj.aoi_exp_traj_list
    # 获取轨迹和AOI交割点集
    ints_p_wkt_arr = ctx.calcul_intersect_points_v2()
    if IS_TEST == '0':
        logger.info(f"轨迹与AOI交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len([item for item in db.labels_ if item != -1]) == 0 and len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, db.labels_):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    if ctx.aoi_info.wkt == "":
        return proceed()
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        if not intersects_link_list and len(points) > 10:
            convex_hull_buffer_10m = multipoint.convex_hull.buffer(10 / COORDINATE_FACTOR_NEW)
            intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull_buffer_10m.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if not link_sp.intersects(aoi_sp):
                continue
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": "",
                                       "convex_hull": convex_hull}
            # 穿过AOI分数 +1
            if link_sp.overlaps(aoi_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(aoi_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3 , 取距离聚类附近20m内大门
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            near_gate_node_ids = [x[0] for x in near_gates] if near_gates else []
            if len(near_gate_node_ids) > 0:
                node_list = ctx.dao.road_dao.get_node_geom(near_gate_node_ids)
                if node_list and len([item for item in node_list
                                      if wkt.loads(item[0]).distance(convex_hull) < 20 / COORDINATE_FACTOR_NEW]) > 0:
                    min_node_info = min(node_list, key=lambda item: wkt.loads(item[0]).distance(convex_hull))
                    traj_points_agg_achieve["link_score"] += 3
                    traj_points_agg_achieve["node_id"] = min_node_info[1]
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    # logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            max_score_result['strategy_value'] = len(points)
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.8:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_GATE
            parking_access.strategy_value = link_info['strategy_value']
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        # link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids_new([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        if link_res['len'] > 30:
            parking_access.node_geom = link_info['convex_hull'].representative_point().wkt
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res['geom']
        parking_access.strategy = context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_LINK
        parking_access.strategy_value = link_info['strategy_value']
        ctx.parking_access_list.append(parking_access)
    proceed()


def traj_area_intersects(ctx: context.Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info("begin traj area intersects")
    # 获取轨迹和AOI交割点集
    ints_p_wkt_arr = ctx.calcul_intersect_points_v2()
    if "旅游景点" in ctx.aoi_info.std_tag or "交通设施" in ctx.aoi_info.std_tag:
        ints_p_wkt_arr = ctx.calcul_intersect_points_v3()
    if IS_TEST == '0':
        logger.info(f"轨迹与AOI交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len([item for item in db.labels_ if item != -1]) == 0 and len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, db.labels_):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    if ctx.aoi_info.wkt == "":
        return proceed()
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    # 获取竞品引导点信息
    competitor_points_list = park_utils.get_competitor_park_access_points(ctx.aoi_info.bid, ctx.parking_info.bid,
                                                                          ctx.parking_info.show_tag)
    traj_sort = 0
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR_NEW]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.centroid
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_GATE_NEW
                parking_access.traj_num = len(points)
                # 每次有效的情报 排序加1 
                traj_sort = traj_sort + 1
                parking_access.traj_sort = traj_sort
                ctx.parking_access_list.append(parking_access)
                continue
        # 直接取聚类中心点
        agg_center_sp = convex_hull.centroid
        # 附近LINK信息
        exist_near_link = ctx.dao.road_dao.get_near_link_by_node_geom_new(
            agg_center_sp.buffer(20 / COORDINATE_FACTOR_NEW).wkt, agg_center_sp.wkt)
        # 如果LINK上存在精准出入口, 则过滤
        if exist_near_link:
            near_link_sp = wkt.loads(exist_near_link['geom'])
            if len([item for item in ctx.sub_pois
                    if wkt.loads(item.point_gcj).distance(near_link_sp) < 10 / COORDINATE_FACTOR_NEW
                       and wkt.loads(item.point_gcj).distance(agg_center_sp) < 100 / COORDINATE_FACTOR_NEW]) > 0:
                logger.info(f"聚类中心点: {agg_center_sp.wkt}, 附近LINK存在精准出入口: {exist_near_link}")
                continue

        # 附近竞品引导点
        exist_competitor_points = [item for item in competitor_points_list
                                   if wkt.loads(item).distance(agg_center_sp) < 30 / COORDINATE_FACTOR_NEW]
        # 经验轨迹终点
        exist_near_exp_traj_end = []
        exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
        for traj_sp in exp_traj_list:
            end_point_sp = Point(traj_sp.coords[-1])
            if end_point_sp.distance(agg_center_sp) < 30 / COORDINATE_FACTOR_NEW:
                exist_near_exp_traj_end.append(traj_sp)
        # 附近50m存在精准出入口
        exist_sub_pois_50m = [item.bid for item in ctx.sub_pois
                              if wkt.loads(item.point_gcj).distance(agg_center_sp) < 50 / COORDINATE_FACTOR_NEW
                              and item.road_relation != '']
        logger.info(f"聚类中心点: {agg_center_sp.wkt}, 附近LINK: {exist_near_link}, "
                    f"附近竞品引导点: {exist_competitor_points}, 经验轨迹终点: {exist_near_exp_traj_end}, "
                    f"exist_sub_pois_50m:{exist_sub_pois_50m}")
        # 都不存在直接过滤
        if not exist_competitor_points and not exist_near_exp_traj_end and not exist_near_link:
            continue
        # 如果附近不存在竞品引导点, 且附近50m存在精准出入口, 且20m无LINK
        if exist_sub_pois_50m and not exist_near_link:
            continue
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = convex_hull.centroid.wkt
        parking_access.strategy = context.OVERGROUND_AOI_TRAJ_AREA_INTERSECT_NODE_NEW
        parking_access.traj_num = len(points)
        # 每次有效的情报 排序加1 
        traj_sort = traj_sort + 1
        parking_access.traj_sort = traj_sort
        ctx.parking_access_list.append(parking_access)
    proceed()


def park_end_traj_area_intersects(ctx: context.Context, proceed):
    """
    停车场终点轨迹与面交割
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info("begin park_end_traj_area_intersects")
    if ctx.parking_info.park_area_gcj_wkt == "" or len(ctx.traj.park_dest_traj_list) < 10:
        logger.info(
            f"is_park_area:{not ctx.parking_info.park_area_gcj_wkt}, traj_num:{len(ctx.traj.park_dest_traj_list)}")
        return proceed()
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    
    # 获取轨迹和AOI交割点集
    # 取停车轨迹
    park_area_buffer_20m_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt).buffer(20 / COORDINATE_FACTOR)
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-30)).strftime("%Y-%m-%d")
    parking_traj_list = ctx.dao.traj_feature_dao.get_parking_points_traj_by_wkt(park_area_buffer_20m_sp.wkt, traj_time)
    parking_traj_sp_list = [wkt.loads(item[0]) for item in parking_traj_list]
    ints_p_wkt_arr = utils.get_area_and_traj_intersects_point(ctx.parking_info.park_area_gcj_wkt, parking_traj_sp_list)
    # logger.info(f"轨迹与停车场面交割点集: {ints_p_wkt_arr}")
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8 and len(agg_points) < 5:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if not link_sp.intersects(park_area_sp):
                continue
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(park_area_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(park_area_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                # logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    # logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            max_score_result['strategy_value'] = len(points)
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    traj_sort = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.8:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_AOI_PARK_TRAJ_AREA_INTERSECT_GATE
            parking_access.strategy_value = link_info['strategy_value']
            parking_access.traj_num = len(points)
            # 每次有效的情报 排序加1 
            traj_sort = traj_sort + 1
            parking_access.traj_sort = traj_sort
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_AOI_PARK_TRAJ_AREA_INTERSECT_LINK
        parking_access.strategy_value = parking_access.strategy_value = link_info['strategy_value']
        parking_access.traj_num = len(points)
        # 每次有效的情报 排序加1 
        traj_sort = traj_sort + 1
        parking_access.traj_sort = traj_sort
        ctx.parking_access_list.append(parking_access)
    proceed()


def exp_traj_and_competitor_parking(ctx: Context, proceed):
    """
    经验轨迹 + 竞品信息
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin exp_traj_and_competitor_parking")
    # 竞品引导点信息
    parking_competitors_points = park_utils.get_competitor_park_access_points(ctx.aoi_info.bid, ctx.parking_info.bid,
                                                                              ctx.parking_info.show_tag)
    logger.info(f"竞品引导点信息: {parking_competitors_points}")
    if not parking_competitors_points:
        proceed()
    # 已关联的精准出入口子点
    sub_pois_wkt_list = [wkt.loads(item.point_gcj) for item in ctx.sub_pois if item.road_relation]
    # 查询主点POI信息
    # 经验轨迹 + 竞品信息
    # exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    # exp_end_point_sp_list = [Point(traj_sp.coords[-1]) for traj_sp in exp_traj_list]
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    aoi_bid_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.poi_wkt else None
    for poi_navi_competitor in parking_competitors_points:
        if not poi_navi_competitor:
            continue
        poi_navi_sp = wkt.loads(poi_navi_competitor)
        # 竞品引导点附近30m存在精准出入口子点, 过滤
        if len([item for item in sub_pois_wkt_list if poi_navi_sp.distance(item) < 30 / COORDINATE_FACTOR_NEW]) > 0:
            continue
        if len(sub_pois_wkt_list) > 0 and park_sp.distance(poi_navi_sp) < 10 / COORDINATE_FACTOR_NEW:
            logger.info(f"已存在精准子点竞品引导点距离主点太近:{sub_pois_wkt_list}")
            continue
        # 引导点距离AOI主点较近,过滤
        if aoi_bid_sp and len(sub_pois_wkt_list) > 0 and aoi_bid_sp.distance(poi_navi_sp) < 10 / COORDINATE_FACTOR_NEW:
            logger.info(f"已存在精准子点竞品引导点距离AOI主点太近:{aoi_bid_sp.wkt}")
            continue

        # 竞品30m范围内已存在推送过情报, 过滤
        is_pushed_intelligence = ctx.dao.poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(
            poi_navi_sp.buffer(30 / COORDINATE_FACTOR_NEW).wkt,
            type=['park_access_overground_add', 'park_access_underground_add'], bid=ctx.parking_info.bid)
        if is_pushed_intelligence:
            continue
        # 经验轨迹终点距离竞品距离小于15m or 经验轨迹穿过竞品距离小于30m
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(poi_navi_sp.buffer(20 / COORDINATE_FACTOR_NEW).wkt)
        if node_gate_list:
            node_list = ctx.dao.road_dao.get_node_geom([item[0] for item in node_gate_list])
            node_res = min(node_list, key=lambda x: poi_navi_sp.distance(wkt.loads(x[0])))
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = node_res[1]
            parking_access.node_geom = node_res[0]
            parking_access.strategy = context.OVERGROUND_AOI_EXP_TRAJ_AND_COMPETITOR_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        # 没有大门,就近绑Link
        near_link = ctx.dao.road_dao.get_near_link_by_node_geom_new(poi_navi_sp.buffer(10 / COORDINATE_FACTOR_NEW).wkt,
                                                                    poi_navi_sp.wkt)
        if not near_link:
            continue
        # link 上附近存在精准出入口子点, 过滤
        near_link_sp = wkt.loads(near_link['geom'])
        if len([item for item in sub_pois_wkt_list if near_link_sp.distance(item) < 5 / COORDINATE_FACTOR_NEW]) > 0:
            logger.info(f"link上附近存在精准出入口子点:{near_link},过滤")
            continue
        link_node_id = park_utils.get_relation_node_by_link(near_link[0], poi_navi_sp.wkt)
        node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = poi_navi_sp.wkt
        parking_access.link_id = near_link[0]
        parking_access.link_geom = near_link[3]
        parking_access.strategy = context.OVERGROUND_AOI_EXP_TRAJ_AND_COMPETITOR_LINK
        ctx.parking_access_list.append(parking_access)
    proceed()


def poi_navi_competitor(ctx: Context, proceed):
    """
    竞品引导点下发
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin poi_navi_competitor")
    # 获取竞品引导点
    poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
    if not poi_navi_list:
        return proceed()
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    # 精准出入口子点
    sub_pois_wkt_list = [wkt.loads(item.point_gcj) for item in ctx.sub_pois if item.road_relation]
    # 查询引导点附近是否已存在精准出入口
    poi_navi_wkt_list = [geom_sp.wkt for item in poi_navi_list for geom_sp in wkt.loads(item['geom']).geoms]
    for poi_navi_wkt in poi_navi_wkt_list:
        # 判断竞品20m范围内是否已存在精准出入口, 存在就过滤
        poi_navi_sp = wkt.loads(poi_navi_wkt)
        near_accurate_sub_pois = [sub_poi_sp for sub_poi_sp in sub_pois_wkt_list
                                  if poi_navi_sp.distance(sub_poi_sp) <= 20 / COORDINATE_FACTOR]
        if len(near_accurate_sub_pois) > 0:
            logger.info(f"竞品引导点附近已存在精准出入口子点:{near_accurate_sub_pois}")
            continue
        # 如果已存在精准子点, 引导点距离主点距离太近则过滤
        if len(ctx.sub_pois) > 0 and park_sp.distance(poi_navi_sp) < 10 / COORDINATE_FACTOR:
            logger.info(f"已存在精准子点竞品引导点距离主点太近:{poi_navi_wkt}")
            continue
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            open_node_gate_list = [x[0] for x in node_gate_list if utils.is_enter_gate(x[0])]
            if open_node_gate_list:
                node_list = ctx.dao.road_dao.get_node_geom(open_node_gate_list)
                # 取距离子点最近的节点
                if node_list:
                    node_res = min(node_list, key=lambda x: poi_navi_sp.distance(wkt.loads(x[0])))
                    # 判断Node是否关联其余AOI大门
                    if park_utils.is_relation_other_aoi(ctx.parking_info.bid, ctx.parking_info.parent_id, node_res[1]):
                        logger.info(f"node已关联其余大门:{node_res[1]}")
                        continue
                    parking_access = context.ParkingAccess()
                    parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                    parking_access.node_id = node_res[1]
                    parking_access.node_geom = node_res[0]
                    parking_access.strategy = context.COMMON_POI_NAVI_COMPETITOR_GATE
                    ctx.parking_access_list.append(parking_access)
                    continue
        # 找子点附近Link
        near_link = ctx.dao.road_dao.get_near_link_by_node_geom(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if near_link:
            link_node_id = park_utils.get_relation_node_by_link(near_link[0], ctx.parking_info.park_wkt)
            node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = node_res[0][0]
            parking_access.link_id = near_link[0]
            parking_access.link_geom = near_link[3]
            parking_access.strategy = context.COMMON_POI_NAVI_COMPETITOR_LINK
            ctx.parking_access_list.append(parking_access)
            continue
    return proceed()


def parking_traj_area_intersects(ctx: context.Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info(f"begin parking_traj_area_intersects_and_dead_link")
    exp_traj_list = ctx.traj.aoi_exp_traj_list
    # 获取停车场轨迹, 地下根据主点buffer 100m & AOI面交集
    park_buffer_100m_sp = wkt.loads(ctx.parking_info.park_wkt).buffer(100 / COORDINATE_FACTOR)
    if ctx.aoi_info.wkt:
        aoi_sp = wkt.loads(ctx.aoi_info.wkt)
        park_buffer_100m_sp = park_buffer_100m_sp.intersection(aoi_sp)
    park_area = ctx.parking_info.park_area_gcj_wkt if ctx.parking_info.park_area_gcj_wkt else park_buffer_100m_sp.wkt
    # 取停车轨迹
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-30)).strftime("%Y-%m-%d")
    parking_traj_list = ctx.dao.traj_feature_dao.get_parking_points_traj_by_wkt(park_area, traj_time)
    if not parking_traj_list:
        return proceed()
    parking_traj_list = [wkt.loads(item[0]) for item in parking_traj_list]
    # 取轨迹与AOI面 or 停车航面交点
    park_area_gcj_wkt = ctx.parking_info.park_area_gcj_wkt \
        if ctx.parking_info.park_area_gcj_wkt else ctx.parking_info.park_area_aoi_30m_wkt
    area_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else wkt.loads(park_area_gcj_wkt)
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_sp.wkt, parking_traj_list, False)
    if not intersect_point_list:
        logger.info(f"停车轨迹与面没有交点;exp_traj_list:{len(parking_traj_list)}")
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=10 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    if ctx.aoi_info.wkt == "":
        return proceed()
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if not link_sp.intersects(aoi_sp):
                continue
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.overlaps(aoi_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(aoi_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                # logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    # logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    traj_sort = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.8:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_AOI_PARKING_TRAJ_CLUSTER_DEAD_LINK_AND_GATE
            parking_access.traj_num = len(points)
            # 每次有效的情报 排序加1 
            traj_sort = traj_sort + 1
            parking_access.traj_sort = traj_sort
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_AOI_PARKING_TRAJ_CLUSTER_DEAD_LINK
        parking_access.traj_num = len(points)
        # 每次有效的情报 排序加1 
        traj_sort = traj_sort + 1
        parking_access.traj_sort = traj_sort
        ctx.parking_access_list.append(parking_access)
    proceed()


def park_desc_traj_area_intersects(ctx: context.Context, proceed):
    """
    停车场自身轨迹与停车场边框交割点
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info(f"begin park_desc_traj_area_intersects")
    if len(ctx.traj.park_dest_traj_list) < 10 or not ctx.parking_info.park_area_gcj_wkt:
        logger.info(
            f"停车场轨迹数量小于10:{len(ctx.traj.park_dest_traj_list)}, 或停车场边框为空:{not ctx.parking_info.park_area_gcj_wkt}")
        return proceed()
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list

    # 停车场终点轨迹交割聚类
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(ctx.parking_info.park_area_gcj_wkt,
                                                                               ctx.traj.park_dest_traj_list, False)
    if not intersect_point_list:
        logger.info(f"停车轨迹与面没有交点;exp_traj_list:{len(ctx.traj.park_dest_traj_list)}")
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=10 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    if ctx.aoi_info.wkt == "":
        return proceed()
    park_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    park_buffer_5m_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    poi_navi_competitors = []
    aoi_poi_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.aoi_info.bid)
    if aoi_poi_navi_competitors:
        poi_navi_competitors = aoi_poi_navi_competitors
    park_navi_competitors = ctx.dao.aoi_resume_query.get_poi_navi_competitors(ctx.parking_info.bid)
    if park_navi_competitors:
        poi_navi_competitors += park_navi_competitors
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)

    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.8:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(2 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            kind = link_info[2]
            link_wkt = link_info[3]
            link_sp = wkt.loads(link_wkt)
            if not link_sp.intersects(park_buffer_5m_sp) or kind == 10:
                continue
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过AOI分数 +1
            if link_sp.overlaps(park_buffer_5m_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(park_buffer_5m_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_5m_sp.wkt)
            open_gates = [x[0] for x in near_gates if utils.is_enter_gate(x[0])]
            if len(open_gates) > 0:
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = open_gates[0]
                # logger.info(f"link上存在可通行的道路大门+3")
            elif len(near_gates) > 0 and len(open_gates) == 0:
                continue
            # link 存在子点+2
            exist_sub_poi_in_link = [sub_poi for sub_poi in ctx.sub_pois
                                     if link_buffer_5m_sp.intersects(wkt.loads(sub_poi.point_gcj))
                                     and not sub_poi.road_relation]
            if len(exist_sub_poi_in_link) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"link存在子点+2")
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            # 附近存在竞品引导点 +2
            if poi_navi_competitors:
                intersects_poi_navi = [poi_navi_wkt for poi_navi_wkt in poi_navi_competitors
                                       if link_buffer_5m_sp.intersects(wkt.loads(poi_navi_wkt))]
                if len(intersects_poi_navi) > 0:
                    traj_points_agg_achieve["link_score"] += 2
                    # logger.info(f"附近存在竞品引导点 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)

    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.8:
            continue
        valid_score_count += link_info["link_score"]
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.OVERGROUND_AOI_PARK_DEST_TRAJ_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        # 获取关联的link
        if not park_utils.filter_link(link_info["link_id"]):
            logger.info(f"link关联不合法 {link_info['link_id']} 已过滤")
            continue
        # 交割Link需要通过经验轨迹提准
        is_valid_link = park_utils.is_valid_exp_traj_distance_link(link_info["link_id"], ctx.traj.aoi_exp_traj_list,
                                                                   ctx.traj.park_exp_traj_list)
        if not is_valid_link:
            logger.info(f"交割Link[{link_info['link_id']}]经验轨迹提准失败, 跳过交割")
            continue
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], ctx.parking_info.park_wkt)
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.OVERGROUND_AOI_PARK_DEST_TRAJ_INTERSECT_LINK
        ctx.parking_access_list.append(parking_access)
    proceed()


def exp_traj_and_gate(ctx: Context, proceed):
    """
    经验轨迹终点(情报提召, 无情报且停车场无入口时使用)
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin exp_traj_and_gate")
    exp_traj_list = ctx.traj.origin_aoi_exp_traj_list + ctx.traj.origin_park_exp_traj_list
    if len(exp_traj_list) == 0:
        return proceed()
    # 如果停车场在AOI外部,只取停车场经验轨迹
    park_in_aoi_outside = False
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
    aoi_area_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
    if park_area_sp and aoi_area_sp and park_area_sp.distance(aoi_area_sp) > 10 / COORDINATE_FACTOR_NEW:
        logger.info(f"停车场在AOI外部,只取停车场经验轨迹")
        park_in_aoi_outside = True

    # 获取通量最大经验轨迹top3经验轨迹, 旅游景区&交通枢纽 取停车场经验轨迹, 停车场没有则用父点经验轨迹
    is_special_category = True if "旅游景点" in ctx.aoi_info.std_tag or "交通设施" in ctx.aoi_info.std_tag else False
    aoi_exp_traj_list = sorted(ctx.traj.origin_aoi_exp_traj_list, key=lambda x: x[1], reverse=True)
    high_confidence_exp_traj_list = []
    if len(ctx.traj.origin_aoi_exp_traj_list) > 0 and not park_in_aoi_outside:
        for traj_info in aoi_exp_traj_list:
            if traj_info[1] < 5 or (is_special_category and len(ctx.traj.origin_park_exp_traj_list) > 0):
                continue
            # 如果起点在AOI内部,则过滤
            start_point_sp = Point(wkt.loads(traj_info[0]).coords[0])
            if aoi_area_sp and aoi_area_sp.contains(start_point_sp):
                logger.info(f"经验轨迹起点在AOI内部,过滤: {traj_info[0]}")
                continue

            if len(high_confidence_exp_traj_list) < 3 or traj_info[3] > 0.05 or traj_info[4] > 0:
                high_confidence_exp_traj_list.append(
                    {'geom': wkt.loads(traj_info[0]), 'cuid_repetition_rate': traj_info[4]})
        if len(high_confidence_exp_traj_list) == 0:
            high_confidence_exp_traj_list = [
                {'geom': wkt.loads(aoi_exp_traj_list[0][0]), 'cuid_repetition_rate': aoi_exp_traj_list[0][4]}]
    high_confidence_park_exp_traj_list = []
    if len(ctx.traj.origin_park_exp_traj_list):
        park_exp_traj_list = sorted(ctx.traj.origin_park_exp_traj_list, key=lambda x: x[1], reverse=True)
        for traj_info in park_exp_traj_list:
            if traj_info[1] < 5:
                continue
            # 如果起点在AOI内部,则过滤
            start_point_sp = Point(wkt.loads(traj_info[0]).coords[0])
            if aoi_area_sp and aoi_area_sp.contains(start_point_sp):
                logger.info(f"经验轨迹起点在AOI内部,过滤: {traj_info[0]}")
                continue
            if len(high_confidence_exp_traj_list) < 3 or traj_info[3] > 0.01 or traj_info[4] > 0:
                high_confidence_park_exp_traj_list.append(
                    {'geom': wkt.loads(traj_info[0]), 'cuid_repetition_rate': traj_info[4]})
        if len(high_confidence_park_exp_traj_list) == 0:
            high_confidence_park_exp_traj_list = [
                {'geom': wkt.loads(park_exp_traj_list[0][0]), 'cuid_repetition_rate': park_exp_traj_list[0][4]}]
    high_confidence_exp_traj_list += high_confidence_park_exp_traj_list

    # 开始根据经验轨迹终点查找附近的大门和Link
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    aoi_area_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
    logger.info(f"\n经验轨迹提召")
    if IS_TEST == '0':
        for exp_traj_sp in high_confidence_exp_traj_list:
            print(exp_traj_sp['geom'].wkt)
    for exp_traj_info in high_confidence_exp_traj_list:
        exp_traj_sp = exp_traj_info['geom']
        end_point_sp = Point(exp_traj_sp.coords[-1])
        # 如果已存在精准子点, 引导点距离主点距离太近 or 经验轨迹通过某个精准子点 则过滤
        if len(ctx.sub_pois) > 0 and (park_sp.distance(end_point_sp) < 30 / COORDINATE_FACTOR
                                      or len([item for item in ctx.sub_pois if exp_traj_sp.intersects(
                    wkt.loads(item.point_gcj).buffer(25 / COORDINATE_FACTOR))]) > 0):
            logger.info(f"已存在精准子点且距离终点过近:{end_point_sp.wkt}")
            continue

        intersects_point_sp = None
        # 如果停车场在AOI内, 且经验轨迹终点距离AOI边框较远, 则取与边框交割点作为情报点
        # 获取经验轨迹在AOI内部长度
        exp_traj_in_aoi_len = exp_traj_sp.intersection(aoi_area_sp).length if aoi_area_sp else 0
        if aoi_area_sp and aoi_area_sp.intersects(park_sp) \
                and exp_traj_in_aoi_len > 15 / COORDINATE_FACTOR_NEW \
                and aoi_area_sp.contains(end_point_sp) \
                and aoi_area_sp.intersects(exp_traj_sp):
            end_point_wkt = utils.cal_line_and_area_intersect_point(exp_traj_sp.wkt, aoi_area_sp.wkt)
            intersects_point_sp = wkt.loads(end_point_wkt) if end_point_wkt else None
            logger.info(f"经验轨迹终点距离AOI边框较远, 取交割点作为情报点: {end_point_wkt},{exp_traj_sp.wkt}")

        # 如果停车场不在AOI内部, 且存在停车场边框, 取停车场边框与经验轨迹交割点
        park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
        if park_area_sp and park_area_sp.intersects(exp_traj_sp) \
                and end_point_sp.distance(park_area_sp.exterior) > 15 / COORDINATE_FACTOR \
                and park_sp.distance(aoi_area_sp) > 10 / COORDINATE_FACTOR:
            intersects_point = utils.cal_line_and_area_intersect_point(exp_traj_sp.wkt, park_area_sp.wkt)
            intersects_point_sp = wkt.loads(intersects_point) if intersects_point else None
            logger.info(f"经验轨迹终点距离停车场边框较远, 取交割点作为情报点: {intersects_point},{exp_traj_sp.wkt}")

        # 如果经验轨迹终点距离边框较远,则取与边框交割点
        end_point_sp = intersects_point_sp if intersects_point_sp else end_point_sp
        logger.info(f"\n经验轨迹位置:{end_point_sp.wkt},{exp_traj_sp.wkt},交割点:{intersects_point_sp}")
        near_gates = ctx.dao.road_dao.get_nav_gate_by_geom(end_point_sp.buffer(20 / COORDINATE_FACTOR_NEW).wkt)
        logger.info(f"附近大门:{near_gates}")
        if near_gates:
            node_ids = [item[0] for item in near_gates]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            node_info = min(node_list, key=lambda x: wkt.loads(x[0]).distance(park_sp))
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = node_info[1]
            parking_access.node_geom = node_info[0]
            parking_access.strategy = context.OVERGROUND_AOI_EXP_TRAJ_END_AND_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        # 不存在大门查找Link, link仅关注高置信熟路轨迹 & 无出入口子点
        logger.info(f"tag:{ctx.aoi_info.std_tag},len_sub_poi:{len(ctx.sub_pois)}")
        if exp_traj_info['cuid_repetition_rate'] > 0 or len(ctx.sub_pois) == 0 or "旅游景点" in ctx.aoi_info.std_tag:
            kind = 7
            if "旅游景点" in ctx.aoi_info.std_tag or '交通设施' in ctx.aoi_info.std_tag:
                kind = 6
            park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
            if ("旅游景点" in ctx.aoi_info.std_tag or '交通设施' in ctx.aoi_info.std_tag) \
                    and park_area_sp and park_area_sp.distance(end_point_sp) > 50 / COORDINATE_FACTOR_NEW:
                continue
            near_link = ctx.dao.road_dao.get_near_link_by_node_geom(end_point_sp.buffer(10 / COORDINATE_FACTOR_NEW).wkt,
                                                                    kind=kind)
            if near_link:
                link_node_id = park_utils.get_relation_node_by_link(near_link[0], ctx.parking_info.park_wkt)
                node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_LINK
                parking_access.node_id = link_node_id
                # parking_access.node_geom = node_res[0][0]
                parking_access.node_geom = end_point_sp.wkt
                parking_access.link_id = near_link[0]
                parking_access.link_geom = near_link[3]
                parking_access.strategy = context.OVERGROUND_AOI_EXP_TRAJ_END_AND_LINK
                ctx.parking_access_list.append(parking_access)
                logger.info(f"经验轨迹关联LINK\t{near_link[0]}")

    return proceed()


def get_exp_and_area_intersects_point(exp_traj, area_wkt, park_area):
    """
    获取经验轨迹与面相交点
    Args:
        exp_traj: 经验轨迹
        aoi_area: 面
        park_area: 面

    Returns:

    """
    intersects_point = None
    if area_wkt:
        intersects_point = utils.cal_line_and_area_intersect_point(exp_traj, area_wkt)
    if park_area and not intersects_point:
        intersects_point = utils.cal_line_and_area_intersect_point(exp_traj, park_area)
    return intersects_point


def area_intersects_link(ctx: Context, proceed):
    """
    针对没有任何特征且没有精准子点, 存在面的特征垂类(当前只针对景区), 直接绑面相交的Link作为情报点
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin area_intersects_link")
    if len(ctx.parking_access_list) > 0 or len(
            ctx.sub_pois) > 0 or '旅游景点' not in ctx.aoi_info.std_tag or not ctx.parking_info.park_area_gcj_wkt:
        return proceed()
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    # 获取面相交的Link
    intersect_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt_new(park_area_sp.wkt)
    if not intersect_link_list:
        return proceed()
    valid_link_list = []
    for link_info in intersect_link_list:
        link_sp = wkt.loads(link_info['geom']).buffer(0.1 / COORDINATE_FACTOR)
        e_nid = link_info['e_nid']
        node_list = ctx.dao.road_dao.get_node_geom([e_nid])
        if not node_list:
            continue
        if link_info['kind'] not in [8, 9] or not link_sp.overlaps(park_area_sp):
            continue
        node_sp = wkt.loads(node_list[0][0])
        if link_info['dir'] != 1 and not park_area_sp.contains(node_sp):
            continue
        valid_link_list.append(link_info)
    if len(valid_link_list) == 0:
        logger.info(f"没有相交的Link")
        return proceed()
    # 多条穿过Link取最短的
    link_res = min(valid_link_list, key=lambda x: x['len'])
    link_node_id = park_utils.get_relation_node_by_link(link_res['link_id'], ctx.parking_info.park_wkt)
    node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
    parking_access = context.ParkingAccess()
    parking_access.type = context.PARKING_ACCESS_TYPE_LINK
    parking_access.node_id = link_node_id
    parking_access.node_geom = node_res[0][0]
    parking_access.link_id = link_res['link_id']
    parking_access.link_geom = link_res['geom']
    parking_access.strategy = context.OVERGROUND_AOI_AREA_OVERLAPS_LINK
    ctx.parking_access_list.append(parking_access)
    return proceed()


def park_access_filter(ctx: Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_qc(ctx)
    return proceed()


def competitor_diff(ctx: context.Context, proceed):
    """
    小区竞品过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin competitor_diff")
    park_access_navi_competitor_list = ctx.dao.poi_online_query.get_competitor_village_by_park_bids(
        [ctx.parking_info.bid])
    logger.info(f"小区竞品数据抓取:{park_access_navi_competitor_list}")
    if not park_access_navi_competitor_list:
        return proceed()
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        if park_sp.distance(poi_navi_sp) > 150 / COORDINATE_FACTOR:
            continue
        # 如果已存在精准子点, 引导点距离主点距离太近则过滤
        if len(ctx.sub_pois) > 0 and park_sp.distance(poi_navi_sp) < 10 / COORDINATE_FACTOR:
            continue
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: poi_navi_sp.distance(wkt.loads(x[0])))
                # 判断Node是否关联其余AOI大门
                if park_utils.is_relation_other_aoi(ctx.parking_info.bid, ctx.parking_info.parent_id, node_res[1]):
                    logger.info(f"node已关联其余大门:{node_res[1]}")
                    continue
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.COMMON_POI_NAVI_HOURSE_COMPETITOR_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 找子点附近Link
        near_link = ctx.dao.road_dao.get_near_link_by_node_geom(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if near_link:
            link_node_id = park_utils.get_relation_node_by_link(near_link[0], ctx.parking_info.park_wkt)
            node_res = ctx.dao.road_dao.get_node_geom([link_node_id])
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = node_res[0][0]
            parking_access.link_id = near_link[0]
            parking_access.link_geom = near_link[3]
            parking_access.strategy = context.COMMON_POI_NAVI_HOURSE_COMPETITOR_LINK
            ctx.parking_access_list.append(parking_access)
            continue
    return proceed()


# 距离停车场太远, 直接过滤


def get_park_info(pg, park_bid):
    """
    获取百度停车场信息
    """
    sql = f"""
        select name, show_tag, st_astext(gcj_geom), open_limit_new, fee, std_tag
        from park_online_data where bid = '{park_bid}'
    """
    pg.cursor_poi.execute(sql)
    res = pg.cursor_poi.fetchone()
    if res is None:
        return None
    return res


def save_park_access_intelligence_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_access_intelligence_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type}, 是否有效:{item.is_valid}, node_id位置\t{item.node_id},"
                        f"node_geom位置\t{item.node_geom},策略:{item.strategy}, park_bid:{ctx.parking_info.bid},"
                        f"traj_num:{item.traj_num},traj_sort:{item.traj_sort},root_pv:{ctx.parking_info.root_pv},"
                        f"remark:{item.remark}"
                        f",link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, strategy_values:{item.strategy_value}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, 是否有效:{park_access.is_valid},node_id位置\t{park_access.node_id}, "
                        f"策略:{park_access.strategy}, strategy_value:{park_access.strategy_value}, "
                        f"link_id\t{park_access.link_id}, 过滤原因:{park_access.filter_reason}")
            continue
        guid = str(uuid.uuid4()).replace("-", "")
        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': ctx.intelligence_type,
            'batch_number': f"park_access_overground_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': f"park_access_overground_add_{guid}",
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
            'park_access_bid': '',
            'confidence': park_access.confidence,
            'remark': park_access.remark,

        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)

    proceed()


def main(bid_list: list):
    """
    地上停车场情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场', '停车场', '立体停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"开始挖掘地上停车场情报:{parking[7]}")
            ctx = Context(parking)
            ctx.intelligence_type = "park_access_overground_add"
            ctx.init_context()
            ctx.get_park_sub_pois()
            # AOI 场景
            if ctx.aoi_info.bid:
                # 获取出入口信息
                ctx.get_aoi_blu_access_list()
                # 轨迹过滤AOI范围外的
                ctx.filter_traj_by_aoi()
                # 计算AOI出入口轨迹通量
                ctx.cal_dest_traj_pass_blu_access()
            ctx.traj.filter_aoi_dest_traj_list = ctx.traj.dest_traj_list
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[7]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    # 取四大垂类停车场BID
    if bid_file == '0' and bids == '0':
        poi_online_query = PoiOnlineQuery()
        park_categories_list = poi_online_query.get_4categories_park_bids()
        bid_list = list(set([item['bid'] for item in park_categories_list]))
    bid_lists = np.array_split(np.array(bid_list), progress)
    main(np.array(bid_list))
    # with multiprocessing.Pool(progress) as pool:
    #     res = pool.map(main, bid_lists)
