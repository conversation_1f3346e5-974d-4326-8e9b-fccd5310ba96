"""
parse tc aoi
"""
import sys
import json
import util
import shapely.wkt

defaultencoding = 'utf-8'
if sys.getdefaultencoding() != defaultencoding:
    reload(sys)
    sys.setdefaultencoding(defaultencoding)

file = open(sys.argv[1])
for line in file:
    items = line.split("\t")
    json_info = items[14]
    try:
        json_info = json.loads(json_info)
        is_clean = json_info['check']
        aoi = json_info['area']
        tencent_id = items[0]
        bid = items[1]
        name = items[2]
        address = items[3]
        city = items[4]

        if is_clean is True and ";" in aoi:
            shape = "POLYGON (("
            coords = aoi.split(";")
            start = coords[0]
            start_x, start_y = float(coords[0].split(",")[0]), float(coords[0].split(",")[1])
            (start_x, start_y) = util.coordtrans("gcj02ll", "bd09mc", start_x, start_y)
            for coord in coords:
                x = float(coord.split(",")[0])
                y = float(coord.split(",")[1])
                (x, y) = util.coordtrans("gcj02ll", "bd09mc", x, y)
                shape += str(x) + " " + str(y) + ", "
            shape = shape + str(start_x) + " " + str(start_y) + "))"
            shape = shapely.wkt.loads(shape)
            center = shape.centroid
            point_x, point_y = center.x, center.y
            # point_x, point_y = 1,1
            print tencent_id + "\t" + name + "\t" + city + "\t" + address + "\t" + str(point_x) + "\t" + \
                str(point_y) + "\t" + "" + "\t" + "" + "\t" "1" + "\t" + "QB_TC" + "\t" + "1" + "\t" + str(shape) + \
                "\t" + bid


    except Exception as e:
        print e
