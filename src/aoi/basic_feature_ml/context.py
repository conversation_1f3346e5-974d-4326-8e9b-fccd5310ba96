# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地上下文
"""
import sys
import os

from pathlib import Path

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi.basic_feature_ml import utils


class PathInfo:
    """
    文件目录信息
    """
    green_base_dir: Path
    batch: str  # 批次
    image_map_dir: Path  # 影像图
    discern_result_dir: Path  # 识别成果
    headmap_dir: Path  # 识别成果
    binary_image_dir: Path  # 二值图
    vectorize_result_dir: Path  # 矢量化成果

    def __init__(self, base_dir: Path, batch: str):
        self.batch = batch
        self.green_base_dir = base_dir
        self.image_map_dir = self.green_base_dir / self.batch / 'image_map'
        self.discern_result_dir = self.green_base_dir / self.batch / 'discern_result'
        self.binary_image_dir = self.green_base_dir / self.batch / 'binary_image'
        self.vectorize_result_dir = self.green_base_dir / self.batch / 'vectorized_result'
        self.headmap_dir = self.green_base_dir / self.batch / 'headmap'

    def ensure_dirs(self, enable_debug=True):
        """
        创建输出目录，如果其不存在。
        """
        utils.make_dir(self.green_base_dir / self.batch)
        utils.make_dir(self.image_map_dir)
        utils.make_dir(self.discern_result_dir)
        utils.make_dir(self.binary_image_dir)
        utils.make_dir(self.vectorize_result_dir)
        utils.make_dir(self.headmap_dir)


class Context:
    batch: str  # 批次
    path_info: PathInfo
    shapely_wkt_list: list
    discern_id_list: list
    afs_shell_cmd: str
    afs_discern_result_filepath: str
    local_discern_result_filepath: str
