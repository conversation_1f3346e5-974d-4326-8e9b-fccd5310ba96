# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步精准子POI
"""
import datetime
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi import PgQuery
from aoi import PgUpdate

dao = PgQuery.PgQuery()
pg_rw_dao = PgUpdate.PgUpdate()

STD_TAG_LIST_EQUAL = "'房地产;住宅区','医疗;综合医院', '医疗;专科医院', '购物;购物中心'"
STD_TAG_LIST_CONTAINS = "旅游景点"
STD_TAG_TO_CATEGORY_MAP = {"房地产;住宅区": "住宅", "医疗;综合医院": "医院", "医疗;专科医院": "医院", "购物;购物中心": "购物中心"}

NAV_AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=map-dataop --password=map-dataop"
SUB_POI_AFS_DIR = "afs://aries.afs.baidu.com:9902/user/map-de-gzhxy/mis/sub_poi"

def run():
    """
    同步精准子POI
    Returns:

    """
    aoi_info_list = dao.get_accurate_v2_subpoi(STD_TAG_LIST_EQUAL, STD_TAG_LIST_CONTAINS)
    data_list = [["category", "main_bid", "main_bid_pv", "son_poi_bid", "son_poi_bid_pv", "son_poi_name", "uid"]]
    number = 0
    for aoi_info in aoi_info_list:
        number += 1
        print(f"当前进度:{number}, 总量:{len(aoi_info_list)}")
        aoi_geom_info = dao.get_aoi_by_bid(aoi_info[1])
        if aoi_geom_info is None or len(aoi_geom_info) == 0:
            continue
        tag = aoi_info[2]
        if tag in STD_TAG_TO_CATEGORY_MAP:
            category = STD_TAG_TO_CATEGORY_MAP[tag]
        elif STD_TAG_LIST_CONTAINS in tag:
            category = "景区"
        else:
            continue
        # 查询范围内所有子点信息
        poi_list = dao.get_intersects_poi_list(aoi_geom_info[2])
        if poi_list is None or len(poi_list) == 0:
            continue
        # 查询到达PV信息
        son_bids = [item[0] for item in poi_list]
        end_poi_pv_list = dao.get_arrive_poi_pv_list(son_bids)
        if end_poi_pv_list is None or len(end_poi_pv_list) == 0:
            continue
        end_poi_pv_map = {item[0]: item for item in end_poi_pv_list}
        for poi in poi_list:
            if poi[0] == aoi_info[1] or poi[0] not in end_poi_pv_map:
                continue
            # 过滤掉到达PV小于5的子点点
            if end_poi_pv_map[poi[0]][1] <= 5:
                continue
            data_list.append([category, aoi_info[1], str(aoi_info[3]), poi[0], str(end_poi_pv_map[poi[0]][1]), poi[1],
                              end_poi_pv_map[poi[0]][2]])
    tmp_result_file_name = f"./{datetime.date.today().strftime('%Y%m%d')}.csv"
    # 成果写入
    file = open(tmp_result_file_name, 'w')
    for statistics_data in data_list:
        strs = '\t'.join(statistics_data) + "\n"
        file.write(strs)
    file.close()
    # 写入AFS
    os.system(f"{NAV_AFS_SHELL} put --override {tmp_result_file_name} {SUB_POI_AFS_DIR}")
    os.remove(tmp_result_file_name)


if __name__ == '__main__':
    run()
