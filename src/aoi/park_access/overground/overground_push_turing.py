# -*- coding: utf-8 -*-
"""
地上推送情报到人工作业
"""
import sys
import os
from loguru import logger
import tqdm
import requests

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi import utils
from src.aoi.park_access import park_access_qc as qc

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()

# logger.remove()
log_path = "./log/overground_intelligence_push.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
# TCQ_BATCH_ID = "TCQDSH2025022001"
# 418北京
# TCQ_BATCH_ID = "TCQDSH2025032501"
# 418上海
# TCQ_BATCH_ID = "TCQDSH2025040102"
# 路侧
# TCQ_BATCH_ID = "TCKCRK2025050101"
# 例行化
# TCQ_BATCH_ID = "TCKCRK2025052002"
# 吉利
TCQ_BATCH_ID = "TCKCRK2025062001"


def park_access_intelligence_push(push_number=10, strategy=None, parent_pv=0):
    """
    出入口情报推送, 优先推送四大垂类,然后PV从高到低推送
    Returns:
    """
    # 如果策略不为空,获取策略所有BID聚合推送
    park_bids = None
    content = ''
    # content = '停车场新增'
    content = '全时轨迹高优'
    if strategy:
        park_access_intelligence_list = poi_online_query.get_park_access_add_intelligence_by_kv(
            type='park_access_overground_add', status='INIT', strategy=strategy, pv=parent_pv)
        if not park_access_intelligence_list:
            logger.info("无待推送情报")
            return
        park_bids = [item['bid'] for item in park_access_intelligence_list]

    # 获取待推送情报主点
    strategy = ['COMMON_POI_NAVI_COMPETITOR_DIFF','COMMON_POI_NAVI_HOURSE_COMPETITOR_GATE',
                  'UNDERGROUND_AOI_POI_NAVI_AND_BLU_ACCESS','UNDERGROUND_AOI_POI_NAVI_COMPETITOR',
                  'UNDERGROUND_AOI_TRAJ_CLUSTER_DEAD_LINK_AND_GATE','UNDERGROUND_AOI_BLU_ACCESS',
                  'COMMON_POI_NAVI_COMPETITOR_GATE','COMMON_POI_NAVI_HOURSE_COMPETITOR_GATE',
                  'COMMON_POI_NAVI_COMPETITOR_GATE',
                  'COMMON_POI_NAVI_HOURSE_COMPETITOR_LINK','OVERGROUND_AOI_TRAJ_AREA_INTERSECT_GATE_NEW',
                  'OVERGROUND_AOI_PARKING_TRAJ_CLUSTER_DEAD_LINK_AND_GATE','COMMON_POI_NAVI_COMPETITOR_DIFF']
    strategy = None
    park_access_intelligence_list = poi_online_query.get_park_access_add_intelligence_by_kv(
        type='park_access_overground_add', status='INIT', bids=park_bids, pv=parent_pv, 
        strategy=strategy, is_get_all_access=False)
    if not park_access_intelligence_list:
        logger.info("无待推送情报")
        return
    park_access_intelligence_map = {}
    for park_access_intelligence in park_access_intelligence_list:
        if park_access_intelligence['bid'] not in park_access_intelligence_map:
            park_access_intelligence_map[park_access_intelligence['bid']] = [park_access_intelligence]
        else:
            park_access_intelligence_map[park_access_intelligence['bid']].append(park_access_intelligence)
    # 开始推送
    pushed_park_access_list = []
    for park_bid, park_access_list in tqdm.tqdm(park_access_intelligence_map.items()):
        if len(pushed_park_access_list) >= push_number:
            logger.info(f"本次推送结束,共推送:{len(pushed_park_access_list)}")
            return
        # 查询停车场信息
        park_poi_list = poi_online_query.get_parking_list_by_kv(bids=[park_bid])
        if not park_poi_list:
            logger.warning(f"未找到停车场POI:{park_bid}")
            continue
        if park_poi_list[0]['status'] == 3:
            logger.warning(f"停车场暂停营业，暂不下发:{park_poi_list[0]['status']}")
            continue
        if len(park_bid) < 5:
            logger.warning(f"停车场bid异常，暂不下发:{park_bid}")
            continue
        park_access_list = poi_online_query.get_park_access_add_intelligence_by_kv(
                                status='INIT', 
                                bids=[park_bid],
                                type=None,
                                is_get_all_access=True)   
        if not park_access_intelligence_list:
            logger.info(f"无停车场出入口情报:{park_bid}")
            continue

        push_park_access_list = []
        valid_intelligence_ids = []
        for item in park_access_list:
            # 判断线上是否已精准
            if qc.is_park_access_already_accurate(park_bid, item['node_id'], item['link_id']):
                logger.info(f"线上已精准:{item}")
                poi_online_rw.update_park_access_intelligence_status_by_id([item['id']], status='ALREADY_EXIST')
                continue
            
            # 判断线上是否已自动化新增精准出入口
            if qc.is_park_access_already_auto_add(park_bid,  item['node_id'], item['link_id'], item['geom']):
                logger.info(f"线上已精准-自动化新增:{item}")
                poi_online_rw.update_park_access_intelligence_status_by_id([item['id']], status='ALREADY_AUTO')
                continue
            park_access_name, show_tag = get_park_access_name(park_poi_list[0]['name'], item['node_id'],
                                                              item['link_id'])
            push_park_access_list.append({
                'bid': '',
                'name': park_access_name,
                'wkt': item['geom'],
                'show_tag': f"停车场{show_tag}",
                "status": 1,
                "parent_bid": "",
                'node_id': item['node_id'],
                'link_id': item['link_id'],
            })
            valid_intelligence_ids.append(item['id'])
        if len(valid_intelligence_ids) > 0:
            source_id = f"park_access_overground_add_{park_access_list[0]['source_id']}"
            qb_id = park_access_request_turing(park_poi_list[0], push_park_access_list, source_id, content)
            if qb_id:
                print(f"push_success:{qb_id}")
            push_status = 'PUSHED' if qb_id else 'INIT'
            poi_online_rw.update_park_access_intelligence_status_batch_by_id(valid_intelligence_ids, status=push_status, 
                                                                    batch=TCQ_BATCH_ID, resp=qb_id)
            pushed_park_access_list.append(park_bid)


def get_park_access_name(park_name, long_node_id, lone_link_id):
    """
    获取停车场出入口名称
    Args:
        park_name:
        long_node_id:
        lone_link_id:

    Returns:

    """
    park_access_name = park_name.replace("-地下停车场", "地下停车场").replace("-地上停车场", "地上停车场").replace(
        "-停车场", "停车场").replace("-立体停车场", "立体停车场")
    if lone_link_id == '' or long_node_id == '':
        return f"{park_access_name}-入口", '入口'
    if lone_link_id:
        return f"{park_access_name}-入口", '入口'
    # 查大门通行性
    nav_gate_semantic = utils.get_nav_gate_semantic(long_node_id)
    if nav_gate_semantic not in ['入口', '出入口']:
        logger.error(f"大门通行性查询失败:{park_access_name}-{long_node_id}-{lone_link_id}-{nav_gate_semantic}")
        # return False, False # 通行性失败,先按照入口推人工核实
        return f"{park_access_name}-入口", '入口'
    return f"{park_access_name}-{nav_gate_semantic}", nav_gate_semantic


def park_access_request_turing(parking_info, park_access_list, source_id, content="例行化"):
    """
    出入口情报推送推送图灵
    Args:
        parking_info:
        park_access_list:
        source_id:

    Returns:

    """
    access_info = []
    park_bid = parking_info['bid']
    for park_access in park_access_list:
        access_info.append({
            'access_bid': '',
            'node_id': park_access['node_id'],
            'link_id':  park_access['link_id'],
            'gate_wkt': park_access['wkt'],
        })
    data = {
        "park_bid": park_bid,
        "access": access_info,
        'pic_urls': [],
        'content': content
    }
        #     'pic_urls': [
        #     {
        #         'x': 115.818208,
        #         'y': 32.92387,
        #         'pic_url': 'http://turing.map.baidu.com/picpre/bos/P_a90315e2946f68c8f8390fd097cbaad0_1747357193268'
        #     }
        # ]
    res = push_turing(15, TCQ_BATCH_ID, source_id, park_bid, 1, **data)
    logger.info(f"推送结果:{res}")
    return res


def push_turing(src, batch_id, source_id, bid, qb_type, **data):
    """
    
    """
    data = {
        "src": src,  # 必填，15 出入口  16 停车场
        "ref_qb_id": source_id,
        "ref_qb_batch_id": batch_id,  # 必填，批次号
        "main_poi_bid": bid,
        "qb_type": qb_type, # 必填，1出入口新增 2关联错  3 冗余
        "extra": {
            "detail": data
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


def park_access_request(parking_info, park_access_list, source_id):
    """
    出入口情报推送推送
    Args:
        parking_info:
        park_access_list:
        source_id:

    Returns:

    """
    data = {
        "src": 12,  # 必填，9:停车场, 10:停车场出入口;12 停车场整体制作
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": TCQ_BATCH_ID,  # 必填，批次号
        "main_poi_bid": parking_info['parent_id'],  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": parking_info['geom'],  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "from_src": "park_access_intelligence_add",
        "qb_type": 2,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": TCQ_BATCH_ID[:3],  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                # "callback": "http://mapde-poi.baidu-int.com/pre/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "根据建议值坐标位置核实灌入停车场的出入口，缺失需新增，线上有点需关联, 线上有点实际不存在需删除",
                "park_list": [{
                    "park": {
                        "bid": parking_info['bid'],
                        "name": parking_info['name'],
                        "wkt": parking_info['geom'],
                        "show_tag": parking_info['show_tag'],
                        "status": 1,
                        "parent_bid": parking_info['parent_id'],
                    },
                    "park_gate_list": park_access_list,
                }],
            }
        }
    }
    logger.info(f"推送数据:{data}")
    resp = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        # url='http://mapde-poi.baidu-int.com/pre/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{resp.text}")
    resp_data_json = resp.json()
    if 'code' in resp_data_json and resp_data_json['code'] == 0:
        return resp_data_json['data']['qb_id']
    return 0


if __name__ == '__main__':
    push_number = sys.argv[1]
    pv = sys.argv[2]
    strategy = sys.argv[3] if sys.argv[3] != '0' else None
    park_access_intelligence_push(int(push_number), strategy, pv)
