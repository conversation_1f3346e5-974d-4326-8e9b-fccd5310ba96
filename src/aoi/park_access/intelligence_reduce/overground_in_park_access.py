# -*- coding: utf-8 -*-
"""
地上停车场情报去无效
"""
import datetime
import multiprocessing
import sys
import os
import tqdm
import json
import numpy as np
import traceback
from shapely import wkt
from loguru import logger
from dataclasses import dataclass, field
from datetime import datetime
from datetime import timedelta
from shapely.geometry import Point, Polygon, MultiPoint
from sklearn.cluster import DBSCAN

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.traj_db import TrajDB
from src.aoi.dao.dest_traj import DestTraj
from src.common import pipeline
from src.aoi import utils
from src.aoi.park_access import park_utils

COORDINATE_FACTOR = 100000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
traj_db = TrajDB()
dest_traj_dao = DestTraj()
# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")
CATEGRORY = ['房地产;住宅区', '房地产;写字楼']


@dataclass
class ParkAccessIntelligence:
    """
    停车场情报
    """
    id: int = field(default=0, init=False)
    bid: str = field(default="", init=False)
    strategy: str = field(default="", init=False)
    node_id: str = field(default="", init=False)
    link_id: str = field(default="", init=False)
    geom: str = field(default="", init=False)
    is_invalid: bool = field(default=False, init=False)
    remark: str = field(default="", init=False)


@dataclass
class ParkingInfo:
    """"
    停车场信息
    """
    bid: str = field(default="", init=False)
    name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    park_wkt: str = field(default="", init=False)
    park_area_gcj_wkt: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    traj_dbname: str = field(default="", init=False)


@dataclass
class Traj:
    """"
    轨迹信息
    """
    end_traj_list: list = field(default_factory=list)
    aoi_exp_traj_list: list = field(default_factory=list)
    park_accept_traj_list: list = field(default_factory=list)


@dataclass
class AOIInfo:
    """
    停车场父AOI信息
    """
    bid: str = field(default="", init=False)
    wkt: str = field(default="", init=False)
    area: str = field(default="", init=False)
    std_tag: str = field(default="", init=False)
    aoi_complete: int = field(default=0, init=False)
    aoi_level: int = field(default=0, init=False)
    park_distance: float = field(default=0, init=False)
    blu_access_list: list = field(default_factory=list, init=False)
    poi_wkt: str = field(default="", init=False)


@dataclass
class Feature:
    """"
    特征
    """
    end_traj_features: list = field(default_factory=list)
    park_accept_features: list = field(default_factory=list)
    navigate_traj_features: list = field(default_factory=list)
    exp_traj_features: list = field(default_factory=list)
    competitor_features: list = field(default_factory=list)
    aoi_exp_traj_features: list = field(default_factory=list)
    park_exp_traj_features: list = field(default_factory=list)


class Context:
    """
    情报去无效处理上下文
    """

    def __init__(self, intelligence):
        self.origin_intelligence = intelligence
        self.intelligence = ParkAccessIntelligence()
        self.parking_info = ParkingInfo()
        self.traj = Traj()
        self.aoi_info = AOIInfo()
        self.feature = Feature()
        self.init_parking_info()
        self.init_aoi_info()
        self.init_intelligence()
        self.init_traj()

    def init_parking_info(self):
        """
        初始化停车场信息
        Returns:

        """
        # 查询停车场信息
        parking_list = poi_online_query.get_parking_list_by_kv(bids=[self.origin_intelligence["bid"]])
        if not parking_list:
            poi_online_rw.update_park_access_intelligence_strategy_value_by_id([self.origin_intelligence["id"]],
                                                                               'intelligence_invalid',
                                                                               'park_not_exist')
            raise ValueError(f"停车场不存在 {self.origin_intelligence['bid']}")
        parking = parking_list[0]
        paring_info = ParkingInfo()
        paring_info.bid = parking['bid']
        paring_info.parent_id = parking['parent_id']
        paring_info.park_wkt = parking['geom']
        paring_info.road_relation = parking['road_relation']
        if parking[6]:
            paring_info.park_area_gcj_wkt = utils.mc_to_gcj(parking['area'])
        self.parking_info = paring_info

    def init_intelligence(self):
        """
        初始化情报
        """
        self.intelligence.id = self.origin_intelligence['id']
        self.intelligence.bid = self.origin_intelligence['bid']
        self.intelligence.strategy = self.origin_intelligence['strategy']
        self.intelligence.node_id = self.origin_intelligence['node_id']
        self.intelligence.link_id = self.origin_intelligence['link_id']
        self.intelligence.geom = self.origin_intelligence['geom']

    def init_aoi_info(self):
        """
        初始化AOI信息
        Returns:

        """
        if not self.parking_info.parent_id:
            return
        aoi_res = master_back_dao.get_aoi_info_by_bid(self.parking_info.parent_id)
        if not aoi_res:
            return
        aoi_info = AOIInfo()
        aoi_info.bid = aoi_res[2]
        aoi_info.area = aoi_res[0]
        aoi_info.wkt = aoi_res[1]
        aoi_info.aoi_complete = aoi_res[4]
        aoi_info.aoi_level = aoi_res[3]
        self.aoi_info = aoi_info
        # 计算停车场与AOI距离
        aoi_sp = wkt.loads(aoi_info.wkt)
        park_sp = wkt.loads(self.parking_info.park_wkt)
        self.aoi_info.park_distance = aoi_sp.distance(park_sp)
        # 查询AOI垂类
        poi_info = poi_online_query.get_poi_by_bids([aoi_res[2]])
        if not poi_info:
            logger.info(f"{self.parking_info.bid}POI失效")
            return
        self.aoi_info.std_tag = poi_info[0]['std_tag']
        self.aoi_info.poi_wkt = poi_info[0]['wkt']

    def init_traj(self):
        """
        初始化轨迹
        """
        if not self.aoi_info.wkt:
            return
        aoi_sp = wkt.loads(self.aoi_info.wkt)
        aoi_buffer_geom = aoi_sp.buffer(20 / COORDINATE_FACTOR)
        date_30_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')
        # 终点轨迹
        aoi_traj_list = utils.get_traj_list_by_bid_and_geom(self.parking_info.parent_id, aoi_buffer_geom.wkt,
                                                            date_30_days_ago)
        self.traj.end_traj_list = list(set([wkt.loads(item[0]) for item in aoi_traj_list])) if aoi_traj_list else []
        # 经验轨迹
        self.traj.aoi_exp_traj_list = utils.get_exp_traj_by_bid(self.parking_info.parent_id, track_number=0,
                                                                traj_rate=1)
        self.traj.park_exp_traj_list = utils.get_exp_traj_by_bid(self.parking_info.bid, track_number=0, traj_rate=1)
        if IS_TEST == '0':
            for traj_sp in self.traj.aoi_exp_traj_list + self.traj.park_exp_traj_list:
                print(traj_sp.wkt)
        # 采纳轨迹
        park_accept_traj_list = traj_db.get_park_accept_traj(geom=aoi_sp.wkt, accept_time=date_30_days_ago)
        if park_accept_traj_list:
            traj_list = [wkt.loads(item['arrived_traj']) for item in park_accept_traj_list]
            traj_list = park_utils.traj_intercept(traj_list, self.aoi_info.wkt)
            traj_list = park_utils.filter_noise_trace_by_angle_and_intersects(traj_list)
            self.traj.park_accept_traj_list = traj_list


def create_intelligence(ctx: Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        end_traj_feature,
        navigate_traj_feature,
        park_accept_traj_feature,
        filter_by_near_exist_accurate_gate,
        filter_by_close_gate,
        filter_by_high_level_road,
        filter_low_traj_rate,
        save_park_access_intelligence_to_db,
    )
    pipe_list(ctx)


def end_traj_feature(ctx: Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info("begin traj area intersects")
    # 取轨迹与AOI边框交割点
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    ints_p_wkt_arr = park_utils.calcul_intersect_points_by_in_traj(aoi_sp.buffer(-5 / COORDINATE_FACTOR).wkt,
                                                                   ctx.traj.end_traj_list,
                                                                   area_buffer_distance=10)
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    if IS_TEST == '0':
        logger.info(f"终点与边框交割点: {ints_p_wkt_arr}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.99:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"终点与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.end_traj_features = cluster_points_wkt_list
    proceed()


def navigate_traj_feature(ctx: Context, proceed):
    """
    导航轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin navigate_traj_feature")
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    traj_list = poi_online_query.get_navigate_traj(bids=[ctx.parking_info.bid])
    if not traj_list:
        logger.info(f"没有导航轨迹")
        return proceed()
    traj_list = [wkt.loads(item['geom']) for item in traj_list]
    intersect_point_list = park_utils.calcul_intersect_points_by_out_traj(aoi_sp.buffer(-5 / COORDINATE_FACTOR).wkt,
                                                                          traj_list, area_buffer_distance=-5)
    if not intersect_point_list:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    if IS_TEST == '0':
        logger.info(f"导航轨迹与边框交割点: {intersect_point_list}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=4).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"导航轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.navigate_traj_features = cluster_points_wkt_list
    proceed()


def park_accept_traj_feature(ctx: Context, proceed):
    """
    采纳轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin park_accept_traj_area_intersects")
    if not ctx.traj.park_accept_traj_list:
        return proceed()
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    traj_list = ctx.traj.park_accept_traj_list
    intersect_point_list = park_utils.calcul_intersect_points_by_in_traj(aoi_sp.buffer(-5 / COORDINATE_FACTOR).wkt,
                                                                         traj_list, area_buffer_distance=10)
    if not intersect_point_list:
        logger.info(f"没有采纳轨迹与边框交割点")
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    if IS_TEST == '0':
        logger.info(f"采纳轨迹与边框交割点: {intersect_point_list}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.95:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"采纳轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.park_accept_features = cluster_points_wkt_list
    proceed()


def filter_by_near_exist_accurate_gate(ctx: Context, proceed):
    """
    过滤附近已存在精准入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin filter_by_near_exist_accurate_gate")
    if ctx.intelligence.is_invalid:
        return proceed()
    intelligence_sp = wkt.loads(ctx.intelligence.geom)
    relation_park_access_list = poi_online_query.get_park_access_by_kv(park_bid=ctx.parking_info.bid, status=1)
    park_access_sp_list = []
    if relation_park_access_list:
        for park_access in relation_park_access_list:
            road_relation = park_access['road_relation']
            if not road_relation or "link_info" not in road_relation or len(road_relation['link_info']) == 0:
                continue
            out_park_access_list = [item for item in road_relation['link_info'] if item['orientation'] == 1]
            if not out_park_access_list:
                continue
            park_access_sp_list.append(wkt.loads(park_access['geom']))
    near_exist_accurate_gate_sp_list = [item for item in park_access_sp_list if
                                        item.distance(intelligence_sp) < 15 / COORDINATE_FACTOR]
    if near_exist_accurate_gate_sp_list:
        ctx.intelligence.is_invalid = True
        ctx.intelligence.remark = "附近已存在精准入口过滤"
    proceed()


def filter_by_close_gate(ctx: Context, proceed):
    """
    过滤紧急大门
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin filter_by_close_gate")
    if not ctx.intelligence.node_id or ctx.intelligence.strategy == 'COMMON_POI_NAVI_COMPETITOR_DIFF':
        return proceed()
    gate_traffic_list = dest_traj_dao.get_node_passage_by_long_node_id(ctx.intelligence.node_id)
    if not gate_traffic_list:
        return proceed()
    if gate_traffic_list[0]['passage'] != 'URGENT':
        return proceed()
    node_sp = wkt.loads(ctx.intelligence.geom)
    # 无终点轨迹到达
    end_traj_arrive = [traj_sp for traj_sp in ctx.feature.end_traj_features
                       if traj_sp.buffer(15 / COORDINATE_FACTOR).contains(node_sp)]
    if end_traj_arrive:
        logger.info(f"终点轨迹到达:{end_traj_arrive[0]}")
        return proceed()
    # 无采纳轨迹到达
    park_accept_traj_arrive = [traj_sp for traj_sp in ctx.feature.park_accept_features
                               if traj_sp.buffer(15 / COORDINATE_FACTOR).contains(node_sp)]
    if park_accept_traj_arrive:
        logger.info(f"采纳轨迹到达:{park_accept_traj_arrive[0]}")
        return proceed()
    # 无经验轨迹到达
    exp_traj_list = ctx.traj.park_exp_traj_list + ctx.traj.aoi_exp_traj_list
    exp_traj_arrive = [traj_sp for traj_sp in exp_traj_list
                       if Point(traj_sp.coords[-1]).buffer(30 / COORDINATE_FACTOR).contains(node_sp)]
    if exp_traj_arrive:
        logger.info(f"经验轨迹到达:{exp_traj_arrive[0]}")
        return proceed()
    ctx.intelligence.is_invalid = True
    ctx.intelligence.remark = "紧急大门过滤"
    proceed()


def filter_low_traj_rate(ctx: Context, proceed):
    """
    低通量轨迹过滤
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin filter_low_traj_rate")
    if ctx.intelligence.is_invalid or ctx.intelligence.strategy == 'COMMON_POI_NAVI_COMPETITOR_DIFF':
        return proceed()
    # 判读是否已存在精准入口
    park_access_list = poi_online_query.get_park_access_by_kv(park_bid=ctx.parking_info.bid)
    accurate_park_access = [item for item in park_access_list if
                            item["status"] == 1 and item['road_relation'] and "link_info" in item[
                                'road_relation'] and len(item['road_relation']['link_info']) > 0]
    if not accurate_park_access:
        return proceed()
    node_sp = wkt.loads(ctx.intelligence.geom)
    # 无终点轨迹到达
    end_traj_arrive = [traj_sp for traj_sp in ctx.feature.end_traj_features
                       if traj_sp.buffer(30 / COORDINATE_FACTOR).contains(node_sp)]
    if end_traj_arrive:
        logger.info(f"终点轨迹到达:{end_traj_arrive[0]}")
        return proceed()
    # 无采纳轨迹到达
    park_accept_traj_arrive = [traj_sp for traj_sp in ctx.feature.park_accept_features
                               if traj_sp.buffer(30 / COORDINATE_FACTOR).contains(node_sp)]
    if park_accept_traj_arrive:
        logger.info(f"采纳轨迹到达:{park_accept_traj_arrive[0]}")
        return proceed()
    # 无导航轨迹到达
    navigate_traj_arrive = [traj_sp for traj_sp in ctx.feature.navigate_traj_features
                            if traj_sp.buffer(30 / COORDINATE_FACTOR).contains(node_sp)]
    if navigate_traj_arrive:
        logger.info(f"导航轨迹到达:{navigate_traj_arrive[0]}")
        return proceed()
    # 无经验轨迹到达
    exp_traj_list = ctx.traj.park_exp_traj_list + ctx.traj.aoi_exp_traj_list
    exp_traj_arrive = [traj_sp for traj_sp in exp_traj_list
                       if Point(traj_sp.coords[-1]).buffer(30 / COORDINATE_FACTOR).contains(node_sp)]
    if exp_traj_arrive:
        logger.info(f"经验轨迹到达:{exp_traj_arrive[0]}")
        return proceed()

    ctx.intelligence.is_invalid = True
    ctx.intelligence.remark = "低通量轨迹过滤"
    proceed()


def filter_by_high_level_road(ctx: Context, proceed):
    """
    横框高等级道路过滤
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin filter_by_high_level_road")
    if ctx.intelligence.is_invalid or ctx.intelligence.strategy == 'COMMON_POI_NAVI_COMPETITOR_DIFF':
        return proceed()
    node_sp = wkt.loads(ctx.intelligence.geom)
    if not utils.is_over_high_road_park_access(node_sp.wkt, ctx.aoi_info.wkt):
        return proceed()
    # 经验轨迹验证
    exp_traj_arrive = [traj_sp for traj_sp in ctx.feature.exp_traj_features
                       if Point(traj_sp.coords[-1]).buffer(10 / COORDINATE_FACTOR).contains(node_sp)]
    if exp_traj_arrive:
        logger.info(f"经验轨迹到达:{exp_traj_arrive[0]}")
        return proceed()
    ctx.intelligence.is_invalid = True
    ctx.intelligence.remark = "横跨高等级道路过滤"
    proceed()


def save_park_access_intelligence_to_db(ctx: Context, proceed):
    """
    保存智能结果到数据库
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin save_park_access_intelligence_to_db")
    if IS_TEST == '0':
        logger.info(f"情报是否无效:{ctx.intelligence.is_invalid},{ctx.intelligence.remark}")
        return proceed()
    if not ctx.intelligence.is_invalid:
        return proceed()
    poi_online_rw.update_park_access_intelligence_filter_reason([ctx.intelligence.id], ctx.intelligence.remark)
    return proceed()


def main(bid_list: list):
    """
    地上停车场情报去无效
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    type = 'park_access_overground_add'
    intelligence_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(type=type, status='INIT',
                                                                                       bids=bid_list.tolist())
    if not intelligence_list:
        logger.debug("No intelligent data found")
        return
    for intelligence in tqdm.tqdm(intelligence_list):
        try:
            logger.info(f"\n地上停车场情报去无效:{intelligence['bid']}\t{intelligence['geom']}")
            ctx = Context(intelligence)
            aoi_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
            if not aoi_sp:
                logger.info(f"父点边框不存在")
                continue
            if ctx.aoi_info.std_tag in CATEGRORY or ctx.aoi_info.std_tag.startswith(
                    '公司') or '教育培训' in ctx.aoi_info.std_tag or ctx.aoi_info.std_tag.startswith(
                '购物') or '政府机构' in ctx.aoi_info.std_tag:
                park_sp = wkt.loads(ctx.parking_info.park_wkt)
                if park_sp.distance(aoi_sp) > 5 / COORDINATE_FACTOR:
                    logger.info(f"父点边框和停车场边框距离大于5m跳过")
                    continue
                create_intelligence(ctx)
                continue
            logger.info(f"父点非指定垂类跳过:{ctx.aoi_info.std_tag}")
        except Exception as e:
            print(f"情报去无效失败:{intelligence['bid']}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if not bid_list:
        park_import_list = poi_online_query.get_park_import_list()
        bid_list = [item['bid'] for item in park_import_list]
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
    # release()
