# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
aoi_resume库查询
"""
import os
import sys
import time

import psycopg2

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class AoiResumeQuery(PgDao):
    """
    aoi_resume 查询
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "aoi_resume_r" in conf:
            poi_online_r_conf = conf['aoi_resume_r']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("aoi_resume_r 配置不存在")

    def get_blu_face_complete_history_by_bid(self, main_bid: str):
        """
        获取最新一条履历记录
        :param main_bid:
        :return:
        """
        sql = """
            select main_bid,aoi_complete,action from blu_face_complete_history 
            where main_bid=%s order by id desc limit 1
        """
        return self.queryone(sql, [main_bid])

    def get_poi_navi_competitors(self, bid: str, parent_id=None):
        """
        获取竞品引导点信息
        Returns:

        """
        sql = f"""
            select end_geom_str from poi_navi_intelligence where bid = %s
        """
        poi_navi_list = self.queryall(sql, [bid])
        if not poi_navi_list:
            return None
        return [item[0] for item in poi_navi_list]




