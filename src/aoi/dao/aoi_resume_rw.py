# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
aoi_resume库查询
"""
import os
import sys
import time

import psycopg2
from psycopg2.extras import execute_values
from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.aoi_resume_field import BluFaceCompleteHistory, PoiNaviIntelligence


class AoiResumeRW(object):
    """
    aoi_resume 查询
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "aoi_resume" in conf:
            self.pg_conf = conf['aoi_resume']
            self.conn = None
            self._conn()

    def _conn(self):
        try:
            self.conn = psycopg2.connect(
                database=self.pg_conf["db"],
                user=self.pg_conf["user"],
                password=self.pg_conf["pwd"],
                host=self.pg_conf["host"],
                port=self.pg_conf["port"]
            )
            return True
        except:
            return False

    def _retry_conn(self, retry_num=3, sleep_time=10):
        _number = 0
        _status = True
        while _status and _number <= retry_num:
            try:
                self.conn.ping()
                _status = False
            except:
                if self._conn():
                    _status = False
                    break
                _number += 1
                time.sleep(sleep_time)

    def execute(self, sql: str, args: tuple):
        """
        执行sql
        :param sql: 执行SQL语句
        :param args:  绑定参数
        :return:
        """
        try:
            self._retry_conn()
            self.cursor = self.conn.cursor()
            result = self.cursor.execute(sql, args)
            self.conn.commit()
            self.cursor.close()
            return result
        except Exception as e:
            print(e)
            return False

    def batch_insert(self, sql: str, args: List[tuple]):
        """
        执行sql
        :param sql: 执行SQL语句
        :param args:  绑定参数
        :return:
        """
        try:
            self._retry_conn()
            self.cursor = self.conn.cursor()
            execute_values(self.cursor, sql, args)
            self.conn.commit()
            self.cursor.close()
        except Exception as e:
            print(e)
            return False

    def insert_blu_face_complete_history(self, record: BluFaceCompleteHistory):
        """
        竞品引导点入库
        :param record:
        :return:
        """
        sql = f"""
            insert into blu_face_complete_history(
                complete_info_id,main_bid,face_id,aoi_complete,update_time,is_white,action
            ) values(%s,%s,%s,%s,%s,%s,%s)
        """
        args = (record.complete_info_id, record.main_bid, record.face_id, record.aoi_complete, record.update_time,
                record.is_white, record.action)
        return self.execute(sql, args)

    def insert_poi_navi_intelligence(self, poi_navi_list: List[PoiNaviIntelligence]):
        """
        竞品引导点入库
        :param poi_navi_list:
        :return:
        """
        sql = """
            insert into poi_navi_intelligence_new(
                bid,gid,end_geom_str,date1,date2,src,end_geom,name
            ) values(%s)
        """
        data_list = []
        for key, poi_navi in enumerate(poi_navi_list):
            data_list.append((poi_navi.bid, poi_navi.gid, poi_navi.end_geom_str, poi_navi.date1, poi_navi.date2,
                             poi_navi.src, poi_navi.end_geom, poi_navi.name))
            if key % 1000 == 0 or key == len(poi_navi_list) - 1:
                self.batch_insert(sql, data_list)
                data_list = []
        return
