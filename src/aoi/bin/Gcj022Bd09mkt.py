#encoding=gbk
"""
Convert Coordinate System

<EMAIL> at 20171012
"""

import os
import sys
import util
import shapely.wkt
import shapely.geometry 

def Main():
    """
    Main Function
    """
    infile = open(sys.argv[1])
    outfile = open(sys.argv[2], "w")
    while 1:
        line = infile.readline()
        if line is None or line == "":
            break
        try:
            line = line.strip('\n\r')
            item = line.split('\t')
            fid = item[0]
            raw_shape = shapely.wkt.loads(item[1])
            item[2] = item[2].decode('utf-8', 'ignore').encode('gbk', 'ignore')
            if isinstance(raw_shape, shapely.geometry.MultiPolygon):
                parts = list(raw_shape.geoms)
                polygon_list = []
                for item in parts:
                    new_coor_list = []
                    coor_list = list(item.exterior.coords)
                    for pt in coor_list:
                        [x, y] = util.coordtrans("gcj02ll", "bd09mc", float(pt[0]), float(pt[1]))
                        new_coor_list.append([x, y])
                    polygon_list.append(shapely.geometry.Polygon(new_coor_list))
                new_shape = shapely.geometry.MultiPolygon(polygon_list)
            if isinstance(raw_shape, shapely.geometry.Polygon):
                new_coor_list = []
                coor_list = list(raw_shape.exterior.coords)
                for pt in coor_list:
                    [x, y] = util.coordtrans("gcj02ll", "bd09mc", float(pt[0]), float(pt[1]))
                    new_coor_list.append([x, y])
                new_shape = shapely.geometry.Polygon(new_coor_list) 
            item[1] = str(new_shape)
            print >> outfile, "%s" % ('\t'.join(item))
        except Exception as e:
            continue

if __name__ == '__main__':
    Main()                   