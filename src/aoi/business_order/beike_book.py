# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝壳书楼商单数据输出
"""
import os
import psycopg2

pdd_sh_conf = {"db": "pdd_sd", "user": "pdd_sd_se_rw", "pwd": "i<PERSON><PERSON><PERSON><PERSON>",
               "host": "**************",
               "port": 6432}

pdd_sd_conn = psycopg2.connect(database=pdd_sh_conf["db"],
                               user=pdd_sh_conf["user"],
                               password=pdd_sh_conf["pwd"],
                               host=pdd_sh_conf["host"],
                               port=pdd_sh_conf["port"])
pdd_sd_dao = pdd_sd_conn.cursor()

root_dir = "/home/<USER>/gengyong/business_order/beike"
bud_path = root_dir + "/data/00-building"
blu_path = root_dir + "/data/01-landutil"
brw_path = root_dir + "/data/03-railway"
province_list = {
    'beijing': "polygon((116.28668789650902 40.028755617673816,116.3455469597384 40.02885498992501,"
               "116.3455526707456 40.0738244847431,116.28669352812746 40.07372511088651,"
               "116.28668789650902 40.028755617673816))",
    'tianjing': "polygon((117.11607023235177 39.0535921703102,117.17391813091858 39.053513777521346,"
                "117.17392383633906 39.0985084516566,117.11607597863163 39.09858684149344,"
                "117.11607023235177 39.0535921703102))",
    'suzhou': "polygon((120.69798615771019 31.305464667020647,120.75068854421306 31.305519380840718,"
              "120.75069230987239 31.350502532739522,120.69798989264237 31.350447817168146,"
              "120.69798615771019 31.305464667020647))"
}


def run():
    """
    商单数据输出
    Returns:

    """
    print(f"开始bud导出")
    bud_export()
    print(f"开始blu导出")
    blu_export()
    print(f"开始brw导出")
    brw_export()
    print(f"导出完成")


def bud_export():
    """
    建筑物数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = bud_path + "/" + province
        sql = f"""
            select 
                bf.face_id,bf.struct_id,case when bs.name_ch='虚拟POI' then '' else bs.name_ch end as name_ch,bf.aoi_id,bf.height,
                0.0 as floors,0.0 as sub_floors,bf.area,bf.perimeter,bf.geom  
            from bud_face bf 
                left join bud_struct bs on bf.struct_id=bs.struct_id 
            where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/bud_face.mif -dsco farmat=mif PG:"host='**************' dbname='master_back' port='5432' user='master_back_se_ro' password='mapread'" -a_srs wgs84 -sql "{sql}" encoding=gb18030
        """

        os.system(cmd)


def blu_export():
    """
    AOI数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = blu_path + "/" + province
        sql = f"""
            select 
                face_id,poi_bid,area,perimeter,name_ch as name,bf.geom from blu_face bf
            where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=gb18030
        """
        os.system(cmd)


def brw_export():
    """
    BRW数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = brw_path + "/" + province
        sql = f"""
            select 
                link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,bf.geom 
            from brw_link bf where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/brw_link.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=gb18030
        """
        os.system(cmd)
        pdd_sd_dao.execute(sql)
        brw_link_list = pdd_sd_dao.fetchall()
        if brw_link_list is None or len(brw_link_list) == 0:
            continue
        node_id_list = []
        for brw_link in brw_link_list:
            node_id_list.append(brw_link[1])
            node_id_list.append(brw_link[2])
        node_id_str = "','".join(node_id_list)
        sql = f"""
                    select 
                        node_id,kind,form
                    from brw_node  where node_id in('{node_id_str}')
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/brw_node.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=gb18030
                """
        os.system(cmd)


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/data")
    make_dir(bud_path)
    make_dir(blu_path)
    make_dir(brw_path)
    for province in province_list:
        make_dir(bud_path + "/" + province)
        make_dir(blu_path + "/" + province)
        make_dir(brw_path + "/" + province)


def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


if __name__ == '__main__':
    init_dir()
    print("目录初始化完成,开始数据导出")
    run()
