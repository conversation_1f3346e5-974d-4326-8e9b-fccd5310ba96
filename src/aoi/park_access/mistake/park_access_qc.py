# -*- coding: utf-8 -*-
"""
出入口质检过滤策略
"""
import datetime
import os
import sys
from shapely.geometry import Polygon, MultiPolygon, Point, MultiPoint
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.dest_traj_1 import DestTrajOne
from src.aoi.dao.dest_traj_2 import DestTrajTwo
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.BFQuery import BFQuery
from src.aoi.dao.road import Road
from src.common import pipeline
from src.aoi.park_access import context
from src.aoi.park_access import park_utils

dest_traj = DestTraj()
dest_traj_dao1 = DestTrajOne()
dest_traj_dao2 = DestTrajTwo()
beeflow_dao = BFQuery()
road_dao = Road()
poi_online_query = PoiOnlineQuery()
bf_query = BFQuery()

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000
AREA_FACTOR = 10000000000
MANUAL_WORKED_VALID_MONTH = 12
FILTER_DISTANCE_CATEGORY = ['交通设施;飞机场', '交通设施;火车站']

same_access_type_arr = ['PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK', \
                        'PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE', \
                        'PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE']

INTELLIGENCE_CONFIDENCE_HIGH = 100
INTELLIGENCE_CONFIDENCE_MID = 80
INTELLIGENCE_CONFIDENCE_LOW = 60
INTELLIGENCE_CONFIDENCE_NO = 0


def park_access_qc(ctx: context.Context):
    """
    挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_repeat,
        # filter_work_by_manual,
        filter_access_already_get,
        filter_inner,
        # filter_pushed_park_access,
        # filter_by_exp_traj,
        # filter_poi_navi_competitor,
    )
    pipe_list(ctx)


def park_access_out_qc(ctx: context.Context):
    """
    挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_repeat_by_point,
    )
    pipe_list(ctx)


def filter_by_distance(ctx: context.Context, proceed):
    """
    过滤已推送的情报 buffer 20范围存在则不推送
    Args:
        ctx:
        proceed:

    Returns:

    """
    show_tags = ['地上停车场', '地下停车场', '立体停车场', '门前停车场', '停车场']
    sub_park_list = []
    if ctx.aoi_info.bid:
        sub_park_list = ctx.dao.poi_online_query.get_parking_list(show_tags=show_tags, parent_id=ctx.aoi_info.bid)
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        park_area_wkt = ctx.parking_info.park_area_gcj_wkt \
            if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt).buffer(
            20 / COORDINATE_FACTOR).wkt
        park_area_sp = wkt.loads(park_area_wkt)
        parent_tag = ctx.aoi_info.std_tag
        if ((parent_tag in FILTER_DISTANCE_CATEGORY or "旅游景点" in parent_tag or len(sub_park_list) > 1)
                and node_sp.distance(park_area_sp) > 150 / 110000):
            park_access.is_valid = False
            park_access.filter_reason = f"点位距离过滤"
            continue
    proceed()


def filter_access_already_get(ctx: context.Context, proceed):
    """
    过滤已经获取的出入口
    Args:
        ctx:
        proceed:
    Returns:
    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.strategy in same_access_type_arr:
            # 冗余不过滤, 需要2个出入口对比的不过滤，判断是否有未下发的
            park_intelligence = poi_online_query.get_park_intelligence_by_access_bid(
            park_access.bid, None, 'park_access_rongyu', 'INIT')
            if park_intelligence:
                park_access.is_valid = False
                park_access.filter_reason = f"情报id:{park_intelligence[0]},{park_access.strategy}"
                logger.info(f"end rongyu is already recall,{park_access.filter_reason}")
            continue

        park_intelligence = poi_online_query.get_park_intelligence_by_access_bid(
            park_access.bid, None, 'park_access_mistake')
        if not park_intelligence:
            continue
        park_access.is_valid = False
        park_access.filter_reason = f"情报id:{park_intelligence[0]},{park_access.strategy}"
        logger.info(f"end is_strategy_already_recall,{park_access.filter_reason}")
    proceed()


def filter_inner(ctx: context.Context, proceed):
    """
    过滤内部出入口
    Args:
        ctx:
        proceed:
    Returns:
    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.strategy in same_access_type_arr:
            # 冗余不过滤, 需要2个出入口对比的不过滤
            continue
        poi_info = poi_online_query.get_park_info(park_access.bid)
        if poi_info:
            if str(poi_info[3]) in ["1", "14"]:
                park_access.is_valid = False
                park_access.filter_reason = f"内部出入口，不对外过滤"
    proceed()


def filter_work_by_manual(ctx: context.Context, proceed):
    """
    过滤人工质检的出入口
    Args:
        ctx:
        proceed:
    Returns:
    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.strategy in same_access_type_arr:
            # 冗余不过滤, 需要2个出入口对比的不过滤
            continue
        if park_access.confidence > 80:
            continue

        # if beeflow_dao.is_manual_worked_bid(park_access.bid, MANUAL_WORKED_VALID_MONTH):
        #     park_access.is_valid = False
        #     park_access.filter_reason = f"当前bid({park_access.bid})已人工作业过"

        # if beeflow_dao.is_manual_worked_bid(park_access.bid, MANUAL_WORKED_VALID_MONTH):
        # park_access.strategy_value = park_access.strategy_value + "_manual_worked"
    proceed()


def filter_repeat(ctx: context.Context, proceed):
    """
    过滤挖掘重复的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_access_list = []
    exist_node_id = dict()
    exist_link_id = dict()
    access_bids = dict()
    for park_access in ctx.parking_access_list:
        if park_access.type == 'park_access_rongyu':
            if park_access.bid not in access_bids:
                access_bids[park_access.bid] = 1
                park_access_list.append(park_access)
            continue
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id not in exist_node_id:
            exist_node_id[park_access.node_id] = 1
            park_access_list.append(park_access)
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK and park_access.link_id not in exist_link_id:
            exist_link_id[park_access.link_id] = 1
            park_access_list.append(park_access)
    ctx.parking_access_list = park_access_list
    proceed()


def filter_repeat_by_point(ctx: context.Context, proceed):
    """
    按坐标过滤20米范围内重复数据
    """
    intelligence_type = "park_access_overground_out_gate_add"
    if ctx.parking_info.show_tag in ["地下停车场", "立体停车场"]:
        intelligence_type = "park_access_underground_out_gate_add"

    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        is_pushed_intelligence = poi_online_query.get_pushed_intelligence_by_geom(
            node_sp.buffer(20 / COORDINATE_FACTOR).wkt, intelligence_type)
        print(is_pushed_intelligence)
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"已推送的情报 buffer 20范围存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_pushed_park_access(ctx: context.Context, proceed):
    """
    过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.strategy in same_access_type_arr:
            # 冗余不过滤, 需要2个出入口对比的不过滤
            continue
        node_sp = wkt.loads(park_access.node_geom)
        # 查询已推送的情报
        if park_access.confidence >= INTELLIGENCE_CONFIDENCE_HIGH:
            intelligence_expire_days = 90
        elif park_access.confidence >= INTELLIGENCE_CONFIDENCE_MID:
            intelligence_expire_days = 120
        else:
            intelligence_expire_days = 180
        intelligence_time = datetime.datetime.now() - datetime.timedelta(days=intelligence_expire_days)
        is_pushed_intelligence = poi_online_query. \
            get_pushed_intelligence_by_geom(type='park_access_mistake', park_access_id=park_access.bid,
                                            created_at=intelligence_time)
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"当前情报库里已存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_pushed_park_access_strict(park_bid: str, park_access_bid: str, strategy_type="park_access_mistake"):
    """
    过滤已推送的出入口
    """
    park_access_list = poi_online_query.get_park_access_mistake_qb(park_bid, park_access_bid, strategy_type)
    if len(park_access_list) > 0:
        return True
    return False


def filter_offline_park_access(park_bid: str, park_access_wkt: str):
    """
    过滤已下线出入口
    Args:
        park_bid:
        park_access_wkt:

    Returns:

    """
    park_access_sp = wkt.loads(park_access_wkt)
    park_access_list = poi_online_query.get_park_access_by_wkt(park_bid,
                                                               None, 1)
    if len(park_access_list) > 0:
        return True
    return False


def filter_offline_access(access_bid):
    """
    过滤已下线出入口
    Args:
        park_bid:
        park_access_wkt:

    Returns:

    """
    park_access_list = poi_online_query.get_park_info(access_bid, 1)
    if park_access_list:
        return True
    return False


def filter_poi_navi_competitor(ctx: context.Context, proceed):
    """
    竞品引导点情报过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 竞品引导点信息
    parking_competitors_points = park_utils.get_competitor_park_access_points(ctx.aoi_info.bid, ctx.parking_info.bid,
                                                                              ctx.parking_info.show_tag)
    if not parking_competitors_points:
        logger.info(f"竞品引导点信息为空")
        return proceed()
    logger.info(f"竞品引导点信息:{[point for point in parking_competitors_points]}")
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid or park_access.confidence >= INTELLIGENCE_CONFIDENCE_HIGH:
            continue
        park_access_buffer_sp = wkt.loads(park_access.node_geom).buffer(20 / COORDINATE_FACTOR)
        # 如果出入口关联Link, 且Link小于50m, 且在AOI范围内, 范围取Link范围
        if park_access.type == "LINK" \
                and wkt.loads(park_access.link_geom).length * COORDINATE_FACTOR_NEW < 80 \
                and ctx.aoi_info.wkt and wkt.loads(ctx.aoi_info.wkt).intersects(wkt.loads(park_access.link_geom)):
            park_access_buffer_sp = wkt.loads(park_access.link_geom).buffer(10 / COORDINATE_FACTOR_NEW)

        near_competitor_points = [item for item in parking_competitors_points
                                  if park_access_buffer_sp.contains(wkt.loads(item))]
        if len(near_competitor_points) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"竞品情报过滤:{near_competitor_points}"
    proceed()


def filter_by_exp_traj(ctx: context.Context, proceed):
    """
    经验轨迹过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"经验轨迹过滤:{len(ctx.traj.aoi_exp_traj_list)}")
    # 获取父点经验轨迹
    if ctx.parking_info.parent_id == "" or len(ctx.traj.aoi_exp_traj_list) == 0:
        return proceed()
    # (取主点top3经验轨迹 & track_num > 10) or 取主点top1经验轨迹 & track_num < 10
    aoi_exp_traj_list = sorted(ctx.traj.aoi_exp_traj_list, key=lambda x: x["num_track"], reverse=True)
    traj_list = []
    for traj_info in aoi_exp_traj_list:
        if traj_info['num_track'] < 10 or traj_info['yaw_rate'] >= 0.8:
            continue
        if len(traj_list) < 3 or traj_info['route_end_percentage'] > 0.1 or traj_info['cuid_repetition_rate'] > 0:
            traj_list.append(wkt.loads(traj_info['geom']))
    if len(traj_list) == 0:
        traj_list = [wkt.loads(aoi_exp_traj_list[0]['geom'])]
    if ctx.debug:
        print("\n父点过滤后经验轨迹:")
        for traj in traj_list:
            print(traj.wkt)

    # 经验轨迹穿过出入口子点 or 在子点附近
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid or park_access.confidence >= INTELLIGENCE_CONFIDENCE_HIGH:
            continue
        park_access_sp = wkt.loads(park_access.node_geom)
        park_access_buffer_5m_sp = park_access_sp.buffer(5 / COORDINATE_FACTOR_NEW)
        for exp_traj_sp in traj_list:
            exp_traj_end_sp = Point(exp_traj_sp.coords[-1])
            # 如果父点经验轨迹与出入口相交, 或者终点距离小于30m, 则认为有效
            if exp_traj_sp.intersects(park_access_buffer_5m_sp) \
                    or exp_traj_end_sp.distance(park_access_sp) < 30 / COORDINATE_FACTOR_NEW:
                park_access.is_valid = False
                park_access.filter_reason = f"主点经验轨迹过滤:{exp_traj_sp.wkt}"
                break
    proceed()


def filter_manual_work(ctx: context.Context, proceed):
    """
    人工作业结果过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 人工作业结果过滤
    manual_work_day_value = 360
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid or park_access.confidence >= INTELLIGENCE_CONFIDENCE_MID:
            continue
        if park_access.confidence >= INTELLIGENCE_CONFIDENCE_LOW:
            manual_work_day_value = 90
        last_month_time = (datetime.datetime.now() - datetime.timedelta(days=manual_work_day_value)).strftime(
            '%Y-%m-%d')
        manual_work_res = bf_query.is_manual_check(park_access.bid, last_month_time)
        if manual_work_res:
            park_access.is_valid = False
            park_access.filter_reason = f"人工作业校验过滤新:{manual_work_res}"
            continue
        # 历史人工作业结果过滤
        history_manual_work_res = poi_online_query.is_manual_check(park_access.bid, last_month_time)
        if history_manual_work_res:
            park_access.is_valid = False
            park_access.filter_reason = f"人工作业校验过滤旧:{history_manual_work_res}"
            continue
    proceed()
