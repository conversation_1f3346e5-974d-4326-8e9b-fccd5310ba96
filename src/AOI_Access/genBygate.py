# -*- coding: UTF-8 -*-
################################################################################
"""
取消输出到导航的文件，多进程运行
输出文件以创建内业计划，并分析过滤的场景
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import sys
import shapely.wkt
import shapely.geometry
import uuid
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize

def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../../../conf/config.ini')
dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi_online = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi_online = conn_poi_online.cursor()

dbname = "poi_online_test"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()


def find_node_to_process_with_bid(bid):
    """
    根据bid寻找附近可能关联的大门信息
    Args:
        bid: poi的bid
    Return:
        face_id: aoi的主键id
        node_id_set: set类型, 大门的nodeid集合
    """
    node_id_set = set()
    sql = "select st_astext(geom),kind,aoi_level, bf.face_id from blu_face bf inner join blu_face_poi bfp \
            on bf.face_id = bfp.face_id where bfp.poi_bid = %s "
    cursor_aoi.execute(sql, [bid])
    res_aoi = cursor_aoi.fetchone()
    if res_aoi is None or len(res_aoi) == 0:
        print("没有关联的AOI\t" + bid)
        return "", node_id_set
    if res_aoi[1] == '52' or res_aoi[2] != 2:
        print("AOI为商圈或非基础院落\t" + bid)
        return "", node_id_set
    aoi_geom = res_aoi[0]
    face_id = res_aoi[3]
    sql = "with tmp as (select link_id, st_intersection(st_geomfromtext(%s, 4326), geom) \
            as geom from nav_link where st_intersects(st_geomfromtext(%s, 4326), geom)) \
            select link_id, st_astext((st_dump(geom)).geom) as geom from tmp;"
    cursor_road.execute(sql, [aoi_geom, aoi_geom])
    res_point = cursor_road.fetchall()
    if res_point is None or len(res_point) == 0:
        print("没有相交的link\t" + bid)
        return face_id, node_id_set
    num_gate = 0
    for point_geom in res_point:
        sql = "select string_agg(node_id, ''',''') from nav_node \
                where st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)"
        cursor_road.execute(sql, [point_geom[1]])
        res_node = cursor_road.fetchall()
        if res_node is None or len(res_node) == 0:
            continue
        node_str = res_node[0][0]
        sql = "select node_id, in_linkid, out_linkid from nav_gate where node_id in \
            ('%s')" % (node_str)
        cursor_road.execute(sql)
        
        res_node = cursor_road.fetchall()
        if res_node is not None:
            num_gate = num_gate + len(res_node)
        for res_node_tmp in res_node:
            sql = "select form from nav_link where link_id = %s or link_id = %s"
            cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
            res_form = cursor_road.fetchall()
            if (res_form[0][0] == "52" and res_form[1][0] != "52") or (
                res_form[1][0] == "52" and res_form[0][0] != "52"):
                node_id_set.add(res_node_tmp[0])
            else:
                print("大门不连接内外部\t" + bid + "\t" + res_node_tmp[0])
    if num_gate == 0:
        print("AOI附近没有大门\t" + bid)
    elif len(node_id_set) == 0:
        print("AOI附近没有连接内外部路的大门\t" + bid)
    return face_id, node_id_set


def find_aoi_nearby_node(face_id):
    """
    根据bid寻找附近可能关联的大门信息
    Args:
        face_id: aoi的主键id
    Return:
        face_id: aoi的主键id
        res_node_info: list类型 
        第一个参数是aoi附近的大门node拼接;
        第二个参数是与AOI之间跨越高等级道路的大门node集合;
        第三个参数是不连接内部路和外部路的大门node
        第四个参数是所有与AOI之间不跨高等级道路的大门
    """
    node_id_str = ""
    node_filter_dict = dict()
    node_filter_not_access_dict = dict()
    node_save_dict= dict()
    sql = "select st_astext(geom) from blu_face where face_id = %s"
    cursor_aoi.execute(sql, [face_id])
    aoi_geom = cursor_aoi.fetchone()
    if aoi_geom is None or len(aoi_geom) == 0:
        return [node_id_str.strip(','), node_filter_dict, node_filter_not_access_dict, node_save_dict]
    aoi_line = shapely.wkt.loads(aoi_geom[0]).boundary.wkt
    sql = "select ng.node_id, st_AsText(st_shortestline( \
            st_geomfromtext(%s, 4326), nd.geom)) as line_geom, st_astext(nd.geom) from nav_gate ng inner join nav_node nd \
            on ng.node_id = nd.node_id \
            where st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0003)"
    cursor_road.execute(sql, [aoi_geom[0], aoi_line])
    node_res = cursor_road.fetchall()
    if node_res is None or len(node_res) == 0:
        return [node_id_str.strip(','), node_filter_dict, node_filter_not_access_dict, node_save_dict]
    for node_id_tmp in node_res:
        sql = "select count(*) from nav_link  where st_intersects(st_geomfromtext(%s, 4326), geom) and kind <= 7"
        cursor_road.execute(sql, [node_id_tmp[1]])
        if cursor_road.fetchone()[0] == 0:
            node_id_str = node_id_str + "," + node_id_tmp[0]
            node_save_dict[node_id_tmp[0]] = node_id_tmp[2]
            sql = "select in_linkid, out_linkid from nav_gate where node_id = %s"
            cursor_road.execute(sql, [node_id_tmp[0]])
            link_info_res = cursor_road.fetchone()
            if link_info_res is None or len(link_info_res) == 0:
                continue
            sql = "select form from nav_link where link_id = %s or link_id = %s"
            cursor_road.execute(sql, [link_info_res[0], link_info_res[1]])
            form_res = cursor_road.fetchall()
            if (form_res[0][0] == '52' and form_res[1][0] == '52') or (
                form_res[0][0] != '52' and form_res[1][0] != '52'):
                node_filter_not_access_dict[node_id_tmp[0]] = node_id_tmp[2]
        else:
            node_filter_dict[node_id_tmp[0]] = node_id_tmp[2]
    return [node_id_str.strip(','), node_filter_dict, node_filter_not_access_dict, node_save_dict]


def find_link_by_node(node_id):
    """
    根据nodeid查找内部路方向的候选link组
    Args:
        node_id: 大门的主键id
    Return:
        link_dict: 字典类型，key是linkid，value是link的几何信息
    """
    link_dict = dict()
    sql = "select link_id, s_nid, e_nid, b.node_id, form, st_astext(geom) \
            from nav_link a inner join nav_gate b \
            on a.link_id = b.in_linkid or a.link_id = b.out_linkid \
            where b.node_id = %s "
    cursor_road.execute(sql, [node_id])
    node_id_str = ""
    res_link = cursor_road.fetchall()
    for v_node in res_link:
        link_dict[v_node[0]] = v_node[5]
        if v_node[4] == "52":
            node_id_str = node_id_str + "','" + v_node[1] + "','" + v_node[2]
    node_id_str = node_id_str.strip("','")
    sql = "select link_id, st_astext(geom) from nav_link where (s_nid in ('%s') or e_nid in ('%s')) and form = '52'" % (
        node_id_str, node_id_str)
    cursor_road.execute(sql)
    for link_tmp in cursor_road.fetchall():
        link_dict[link_tmp[0]] = link_tmp[1]
    return link_dict
    

def find_aoi_by_link(link_geom_dict, node_id, face_id):
    """
    根据linkid和aoi的范围框信息寻找与link相连的框内的内部路组
    """
    face_id_dict = dict()
    node_filter_dict = dict()
    sql = "select st_astext(geom) from nav_node where node_id = %s"
    cursor_road.execute(sql, [node_id])
    res_node = cursor_road.fetchone()
    if res_node is None or len(res_node) == 0:
        return face_id_dict
    node_region = shapely.wkt.loads(res_node[0])
    for link_id, link_geom in link_geom_dict.items():
        link_region = shapely.wkt.loads(link_geom)
        #查找相交的AOI
        sql = "select face_id, st_astext(geom) from blu_face where st_crosses(st_geomfromtext(%s, \
                    4326), geom) and kind != '52' and aoi_level = 2"
        cursor_aoi.execute(sql, [link_geom])
        res_aoi = cursor_aoi.fetchall()
        for res_aoi_tmp in res_aoi:
            aoi_region = shapely.wkt.loads(res_aoi_tmp[1])
            link_inter = link_region.intersection(aoi_region)
            point_region = link_region.intersection(aoi_region.boundary)
            if point_region.geom_type == "MultiPoint" or point_region.distance(node_region) > 0.0004:
                continue
            if link_inter.length / aoi_region.length < 0.005:
                #link与aoi相交的部分长度小于周长的0.5%
                sql = "select node_id, st_astext(a.geom) from nav_node a \
                    inner join nav_link b on a.node_id = b.s_nid \
                    or a.node_id = b.e_nid where b.link_id = %s"
                cursor_road.execute(sql, [link_id])
                res_node = cursor_road.fetchall()
                for res_node_tmp in res_node:
                    #找到link两个端点的坐标，判断是否在AOI内
                    if aoi_region.contains(shapely.wkt.loads(res_node_tmp[1])):
                        #在aoi内，判断是否连接了别的内部路
                        sql = "select count(*) from nav_link \
                            where (s_nid = %s or e_nid = %s) and link_id != %s and form = '52'"
                        cursor_road.execute(sql, [res_node_tmp[0], res_node_tmp[0], link_id])
                        if cursor_road.fetchone()[0] == 0:
                            #没有连接其余内部路，不认为与该aoi匹配
                            if face_id == res_aoi_tmp[0]:
                                node_filter_dict[res_node_tmp[0]] = res_node_tmp[1]
                            break
                        else:
                            face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]
            else:
                face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]

    return face_id_dict, node_filter_dict


def search_inner_link_group(link_id, aoi_geom):
    """
    根据linkid和aoi的范围框信息寻找与link相连的框内的内部路组
    """
    aoi_region = shapely.wkt.loads(aoi_geom)
    link_geom_dict = dict()
    link_complete_geom_dict = dict()
    link_set = set()
    link_set_tmp = set()
    link_set.add(link_id)
    link_set_tmp.add(link_id)
    snid_dict = {}
    enid_dict = {}
    
    sql = "select s_nid, e_nid, st_astext(geom) from nav_link where link_id = %s "
    cursor_road.execute(sql, [link_id])
    res_link = cursor_road.fetchone()
    snid_dict[link_id] = res_link[0]
    enid_dict[link_id] = res_link[1]
    link_geom_dict[link_id] = aoi_region.intersection(shapely.wkt.loads(res_link[2])).wkt
    link_complete_geom_dict[link_id] = shapely.wkt.loads(res_link[2]).wkt

    while len(link_set) > 0:
        link_tmp = link_set.pop()
        s_nid_tmp = snid_dict[link_tmp]
        e_nid_tmp = enid_dict[link_tmp]
        sql = "select adjoin_nid from nav_node where node_id in (%s,%s) and adjoin_nid != ''"
        cursor_road.execute(sql, [s_nid_tmp, e_nid_tmp])
        adjoin_nid_res = cursor_road.fetchall()
        adjoin_nid_tmp = ""
        if adjoin_nid_res is not None and len(adjoin_nid_res) > 0:
            for adjoin_nid_res_tmp in adjoin_nid_res:
                adjoin_nid_tmp = adjoin_nid_tmp + adjoin_nid_res_tmp[0] + "','"
            adjoin_nid_tmp = adjoin_nid_tmp[:-3]
        sql = "select link_id,s_nid,e_nid, st_astext(geom) from nav_link where \
                (s_nid in ('%s', '%s', '%s') \
                or e_nid in ('%s', '%s','%s')) and form = '52' " \
                % (s_nid_tmp, e_nid_tmp, adjoin_nid_tmp, s_nid_tmp, e_nid_tmp, adjoin_nid_tmp)
        cursor_road.execute(sql)
        res = cursor_road.fetchall()
        if res is None or len(res) == 0:
            continue
        for tmp_res in res:
            tmp_link = tmp_res[0]
            tmp_snid = tmp_res[1]
            tmp_enid = tmp_res[2]
            tmp_geom = tmp_res[3]
            if tmp_link not in link_set_tmp and aoi_region.intersects(shapely.wkt.loads(tmp_geom)):
                link_set.add(tmp_link)
                link_set_tmp.add(tmp_link)
                snid_dict[tmp_link] = tmp_snid
                enid_dict[tmp_link] = tmp_enid
                link_geom_dict[tmp_link] = aoi_region.intersection(shapely.wkt.loads(tmp_geom)).wkt
                link_complete_geom_dict[tmp_link] = shapely.wkt.loads(tmp_geom).wkt
    
    return link_geom_dict, link_complete_geom_dict


def find_aoi_with_outlink(node_id):
    """
    根据node查询外部路方向的候选AOI
    """
    link_dict = dict()
    sql = "select link_id, s_nid, e_nid, b.node_id, form, st_astext(geom)  \
            from nav_link a inner join nav_gate b \
            on a.link_id = b.out_linkid or a.link_id = b.in_linkid \
            where b.node_id = %s and a.form != '52'"
    cursor_road.execute(sql, [node_id])
    link_res = cursor_road.fetchone()
    node_id_extension = link_res[2] if link_res[1] == node_id else link_res[1]
    sql = "select link_id, st_astext(geom) from nav_link where (s_nid = %s or e_nid = %s ) \
            and link_id != %s and form != '52'"
    cursor_road.execute(sql, [node_id_extension, node_id_extension, link_res[0]])
    link_info = cursor_road.fetchall()
    if link_info is None or len(link_info) == 0: 
        return link_dict
    for link_info_tmp in link_info:
        #查找相交的AOI
        sql = "select face_id, st_astext(geom) from blu_face where st_crosses(st_geomfromtext(%s, \
                    4326), geom) and st_contains(geom, st_geomfromtext(%s, 4326)) and kind != '52' and aoi_level = 2"
        cursor_aoi.execute(sql, [link_info_tmp[1], link_res[5]])
        res_aoi = cursor_aoi.fetchall()
        if res_aoi is None or len(res_aoi) == 0: 
            continue
        for res_aoi_tmp in res_aoi:
            link_dict[res_aoi_tmp[0]] = link_res[0], res_aoi_tmp[1]
    return link_dict


def union_link(link_geom_dict):
    """
    把字典中线的几何信息合并起来
    """
    if len(link_geom_dict) < 1:
        return ""
    link_geom_union = ""
    
    for link_geom in link_geom_dict.values():
        if link_geom_union == "":
            link_geom_union = link_geom
            continue
        sql = "select st_astext(ST_LineMerge(st_union(st_geomfromtext(%s, 4326), st_geomfromtext(%s, 4326))))"
        cursor_road.execute(sql, [link_geom_union, link_geom])
        link_geom_union = cursor_road.fetchone()[0]
    return link_geom_union


def cut_polygon_by_line(polygon, line):
    """
    用线切分面
    """
    line.append(polygon.boundary)
    merged = linemerge(line)
    borders = unary_union(merged)
    polygons = polygonize(borders)
    return list(polygons)


def node_id_to_short_id(node_id):
    """
    node_id转换成短node
    """
    content = {
            "client_id": "idmapping", 
            "user_id": "lixia", 
            "req_id": 1, 
            "authcode": "QWUIQHERWIZXOIHJNBG", 
            "action": 107, \
            "params": {"id_table_name": "image_N", 
                "mapping_method": "2", 
                "id_list": node_id
            }}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    requrl = "http://api.guoke.map.baidu.com/trunk/idmap"
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['contents']['errno'] != 0:
        return
    return res['contents']['data']['list']


def aoi_contains_geom(face_id, geom):
    """
    判断几何点或线是否在AOI范围框内
    """
    sql = "select st_astext(geom) from blu_face where face_id = %s"
    cursor_aoi.execute(sql, [face_id])
    aoi_res = cursor_aoi.fetchone()
    if aoi_res is None or len(aoi_res) == 0:
        return False
    return shapely.wkt.loads(aoi_res[0]).contains(shapely.wkt.loads(geom))


def get_node_geom(node_id):
    """
    根据nodeid查询范围信息
    """
    sql = "select st_astext(geom) from nav_node where node_id = %s"
    cursor_road.execute(sql, [node_id])
    res = cursor_road.fetchone()
    if res is None or len(res) == 0:
        return res
    return res[0]


def get_aoi_info(face_id, fields):
    """
    查询aoi指定字段的信息
    """
    sql = "select %s from blu_face where face_id = '%s'" % (fields, face_id)
    cursor_aoi.execute(sql)
    res = cursor_aoi.fetchone()
    if res is None:
        return res
    return res


def run(sub_file):
    """更新大门通行属性的入口函数
    """
    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))) 
    fw = open("gate_info" + str(sub_file), "a")
    f_nav = open("gate_info_nav" + str(sub_file), "a")
    f_sta = open("gate_info_sta" + str(sub_file), "a")
    f_check = open("manu_check" + str(sub_file), "a")
    f_creatplan = open("creat_plan_bid" + str(sub_file), "a")
    create_plan_bid_list = []
    bid_list = []

    with open(sub_file, "r") as fbid:
        lines = fbid.readlines()
        for line in lines:
            bid_list.append(line.split('\t')[0].strip())
    
    for bid in bid_list:
        #全国匹配时，用于判断bid是否已经匹配
        sql = "select count(*) from bid_matched_list where bid = %s"
        cursor_poi.execute(sql, [bid])
        if cursor_poi.fetchone()[0] > 0:
            continue
        #获取poi的名称
        sql = "select name from poi where bid = %s"
        cursor_poi_online.execute(sql, [bid])
        poi_res = cursor_poi_online.fetchone()
        name_poi = ""
        if poi_res is not None and len(poi_res) > 0:
            name_poi = poi_res[0]
        #如果bid失效了，AOI正常进行匹配
        # else:
        #     continue
        face_id_dst, node_set = find_node_to_process_with_bid(bid)
        fields = "st_astext(geom), mesh_id"
        res = get_aoi_info(face_id_dst, fields)
        if res is None:
            continue
        aoi_geom = res[0]
        mesh_id = res[1]
        node_id_success = ""
        node_id_fail = ""
        related_node_link_dict = dict()
        for node_id in node_set:
            node_geom = get_node_geom(node_id)
            if node_geom is None:
                continue
            link_geom_dict = find_link_by_node(node_id)
            face_id_dict, node_filter_dict = find_aoi_by_link(link_geom_dict, node_id, face_id_dst)
            for node_tmp in node_filter_dict.keys():
                #这里的大门被策略过滤了, 不进行轨迹验证
                f_check.write("{}\t{}\t{}\t{}\t{}\n".format(bid, name_poi, node_tmp,
                              node_filter_dict[node_tmp], "link与aoi相交线的长度太短"))
            max_area = 0.0
            max_length = 0.0
            face_id_opt = ""
            if len(face_id_dict) == 0:
                face_id_dict = find_aoi_with_outlink(node_id)
            if len(face_id_dict) == 0:
                sql = "insert into gate_aoi_match_batch_fail (node_id, face_id, bid, status, mesh_id, geom_node, geom_aoi) values \
                        (%s, %s, %s, 1, %s, st_geomfromtext(%s,4326),  st_geomfromtext(%s,4326))"
                cursor_poi.execute(sql, [node_id, face_id_dst, bid, mesh_id, node_geom, aoi_geom])
                fw.write("fail\t" + bid + "\t" + name_poi + "\t" + node_id + "\t" + str(link_geom_dict) + "\n")
                #这里大门和aoi在两条link之内不相交，可能是数据问题，可以进行轨迹验证
                if aoi_contains_geom(face_id_dst, node_geom):
                    f_check.write("{}\t{}\t{}\t{}\t{}\n".format(bid, name_poi, node_id, node_geom, "疑似aoi框太大,与aoi无交点"))
                node_id_fail = node_id_fail + "," + node_id
                continue
            for face_id, aoi_info in face_id_dict.items():
                #匹配到多个AOI，需要筛选置信度最高的
                #搜索内部路组
                link_dict, link_complete_dict = search_inner_link_group(aoi_info[0], aoi_info[1])
                aoi_region = shapely.wkt.loads(aoi_info[1])

                #组合link
                #link_all = union_link(link_complete_dict, cursor_road)
                link_region_list = []
                for link_info in link_complete_dict.values():
                    link_region_list.append(shapely.wkt.loads(link_info))
                link_all = linemerge(link_region_list)
                #过滤掉因aoi和道路异常相交引起的badcase
                if len(link_dict) <= 3 and link_all.intersection(aoi_region.boundary).geom_type == "MultiPoint":
                    #判断线拆分的aoi的区域面积所占的比例
                    polys = cut_polygon_by_line(aoi_region, link_region_list)
                    if len(polys) == 2 and (polys[0].area / aoi_region.area <
                           0.01 or polys[0].area / aoi_region.area > 0.99):
                        if face_id == face_id_dst:
                            #这里的大门被策略过滤了, 不进行轨迹验证
                            f_check.write("{}\t{}\t{}\t{}\t{}\n".format(
                                bid, name_poi, node_id, node_geom, "link与aoi相交部分的面积太小"))
                        continue
                    
                #计算凸包的面积和周长
                link_length_tmp = link_all.length
                link_area_tmp = link_all.convex_hull.area
                if max_area < link_area_tmp:
                    face_id_opt = face_id
                    max_area = link_area_tmp
                if max_area == 0.0 and max_length < link_length_tmp:
                    max_length = link_length_tmp 
                    face_id_opt = face_id
            if face_id_opt == "":
                sql = "insert into gate_aoi_match_batch_fail (node_id, face_id, bid, status, mesh_id, geom_node, geom_aoi) values \
                        (%s, %s, %s, 0, %s, st_geomfromtext(%s,4326),  st_geomfromtext(%s,4326))"
                cursor_poi.execute(sql, [node_id, face_id_dst, bid, mesh_id, node_geom, aoi_geom])
                fw.write("fail\t" + bid + "\t" + name_poi + "\t" + node_id + "\t" + \
                         str(link_geom_dict) + "\t" + str(face_id_dict) + "\n")
                node_id_fail = node_id_fail + "," + node_id
                continue
            if face_id_opt != face_id_dst:
                #匹配到非清单中的AOI
                continue
            related_node_link_dict[node_id] = face_id_dict[face_id_opt][0]
            node_id_success = node_id_success + "," + node_id
            sql = "select coalesce(poi_bid, ''), name_ch from blu_Face bf left join blu_face_poi bfp \
                    on bf.face_id = bfp.face_id where bf.face_id = %s"
            cursor_aoi.execute(sql, [face_id_opt])
            res_bid = cursor_aoi.fetchone()
            if res_bid is None or len(res_bid) == 0:
                bid = ""
                name_aoi = ""
            else:
                bid = res_bid[0]
                name_aoi = res_bid[1]
            sql = "insert into gate_aoi_match_batch_success (node_id, face_id, bid, src, status, mesh_id, geom_node, geom_aoi) values \
                        (%s, %s, %s, 0, 0, %s, st_geomfromtext(%s,4326),  st_geomfromtext(%s,4326))"
            cursor_poi.execute(sql, [node_id, face_id_dst, bid, mesh_id, node_geom, aoi_geom])
            fw.write("success\t%s\t%s\t%s\t%s\t%s\t%s\n" % (node_id, str(link_geom_dict), \
                    str(face_id_dict.keys()), face_id_opt, bid, name_aoi))
        if node_id_success != "":
            sql = "insert into aoi_gate_match_batch_result (bid, face_id, success_node_id, fail_node_id, \
                    level, status, mesh_id) values (%s, %s, %s, %s,  1, 0, %s)"
            cursor_poi.execute(sql, [bid, face_id_dst, node_id_success, node_id_fail.strip(','), mesh_id])
            f_sta.write("success" + "\t" + bid + "\t" + name_poi + "\t" + face_id_dst + "\t" + \
                        node_id_success.strip(',') + "\t" + node_id_fail.strip(',') + "\n")
        else:
            res_node_info = find_aoi_nearby_node(face_id_dst)
            node_info = res_node_info[0]
            node_filter_dict_tmp = res_node_info[1]
            node_filter_not_access_dict = res_node_info[2]
            node_save_dict = res_node_info[3]
            for node_tmp in node_filter_dict_tmp.keys():
                f_check.write("{}\t{}\t{}\t{}\t{}\n".format(bid, name_poi, node_tmp,
                              node_filter_dict_tmp[node_tmp], "大门与aoi的连线跨高等级道路"))
            for node_tmp in node_filter_not_access_dict.keys():
                f_check.write("{}\t{}\t{}\t{}\t{}\n".format(bid, name_poi, node_tmp,
                              node_filter_not_access_dict[node_tmp], "大门不连接内部路与外部路"))
            sql = "insert into aoi_gate_match_batch_result (bid, face_id, success_node_id, fail_node_id, \
                level, status, mesh_id) values (%s, %s, %s, %s,  0, 0, %s)"
            cursor_poi.execute(sql, [bid, face_id_dst, node_id_success, node_id_fail.strip(','), mesh_id])
            f_sta.write("fail\t" + bid + "\t" + name_poi + "\t" + face_id_dst + "\t" + node_info + "\n")
            item = {"bid":bid, "aoi_src_data_extra":[]}
            for node_info_tmp in node_save_dict.keys():
                item['aoi_src_data_extra'].append({"trans_id":node_info_tmp + "_" + 
                    time.strftime('%Y%m%d', time.localtime(time.time())), 
                    "mark_wkt":node_save_dict[node_info_tmp]})
            create_plan_bid_list.append(item)

        #提交    
        sql = "insert into bid_matched_list (bid) values (%s)"
        cursor_poi.execute(sql, [bid])
        
        conn_aoi.rollback()
        conn_road.rollback()
        conn_poi.commit()
        conn_poi_online.rollback()

    json.dump(create_plan_bid_list, f_creatplan)
    f_creatplan.close()
    fw.close()
    f_nav.close()
    f_sta.close()
    f_check.close()
    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
            
            
if __name__ == '__main__':
    # bid_list = []
    # dbname = "master_back"
    # _host = cf.get(dbname, 'host')
    # _port = cf.get(dbname, 'port')
    # _user = cf.get(dbname, 'user')
    # _database = cf.get(dbname, 'database')
    # _password = cf.get(dbname, 'password')
    # conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
    # cursor_aoi = conn_aoi.cursor()
    run(sys.argv[1])
    """sql = "select poi_bid from blu_face_poi bfp inner join blu_face bf on bfp.face_id = bf.face_id where kind != '52' and aoi_level = 2"
    cursor_aoi.execute(sql)
    res_bid = cursor_aoi.fetchall()
    for bid_tmp in res_bid:
        print bid_tmp[0]
        bid_list.append(bid_tmp[0])
    process_list = []
    for i in range(len(bid_list)/80000):
        p = Process(target=run, args=[bid_list[i*80000:min((i+1)*80000, len(bid_list) -1)], i])
        p.start()
        process_list.append(p)
    
    for p in process_list:
        p.join()
    """
