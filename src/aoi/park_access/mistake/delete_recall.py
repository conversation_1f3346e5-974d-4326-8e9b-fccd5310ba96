#coding:utf-8
"""
删除情报回捞，出入口关联错情报入库
"""
import sys
import time
import uuid
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely import wkt
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao

poi_online_rw = PoiOnlineRW()
poi_online_query = PoiOnlineQuery()
aoi_query = MasterBackDao()

# parking_list = poi_online_query.get_poi_info_by_bid(bid)

JQD_BATCH_ID = "JQDCWP2024101810"
JQD_BATCH_ID_DEL_RECALL = 'JQDGLC2024120502'
# JQD_BATCH_ID_ONE_PRECISE_ACCESS = "JQDCWP2024101811"
# JQD_BATCH_ID_RONGYU = "JQDCWP2024111201"
JQD_BATCH_ID_RONGYU = "JQDCWP2024112901"


def diff_online(park_bid, access_bid):
	"""
	线上查询diff
	"""
	sql = "select st_astext(gcj_geom), st_astext(bd_geom) from parking where bid='{}'".format(access_bid)
	access_info = aoi_query.queryone(sql, [])
	if access_info is None:
		return False, 'access not exist'
	gcj_wkt = access_info[0]
	bd_wkt = access_info[1]
	access_sp = wkt.loads(bd_wkt).buffer(30)
	sql = "select road_relation_childrens,show_tag from park_online_data where  bid = '{}'".format(park_bid)
	park_info = poi_online_query.queryone(sql, [])
	sql = "select road_relation from parking where parent_id = '{}' and status not in (2, 17)".format(park_bid)
	access_list_cd =  aoi_query.queryall(sql, [])
	if park_info is None or access_list_cd is None :
		return False, 'parking not exist'
	if park_info: 
		if park_info[0] and "link_info" in park_info[0]:
			for item in park_info[0]['link_info']:
				if item['point'] != '' and item['orientation'] == 1:
					point_sp = Point(item['point'].split(',')[0], item['point'].split(',')[1])
					# print(point_sp)
					if access_sp.contains(point_sp):
						print(park_bid, access_bid, "30m附近已有出入口")
						return False, '30m附近已有出入口'
	if access_list_cd:
		for access in access_list_cd:
			if access[0] and "link_info" in access[0]:
				for item in access[0]['link_info']:
					if item['point'] != '' and item['orientation'] == 1:
						point_sp = Point(item['point'].split(',')[0], item['point'].split(',')[1])
						# print(point_sp)
						if access_sp.contains(point_sp):
							print(park_bid, access_bid, "30m附近已有出入口")
							return False, '30m附近已有出入口'
	print(park_bid, access_bid, "need-add")
	return True, 'need-add'


def insert_qb(park_bid, access_bid, jdq_batch='JQDGLC2024120502', strategy='PARK_ACCESS_MISTAKE_BY_DEL_RECALL'):
	"""
	插入情报
	"""
	sql = """
		select road_relation_childrens,show_tag,parent_id 
		from park_online_data 
		where  bid = '{park_bid}'
		"""
	park_info = poi_online_query.queryone(sql, [])
	if not park_info:
		sql = "select road_relation_childrens,show_tag,parent_id from parking where  bid = '{}'".format(park_bid)
		park_info = aoi_query.queryone(sql, [])
	sql = "select st_astext(gcj_geom), st_astext(bd_geom) from parking where bid='{}'".format(access_bid)
	access_info = aoi_query.queryone(sql, [])
	gcj_wkt = access_info[0]

	resp = ''
	guid = str(uuid.uuid4()).replace("-", "")
	data = {
            'bid': park_bid,
            'parent_bid': park_info[2],
            'strategy': strategy,
            'strategy_value': "",
            'type': 'park_access_mistake',
            'batch_number': f"park_access_mistake_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': gcj_wkt,
            'guid': guid,
            'source_id': "park_access_mistake_check_" + guid,
            'show_tag': park_info[1],
            'outside_id': '',
            'status': 'INIT',
            'resp': resp,
            'node_id': "",
            'link_id': "",
            'jdq_batch': jdq_batch,
            'road_relation': "",
			'confidence': '0',
			'remark': '',
            'park_access_bid': access_bid
        }
	# print(data)
	# exit(1)
	poi_online_rw.insert_mistake_intelligence(data)

# 删除情报回捞
# if __name__ == '__main__':
# 	input_file = sys.argv[1]
# 	with open(input_file, 'r') as f:
# 		for line in f.readlines():
# 			access_bid = line.strip().split('\t')[0]
# 			park_bid = line.strip().split('\t')[1]
# 			if park_bid and access_bid:
# 				res, msg = diff_online(park_bid, access_bid)
# 				if res:
# 					insert_qb(park_bid, access_bid)


def get_strategy_task(access_bid):
	"""
	获取策略任务
	"""
	sql = f"""select status,id,strategy from park_access_intelligence 
			where type = 'park_access_mistake' 
			and park_access_bid = '{access_bid}' 
			order by created_at desc limit 1"""
	res = poi_online_query.queryone(sql, [])
	return res		
	


# 斌哥竞品情报下发
if __name__ == '__main__':
	input_file = sys.argv[1]
	with open(input_file, 'r') as f:
		for line in f.readlines():
			access_bid = line.strip().split('\t')[1]
			park_bid = line.strip().split('\t')[0]
			jdq_batch = JQD_BATCH_ID
			strategy = "PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_OFFLINE"
			# strategy = "PARK_ACCESS_MISTAKE_BY_COMPLETE_CHARGE_DIFF_OFFLINE"
			# strategy = "PARK_ACCESS_MISTAKE_BY_PASSAGE_OFFLINE"
			res = get_strategy_task(access_bid)
			if not res:
				print(f"未下发过，插入\t{access_bid}")
				insert_qb(park_bid, access_bid, jdq_batch, strategy)
			else:
				if res[0] in ['PUSHED', 'PUSHED_BEFORE']:
					print(f"已下发过，跳过\t{access_bid}")
				else:
					# if res[2] == "PARK_ACCESS_MISTAKE_BY_LOW_ACCEPT":
					# 	print(f"低采纳高优 跳过\t{access_bid}")
					# else:
					print(f"未下发过，更新\t{access_bid}")
					poi_online_rw.update_park_access_intelligence_strategy_status_by_id([res[1]], "INIT", strategy, resp='')

# aq_py3 delete_recall.py  delete_recall.txt 
