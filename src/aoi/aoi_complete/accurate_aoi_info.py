# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准AOI情报产出，层级判断
"""
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from src.aoi.aoi_complete import aoi_complete_cal
from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext


def accurate_aoi_level_cal(ctx: AoiCompleteContext, bid_to_node_map):
    """
    精准AOI等级计算
    :param bid_to_node_map: {bid => node_id_list}
    :return: {bid => {level => level, node_id => node_id_list}}
    """
    aoi_result = {}
    for bid, node_id_list in bid_to_node_map.items():
        aoi_level, deletion_node_id_list = get_aoi_level_by_bid_and_node(ctx, bid, node_id_list)
        aoi_result[bid] = {"level": aoi_level, "node_id": deletion_node_id_list}
        print(f"bid_list：{bid}, node_id_list:{node_id_list}, result:{aoi_result[bid]}")
    return aoi_result


def get_aoi_level_by_bid_and_node(ctx: AoiCompleteContext, bid, node_id_list):
    """
    获取BID对应的完整性等级
    :param bid:
    :return aoi_level: AOI 等级；deletion_node_id_list：缺失的NodeID
    """
    # aoi等级
    aoi_level = aoi_complete_cal.AOI_COMPLETE_NOT_MATCH
    # 缺失的大门情报
    deletion_node_id_list = []
    aoi_info = ctx.pg_query_dao.get_aoi_by_bid(bid)
    if aoi_info is None or len(aoi_info) == 0:
        return aoi_level, deletion_node_id_list

    # # 查询母库关联大门成果
    # node_id_list = []
    # aoi_node_list = aoi_complete_cal.dao.get_node_by_main_bid(bid)
    # print(f"母库关联大门成果：{aoi_node_list}")
    # if aoi_node_list is not None and len(aoi_node_list) > 0:
    #     node_id_list = [aoi_node[0] for aoi_node in aoi_node_list]
    #
    # # 母库Node diff 履历库Node
    # node_history_diff_master_node(node_id_list, bid)
    # if len(node_id_list) == 0:
    #     return aoi_level, deletion_node_id_list

    # 查询主点信息
    poi_info = ctx.pg_query_dao.get_poi_by_bid(bid)
    if poi_info is None or len(poi_info) == 0:
        return aoi_level, deletion_node_id_list

    # # 基础AOI计算
    aoi_level = aoi_complete_cal.basic_aoi_cal(ctx, node_id_list, poi_info, bid)
    print(f"基础AOI计算结果：{aoi_level}")
    if aoi_level != aoi_complete_cal.AOI_COMPLETE_BASIC:
        return aoi_level, deletion_node_id_list

    # 精准AOI计算
    aoi_level, deletion_node_id_list = aoi_complete_cal.accurate_aoi_cal(ctx, node_id_list, poi_info, aoi_info[2])
    print(f"精准AOI计算：{aoi_level}, deletion_node_id_list:{deletion_node_id_list}")

    return aoi_level, deletion_node_id_list


def node_history_diff_master_node(ctx: AoiCompleteContext, node_id_list, bid):
    """
    履历库 node 与母库Node diff
    :param bid:
    :return:
    """
    # 查询待入库的Node
    node_history_list = ctx.pg_query_dao.get_aoi_gate_history_by_bid(bid)
    if node_history_list is None or len(node_history_list) == 0:
        return
    for node_history in node_history_list:
        if node_history[1] == 'add':
            node_id_list.append(node_history[0])
        elif node_history[1] == 'delete' and node_history[0] in node_id_list:
            node_id_list.remove(node_history[0])
    return


def get_aoi_level_by_bid(ctx: AoiCompleteContext, bid_list):
    """
    获取BID对应的完整性等级
    :param bid:
    :return aoi_level: AOI 等级；deletion_node_id_list：缺失的NodeID
    """
    result = {}
    for key, bid in enumerate(bid_list):
        print(f"当前进度：{key},成果：{len(result)}")
        # aoi等级
        aoi_level = aoi_complete_cal.AOI_COMPLETE_NOT_MATCH
        # 缺失的大门情报
        aoi_info = ctx.pg_query_dao.get_aoi_by_bid(bid)
        if aoi_info is None or len(aoi_info) == 0:
            print("没有关联AOI")
            continue

        # # 查询母库关联大门成果
        node_id_list = []
        aoi_node_list = ctx.pg_query_dao.get_node_by_main_bid(bid)
        print(f"母库关联大门成果：{aoi_node_list}")
        if aoi_node_list is not None and len(aoi_node_list) > 0:
            node_id_list = [aoi_node[0] for aoi_node in aoi_node_list]

        # 查询主点信息
        poi_info = ctx.pg_query_dao.get_poi_by_bid(bid)
        if poi_info is None or len(poi_info) == 0:
            print("失效")
            continue

        # 精准AOI计算
        aoi_level, deletion_node_id_list = aoi_complete_cal.accurate_aoi_cal(ctx, node_id_list, poi_info, aoi_info[2])
        if len(deletion_node_id_list) > 0:
            result[bid] = deletion_node_id_list
    return result


if __name__ == '__main__':
    with AoiCompleteContext() as _ctx:
        bid_to_node_map = {'6279446990180967433': ['8752437959060007655']}
        accurate_aoi_level_cal(_ctx, bid_to_node_map)
