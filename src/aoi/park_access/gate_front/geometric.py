"""
通用的几何工具类（从 aoi-ml 里拷过来的，这个 aoi-strategy 真是垃圾）
"""
import math

import numpy as np
import shapely.ops
from shapely import LineString, Point, Polygon, wkt

METER = 1e-5
METER_2 = METER**2


def get_sidelines(polygon: Polygon, baseline: LineString, eps=0.5 * METER) -> tuple[LineString, LineString]:
    """
    获取门前停车场的（外侧线，内侧线），若 polygon 两端不规则，可能抛出 ValueError 异常。
    """

    def get_side_point(geom_line: LineString, line: LineString, pt: Point):
        clip_pts = geom_line.intersection(line)
        side_pts = flat_point(clip_pts)
        if len(side_pts) < 2:
            raise ValueError(f"at least two point but got {len(side_pts)}")

        sorted_side_pts = sorted(side_pts, key=lambda x: pt.distance(x))
        is_odd = len(sorted_side_pts) % 2 == 1
        inner_index = -2 if is_odd else -1  # 一刀切下，穿进+穿出必有偶数交点
        return sorted_side_pts[0], sorted_side_pts[inner_index]

    def get_polygon_line(pts, start_pt):
        i = pts.index(start_pt)
        pts = pts[i:] + pts[:i]
        pts = [*pts, pts[0]]
        return LineString(pts)

    def clip_line(to_clip_line, p0, p1):
        d0 = to_clip_line.project(p0)
        d1 = to_clip_line.project(p1)
        d0, d1 = (d0, d1) if d0 < d1 else (d1, d0)
        return shapely.ops.substring(to_clip_line, d0, d1).simplify(0.01 * METER)

    def insert_point(pts: list[Point], line: LineString, p: Point):
        pt_dists = [line.project(p) for p in pts]
        p_dist = line.project(p)
        i = sum(1 for d in pt_dists if d < p_dist)
        pts.insert(i, p)

    exterior_line = polygon.exterior
    points = [Point(x, y) for x, y in exterior_line.coords]

    dists = [baseline.project(p) for p in points]
    baseline = shapely.ops.substring(baseline, min(dists) + eps, max(dists) - eps)
    p_first, clip_a, p_last, clip_b = _get_knifes(baseline, 50 * METER)
    e_p0, i_p0 = get_side_point(exterior_line, clip_a, p_first)
    e_p1, i_p1 = get_side_point(exterior_line, clip_b, p_last)

    points = points[:-1]
    insert_point(points, exterior_line, e_p0)
    insert_point(points, exterior_line, i_p0)
    insert_point(points, exterior_line, e_p1)
    insert_point(points, exterior_line, i_p1)

    polygon_line = get_polygon_line(points, i_p0)
    e_line = clip_line(polygon_line, e_p0, e_p1)
    polygon_line = get_polygon_line(points, e_p0)
    i_line = clip_line(polygon_line, i_p0, i_p1)
    return e_line, i_line


def move_linestring(line: LineString, move_dist: float) -> LineString:
    """
    已类似 buffer 的方式，侧向平移一条线上的点，默认向直线的左侧平移，向右平移时，需要将 move_dist 取负数
    NOTE: 使用 shapely.buffer 后截取线，不要自己写角平分线平移！否则角平分线交错后处理起来很麻烦
    """

    def get_index(pts: np.ndarray, point: np.ndarray):
        dists = np.linalg.norm(pts - point, axis=1)
        i = dists.argmin()
        assert abs(dists[i]) < 1e-8, f"not found '{point}' in pts"
        return i

    def get_next_index(i_prev, i_curr, length):
        i_next = i_curr + (-1 if i_curr < i_prev else 1)
        i_next = i_next if i_next >= 0 else length - 1
        i_next = i_next if i_next < length else 0
        return i_next

    def rearrange(pts, start_pt):
        i = get_index(pts, start_pt)
        if 0 < i < len(pts) - 1:
            pts = np.concatenate((pts[i:], pts[:i]))
        else:
            pts = pts

        return pts

    side_pts = np.array([(x, y) for x, y in line.coords])
    p0, p1, q0, q1 = side_pts[0], side_pts[1], side_pts[-1], side_pts[-2]
    buffer_polygon = line.buffer(move_dist, cap_style="flat", single_sided=True)
    buffer_pts = np.array([(x, y) for x, y in buffer_polygon.exterior.coords])
    buffer_pts = buffer_pts[:-1]
    i_p0, i_p1 = get_index(buffer_pts, p0), get_index(buffer_pts, p1)
    i_q0, i_q1 = get_index(buffer_pts, q0), get_index(buffer_pts, q1)

    i_p_next = get_next_index(i_p1, i_p0, len(buffer_pts))
    i_q_next = get_next_index(i_q1, i_q0, len(buffer_pts))

    p_next, q_next = buffer_pts[i_p_next], buffer_pts[i_q_next]
    buffer_pts = rearrange(buffer_pts, p0)

    a, b = get_index(buffer_pts, p_next), get_index(buffer_pts, q_next)
    a, b = (a, b) if a < b else (b, a)
    clip_pts = buffer_pts[a : b + 1]
    return LineString(clip_pts)


def remove_self_intersection(line: LineString) -> LineString:
    """
    去除线段的自相交，即去掉成环的部分，返回 LineString
    """
    if line.is_simple:
        return line

    line = line.intersection(line)
    line = shapely.ops.linemerge(line)
    lines = [x for x in flat_line(line) if not x.is_closed]
    line = shapely.ops.linemerge(lines)
    if not line.is_simple:
        # 一次只能去除第一层级的环，想象一根线的中部长着一个 8 字环，就需要递归 2 次才能去除
        return remove_self_intersection(line)

    return line


def is_left(point: Point, line: LineString) -> bool:
    """
    判断一个点在一条折线的左侧还是右侧
    """

    def get_range(distances: list[float]):
        distances = list(enumerate(distances))
        for (c_i, c_d), (n_i, n_d) in zip(distances, distances[1:]):
            if c_d * n_d <= 0:
                return c_i, n_i

    line_points = [Point(x, y) for x, y in line.coords]
    if len(line_points) == 2:
        start_index, end_index = (0, 1)
    else:
        poi_dist = line.project(point)
        dists = [line.project(p) for p in line_points]
        delta_dists = [d - poi_dist for d in dists]
        start_index, end_index = get_range(delta_dists)

    start_point, end_point = line_points[start_index], line_points[end_index]
    v_line = np.array([end_point.x, end_point.y]) - np.array([start_point.x, start_point.y])
    v_poi = np.array([point.x, point.y]) - np.array([start_point.x, start_point.y])
    return cross2d(v_line, v_poi) > 0


def is_left_polygon(target: Polygon, line: LineString):
    """
    判断多边形中最远的点是否在线段的左侧
    """
    pts = [Point(pt) for pt in target.exterior.coords]
    far_pt = max(pts, key=lambda x: x.distance(line))
    return is_left(far_pt, line)


def extend_linestring(line: LineString, dist: float) -> LineString:
    """
    延长 LineString 的两端，根据指定的距离
    :param line: 待延长的 LineString
    :param dist: 需要延长的距离
    :return: 返回延长后的 LineString
    """

    def extend_point(p0: Point, p1: Point):
        vec = np.array([p1.x - p0.x, p1.y - p0.y])
        vec = vec / np.linalg.norm(vec)
        vec = vec * dist
        return Point(p1.x + vec[0], p1.y + vec[1])

    points = [Point(x, y) for x, y in line.coords]
    points = [extend_point(points[1], points[0]), *points[1:-1], extend_point(points[-2], points[-1])]
    return LineString(points)


def trim_linestring(line: LineString, angle_cos: float, tolerance_len: float) -> LineString:
    """
    去除线段末端的小拐角，通常指：角度小、长度短的末端
    :param line: 待处理的 LineString
    :param angle_cos: 角度余弦值，小于该值则认为是异常点
    :param tolerance_len: 距离阈值，小于该值则认为是异常点
    :return: 返回去除异常点的 LineString
    """

    def is_abnormal(p, o, q):
        v1, v2 = p - o, q - o
        v1_len = np.linalg.norm(v1)
        v1 = v1 / v1_len
        v2 = v2 / np.linalg.norm(v2)
        return np.dot(v1, v2) > angle_cos and v1_len < tolerance_len

    pts = np.array([(x, y) for x, y in line.coords])
    if len(pts) < 3:
        return line

    if is_abnormal(pts[0], pts[1], pts[2]):
        pts = pts[1:]

    if len(pts) >= 3 and is_abnormal(pts[-1], pts[-2], pts[-3]):
        pts = pts[:-1]

    return LineString(pts)


def trim_linestring_for_buffer(line: LineString, buffer: float):
    """
    去除线段末端的小拐角，以 flat buffer 时，末端 buffer 线不交错为准
    """

    def get_left_vec(base_vec: np.ndarray, vec: np.ndarray):
        return vec if cross2d(base_vec, vec) > 0 else -vec

    def get_end_buffer_line(p0, p1, is_first: bool):
        vec = get_normalized_vec(p1, p0)
        move_vec = get_vertical_vec(vec)
        move_vec = get_left_vec(-vec if is_first else vec, move_vec)
        return LineString([p0, p0 + move_vec * buffer])

    def get_buffer_line(p0, p1, p2):
        v1, v2 = get_normalized_vec(p1, p0), get_normalized_vec(p1, p2)
        bisector_vec = get_bisector_vec(v1, v2)
        bisector_vec = get_left_vec(v2, bisector_vec)
        angle_sin = abs(cross2d(v2, bisector_vec))
        dist = buffer / float(angle_sin)
        return LineString([p1, p1 + bisector_vec * dist])

    side_pts = [np.array((x, y)) for x, y in line.coords]
    len_pts = len(side_pts)
    if len_pts < 3:
        return line

    buffer_lines = []
    # 中间点取角平分线
    for i in range(1, len_pts - 1):
        buffer_line = get_buffer_line(side_pts[i - 1], side_pts[i], side_pts[i + 1])
        buffer_lines.append(buffer_line)

    pointer_first = 0
    pointer_last = len_pts - 1
    buffer_line0 = get_end_buffer_line(side_pts[0], side_pts[1], is_first=True)
    buffer_line1 = get_end_buffer_line(side_pts[-1], side_pts[-2], is_first=False)
    while pointer_last - pointer_first + 1 >= 3:
        if buffer_line0.intersects(buffer_line1):
            # 随便去掉一个点
            pointer_last -= 1
            buffer_line1 = get_end_buffer_line(side_pts[pointer_last], side_pts[pointer_last - 1], is_first=False)
            continue

        buf_lines = buffer_lines[pointer_first : pointer_last - 1]
        if any(buffer_line0.intersects(x) for x in buf_lines):
            pointer_first += 1
            buffer_line0 = get_end_buffer_line(side_pts[pointer_first], side_pts[pointer_first + 1], is_first=True)
            continue

        if any(buffer_line1.intersects(x) for x in buf_lines):
            pointer_last -= 1
            buffer_line1 = get_end_buffer_line(side_pts[pointer_last], side_pts[pointer_last - 1], is_first=False)
            continue

        # 全部通关
        break

    return LineString(side_pts[pointer_first : pointer_last + 1])


def max_buffer(p: np.ndarray, o: np.ndarray, q: np.ndarray) -> float:
    """
    计算一个折角在进行 flat buffer 时，沿伸线不会交错的最大距离
    """
    v1 = p - o
    v1_len = np.linalg.norm(v1)
    v1 = v1 / v1_len
    v2 = get_normalized_vec(o, q)
    v_b = get_bisector_vec(v1, v2)
    angle_cos = np.dot(v1, v_b)
    if angle_cos <= 0:
        return math.inf

    angle_sin = abs(cross2d(v1, v_b))
    angle_tan = angle_sin / angle_cos
    max_buf = v1_len * angle_tan
    return max_buf


def get_foot_point(pt: Point, line: LineString, eps=1e-8):
    """
    获取点在直线上的垂足（确保一定要垂直，否则返回 None）
    """
    dist = line.project(pt)
    foot_point = line.interpolate(dist)
    if 0 < dist < line.length:
        return foot_point

    pts = [(x, y) for x, y in line.coords]
    if abs(dist) < eps:
        p0, p1 = pts[0], pts[1]
    elif abs(dist - line.length) < eps:
        p0, p1 = pts[-1], pts[-2]
    else:
        raise ValueError("foot point not on the line")

    v_poi = np.array([pt.x, pt.y]) - np.array([foot_point.x, foot_point.y])
    v_poi = v_poi / np.linalg.norm(v_poi)
    v_line = np.array(p1) - np.array(p0)
    v_line = v_line / np.linalg.norm(v_line)
    if abs(np.dot(v_poi, v_line)) < eps:
        return foot_point
    else:
        return None


def get_normalized_vec(p1: np.ndarray, p2: np.ndarray) -> np.ndarray:
    """
    返回单位向量：p1 -> p2
    """
    v = p2 - p1
    v = v / np.linalg.norm(v)
    return v


def get_bisector_vec(v1: np.ndarray, v2: np.ndarray) -> np.ndarray:
    """
    获取两个向量的角平分线向量（返回单位向量）
    """
    v1 = v1 / np.linalg.norm(v1)
    v2 = v2 / np.linalg.norm(v2)

    c = v1 + v2
    c_len = np.linalg.norm(c)
    if c_len < 1e-8:
        return get_vertical_vec(v1)
    else:
        return c / c_len


def get_vertical_vec(vec: np.ndarray) -> np.ndarray:
    """
    获取垂直向量（返回单位向量）
    """
    vec = np.array([-vec[1], vec[0]])
    vec = vec / np.linalg.norm(vec)
    return vec


def cross2d(a: np.ndarray, b: np.ndarray):
    """
    计算二维向量叉积，返回标量，而非矢量
    """
    return a[0] * b[1] - a[1] * b[0]


def _get_knifes(line: LineString, length: float):
    def get_one(a, b):
        vec = a - b
        vec = get_vertical_vec(vec) * length
        p1 = vec + a
        p2 = -vec + a
        return LineString([Point(*p1), Point(*p2)])

    points = [np.array([x, y]) for x, y in line.coords]
    return Point(*points[0]), get_one(points[0], points[1]), Point(*points[-1]), get_one(points[-1], points[-2])


def single_polygon(geom):
    """
    确保 geom 是 Polygon 类型（取 MultiPolygon 最大面）
    """
    geom = wkt.loads(geom) if isinstance(geom, str) else geom
    if geom.geom_type == "MultiPolygon":
        return max(geom.geoms, key=lambda g: g.area)
    elif geom.geom_type == "GeometryCollection":
        polygons = [x for x in geom.geoms if x.geom_type == "Polygon"]
        return max(polygons, key=lambda g: g.area) if polygons else None
    elif geom.geom_type == "Polygon":
        return geom

    raise ValueError(f"Unsupported geometry type: {geom.geom_type}, geom: {geom}")


def flat_polygon(geom_wkt):
    """
    将 MultiPolygon 展开为 Polygon
    """
    return _flat_geom(geom_wkt, "Polygon")


def flat_line(geom_wkt):
    """
    将 MultiLineString 展开为 LineString
    """
    return _flat_geom(geom_wkt, "LineString")


def flat_point(geom_wkt):
    """
    将 MultiPoint 展开为 Point
    """
    return _flat_geom(geom_wkt, "Point")


def _flat_geom(geom_wkt, geom_type):
    multi_type = f"Multi{geom_type}"

    def flat(geometry):
        g_type = geometry.geom_type
        if g_type == geom_type:
            yield geometry
        elif g_type == multi_type:
            yield from geometry.geoms
        elif g_type == "GeometryCollection":
            yield from (x for g in geometry.geoms for x in flat(g))

    geom = wkt.loads(geom_wkt) if type(geom_wkt) is str else geom_wkt
    return list(flat(geom))
