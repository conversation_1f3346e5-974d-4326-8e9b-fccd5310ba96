"""
聚合院落曾经筛选

# 1 判断是否跨高等级路

# 根据子点和聚合院落: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/sK3Q3rlBGn/sSPYSQwHLs/Ve9IIF8L7B3iJP

"""
import dataclasses
import datetime
import os.path
import sys

import pandas as pd
import tqdm
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())

from src.common import pgsql, mysql
from src.door_naming.strategy_v2 import common
import shapely.wkt
from shapely.ops import unary_union
import difflib

GATHER_TAG = "旅游景点,旅游景点;博物馆,旅游景点;动物园,旅游景点;风景区," \
             "旅游景点;公园,旅游景点;海滨浴场,旅游景点;教堂,旅游景点;景点," \
             "旅游景点;其他,旅游景点;水族馆,旅游景点;寺庙,旅游景点;文物古迹," \
             "旅游景点;游乐园,旅游景点;植物园,购物,购物;百货商场,购物;超市,购物;购物中心," \
             "购物;家居建材,购物;其他,购物;市场,教育培训,教育培训;成人教育,教育培训;高等院校," \
             "教育培训;科技馆,教育培训;科研机构,教育培训;培训机构,教育培训;其他," \
             "教育培训;特殊教育学校,教育培训;图书馆,教育培训;小学,教育培训;幼儿园," \
             "教育培训;中学,医疗,医疗;疗养院,医疗;其他,医疗;专科医院,医疗;综合医院," \
             "运动健身;体育场馆,文化传媒;广播电视,文化传媒;美术馆,文化传媒;文化宫," \
             "文化传媒;展览馆,休闲娱乐;度假村,休闲娱乐;剧院,休闲娱乐;农家院,休闲娱乐;休闲广场," \
             "房地产,房地产;其他,房地产;宿舍,房地产;写字楼,房地产;住宅区,公司企业,公司企业;厂矿," \
             "公司企业;公司,公司企业;农林园艺,公司企业;其他,公司企业;园区,政府机构,政府机构;福利机构," \
             "政府机构;各级政府,政府机构;公检法机构,政府机构;其他,政府机构;涉外机构,政府机构;行政单位," \
             "政府机构;政治教育机构,政府机构;中央机构,酒店,酒店;公寓式酒店,酒店;快捷酒店,酒店;民宿," \
             "酒店;其他,酒店;星级酒店,生活服务;物流公司,汽车服务,汽车服务;其他,汽车服务;汽车检测场," \
             "汽车服务;汽车美容,汽车服务;汽车配件,汽车服务;汽车维修,汽车服务;汽车销售".split(',')

# 暂时处理的房地产类型
HOUSE_TAG = "房地产;住宅区,房地产;其他".split(',')

RELATION_LEVEL = '层级关系'
RELATION_COVER_BID = '压盖关系_bid[无层级关系,请检查]'
RELATION_COVER_NAME = '压盖关系_name[无层级关系,请检查]'


@dataclasses.dataclass
class GatherHighLevelRoad:
    """
    高等级路
    """
    link_id: str
    geom: str
    kind: str
    form: str


@dataclasses.dataclass
class GatherSubAoi:
    """
    子区域, 存在名称相似度≥50%分区
    """
    bid: str
    name: str
    std_tag: str
    name_rate: float = 0.0
    relation_type: str = ''


@dataclasses.dataclass
class GatherAoi:
    """
    aoi
    """
    bid: str
    face_id: str
    name: str
    std_tag: str
    geom: str
    area: float
    strategy_level: int = -1
    can_strategy: str = '人工解'
    msg: str = ''
    road_union: str = ''
    covered_high_road: list[GatherHighLevelRoad] = dataclasses.field(default_factory=list)
    gather_sub_aoi: list[GatherSubAoi] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class Ctx:
    """
    ctx
    """
    bid_list: list[str] = dataclasses.field(default_factory=list)
    gather_list: list[GatherAoi] = dataclasses.field(default_factory=list)


def fill_bids(ctx: Ctx):
    """
    填充bid
    :param ctx:
    :return:
    """
    # df = pd.read_csv('bid_20250425.txt', converters={"bid": str})
    # bids = list(df['bid'])
    # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/neZ33vGA1I/2e0466cc305c41
    # 修复里面的聚合和基础
    # bids = ['4844336887923177836']
    # ctx.bid_list = bids
    sql = 'select b.poi_bid, c.std_tag from blu_face a inner join blu_face_poi b on a.face_id=b.face_id' \
          ' inner join poi_std_tag c on b.poi_bid=c.bid' \
          ' where a.src != %(src)s and a.aoi_level=1 and c.std_tag in %(tags)s'
    df = pd.read_sql(sql, pgsql.get_engine(pgsql.ENGINE_MASTERBACK_PROXY),
                     params={'src': 'SD', 'tags': tuple(HOUSE_TAG)})
    df.to_csv(f'bid_{datetime.date.today().strftime("%Y%m%d")}.txt', index=False, sep='\t')
    ctx.bid_list = list(df['poi_bid'])


def load_gather_sub_aoi(aoi: GatherAoi):
    """
    加载聚合院落下的子区域
    :param aoi:
    :return:
    """
    # 自带的层级关系
    handled_face_id = [aoi.face_id]
    sql = 'select b.face_id, b.poi_bid from blu_aoi_rel a' \
          ' inner join blu_face_poi b on a.face_id2=b.face_id ' \
          ' inner join blu_face c on b.face_id=c.face_id ' \
          ' where a.face_id1=:d1 and c.src != :d2 and b.poi_bid != :d3'
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"d1": aoi.face_id, "d2": 'SD', "d3": ""})
    for (face_id, poi_bid) in ret:
        poi_info = common.get_poi_info_by_bid(poi_bid)
        if poi_info:
            sub_aoi = GatherSubAoi(face_id, poi_info.name, poi_info.std_tag, relation_type=RELATION_LEVEL)
            sub_aoi.name_rate = difflib.SequenceMatcher(None, aoi.name, poi_info.name).ratio()
            aoi.gather_sub_aoi.append(sub_aoi)
            handled_face_id.append(face_id)

    # 压盖的基础院落
    sql = 'with tmp as (select st_geomfromtext(:d1, 4326) as aoi_geom) ' \
          ' select a.face_id, a.name_ch, b.poi_bid, b.poi_id, a.update_time from ' \
          ' blu_face a inner join blu_face_poi b on a.face_id=b.face_id, tmp ' \
          ' where ' \
          '     st_intersects(a.geom,tmp.aoi_geom) and a.src != :d2 and a.aoi_level=2 '
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {'d1': aoi.geom, 'd2': 'SD'})
    for row in ret:
        face_id = row.face_id
        if face_id in handled_face_id:
            continue
        bid = row.poi_bid
        name_ch = row.name_ch
        update_time = row.update_time
        # 根据bid
        if row.poi_bid:
            poi_info = common.get_poi_info_by_bid(row.poi_bid)
            if poi_info:
                sub_aoi = GatherSubAoi(bid, poi_info.name, poi_info.std_tag, relation_type=RELATION_COVER_BID)
                sub_aoi.name_rate = difflib.SequenceMatcher(None, aoi.name, poi_info.name).ratio()
                aoi.gather_sub_aoi.append(sub_aoi)
                handled_face_id.append(face_id)
                continue
        if update_time > datetime.datetime.today() - datetime.timedelta(days=60):
            # 近60天的数据
            sub_aoi = GatherSubAoi(bid, name_ch, '', relation_type=RELATION_COVER_NAME)
            sub_aoi.name_rate = difflib.SequenceMatcher(None, aoi.name, name_ch).ratio()
            handled_face_id.append(face_id)
            aoi.gather_sub_aoi.append(sub_aoi)


def load_covered_high_level_road(aoi: GatherAoi):
    """
    高等级路处理
    1-7级路,数据高等级路
    8级路,form 不是 52 也是高等级路
    :param aoi:
    :return:
    """
    sql = 'with tmp as (select st_geomfromtext(:d1, 4326) as geom)' \
          'select a.link_id, st_astext(a.geom)  as geom, a.kind, a.form' \
          ' from nav_link a , tmp  where st_intersects(a.geom, tmp.geom ) and a.kind <= 8 '
    ret = pgsql.query_all(pgsql.ENGINE_ROAD, sql, {'d1': aoi.geom})
    for item in ret:
        high_level_road = GatherHighLevelRoad(link_id=item.link_id, geom=item.geom, kind=item.kind, form=item.form)
        if '52' in item.form:
            continue
        # 过滤掉高架和隧道
        if '14' in high_level_road.form or '31' in high_level_road.form:
            continue
        aoi.covered_high_road.append(
            high_level_road
        )
    polygon_list = []
    # 将道路buffer10米后组成一个polygon
    for item in aoi.covered_high_road:
        polygon_list.append(shapely.wkt.loads(item.geom).buffer(5 * 1.1 * 1e-5).simplify(1e-5))
    if len(polygon_list) == 1:
        aoi.road_union = polygon_list[0].wkt
    elif len(polygon_list) > 1:
        merged = unary_union(polygon_list)
        aoi.road_union = merged.wkt


def strategy(aoi: GatherAoi):
    """
    策略处理
    :param aoi:
    :return:
    """
    # 判断有没有存在名称相似度≥50%的子区域
    for sub_aoi in aoi.gather_sub_aoi:
        if sub_aoi.name_rate >= 0.5:
            aoi.msg = f"存在名称相似度≥50%的子区域[{sub_aoi.name}]-[{sub_aoi.relation_type}], 设置为聚合"
            aoi.strategy_level = 1
            aoi.can_strategy = '策略解'
            return
    # 如果没有高等级路,直接闭环
    if aoi.road_union == '':
        aoi.msg = '区域内无高等级路, 可设置为基础'
        aoi.strategy_level = 2
        aoi.can_strategy = '策略解'
        return
    diff = shapely.wkt.loads(aoi.geom).difference(shapely.wkt.loads(aoi.road_union))
    if diff.geom_type == 'MultiPolygon':
        aoi.msg = '高等级路横穿aoi区域, 设置为聚合'
        aoi.strategy_level = 1
        aoi.can_strategy = '策略解'
        return

    buffered = shapely.wkt.loads(aoi.geom).buffer(-26 * 1.1 * 1e-5)
    if buffered.intersects(shapely.wkt.loads(aoi.road_union)):
        aoi.msg = '[低置信]高等级道路在聚合院落30m范围内部,设置为聚合'
        aoi.strategy_level = 1
        aoi.can_strategy = '策略解'
        return

    aoi.msg = '无高等级路,无名称相似子区域,设置为基础 '
    aoi.strategy_level = 2
    aoi.can_strategy = '策略解'
    return


def handle(ctx: Ctx):
    """
    任务处理
    :param ctx:
    :return:
    """
    poi_info_dict = common.multi_get_poi_info_by_bid(ctx.bid_list)
    aoi_info_dict = common.multi_get_aoi_by_main_bid(ctx.bid_list)
    for bid in tqdm.tqdm(ctx.bid_list):
        if bid in poi_info_dict and bid in aoi_info_dict:
            poi_info = poi_info_dict[bid]
            aoi_info = aoi_info_dict[bid]
            g = GatherAoi(
                bid=bid,
                face_id=aoi_info.face_id,
                name=poi_info.name,
                std_tag=poi_info.std_tag,
                geom=aoi_info.geom,
                area=aoi_info.area,
            )

            load_covered_high_level_road(g)
            load_gather_sub_aoi(g)
            try:
                strategy(g)
            except Exception as e:
                g.msg = f'执行任务出现错误:{e.args}'

            ctx.gather_list.append(g)


def export(ctx: Ctx):
    """
    导出
    :param ctx:
    :return:
    """
    lines = []
    for aoi in ctx.gather_list:
        tmp = dataclasses.asdict(aoi)
        del tmp['geom']
        del tmp['covered_high_road']
        del tmp['gather_sub_aoi']
        del tmp['road_union']
        lines.append(tmp)
    pd.DataFrame(lines).to_csv(f"聚合院落层级策略处理_{datetime.date.today().strftime('%Y%m%d')}.tsv", sep='\t',
                               index=False)


def run():
    """
    执行
    :return:
    """
    ctx = Ctx()
    fill_bids(ctx)
    handle(ctx)
    export(ctx)


if __name__ == '__main__':
    run()
