# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地上下文
"""
import sys
import os

from pathlib import Path

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")


class Step:
    """
    步骤
    """
    inited = "inited"
    vectored = "VECTORED"
    handled = "HANDLED"


class FilterValue:
    """
    参数过滤值
    """
    filter_area = 0.0000000030  # 面积过滤
    simplify_distance = 0.00001  # 抽稀距离
    merge_distance = 0.0000007  # 合并距离


class Context:
    """
    建筑物后处理上下文
    """
    step: Step
    filter_values: FilterValue
    batch: str  # 批次
    shapely_wkt_list: list
    street_info: dict