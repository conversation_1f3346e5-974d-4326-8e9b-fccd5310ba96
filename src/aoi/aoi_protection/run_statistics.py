"""
统计
"""
import datetime
from pathlib import Path
import os
import sys

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())
from src.door_naming.DaoBeeflow import DaoBeeflow
from src.aoi.aoi_protection import cfg
from src.common import common_tool

today = datetime.datetime.today()
yesterday = datetime.datetime.today() - datetime.timedelta(days=1)
yesterday = yesterday.replace(hour=0, minute=0, second=1)


def run_sql(dao: DaoBeeflow, sql):
    dao.conn_beeflow.ping(reconnect=True)
    dao.cursor_beeflow.execute(sql)
    return dao.cursor_beeflow.fetchone()[0]


def run():
    """
    统计
    :return:
    """
    work_type_map = {
        0: '未处理',
        cfg.WORK_TYPE_STRATEGY: '策略处理',
        cfg.WORK_TYPE_MANUAL: '人工作业',
        cfg.WORK_TYPE_REPEAT: '重复下发拦截',
    }
    handle_status_map = {
        cfg.HANDLE_STATUS_DEFAULT: '未处理',
        cfg.HANDLE_STATUS_STRATEGY: "等待策略处理",
        cfg.HANDLE_STATUS_WAIT_PREVIEW: "等待创建预览计划",
        cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE: "等待人工作业结论",
        cfg.HANDLE_STATUS_SUCCESS: "处理完成",
        cfg.HANDLE_STATUS_FAIL: '处理失败',
    }

    yesterday_str = yesterday.strftime("%Y-%m-%d %H:%M:%S")
    today_str = today.strftime("%Y-%m-%d %H:%M:%S")
    msg = '[poi失效AOI主点核实天级统计]\n' \
          f'统计时间: {yesterday_str} 至 {today_str}\n'
    with DaoBeeflow() as dao:
        sql = f"select count(*)" \
              f" from aoi_protect_information" \
              f" where created_at between '{yesterday_str}' and '{today_str}'"
        near_count_all = run_sql(dao, sql)
        msg += f"poi上报总数: {near_count_all}\n"

        sql = f"select count(*)" \
              f" from aoi_protect_information" \
              f" where created_at between '{yesterday_str}' and '{today_str}' and batch_type={cfg.MANUAL_HIGH}"
        near_count_high = run_sql(dao, sql)
        msg += f"poi上报,高优: {near_count_high}\n"

        sql = f"select count(*)" \
              f" from aoi_protect_information" \
              f" where created_at between '{yesterday_str}' and '{today_str}' and batch_type!={cfg.MANUAL_HIGH}"
        near_count_common = run_sql(dao, sql)
        msg += f"poi上报,常规: {near_count_common}\n"

        msg += "\n任务自动化分类统计\n"
        sql = 'select count(*), work_type, batch_type from aoi_protect_information ' \
              f" where created_at between '{yesterday_str}' and '{today_str}' " \
              f" group by batch_type, work_type"
        dao.cursor_beeflow.execute(sql)

        for (ct, work_type, batch_type) in dao.cursor_beeflow.fetchall():
            batch_type_str = '高优任务' if batch_type == cfg.MANUAL_HIGH else '常规任务'

            msg += f"任务级别: {batch_type_str}, 任务分类: {work_type_map[work_type]}, 任务数量:{ct} \n"

        msg += "\n人工任务处理状态统计:[近两天]\n"
        sql = 'select count(*), handle_status, batch_type from aoi_protect_information' \
              f" where updated_at between '{yesterday_str}' and '{today_str}'" \
              f" and work_type={cfg.WORK_TYPE_MANUAL} group by batch_type, handle_status "
        dao.cursor_beeflow.execute(sql)
        for (ct, handle_status, batch_type) in dao.cursor_beeflow.fetchall():
            batch_type_str = '高优任务' if batch_type == cfg.MANUAL_HIGH else '常规任务'
            msg += f"任务级别: {batch_type_str}, 人工任务状态: {handle_status_map[handle_status]}, 任务数量:{ct} \n"

        msg += "\n人工任务处理状态统计:[总计]\n"
        sql = 'select count(*), handle_status, batch_type from aoi_protect_information' \
              f" where " \
              f" work_type={cfg.WORK_TYPE_MANUAL} group by batch_type, handle_status "
        dao.cursor_beeflow.execute(sql)
        for (ct, handle_status, batch_type) in dao.cursor_beeflow.fetchall():
            batch_type_str = '高优任务' if batch_type == cfg.MANUAL_HIGH else '常规任务'
            msg += f"任务级别: {batch_type_str}, 人工任务状态: {handle_status_map[handle_status]}, 任务数量:{ct} \n"

        msg += "\n下线状态统计\n"
        sql = 'select count(*) from aoi_protect_information ' \
              f" where updated_at between '{yesterday_str}' and '{today_str}' and " \
              ' can_offline =1 '
        can_offline_ct = run_sql(dao, sql)
        msg += f"可下线:{can_offline_ct}\n"
        sql = 'select count(*) from aoi_protect_information ' \
              f" where updated_at between '{yesterday_str}' and '{today_str}' and " \
              ' can_offline =2 '
        cannot_offline_ct = run_sql(dao, sql)
        msg += f'不可下线: {cannot_offline_ct}\n'

        sql = 'select count(*) from aoi_ugc_main_geom_change_info' \
              f" where created_at between '{yesterday_str}' and '{today_str}'  "
        count_ugc_offline = run_sql(dao, sql)
        msg += f"\nugc情报下线数据: {count_ugc_offline}\n"
        print(msg)

        common_tool.send_hi(msg, None, token=cfg.GROUP_TOKEN, group_id=cfg.GROUP_ID)


if __name__ == '__main__':
    run()
