# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
poi_online_rw.py
"""
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class PoiOnlineRW(PgDao):
    """
    用于查询 poi_online_r 数据库中的 blu_face_complete 表等
    """

    def __init__(self, shared_pool=True, pool=None):
        conf = CommTool.get_config("pg").get("poi_online")
        if not conf:
            raise Exception("poi_online 配置不存在")
        self.db_conf = conf

        # 传递配置和连接池给父类，由父类统一管理连接池
        super().__init__(pg_conf=conf, pool=pool, use_shared_pool=shared_pool)

    def insert_park_access_offline(self, data):
        """
        停车场出入口下线
        :param data:
        :return:
        """
        sql = f"""
            insert into park_access_offline(
                bid,road_relation,park_bid,strategy
            ) values(%s,%s,%s,%s)
        """
        return self.execute(sql, [data['bid'], data['road_relation'], data['park_bid'], data['strategy']])

    def update_park_access_offline_status(self, bid, resp):
        """
        停车场出入口下线状态变更
        Args:
            bid:
            resp:

        Returns:

        """
        sql = f"update park_access_offline set status=1, resp=%s where bid=%s"
        return self.execute(sql, [resp, bid])

    def insert_parking_manual_prepush(self, data):
        """
        人工预推送情报
        :param data:
        :return:
        """
        sql = f"""
            insert into parking_manual_prepush(qb_id,model,recall_desc,parent_id,parking_bid,geom,strategy,dt,batch,node_id)
            values(%s,%s,%s,%s,%s,st_geomfromtext(%s,'4326'),%s,%s,%s,%s)
        """
        return self.execute(sql, [data['qb_id'], data['model'], data['recall_desc'], data['parent_id'],
                                  data['parking_bid'], data['geom'], data['strategy'], data['dt'],
                                  data['batch'], data['node_id']])

    def insert_gate_front_intelligence(self, data):
        """
        人工预推送情报门前
        :param data:
        :return:
        """
        sql = f"""
            insert into park_access_intelligence(bid,strategy,type,batch_number,geom,guid,source_id,show_tag,
                outside_id,status,resp,road_relation,jdq_batch, node_id,link_id,outside_task_id)
            values(%s,%s,%s,%s,st_geomfromtext(%s,'4326'),%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) returning id
        """
        return self.execute_and_return_id(sql, [data['bid'], data['strategy'], data['type'], data['batch_number'],
                                                data['geom'],
                                                data['guid'], data['source_id'], data['show_tag'], data['outside_id'],
                                                data['status'],
                                                data['resp'], data['road_relation'], data['jdq_batch'], data['node_id'],
                                                data['link_id'], data['outside_task_id']])

    def insert_mistake_intelligence(self, data):
        """
        人工预推送情报-错误关联
        :param data:
        :return:
        """
        sql = f"""
            insert into park_access_intelligence(bid,strategy,type,batch_number,geom,guid,source_id,show_tag,
                outside_id,status,resp,road_relation,jdq_batch, node_id,link_id,parent_bid,strategy_value,
                park_access_bid,confidence,remark)
            values(%s,%s,%s,%s,st_geomfromtext(%s,'4326'),%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
        """
        return self.execute(sql, [data['bid'], data['strategy'], data['type'], data['batch_number'], data['geom'],
                                  data['guid'], data['source_id'], data['show_tag'], data['outside_id'], data['status'],
                                  data['resp'], data['road_relation'], data['jdq_batch'], data['node_id'],
                                  data['link_id'], data['parent_bid'], data['strategy_value'], data['park_access_bid'],
                                  data['confidence'], data['remark']])
                                
    def update_park_storefront_intelligence_status_by_face_id(self, face_id, status):
        """
        更新停车场情报状态
        :param face_id:
        :param status:
        :return:
        """
        sql = f"update park_storefront_intelligence set status=%s where face_id=%s"
        return self.execute(sql, [status, face_id])

    def update_park_access_intelligence_status_by_id(self, intelligence_ids, status, resp=''):
        """
        停车场出入口下线状态变更
        Args:
            intelligence_ids:
            resp:

        Returns:

        """
        sql = f"update park_access_intelligence set status=%s,resp=%s where id=ANY (%s)"
        return self.execute(sql, [status, resp, intelligence_ids])

    def update_park_access_intelligence_confidence_by_id(self, intelligence_ids, confidence):
        """
        停车场出入口置信度修改
        Args:
            intelligence_ids:
            resp:

        Returns:

        """
        sql = f"update park_access_intelligence set confidence=%s where id=ANY (%s)"
        return self.execute(sql, [confidence, intelligence_ids])

    def update_park_access_intelligence_status_batch_by_id(self, intelligence_ids, status, batch, resp=''):
        """
        停车场出入口下线状态变更
        Args:
            intelligence_ids:
            resp:

        Returns:

        """
        sql = f"update park_access_intelligence set status=%s,jdq_batch=%s,resp=%s where id=ANY (%s)"
        return self.execute(sql, [status, batch, resp, intelligence_ids])

    def update_park_access_intelligence_strategy_status_by_id(self, intelligence_ids, status, strategy, resp=''):
        """
        停车场出入口下线状态变更
        Args:
            intelligence_ids:
            resp:

        Returns:

        """
        sql = f"update park_access_intelligence set status=%s,strategy=%s,resp=%s where id=ANY (%s)"
        return self.execute(sql, [status, strategy, resp, intelligence_ids])

    def update_park_access_intelligence_status_by_source_id(self, source_id, conclusion):
        """
        停车场出入口下线状态变更
        Args:
            source_id:
            conclusion:

        Returns:

        """
        sql = f"update park_access_intelligence set conclusion=%s where source_id=%s"
        return self.execute(sql, [conclusion, source_id])

    def update_effect_raising_create_park_status_by_qb_id(self, face_id, status, remark=''):
        """
        更新停车场情报状态
        :param face_id:
        :param status:
        :return:
        """
        sql = f"update effect_raising_create_park set handle_status=%s,remark=%s where id=%s"
        return self.execute(sql, [status, remark, face_id])

    def update_effect_raising_create_park_project_by_qb_id(self, qb_id, project):
        """
        更新停车场情报状态
        :param face_id:
        :param status:
        :return:
        """
        sql = f"update effect_raising_create_park set project=%s where id=%s"
        return self.execute(sql, [project, qb_id])

    def update_effect_raising_create_park_qb_id_by_id(self, id, qb_id):
        """
        更新停车场情报状态
        :param id:
        :param qb_id: 一体化情报ID
        :return:
        """
        sql = f"update effect_raising_create_park set qb_id=%s where id=%s"
        return self.execute(sql, [qb_id, id])

    def get_gate_front_park_access_intelligence(self, type: str, status=None, face_ids=None, strategy=None):
        """
        获取的停车场情报
        Args:
            type:
            status:
            bids:

        Returns:

        """
        args = [type]
        sql = f"""
            select a.id,a.outside_id,a.bid,a.parent_bid,st_astext(a.geom),a.source_id,a.park_access_bid,a.node_id,a.link_id
            from park_access_intelligence a
                left join park_storefront_intelligence b on a.outside_id = b.face_id
            where a.type = %s
        """
        if status:
            sql += f" and a.status = %s"
            args.append(status)
        if face_ids:
            sql += f" and a.outside_id = ANY(%s)"
            args.append(face_ids)
        if strategy:
            sql += f" and a.strategy = %s"
            args.append(strategy)
        return self.queryall(sql, args)

    def update_park_storefront_intelligence_conclusion(self, ptid, conclusion, intelligence_ids):
        """
        更新停车场情报状态
        :param ptid:
        :param conclusion:
        :param intelligence_id:
        :return:
        """
        sql = f"update park_access_intelligence set pt_id=%s,conclusion=%s where id=ANY(%s)"
        return self.execute(sql, [ptid, conclusion, intelligence_ids])

    def update_park_storefront_strategy_diff_status_by_face_id(self, face_ids, task_ids, status, intelligence_ids=None):
        """
        更新停车场情报状态
        :param face_id:
        :param status:
        :return:
        """
        args = [status, face_ids, task_ids]
        sql = f"update park_storefront_strategy_diff set status=%s where face_id=ANY(%s) and task_id=ANY(%s)"
        if intelligence_ids and len(intelligence_ids) > 0:
            args = [status, intelligence_ids, face_ids, task_ids]
            sql = f"""
                update park_storefront_strategy_diff set status=%s,access_intelligence_ids=%s
                where face_id=ANY(%s) and task_id=ANY(%s)
            """
        return self.execute(sql, args)

    def get_storefront_intelligence(self, status=None, face_ids=None, task_ids=None, strategy_ids=None):
        """
        获取差分完成之后待挖掘出入口的门前面
        Args:
            status:
            face_ids:
            task_ids:

        Returns:

        """
        args = []
        sql = """
            select a.id,a.face_id,st_astext(b.geom) as geom,a.result,a.bid,a.task_id,a.access_intelligence_ids 
            from park_storefront_strategy_diff a
                left join park_storefront_strategy b on a.strategy_id = b.id
            where 1=1 
        """
        if status:
            sql += " and a.status = ANY(%s)"
            args.append(status)
        if face_ids:
            sql += " and a.face_id = ANY(%s)"
            args.append(face_ids)
        if task_ids:
            sql += " and a.task_id = ANY(%s)"
            args.append(task_ids)
        if strategy_ids:
            sql += " and a.strategy_id = ANY(%s)"
            args.append(strategy_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def update_park_storefront_task_status_by_task_id(self, task_ids, status, original_status):
        """
        更新停车场情报状态
        :param task_ids:
        :param status:
        :param original_status:
        :return:
        """
        sql = f"update park_storefront_task set status=%s where task_id=ANY(%s) and status = %s"
        return self.execute(sql, [status, task_ids, original_status])

    def insert_park_storefront_task(self, data):
        """
        插入停车场任务
        Args:
            data:

        Returns:

        """
        sql = f"""
            insert into park_storefront_task(region_id, geom, batch, status) 
            values(%s,st_geomfromtext(%s,'4326'),%s,%s) returning task_id
        """
        return self.execute_and_return_id(sql, [data['region_id'], data['geom'], data['batch'], data['status']])

    def insert_park_storefront_strategy_diff(self, data):
        """
        插入差分成果表
        Args:
            data:

        Returns:

        """
        sql = """
            insert into park_storefront_strategy_diff(
                task_id, face_id,status,bid,access_ids,strategy_id,access_intelligence_ids
            )
            values(%s,%s,%s,%s,%s,%s,%s)
        """
        self.execute(sql, [data['task_id'], data['face_id'], data['status'], data['bid'], data['access_ids'],
                           data['strategy'], data['access_intelligence_ids']])

    def update_park_storefront_prod_parking(self, from_status, to_status, park_id):
        """
        更新停车场情报状态
        :param from_status:
        :param to_status:
        :param park_id:
        :return:
        """
        sql = f"""
            update park_storefront_prod_parking set status=%s 
            where bid_status='effected' and id = %s and status = %s
        """
        return self.execute(sql, [to_status, park_id, from_status])

    def update_park_storefront_prod_parking_by_bid(self, status, park_bid, rollback_reason=None):
        """
        更新停车场情报状态
        :param from_status:
        :param to_status:
        :param park_id:
        :return:
        """
        args = [status, park_bid]
        sql = f"update park_storefront_prod_parking set status=%s where bid_status='effected' and bid = %s"
        if rollback_reason:
            sql = f"""
                update park_storefront_prod_parking set status=%s,rollback_reason=%s 
                where bid_status='effected' and bid = %s
            """
            args = [status, rollback_reason, park_bid]
        return self.execute(sql, args)

    def update_park_storefront_prod_parking_by_bid_and_status(self, from_status, to_status, park_bids):
        """
        更新停车场情报状态
        :param from_status:
        :param to_status:
        :param park_bids:
        :return:
        """
        sql = f"""
            update park_storefront_prod_parking set status=%s 
            where bid_status='effected' and status = %s and bid = ANY(%s)
        """
        return self.execute(sql, [to_status, from_status, park_bids])

    def update_repaired_user_and_status(self, repaired_ids, user_id, user_name, status):
        """
        更新修复信息
        """
        sql = f"""
            update park_storefront_repair 
            set status=%s, user_id=%s, user_name=%s 
            where id=ANY(%s) 
        """
        return self.execute(sql, [status, user_id, user_name, repaired_ids])

    def insert_park_storefront_prod_access(self, data):
        """
        插入出入口成果表
        Args:
            data:

        Returns:

        """
        sql = """
            insert into park_storefront_prod_access(
                source_id, parent_bid,name,address,road_relation,geom,remark,face_id,status,scene,suggest_is_open,
                park_id, pic_urls 
            )
            values(%s,%s,%s,%s,%s,st_geomfromtext(%s,'4326'),%s,%s,%s,%s,%s,%s,%s)
        """
        self.execute(sql, [data['source_id'], data['parent_bid'], data['name'], data['address'], data['road_relation'],
                           data['geom'], data['remark'], data['face_id'], data['status'], data['scene'],
                           data['suggest_is_open'], data['park_id'], data['pic_urls']])

    def update_park_storefront_prod_access(self, access_id: int, data: dict):
        """
        更新出入口成果
        """
        keys = [
            'source_id', 'parent_bid', 'name', 'address', 'road_relation',
            'remark', 'face_id', 'status', 'scene', 'suggest_is_open', 'park_id', 'pic_urls',
        ]
        set_vals = []
        qry_vals = []
        for a_key in keys:
            if a_key not in data:
                continue
            set_vals.append(f"{a_key} = %s")
            qry_vals.append(data[a_key])
        if 'geom' in data:
            set_vals.append("geom = st_geomfromtext(%s,'4326')")
            qry_vals.append(data['geom'])
        qry = f"""
        update park_storefront_prod_access 
        set {','.join(set_vals)} 
        where id = %s
        """
        self.execute(qry, qry_vals + [access_id])

    def update_parking_turing_result(self, status, turning_id):
        """
        更新停车场情报状态
        Args:
            status:
            turning_id:

        Returns:

        """
        sql = f"update parking_turing_result set status=%s where turing_id = %s"
        return self.execute(sql, [status, turning_id])

    def update_park_access_intelligence_outside_id_by_id(self, intelligence_ids, outside_id, remark=''):
        """
        停车场停保推送更
        Args:
            intelligence_ids:
            resp:

        Returns:

        """
        sql = f"update park_access_intelligence set outside_id=%s,status='PUSHED',remark=%s where id=ANY (%s)"
        return self.execute(sql, [outside_id, remark, intelligence_ids])

    def update_park_storefront_prod_parking_park_id(self, source_ids):
        """
        更新停车场情报状态
        :param source_id:
        :return:
        """
        sql = f"""
            update park_storefront_prod_access set park_id=outside_task_id from park_access_intelligence 
            where park_storefront_prod_access.source_id=park_access_intelligence.source_id 
            and park_access_intelligence.source_id=ANY(%s)
        """
        return self.execute(sql, [source_ids])

    def update_park_storefront_repair_status(self, turing_id: str, status: str):
        """
        更新修复结果信息
        """
        qry = f"""
        update park_storefront_repair set status = %s where user_id = %s
        """
        return self.execute(qry, [status, turing_id])

    def insert_traj_navigate(self, data):
        """
        导航轨迹成果
        Args:
            data:

        Returns:

        """
        sql = """
            INSERT INTO traj_navigate (
                long_link_id, short_link_id, geom, traj_id, gps_time, dir, weight, prediction, cuid, ori_traj_id, 
                confidence, encode_cuid
            ) VALUES (
                %s, %s, ST_GeomFromText(%s, 4326), %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) on conflict(traj_id) do nothing;
        """
        self.execute(sql, [data['long_link_id'], data['short_link_id'], data['geom'], data['traj_id'], data['gps_time'],
                           data['dir'], data['weight'], data['prediction'], data['cuid'], data['ori_traj_id'],
                           data['confidence'], data['encode_cuid']])

    def insert_traj_navigate_list(self, data_list):
        """
        导航轨迹成果
        Args:
            data_list:

        Returns:

        """
        sql = """
            INSERT INTO navigate_traj (
                long_link_id, short_link_id, geom, traj_id, gps_time, dir, weight, prediction, cuid, ori_traj_id, 
                confidence, encode_cuid,bid
            ) VALUES (
                %s, %s, ST_GeomFromText(%s, 4326), %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        self.batch_insert(sql, data_list)

    def update_park_access_intelligence_strategy_value_by_id(self, intelligence_ids, strategy_value, remark=''):
        """
        停车场停保推送更a
        Args:
            intelligence_ids:
            strategy_value:
            remark:

        Returns:

        """
        args = [strategy_value, remark, intelligence_ids]
        sql = f"update park_access_intelligence set strategy_value=%s,remark=%s where id=ANY (%s)"
        if not remark:
            args = [strategy_value, intelligence_ids]
            sql = f"update park_access_intelligence set strategy_value=%s where id=ANY (%s)"
        return self.execute(sql, args)

    def update_park_access_add_task_status(self, status='', msg='', task_id=None):
        """
        修改推送状态
        """
        if not task_id:
            return False
        sql = f"update park_access_add_task set status=%s, msg=%s where task_id=%s"
        return self.execute(sql, [status, msg, task_id])

    def update_park_access_add_task(self, task_id, data):
        """
        字典更新属性
        """
        if not task_id:
            return False
        keys = [
            'source_id', 'park_bid', 'status', 'access_name', 'node_id', 'long_node_id', 'long_link_id'
                                                                                         'link_id', 'access_show_tag',
            'priority', 'msg', 'type', 'update_time'
        ]
        set_vals = []
        qry_vals = []
        for a_key in keys:
            if a_key not in data:
                continue
            set_vals.append(f"{a_key} = %s")
            qry_vals.append(data[a_key])
        if 'gcj_geom' in data:
            set_vals.append("gcj_geom = st_geomfromtext(%s,'4326')")
            qry_vals.append(data['gcj_geom'])
        qry = f"""
            update park_access_add_task 
                set {','.join(set_vals)} 
            where task_id = %s
        """
        self.execute(qry, qry_vals + [task_id])

    def update_park_access_intelligence_filter_reason(self, intelligence_ids, filter_reason, status=None):
        """
        停车场停保推送更a
        Args:
            intelligence_ids:
            filter_reason:

        Returns:

        """
        args = [filter_reason, intelligence_ids]
        sql = f"update park_access_intelligence set filter_reason=%s where id=ANY (%s)"
        if status:
            sql = f"update park_access_intelligence set filter_reason=%s,status=%s where id=ANY (%s)"
            args = [filter_reason, status, intelligence_ids]
        return self.execute(sql, args)

    def update_park_access_traj_flux_by_id(self, traj_flux, traj_flux_strategy, intelligence_ids):
        """
        停车场出入口下线状态变更
        Args:
            traj_flux:
            traj_flux_strategy:

        Returns:

        """
        sql = f"update park_access_intelligence set traj_flux=%s,traj_flux_value=%s where id=ANY (%s)"
        return self.execute(sql, [traj_flux, traj_flux_strategy, intelligence_ids])

    def insert_hd_traj_pass_result(self, data):
        """
        导航轨迹成果
        Args:
            data_list:

        Returns:

        """
        sql = """
            INSERT INTO hd_traj_pass_parking (
                tesla_pass_parking_num, byd_pass_parking_num, tesla_pass_park_access_num, byd_pass_park_access_num,bid
            ) VALUES (%s, %s, %s, %s, %s) on conflict(bid) do update set 
            tesla_pass_parking_num=excluded.tesla_pass_parking_num, byd_pass_parking_num=excluded.byd_pass_parking_num,
            tesla_pass_park_access_num=excluded.tesla_pass_park_access_num, 
            byd_pass_park_access_num=excluded.byd_pass_park_access_num
        """
        self.execute(sql, [data['tesla_pass_parking_num'], data['byd_pass_parking_num'],
                           data['tesla_pass_park_access_num'], data['byd_pass_park_access_num'], data['bid']])

    def update_park_access_light_check(self, data, condition):
        """
        同步掘金结果到数据库
        :param data: 要更新的键值对字典，如 {'status': 1, 'result': 'success'}
        :param condition: 查询条件的键值对，如 {'id': 123, 'task_type': 'juejin'}
        :return: 更新的记录数
        """
        if not data or not condition:
            raise ValueError("data和condition不能为空")

        # 构建SET子句
        set_clause = ", ".join([f"{k} = %s" for k in data.keys()])
        
        # 构建WHERE子句
        where_clause = " AND ".join([f"{k} = %s" for k in condition.keys()])
        
        # 合并参数值（注意顺序：先data值，后condition值）
        params = list(data.values()) + list(condition.values())
        
        # 执行SQL
        sql = f"UPDATE park_access_light_check SET {set_clause} WHERE {where_clause}"
        return self.execute(sql, params)
    
    def update_light_check_status_by_id(self,
            light_check_id_list=None, 
            status=None,
            push_time=None,
            update_time=None,
            batch=None):
        """
        更新轻核实状态
        """
        if not light_check_id_list:
            return False
        sql = f"update park_access_light_check set status=%s,push_time=%s,update_time=%s,batch=%s where id=ANY (%s)"
        return self.execute(sql, [status, push_time, update_time, batch, light_check_id_list])

    def insert_light_check_list(self, data_dict):
        """
        导航轨迹成果
        Args:
            data_list:

        Returns:

        """
        # data_list 是 dict 键是要插入的键，值是要插入的值，动态生成sql，键的数量不固定 也是动态的
        columns = ', '.join(data_dict.keys())
        placeholders = ', '.join([f"%({key})s" for key in data_dict.keys()])
        sql = f"INSERT INTO park_access_light_check ({columns}) VALUES ({placeholders})"
        # print(f"执行的SQL: {sql}")
        # print(f"参数: {data_dict}")
        return self.execute(sql, data_dict)

    def update_park_access_intelligence(self, data, condition):
        """
        更新出入口情报
        :param data: 要更新的键值对字典，如 {'status': 1, 'result': 'success'}
        :param condition: 查询条件的键值对，如 {'id': 123, 'task_type': 'juejin'}
        :return: 更新的记录数
        """
        if not data or not condition:
            raise ValueError("data和condition不能为空")

        # 构建SET子句
        set_clause = ", ".join([f"{k} = %s" for k in data.keys()])
        
        # 构建WHERE子句
        where_clause = " AND ".join([f"{k} = %s" for k in condition.keys()])
        
        # 合并参数值（注意顺序：先data值，后condition值）
        params = list(data.values()) + list(condition.values())
        
        # 执行SQL
        sql = f"UPDATE park_access_intelligence SET {set_clause} WHERE {where_clause}"
        return self.execute(sql, params)