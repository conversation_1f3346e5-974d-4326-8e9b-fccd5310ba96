# !/usr/bin/env python3
"""
数据库模块
"""

import os
import configparser

import requests
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.engine import URL


config = configparser.ConfigParser()
work_dir = os.environ.get("WORK_DIR")
config.read(os.path.join(work_dir or os.getcwd(), "conf/config.ini"))


def get_road_db_info(master_road):
    """
    获取路网数据库信息
    :param master_road:
    :return:
    """
    data = {"pathname": config[master_road]['road_pathname'], "action": "query", "type": "dbinfo"}
    resp = requests.post(url=config[master_road]['pg_naming_url'], json=data).json()['data']
    return dict(
        database=resp["db"],
        user=resp["user"],
        password=resp["passwd"],
        host=resp["host"],
        port=resp["port"]
    )


def db_init(config_name, drivername="postgresql", auto_commit=False):
    """
    Initialize database connection.
    Args:
        config_name:
        drivername: default postgresql
    Returns:

    """
    db_conf = config[config_name]
    if config_name == "master_road":
        db_conf = get_road_db_info(config_name)

    db_url = URL.create(
        drivername=drivername,
        host=db_conf['host'],
        username=db_conf['user'],
        password=db_conf['password'],
        database=db_conf['database'],
        port=db_conf['port'],
    )
    if auto_commit:
        _db_engine = create_engine(db_url, echo=False, isolation_level="AUTOCOMMIT")
        _db_session = scoped_session(sessionmaker(bind=_db_engine, autocommit=True))
    else:
        _db_engine = create_engine(db_url, echo=False)
        _db_session = scoped_session(sessionmaker(bind=_db_engine))
    return _db_engine, _db_session
