"""
无门的主点数据挖掘
"""
import dataclasses
import os

import shapely.wkt
import tqdm
from typing import Optional
import pandas as pd
import numpy as np
import traceback
from src.common import pgsql, dest_traj_tools
from src.door_naming.strategy_v2 import common
from pathlib import Path
import datetime

now_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
today = datetime.datetime.today().strftime("%Y%m%d")
work_dir = Path(os.path.abspath(__file__)).parent / 'door_traj_found' / today
work_dir.mkdir(exist_ok=True, parents=True)


@dataclasses.dataclass
class Nodes:
    """
    node_id
    """
    geom: str
    gather_pv: str
    near_10_door_node_id: str


@dataclasses.dataclass
class MainInfo:
    """
    主点信息
    """
    main_bid: str
    std_tag: str
    name: str
    pv: str
    src: str
    area: float
    aoi_geom: str
    aoi_complete: int
    nodes: list[Nodes] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class Ctx:
    """
    ctx
    """
    bid_list: list[str] = dataclasses.field(default_factory=list)
    bid_list_file: str = ''
    result_path = work_dir / f'no_gate_main_{now_str}.tsv'
    bid_no_door: list[str] = dataclasses.field(default_factory=list)
    lists: list[MainInfo] = dataclasses.field(default_factory=list)


def append_to_csv(ctx: Ctx, main: Optional[MainInfo], add_header=False):
    """
    :param ctx:
    :param main:
    :param add_header:
    :return:
    """
    file_name = ctx.result_path
    if add_header:
        if os.path.exists(file_name):
            os.remove(file_name)
        with open(file_name, 'a+', encoding='utf-8') as f:
            f.write("main_bid\tname\tstd_tag\tpv\tsrc\taoi_complete\taoi_area\t"
                    "geom\tgather_pv\tnear_10_door_node_id\n")
            return
    if len(main.nodes) < 1:
        return
    with open(file_name, 'a+', encoding='utf-8') as f:
        if main.aoi_complete >= 3:
            for main_node in main.nodes:
                if main_node.near_10_door_node_id != '':
                    f.write(
                        f"{main.main_bid}\t{main.name}\t{main.std_tag}\t{main.pv}\t"
                        f"{main.src}\t{main.aoi_complete}\t{main.area}\t{main_node.geom}"
                        f"\t{main_node.gather_pv}\t{main_node.near_10_door_node_id}\n")
                else:
                    if int(main_node.gather_pv) > 30:
                        f.write(
                            f"{main.main_bid}\t{main.name}\t{main.std_tag}\t{main.pv}\t"
                            f"{main.src}\t{main.aoi_complete}\t{main.area}\t{main_node.geom}"
                            f"\t{main_node.gather_pv}\t{main_node.near_10_door_node_id}\n")
        else:
            for main_node in main.nodes:
                f.write(
                    f"{main.main_bid}\t{main.name}\t{main.std_tag}\t{main.pv}\t"
                    f"{main.src}\t{main.aoi_complete}\t{main.area}\t{main_node.geom}"
                    f"\t{main_node.gather_pv}\t{main_node.near_10_door_node_id}\n")


def node_near_10(main: MainInfo, node: Nodes):
    """
    如果node在框内,按照30米算,不在框内,按照10米算
    :param main:
    :param node:
    :return:
    """
    sql = 'with tmp as (select st_geomfromtext(:geom_buffer, 4326) as node_buffer)' \
          'select a.node_id, st_astext(b.geom) as geom' \
          ' from nav_gate a inner join nav_node b on a.node_id=b.node_id,tmp ' \
          'where ST_Intersects(tmp.node_buffer, b.geom);'
    # node 在挖掘范围30米内
    geom_buffer = shapely.wkt.loads(node.geom).buffer(30 * 1e-5)
    ret = pgsql.query_all(pgsql.ENGINE_ROAD, sql, {"geom_buffer": geom_buffer.wkt})
    if ret:
        for node_id, node_geom in ret:
            # 取node 在边框15米范围内的数据, 在边框内设置30米,边框外15米(大概就是不跨路)
            dis = shapely.wkt.loads(main.aoi_geom).distance(shapely.wkt.loads(node_geom)) * 1.1e5
            if dis < 15:
                node.near_10_door_node_id = node_id
                return


def find_by_dest_traj(main: MainInfo):
    """
    加载轨迹数据
    :param main:
    :return:
    """
    trajs = dest_traj_tools.get_dest_traj(main.main_bid, main.aoi_geom, dest_traj_tools.TRACK_TYPE_DIFF)
    # for x in trajs:
    #     print(x.wkt)
    _, cluster_result = dest_traj_tools.get_cluster_by_cross_aoi(trajs, main.aoi_geom, eps=20, min_samples=3)
    if len(cluster_result) > 5:
        cluster_result = cluster_result[:5]
    for item in cluster_result:
        pv = item['ct']
        if pv < 8:
            continue
        n = Nodes(item['centroid'], pv, '')
        node_near_10(main, n)
        main.nodes.append(n)


def bool_has_door_bound(main: MainInfo):
    """
    检查是否已经有大门绑定了
    :param main:
    :return:
    """
    sql = 'select a.access_id from blu_access a inner join blu_access_gate_rel b' \
          ' on a.access_id=b.access_id where a.main_bid=:main_bid'
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"main_bid": main.main_bid})
    # 一个aoi绑定了两个或者以上的门,暂时不处理
    if len(ret) > 2:
        return True
    return False


def run(ctx: Ctx):
    """
    主入口, 挖掘缺门场景
    支持传入bid_list, bid_file 或者直接传入数据
    :param ctx:
    :return:
    """
    if len(ctx.bid_list) > 0:
        sql = "select main_bid, aoi_complete from blu_face_complete " \
              " where main_bid in ({}) ".format("'" + "','".join(ctx.bid_list) + "'")
        df = pd.read_sql(sql, pgsql.get_engine(pgsql.ENGINE_MASTERBACK_PROXY))
    elif ctx.bid_list_file != '':
        with open(ctx.bid_list_file) as f:
            bid_list = [x.strip() for x in f]
        sql = "select main_bid, aoi_complete from blu_face_complete " \
              " where main_bid in ({}) ".format("'" + "','".join(bid_list) + "'")
        df = pd.read_sql(sql, pgsql.get_engine(pgsql.ENGINE_MASTERBACK_PROXY))
    else:
        sql = 'select main_bid, aoi_complete from blu_face_complete where aoi_complete < 2 '
        df = pd.read_sql(sql, pgsql.get_engine(pgsql.ENGINE_MASTERBACK_PROXY))
    groups = np.array_split(df, len(df) // 200 + 1)
    # 添加表头
    append_to_csv(ctx, None, True)
    for group in tqdm.tqdm(groups):
        try:
            for _, row in tqdm.tqdm(group.iterrows(), total=len(group), leave=False):
                bid = row['main_bid']
                aoi_complete = row['aoi_complete']
                poi_info = common.get_poi_info_by_bid(bid)
                aoi_info = common.get_aoi_by_main_bid(bid)
                if not poi_info or not aoi_info:
                    continue
                aoi_level = aoi_info.aoi_level
                if aoi_level != 2:
                    continue
                std_tag = poi_info.std_tag
                pv = poi_info.click_pv
                aoi_geom = aoi_info.geom
                src = aoi_info.src
                if src == 'SD':
                    continue
                if '景点' in std_tag or '购物中心' in std_tag \
                        or '写字楼' in std_tag or '交通' in std_tag or '体育场馆' in std_tag or '剧院' in std_tag:
                    continue
                if aoi_info.area < 5000:
                    continue
                main = MainInfo(main_bid=bid,
                                std_tag=std_tag,
                                pv=pv,
                                aoi_geom=aoi_geom,
                                src=src,
                                aoi_complete=aoi_complete,
                                name=poi_info.name,
                                area=aoi_info.area,
                                )

                if bool_has_door_bound(main):
                    # 已经绑定很多门,就不挖掘了
                    continue
                # 根据轨迹数据挖掘
                find_by_dest_traj(main)
                if len(main.nodes) < 1:
                    ctx.bid_no_door.append(main.main_bid)
                # 结果实时写入数据库
                append_to_csv(ctx, main)
        except Exception as e:
            print(f"存在错误了, {e.args}, 跳过", traceback.format_exc())


if __name__ == '__main__':
    _ctx = Ctx()
    # _ctx.bid_list = ['12127263162521002473']
    _ctx.bid_list_file = 'bj_bid_list.txt'
    run(_ctx)
