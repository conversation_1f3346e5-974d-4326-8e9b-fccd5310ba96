"""
计算任务
"""
import os
import sys
from pathlib import Path

root_dir = Path(os.path.abspath(__file__)).parents[3]

if root_dir.as_posix() not in sys.path:
    sys.path.insert(0, root_dir.as_posix())

from src.aoi.query_new.PgQueryNew import PgQueryNew
from src.aoi.query_new.PgUpdateNew import PgUpdateNew
from typing import Optional
from src.aoi.BFQuery import BFQuery


class AoiCompleteContext:
    """
    计算任务上下文
    """

    def __init__(self, init=True):
        self.pg_query_dao: Optional[PgQueryNew] = None
        self.pg_rw_dao: Optional[PgUpdateNew] = None
        self.mysql_bf: Optional[BFQuery] = None
        if init:
            self.connect()
        self.LOG_CONTENT = {}

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def connect(self):
        """
        数据库连接
        :return:
        """
        self.pg_query_dao = PgQueryNew(init_track=False)
        self.pg_rw_dao = PgUpdateNew()
        self.mysql_bf = BFQuery()

    def reconnect(self):
        """
        数据库重连
        mysql不需要重连
        :return:
        """
        self.close()
        self.connect()

    def close(self):
        """
        关闭数据库连接,忽略错误
        :return:
        """
        try:
            self.pg_query_dao.clean()
            self.pg_rw_dao.clean()
            self.pg_rw_dao = None
            self.pg_query_dao = None
        except Exception as e:
            self.pg_rw_dao = None
            self.pg_query_dao = None
            print("关闭数据库异常", e)
