# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地后处理策略
"""
import glob
import sys
import os
import json

from osgeo import ogr, gdal, gdalconst
import shapely.ops
from shapely.validation import make_valid
import psycopg2
import shapely
from shapely import *
from shapely import wkt, geometry
import numpy as np
from shapely.strtree import STRtree

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from common import pipeline
from aoi.basic_feature_ml import context
from aoi.basic_feature_ml import heatmap
from aoi.basic_feature_ml import vectorize
from aoi.basic_feature_ml import utils
from aoi import PgQuery
from aoi import PgUpdate
from aoi import BFQuery
from aoi import CommTool
from aoi.dao.poi_online_query import PoiOnlineQuery
import smooth_wkt

poi_online_query = PgQuery.PgQuery()
poi_online_rw = PgUpdate.PgUpdate()
bf_query = BFQuery.BFQuery()
poi_online_dao = PoiOnlineQuery()


def pipes():
    """
    导出所有 pipe 并组合为一个 pipeline。
    """
    return pipeline.Pipeline(
        init_discern_result,
        headmap_switch_binary_image,
        binary_image_vectorize,
        pick_up_wkt_from_geojson,
        init_discern_wkt,
        merge_discern_wkt_by_rtree,
        deal_merge_gap,
        diff_bud_green_wkt,
        deal_green_hole_wkt,
        corrode_bud_wkt,
        diff_ld_road_green_wkt,
        diff_no_ld_road_green_wkt,
        diff_inner_road_green_wkt,
        pick_up_wkt_from_aoi_and_road,
        filter_min_angle_point,
        fix_gun_shapely,
        smooth_shapely_wkt,
        last_deal_result_save,
    )


def init_discern_result(ctx: context.Context, proceed):
    """
    初始化识别成果
    :param ctx:
    :param proceed:
    :return:
    """
    # 拉取识别成果
    download_cmd = f"{ctx.afs_shell_cmd} get {ctx.afs_discern_result_filepath} {ctx.path_info.headmap_dir}"
    ret = os.system(download_cmd)
    if ret != 0:
        raise ValueError(f'影像图下载失败: {download_cmd}')
    # 解压到成果目录
    unzip_cmd = f"unzip -o -q -d {ctx.path_info.discern_result_dir} {ctx.local_discern_result_filepath}"
    ret = os.system(unzip_cmd)
    if ret != 0:
        raise ValueError(f'解压到成果目录失败: {unzip_cmd}')
    print(f"识别成果拉取完成")
    proceed()


def headmap_switch_binary_image(ctx: context.Context, proceed):
    """
    热力图转换二值图
    :param ctx:
    :param proceed:
    :return:
    """
    headmap_dir = ctx.path_info.discern_result_dir
    binary_dir = ctx.path_info.binary_image_dir
    headmap_list = glob.glob("{}/*.jpg".format(headmap_dir))
    for key, headmap in enumerate(headmap_list):
        binary_img_filename = headmap.rsplit("/", 1)[1].replace("jpg", "png")
        heatmap.switch_binary_image(headmap, str(binary_dir / binary_img_filename))
    print(f"{headmap_dir}热力图转换完成：{len(headmap_list)}")
    proceed()


def binary_image_vectorize(ctx: context.Context, proceed):
    """
    矢量化
    :param ctx:
    :param proceed:
    :return:
    """
    # 开始矢量化
    binary_image_dir = ctx.path_info.binary_image_dir
    binary_image_list = glob.glob("{}/*.png".format(binary_image_dir))
    batch_number = str(ctx.batch)
    vectorize_result_dir = str(ctx.path_info.vectorize_result_dir)
    for binary_image_filename in binary_image_list:
        print(f"矢量化：{binary_image_filename}")
        # 获取识别图原始信息
        image_file_name = binary_image_filename.rsplit("/", 1)[1].replace("png", "jpg")
        try:
            origin_image_info = poi_online_dao.get_image_geom(batch_number, image_file_name)
            if not origin_image_info:
                continue
        except Exception as e:
            print(e)
            continue
            # raise ValueError(f'矢量化图片不存在: {binary_image_filename}')
        vector_file = vectorize.binary_img_switch_geojson(binary_image_filename, origin_image_info[1],
                                                          vectorize_result_dir)
        if vector_file is None:
            print("矢量化失败：{}".format(binary_image_filename))
            break
        # 矢量化文件入库
        poi_online_rw.update_image_vector_file_by_id(binary_image_filename.rsplit('/', 1)[1].replace("png", "json"),
                                                     origin_image_info[0])
    print(f"矢量化完成")
    proceed()


def pick_up_wkt_from_geojson(ctx: context.Context, proceed):
    """
    wkt 提取
    :param ctx:
    :param proceed:
    :return:
    """
    id = 0
    while 1:
        discern_info_list = poi_online_dao.get_geojson_list_by_id(id, ctx.batch, 1000)
        if discern_info_list is None or len(discern_info_list) == 0:
            print("识别框查询完成")
            break
        for discern_info in discern_info_list:
            print(f"提取WKT：ID：{discern_info}")
            id = discern_info[0]
            geojson_file = str(ctx.path_info.vectorize_result_dir / discern_info[1])
            image_wkt = None
            with open(geojson_file) as geojson1:
                geojson = json.load(geojson1)
                for values in geojson["features"]:
                    if values["properties"]["DN"] == 255:
                        wkt_shape = shapely.geometry.shape(values['geometry'])
                        shapely_wkt = make_valid(wkt_shape.simplify(ctx.simplify_distance, preserve_topology=True))
                        if shapely_wkt.area < ctx.filter_min_area:
                            continue
                        if image_wkt is None:
                            image_wkt = shapely_wkt
                        else:
                            image_wkt = image_wkt.union(shapely_wkt)
                if image_wkt is not None:
                    poi_online_rw.update_image_vector_result_by_id(image_wkt.wkt, id)
    proceed()


def init_discern_wkt(ctx: context.Context, proceed):
    """
    初始化识别范围框
    :param ctx:
    :param proceed:
    :return:
    """
    discern_wkt_list = []
    discern_id_list = []
    discern_result_list = poi_online_dao.get_discern_wkt_list(ctx.batch)
    for discern_result in discern_result_list:
        split_discern_wkt_list = utils.split_multi_polygon([wkt.loads(discern_result[1])])
        for split_discern_wkt in split_discern_wkt_list:
            discern_wkt_list.append(split_discern_wkt)
            discern_id_list.append(discern_result[0])
    ctx.shapely_wkt_list = discern_wkt_list
    ctx.discern_id_list = discern_id_list
    print("获取识别面成果：{}".format(len(discern_wkt_list)))
    proceed()


def merge_discern_wkt_by_rtree(ctx: context.Context, proceed):
    """
    合并绿地识别框，间距小于5米的绿地面合并，去除间隙
    :param ctx: 识别矢量后的框
    :return: 合并后的shapely list
    """
    discern_wkt_list = ctx.shapely_wkt_list
    discern_id_list = ctx.discern_id_list
    merge_result_wkt_list = []
    tree = STRtree(discern_wkt_list)
    index_by_id = dict((id(pt), i) for i, pt in enumerate(discern_wkt_list))

    already_cal_index = []
    for key, discern_wkt in enumerate(discern_wkt_list):
        print("开始合并，识别框总量：{}, 当前进度{}, 成果量：{}".format(len(discern_wkt_list), key, len(merge_result_wkt_list)))
        index_id = index_by_id[id(discern_wkt)]
        if index_id in already_cal_index:
            continue
        already_cal_index.append(index_id)
        merge_wkt = discern_wkt
        merge_wkt_id = [str(discern_id_list[key])]
        is_continue = False
        while True:
            merge_wkt_buffer = merge_wkt.buffer(ctx.merge_distance)
            for wkt_index_id in tree.query(merge_wkt_buffer, "intersects"):
                if wkt_index_id in already_cal_index:
                    continue
                merge_wkt_id.append(str(discern_id_list[wkt_index_id]))
                is_continue = True
                already_cal_index.append(wkt_index_id)
                merge_wkt = utils.merge_polygon(merge_wkt, discern_wkt_list[wkt_index_id])
            if not is_continue:
                break
            is_continue = False
        merge_result_wkt_list.append(merge_wkt)
        # 合并结果入库
        record = {}
        record["geom"] = merge_wkt.wkt
        record["discern_id"] = ",".join(list(set(merge_wkt_id)))
        record["batch"] = ctx.batch
        poi_online_rw.insert_green_merge_result(record)
    ctx.shapely_wkt_list = merge_result_wkt_list
    proceed()


def deal_merge_gap(ctx: context.Context, proceed):
    """
    消除合并间隙
    :param wkt_shapely_list:
    :return:
    """
    wkt_shapely_list = ctx.shapely_wkt_list
    deal_hole_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("消除合并间隙：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        deal_hole_result.append(wkt_shapely.buffer(0.000015).simplify(0.00002).buffer(-0.000015))
    ctx.shapely_wkt_list = deal_hole_result
    proceed()


def diff_bud_green_wkt(ctx: context.Context, proceed):
    """
    擦除识别面 建筑面
    :param wkt_list: 合并后的绿地面
    :return:
    """
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    diff_bud_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "BUD_DIFF"}
        print("建筑面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 查询相交建筑面
        bud_face_list = poi_online_query.get_building_by_geom(wkt_shapely.buffer(0.00050).wkt)
        if bud_face_list is None or len(bud_face_list) == 0:
            diff_bud_result.append(wkt_shapely)
            poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
            continue
        struct_id = []
        for bud_face in bud_face_list:
            if not wkt_shapely.overlaps(wkt.loads(bud_face[1])):
                continue
            struct_id.append(bud_face[0])
            wkt_shapely = utils.check_valid_shapely(wkt_shapely.difference(wkt.loads(bud_face[1])))
        diff_bud_result.append(wkt_shapely)

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(struct_id)
        deal_progress_record["geom"] = wkt_shapely.wkt
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    ctx.shapely_wkt_list = diff_bud_result
    proceed()


def deal_green_hole_wkt(ctx: context.Context, proceed):
    """
    空洞处理
    :param wkt_shapely:
    :return:
    """
    # 空洞过滤面积
    filter_hole_area = 0.0000000250
    ctx.shapely_wkt_list = utils.deal_green_hole_wkt(ctx.shapely_wkt_list, filter_hole_area)
    proceed()


def corrode_bud_wkt(ctx: context.Context, proceed):
    """
    建筑物腐蚀处理
    :param wkt_shapely_list:
    :return:
    """
    bud_buffer_distance = 0.0001
    corrode_simplify_distance = 0.0001
    buffer_to_origin_area_rate = 0.35
    protected_distance = 0.00007
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    deal_hole_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        corrode_buffer_distance = 0.0001
        print("建筑物腐蚀：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 校验识别框附近是否存在建筑物
        bud_buffer_list = poi_online_query.get_building_by_geom(wkt_shapely.buffer(bud_buffer_distance))
        if bud_buffer_list is None or len(bud_buffer_list) == 0:
            deal_hole_result.append(wkt_shapely)
            continue

        # LD 道路buffer 5m 保护下，防止腐蚀完了
        merge_shapely = None
        ld_wkt_list, boundary_list = poi_online_query.get_ld_road_by_wkt(wkt_shapely.wkt, protected_distance)
        if ld_wkt_list is not None and len(ld_wkt_list) > 0:
            for ld_wkt in ld_wkt_list:
                if merge_shapely is None:
                    merge_shapely = wkt.loads(ld_wkt)
                else:
                    ld_wkt_shape = utils.check_valid_shapely(wkt.loads(ld_wkt))
                    merge_shapely = utils.check_valid_shapely(merge_shapely.union(ld_wkt_shape))
        # 非LD外部路 buffer 5m 保护
        no_ld_road_list = poi_online_query.get_not_ld_out_road(wkt_shapely.wkt, protected_distance)
        if no_ld_road_list is not None and len(no_ld_road_list) > 0:
            for no_ld_road in no_ld_road_list:
                if merge_shapely is None:
                    merge_shapely = wkt.loads(no_ld_road[6])
                else:
                    no_ld_road_shape = utils.check_valid_shapely(wkt.loads(no_ld_road[6]))
                    merge_shapely = utils.check_valid_shapely(merge_shapely.union(no_ld_road_shape.buffer(0)))
        road_proteced_shapely = None
        if merge_shapely is not None:
            merge_shapely = merge_shapely.buffer(protected_distance)
            road_proteced_shapely = merge_shapely.intersection(wkt_shapely)
            road_proteced_shapely = road_proteced_shapely.buffer(-0.00001).simplify(0.000005).buffer(0.00001)
            road_proteced_shapely = road_proteced_shapely.simplify(0.00003).buffer(0.00003).buffer(-0.00003)

        # 计算buffer距离，防止向内部buffer直接给面干没了
        current_buffer_to_origin_area_rate = wkt_shapely.buffer(-corrode_buffer_distance).area / wkt_shapely.area
        while current_buffer_to_origin_area_rate < buffer_to_origin_area_rate:
            corrode_buffer_distance -= corrode_buffer_distance / 8
            current_buffer_to_origin_area_rate = wkt_shapely.buffer(-corrode_buffer_distance).area / wkt_shapely.area

        corrode_wkt = wkt_shapely.buffer(-corrode_buffer_distance).simplify(corrode_simplify_distance).buffer(
            corrode_buffer_distance)
        intersection_wkt = wkt_shapely.intersection(corrode_wkt)

        # 道路保护面合并回去
        if road_proteced_shapely is not None:
            intersection_wkt = intersection_wkt.union(road_proteced_shapely)
        corrode_result_wkt = intersection_wkt.simplify(ctx.simplify_distance).buffer(ctx.simplify_distance).buffer(
            -ctx.simplify_distance)
        deal_hole_result.append(corrode_result_wkt)
    ctx.shapely_wkt_list = deal_hole_result
    proceed()


def diff_ld_road_green_wkt(ctx: context.Context, proceed):
    """
    根据LD道路面擦除
    LD道路面：nav_lane_group 的boundary_list 第一个与最后一个就是nav_lane_marking_pl两条边界线
    :param wkt_shapely_list:
    :return:
    """
    diff_ld_result = []
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "LD_ROAD_DIFF"}
        print("根据LD道路面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 获取LD道路面
        ld_wkt_list, boundary_list = poi_online_query.get_ld_road_by_wkt(wkt_shapely.wkt, 0.00050)
        if ld_wkt_list is None or len(ld_wkt_list) == 0:
            diff_ld_result.append(wkt_shapely)
            poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
            continue
        # buffer 0.5LD道路面然后裁切识别面
        merge_ld_wkt = utils.check_valid_shapely(wkt.loads(ld_wkt_list[0]))
        for ld_wkt in ld_wkt_list:
            ld_wkt_shape = utils.check_valid_shapely(wkt.loads(ld_wkt))
            merge_ld_wkt = utils.check_valid_shapely(merge_ld_wkt.union(ld_wkt_shape))
        buffer_ld_wkt = merge_ld_wkt.buffer(0.000005)
        diff_ld_result.append(utils.check_valid_shapely(wkt_shapely.difference(buffer_ld_wkt)))

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(boundary_list)
        deal_progress_record["geom"] = wkt_shapely.wkt
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    ctx.shapely_wkt_list = diff_ld_result
    proceed()


def diff_no_ld_road_green_wkt(ctx: context.Context, proceed):
    """
    无LD道路面擦除 buffer 0.5m
    :param wkt_shapely_list:
    :return:
    """
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    diff_no_ld_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "NO_LD_ROAD_DIFF"}
        print("根据无LD道路面擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        # 获取非LD道路
        no_ld_road_list = poi_online_query.get_not_ld_out_road(wkt_shapely.wkt, 0.00050)
        if no_ld_road_list is None or len(no_ld_road_list) == 0:
            diff_no_ld_result.append(wkt_shapely)
            poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
            continue
        # 计算道路宽度
        merge_road_wkt = wkt.loads(no_ld_road_list[0][6])
        link_id_list = []
        for no_lod_road in no_ld_road_list:
            link_id_list.append(no_lod_road[0])
            road_shapely_wkt = wkt.loads(no_lod_road[6])
            road_width = CommTool.get_road_with(no_lod_road[3], no_lod_road[1], no_lod_road[4], no_lod_road[5],
                                                no_lod_road[2])
            road_buffer_width = road_width * 0.00001 + 0.000005
            road_buff_wkt = road_shapely_wkt.buffer(road_buffer_width)
            merge_road_wkt = merge_road_wkt.union(road_buff_wkt)
        diff_wkt_result = utils.check_valid_shapely(wkt_shapely.difference(merge_road_wkt))
        diff_no_ld_result.append(diff_wkt_result)

        # 过程量入库
        deal_progress_record["filter_element"] = ','.join(link_id_list)
        deal_progress_record["geom"] = diff_wkt_result.wkt
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    ctx.shapely_wkt_list = diff_no_ld_result
    proceed()


def diff_inner_road_green_wkt(ctx: context.Context, proceed):
    """
    通过内部路擦除  buffer 车道数>1 && 车道数 * 3m
    :param wkt_shapely_list:
    :return:
    """
    wkt_shapely_list = ctx.shapely_wkt_list
    diff_inner_road_result = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "INNER_ROAD_DIFF"}
        print("根据内部路擦除：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        inner_road_list = poi_online_query.get_inner_road(wkt_shapely.buffer(0.00050).wkt)
        if inner_road_list is None or len(inner_road_list) == 0:
            diff_inner_road_result.append(wkt_shapely)
            poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
            continue
        merge_inner_road = wkt.loads(inner_road_list[0][1])
        link_id_list = []
        for inner_road in inner_road_list:
            link_id_list.append(inner_road[2])
            inner_road_shapely_wkt = wkt.loads(inner_road[1])
            buffer_width = inner_road[0] * 0.00003
            inner_road_buffer_wkt = inner_road_shapely_wkt.buffer(buffer_width)
            merge_inner_road = merge_inner_road.union(inner_road_buffer_wkt)
        diff_result_wkt = utils.check_valid_shapely(wkt_shapely.difference(merge_inner_road))
        diff_inner_road_result.append(diff_result_wkt)

        # 过程量记录入库，用于前期策略快速迭代分析问题
        deal_progress_record["filter_element"] = ','.join(link_id_list)
        deal_progress_record["geom"] = diff_result_wkt.wkt
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    ctx.shapely_wkt_list = diff_inner_road_result
    proceed()


def pick_up_wkt_from_aoi_and_road(ctx: context.Context, proceed):
    """
    根据AOI与道路识别面提取绿地识别面
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000001000  # 根据AOI过滤小于1000平米 道路100平米
    filter_road_area = 0.0000000100
    aoi_pick_up_buffer_distance = 0.0001  # AOI buffer 10米提取绿地
    road_pick_up_buffer_distance = 0.0001  # 道路 buffer 5米提取绿地
    # 拆分multipolygon
    split_wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    filter_shapely_wkt = []
    for key, wkt_shapely in enumerate(split_wkt_shapely_list):
        pick_up_wkt = None
        print("根据AOI与道路识别面提取绿地识别面：边框总量：{}, 当前进度：{}".format(len(split_wkt_shapely_list), key))
        # 记录处理过程，用于分析迭代
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 0, "remark": "", "source": "",
                                "filter_element": "", "step": "PICK_UP"}
        # 根据AOI提取识别面，AOI buffer 10 提取绿地
        intersects_aoi_list = poi_online_query.get_intersection_not_sq_aoi(wkt_shapely.wkt, aoi_pick_up_buffer_distance)
        if intersects_aoi_list is not None and len(intersects_aoi_list) > 0:
            # 合并相交AOI面
            merge_intersects_aoi = wkt.loads(intersects_aoi_list[0][1]).buffer(aoi_pick_up_buffer_distance)
            face_id_list = []
            for intersects_aoi in intersects_aoi_list:
                face_id_list.append(intersects_aoi[0])
                buffer_aoi = wkt.loads(intersects_aoi[1]).buffer(aoi_pick_up_buffer_distance)
                merge_intersects_aoi = merge_intersects_aoi.union(buffer_aoi)
            # 相交面积过滤
            intersection_shapely = merge_intersects_aoi.intersection(wkt_shapely)
            intersection_area = intersection_shapely.area
            deal_progress_record["remark"] = "AOI:area|{}".format(intersection_area)
            if intersection_area > filter_aoi_area:
                # filter_shapely_wkt.append(intersection_shapely)
                pick_up_wkt = intersection_shapely
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = "AOI"
                deal_progress_record["filter_element"] = ','.join(face_id_list)

        # 根据LD道路面 buffer 5米提取识别面
        intersection_ld_road_list, boundary_list = poi_online_query.get_ld_road_by_wkt(wkt_shapely.wkt,
                                                                                       road_pick_up_buffer_distance)
        if intersection_ld_road_list is not None and len(intersection_ld_road_list) > 0:
            merge_ld_wkt = utils.check_valid_shapely(wkt.loads(intersection_ld_road_list[0]))
            for intersection_ld_wkt in intersection_ld_road_list:
                intersection_ld_shape = utils.check_valid_shapely(wkt.loads(intersection_ld_wkt))
                merge_ld_wkt = merge_ld_wkt.union(intersection_ld_shape).buffer(road_pick_up_buffer_distance)
            ld_intersection_shapely = merge_ld_wkt.intersection(wkt_shapely)
            ld_intersection_area = ld_intersection_shapely.area
            deal_progress_record["remark"] = deal_progress_record["remark"] + ";LD:area|{}".format(ld_intersection_area)
            if ld_intersection_area > filter_road_area:
                # filter_shapely_wkt.append(ld_intersection_shapely)
                ld_intersection_shapely = utils.gemetry_collection_split_and_union(ld_intersection_shapely)
                if pick_up_wkt is None:
                    pick_up_wkt = ld_intersection_shapely
                else:
                    pick_up_wkt = pick_up_wkt.union(ld_intersection_shapely)
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = deal_progress_record["source"] + "&LD"
                deal_progress_record["filter_element"] = deal_progress_record["filter_element"] + " & " + ','.join(
                    boundary_list)

        # 根据非LD道路面 buffer 5米提取识别面
        no_ld_road_list = poi_online_query.get_not_ld_out_road(wkt_shapely.wkt, road_pick_up_buffer_distance)
        if no_ld_road_list is not None and len(no_ld_road_list) > 0:
            merge_road_wkt = wkt.loads(no_ld_road_list[0][6])
            link_id_list = []
            for no_lod_road in no_ld_road_list:
                link_id_list.append(no_lod_road[0])
                road_shapely_wkt = wkt.loads(no_lod_road[6])
                road_width = CommTool.get_road_with(no_lod_road[3], no_lod_road[1], no_lod_road[4], no_lod_road[5],
                                                    no_lod_road[2])
                road_buffer_width = road_width * 0.00001 + road_pick_up_buffer_distance
                road_buff_wkt = road_shapely_wkt.buffer(road_buffer_width)
                merge_road_wkt = merge_road_wkt.union(road_buff_wkt)
            intersection_no_ld_shapely = merge_road_wkt.intersection(wkt_shapely)
            intersection_no_ld_area = intersection_no_ld_shapely.area
            deal_progress_record["remark"] = deal_progress_record["remark"] + ";NO_LD:area|{}".format(
                intersection_no_ld_area)
            if intersection_no_ld_area > filter_road_area:
                # filter_shapely_wkt.append(intersection_no_ld_shapely)
                intersection_no_ld_shapely = utils.gemetry_collection_split_and_union(intersection_no_ld_shapely)
                if pick_up_wkt is None:
                    pick_up_wkt = intersection_no_ld_shapely
                else:
                    pick_up_wkt = pick_up_wkt.union(intersection_no_ld_shapely)
                deal_progress_record["is_valid"] = 1
                deal_progress_record["source"] = deal_progress_record["source"] + "&NO_LD"
                deal_progress_record["filter_element"] = deal_progress_record["filter_element"] + " & " + ','.join(
                    link_id_list)
                # 过程记录入库
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
        if pick_up_wkt is not None:
            pick_wkt_list = utils.split_multi_polygon([pick_up_wkt])
            filter_shapely_wkt = filter_shapely_wkt + pick_wkt_list
            for pick_up_wkt in pick_wkt_list:
                deal_progress_record = {"geom": pick_up_wkt.wkt, "batch": ctx.batch, "is_valid": 1, "remark": "",
                                        "source": "",
                                        "filter_element": "", "step": "PICK_UP_RESULT"}
                poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    ctx.shapely_wkt_list = filter_shapely_wkt
    proceed()


def filter_min_angle_point(ctx: context.Context, proceed):
    """
    角度填平
    :param wkt_shapely_list:
    :return:
    """
    # 过滤角度, 去除细缝
    filter_angle = 60
    new_filter_angle = 30
    filter_angle_shapely_list = []
    wkt_shapely_list = [wkt_shapely.simplify(0.00001) for wkt_shapely in ctx.shapely_wkt_list]
    wkt_shapely_list = utils.split_multi_polygon(wkt_shapely_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("角度过虑：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        while True:
            wkt_shapely, is_exists_filter_angle = utils.angle_straightening(filter_angle, new_filter_angle, wkt_shapely)
            if not is_exists_filter_angle:
                break
        filter_angle_shapely_list.append(wkt_shapely)
    ctx.shapely_wkt_list = filter_angle_shapely_list
    proceed()


def fix_gun_shapely(ctx: context.Context, proceed):
    """
    修复枪型形状
    :param wkt_shapely_list:
    :return:
    """
    simply_wkt_shapely_list = []
    fix_gun_shapely_list = []
    for wkt_shapely in ctx.shapely_wkt_list:
        simply_wkt_shapely_list.append(wkt_shapely.buffer(-0.000005).buffer(0.000005).simplify(ctx.simplify_distance))
    simply_wkt_shapely_list = utils.split_multi_polygon(simply_wkt_shapely_list)
    for key, simply_wkt_shapely in enumerate(simply_wkt_shapely_list):
        print("修复枪型形状：边框总量：{}, 当前进度：{}".format(len(simply_wkt_shapely_list), key))
        wkt_shapely = utils.vertical_gun_acute_angle(simply_wkt_shapely)
        fix_gun_shapely_list.append(wkt_shapely)
    ctx.shapely_wkt_list = fix_gun_shapely_list
    proceed()


def smooth_shapely_wkt(ctx: context.Context, proceed):
    """
    圆滑处理
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000000040  # 过滤提取之后较小的面积
    filter_angle = 40
    new_filter_angle = 30
    smooth_deal_result_list = []
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("圆滑处理：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        if wkt_shapely.area < filter_aoi_area:
            continue
        # wkt_shapely = wkt_shapely.simplify(SIMPLIFY_DISTANCE)
        wkt_shapely, is_exists_filter_angle = utils.angle_straightening(filter_angle, new_filter_angle, wkt_shapely)
        smooth_wkt_str = smooth_wkt.smooth_polygon(wkt_shapely.wkt)
        if smooth_wkt_str is not None:
            if wkt.loads(smooth_wkt_str).area < filter_aoi_area:
                continue
            smooth_deal_result_list.append(wkt.loads(smooth_wkt_str))
    ctx.shapely_wkt_list = smooth_deal_result_list
    proceed()


def last_deal_result_save(ctx: context.Context, proceed):
    """
    处理成果入库
    :param wkt_shapely_list:
    :return:
    """
    filter_aoi_area = 0.0000000040  # 过滤提取之后较小的面积
    wkt_shapely_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    for wkt_shapely in wkt_shapely_list:
        if wkt_shapely.area < filter_aoi_area:
            continue
        deal_progress_record = {"geom": wkt_shapely.wkt, "batch": ctx.batch, "is_valid": 1, "remark": "", "source": "",
                                "filter_element": "", "step": "RESULT"}
        poi_online_rw.insert_green_after_progress_rcord(deal_progress_record)
    proceed()
