# -*- coding: utf-8 -*-
"""
pg_dao.py - 优化后的 PostgreSQL 数据访问基类
"""
import psycopg2
import psycopg2.extras
from psycopg2.pool import PoolError, SimpleConnectionPool
import time
from threading import Lock
from contextlib import contextmanager
from loguru import logger
import traceback
from typing import Optional, Dict, Any, List, Union, Tuple
from src.aoi import CommTool

# 配置日志
logger.add("./log/pg.log", rotation="10 MB", retention="30 days")


class PgDao:
    """
    基类
    """
    _shared_pools = {}  # 类变量，存储共享连接池
    _lock = Lock()  # 线程安全锁

    def __init__(self, pg_conf: Optional[Dict[str, Any]] = None,
                 pool: Optional[SimpleConnectionPool] = None,
                 use_shared_pool: bool = True,
                 min_connections: int = 2,
                 max_connections: int = 5):
        """
        初始化 PgDao

        :param pg_conf: 数据库配置字典，包含host,port,user,pwd,db等
        :param pool: 外部传入的连接池(优先使用)
        :param use_shared_pool: 是否使用共享连接池
        :param min_connections: 连接池最小连接数
        :param max_connections: 连接池最大连接数
        """
        self._pool = pool
        self.pg_conf = pg_conf
        self.use_shared_pool = use_shared_pool
        self.min_connections = min_connections
        self.max_connections = max_connections
        self._initialize_pool()

    def _initialize_pool(self) -> bool:
        """初始化连接池"""
        try:
            if self._pool:
                return True

            if self.pg_conf is None:
                raise ValueError("pg_conf cannot be None when no pool provided")

            if self.use_shared_pool:
                # 使用共享连接池(线程安全实现)
                key = self._get_pool_key()
                with self._lock:
                    if key not in self.__class__._shared_pools:
                        self.__class__._shared_pools[key] = self._create_new_pool()
                    self._pool = self.__class__._shared_pools[key]
            else:
                # 创建独立连接池
                self._pool = self._create_new_pool()

            return True
        except Exception as e:
            logger.error(f"连接池初始化失败: {e}\n{traceback.format_exc()}")
            return False

    def _get_pool_key(self) -> tuple:
        """生成连接池的唯一标识键"""
        return (
            self.pg_conf['host'],
            self.pg_conf['port'],
            self.pg_conf['user'],
            self.pg_conf['db']
        )

    def _create_new_pool(self) -> SimpleConnectionPool:
        """创建新的连接池"""
        return SimpleConnectionPool(
            minconn=self.min_connections,
            maxconn=self.max_connections,
            host=self.pg_conf['host'],
            port=self.pg_conf['port'],
            user=self.pg_conf['user'],
            password=self.pg_conf['pwd'],
            database=self.pg_conf['db'],
            connect_timeout=5
        )

    def _retry_conn(self, retry_num: int = 3, sleep_time: int = 5) -> bool:
        """
        重试连接机制（修复共享连接池关闭后无法恢复的问题）

        :param retry_num: 最大重试次数
        :param sleep_time: 基础等待时间(秒)
        :return: 是否重试成功
        """
        if self.pg_conf.get('source') == 'road':
            self._refresh_road_config()

        for attempt in range(retry_num):
            try:
                logger.info(f"尝试重新连接数据库(第{attempt + 1}次)...")

                # 关闭旧连接池
                if self._pool:
                    try:
                        self._pool.closeall()
                        logger.info("旧连接池已关闭")
                    except Exception as e:
                        logger.warning(f"关闭旧连接池失败: {e}")

                # 如果是共享连接池，需要移除旧引用
                if self.use_shared_pool:
                    key = self._get_pool_key()
                    with self._lock:
                        self.__class__._shared_pools.pop(key, None)

                self._pool = None  # 确保下一次 _initialize_pool 会重建

                # 初始化新连接池
                success = self._initialize_pool()
                logger.info(f"连接池初始化成功: {success}")

                if success:
                    # 测试连接是否正常
                    with self._get_cursor() as cur:
                        cur.execute("SELECT 1")
                    logger.info("数据库连接恢复成功")
                    return True

            except Exception as e:
                logger.warning(f"重试连接失败(第{attempt + 1}次): {e}")
                time.sleep(sleep_time * (attempt + 1))  # 线性退避

        logger.error(f"数据库连接恢复失败，已达最大重试次数{retry_num}")
        return False

    def _refresh_road_config(self) -> None:
        """刷新道路库配置"""
        road_conf = {}
        road_info = CommTool.get_road_db_from_dcmpf()
        if 'db' in road_info:
            road_conf = road_info
            road_conf['pwd'] = road_info['passwd']
        road_conf['source'] = 'road'
        self.pg_conf = road_conf

    @contextmanager
    def _get_cursor(self, cursor_factory=None):
        """
        获取数据库游标的上下文管理器

        :param cursor_factory: 游标工厂(如DictCursor)
        :yield: 数据库游标
        """
        conn = None
        try:
            conn = self.get_conn()
            with conn.cursor(cursor_factory=cursor_factory) as cur:
                yield cur
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                self.put_conn(conn)

    def get_conn(self):
        """从连接池获取连接（增加连接池关闭判断）"""
        try:
            if self._pool is None:
                raise PoolError("连接池尚未初始化")
            # psycopg2 的 SimpleConnectionPool 没有显式 closed 属性，这里我们判断是否报错
            return self._pool.getconn()
        except PoolError as e:
            logger.error(f"连接池异常（可能已关闭或耗尽），等待并重试...{e}")
            raise e

    def put_conn(self, conn):
        """归还连接到连接池"""
        try:
            self._pool.putconn(conn)
        except Exception as e:
            logger.warning(f"归还连接到连接池失败: {e}")
            try:
                conn.close()
            except:
                pass

    def get_pool_status(self) -> dict:
        """获取连接池状态信息"""
        if not hasattr(self._pool, '_pool'):
            return {"status": "pool_not_initialized"}

        return {
            "min": self._pool.minconn,
            "max": self._pool.maxconn,
            "used": len(getattr(self._pool, '_used', [])),
            "idle": len(getattr(self._pool, '_pool', [])),
            "waiting": getattr(self._pool, '_waiting', 0)
        }

    def queryall(self, sql: str, params=None, retry_times: int = 3) -> List[Tuple]:
        """
        执行查询并返回所有结果

        :param sql: SQL查询语句
        :param params: 查询参数
        :param retry_times: 最大重试次数
        :return: 查询结果列表
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor() as cur:
                    cur.execute(sql, params)
                    return cur.fetchall()
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"查询失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
                if not self._retry_conn(retry_num=1, sleep_time=2):
                    time.sleep(min(5, attempt))
                    continue
            except Exception as e:
                logger.error(f"未知查询错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"查询失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def queryall_by_kv_cursor(self, sql: str, params=None, retry_times: int = 3) -> List[Dict]:
        """
        执行查询并返回字典形式的所有结果

        :param sql: SQL查询语句
        :param params: 查询参数
        :param retry_times: 最大重试次数
        :return: 字典形式的查询结果列表
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor(psycopg2.extras.DictCursor) as cur:
                    cur.execute(sql, params)
                    return [dict(row) for row in cur.fetchall()]
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"查询失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
                if not self._retry_conn(retry_num=1, sleep_time=2):
                    time.sleep(min(5, attempt))
                    continue
            except Exception as e:
                logger.error(f"未知查询错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"查询失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def queryone(self, sql: str, params=None, retry_times: int = 3) -> Optional[Tuple]:
        """
        执行查询并返回单条结果

        :param sql: SQL查询语句
        :param params: 查询参数
        :param retry_times: 最大重试次数
        :return: 单条查询结果或None
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor() as cur:
                    cur.execute(sql, params)
                    return cur.fetchone()
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"查询失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
                if not self._retry_conn(retry_num=1, sleep_time=2):
                    time.sleep(min(5, attempt))
                    continue
            except Exception as e:
                logger.error(f"未知查询错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"查询失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def queryone_by_kv_cursor(self, sql: str, params=None, retry_times: int = 3) -> Optional[Dict]:
        """
        执行查询并返回字典形式的单条结果

        :param sql: SQL查询语句
        :param params: 查询参数
        :param retry_times: 最大重试次数
        :return: 字典形式的单条查询结果或None
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor(psycopg2.extras.DictCursor) as cur:
                    cur.execute(sql, params)
                    result = cur.fetchone()
                    return dict(result) if result else None
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"查询失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
                if not self._retry_conn(retry_num=1, sleep_time=2):
                    time.sleep(min(5, attempt))
                    continue
            except Exception as e:
                logger.error(f"未知查询错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"查询失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def execute(self, sql: str, params=None, retry_times: int = 3) -> None:
        """
        执行非查询SQL语句(INSERT/UPDATE/DELETE)

        :param sql: SQL语句
        :param params: 参数
        :param retry_times: 最大重试次数
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor() as cur:
                    cur.execute(sql, params)
                return
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"执行失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
            except Exception as e:
                logger.error(f"未知执行错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"执行失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def batch_insert(self, sql: str, params_list: List[Tuple], retry_times: int = 3) -> None:
        """
        批量插入数据

        :param sql: 插入SQL语句
        :param params_list: 参数列表
        :param retry_times: 最大重试次数
        """
        attempt = 0
        last_exception = None

        while attempt < retry_times:
            attempt += 1
            try:
                with self._get_cursor() as cur:
                    psycopg2.extras.execute_batch(cur, sql, params_list)
                return
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                last_exception = e
                logger.warning(f"批量插入失败(尝试第{attempt}次): {e}")
                if not self._retry_conn():
                    time.sleep(min(5, attempt))
            except PoolError as e:
                last_exception = e
                logger.warning(f"连接池耗尽(尝试第{attempt}次),{e},{sql}")
                time.sleep(min(10, attempt * 2))
            except Exception as e:
                logger.error(f"未知批量插入错误: {traceback.format_exc()}")
                raise e

        raise Exception(f"批量插入失败，已达最大重试次数{retry_times}. 最后错误: {str(last_exception)}")

    def close(self) -> None:
        """关闭连接池"""
        if self._pool:
            try:
                self._pool.closeall()
                logger.info("数据库连接池已关闭")
            except Exception as e:
                logger.warning(f"关闭连接池时出错: {e}")

    def __enter__(self):
        """支持with语句"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出with语句时自动关闭连接池"""
        self.close()


def create_pg_pool(pg_conf: Dict[str, Any], minconn: int = 2, maxconn: int = 10) -> SimpleConnectionPool:
    """
    创建 PostgreSQL 连接池

    :param pg_conf: 数据库配置
    :param minconn: 最小连接数
    :param maxconn: 最大连接数
    :return: 连接池对象
    """
    return SimpleConnectionPool(
        minconn=minconn,
        maxconn=maxconn,
        host=pg_conf["host"],
        port=pg_conf["port"],
        user=pg_conf["user"],
        password=pg_conf["pwd"],
        database=pg_conf["db"],
        connect_timeout=5
    )


def close_pg_pools(pool_dict: Dict[str, SimpleConnectionPool]) -> None:
    """
    关闭多个连接池

    :param pool_dict: 连接池字典
    """
    for name, pool in pool_dict.items():
        if pool:
            try:
                pool.closeall()
                logger.info(f"已关闭连接池: {name}")
            except Exception as e:
                logger.warning(f"关闭连接池{name}时出错: {e}")