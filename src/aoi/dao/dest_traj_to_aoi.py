# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
轨迹库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class DestTrajToAOI(PgDao):
    """
    轨迹库
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "dest_traj_to_aoi" in conf:
            poi_online_r_conf = conf['dest_traj_to_aoi']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("dest_traj_to_aoi 配置不存在")

    def insert_sync_traj_bid_record(self, data: dict):
        """
        轨迹BID入库
        Args:
            data:

        Returns:

        """
        sql = f"""
            insert into sync_traj_bid_record(bid,traj_type,batch,cal_pv,std_tag) values(%s,%s,%s,%s,%s) 
            on conflict (bid,traj_type) do update set batch=%s
        """
        self.execute(sql, [data["bid"], "DEST", data["batch"], data["click_pv"], data["std_tag"], data["batch"]])
        return True

    def get_nav_gate_traffic(self, long_node_ids: list):
        """
        获取大门通行性
        Args:
            long_node_ids:

        Returns:

        """
        sql = f"""
           select node_id, gate_id, passage from gates_semantic 
           where node_id = ANY(%s)
       """
        return self.queryall(sql, [long_node_ids])

    def get_exp_traj_by_bid(self, uid: str):
        """
        获取经验轨迹
        Args:
            uid:
        """
        sql = f"""select st_astext(end_track_line),num_track,dist_dest,route_end_percentage,cuid_repetition_rate
              from exp_traj_monthly where end_poi_uid = %s order by num_track desc
        """
        res = self.queryall(sql, [uid])
        if not res:
            sql = f"""
                select st_astext(end_track_line),num_track,dist_dest,route_end_percentage,cuid_repetition_rate 
                from exp_traj_monthly
                where end_poi_distribution @> %s
            """
            json_condition = f'[{{"end_poi_uid": "{uid}"}}]'
            res = self.queryall(sql, [json_condition])
        return res

    def get_quarter_exp_traj_by_bid(self, uid: str):
        """
        获取季度经验轨迹
        Args:
            uid:
        """
        sql = f"""select st_astext(end_track_line),num_track,dist_dest,route_end_percentage,cuid_repetition_rate
              from exp_traj_quarter where end_poi_uid = %s order by num_track desc
        """
        res = self.queryall(sql, [uid])
        if not res:
            sql = f"""
                select st_astext(end_track_line),num_track,dist_dest,route_end_percentage,cuid_repetition_rate 
                from exp_traj_quarter
                where end_poi_distribution @> %s
            """
            json_condition = f'[{{"end_poi_uid": "{uid}"}}]'
            res = self.queryall(sql, [json_condition])
        return res

    def get_exp_traj_by_bid_new(self, bid: str):
        """
        获取经验轨迹
        Args:
            bid:
        """
        sql = f"""
            select st_astext(end_track_line) as geom,num_track,dist_dest,route_end_percentage, cuid_repetition_rate,yaw_rate 
            from exp_traj_monthly 
            where end_poi_uid = %s order by num_track desc
        """
        return self.queryall_by_kv_cursor(sql, [bid])

    def get_exp_traj_by_bid_more(self, uid: str):
        """
        获取经验轨迹
        Args:
            bid:
        """
        sql = f"""select st_astext(end_track_line),num_track,num_yaw,yaw_rate,cuid_repetition_rate
              from exp_traj_monthly where end_poi_uid = %s 
              or end_poi_distribution @> %s 
              order by num_track desc
        """
        json_condition = f'[{{"end_poi_uid": "{uid}"}}]'
        return self.queryall(sql, [uid, json_condition])