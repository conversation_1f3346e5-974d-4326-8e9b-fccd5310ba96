#-*- coding: utf-8 -*-
"""
存量出入口入库批量生成
"""
import time
from uuid import uuid4
import psycopg2
import requests
import json

curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()

backConf = {"db": "master_back",
            "user": "master_back_se_ro", "pwd": "mapread",
            "host": "**************", "port": 5432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}
# poicd正式
poicdConf = {"db": "poi_cd",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

transConf = {"db": "trans_id",
            "user": "trans_id_se_rw", "pwd": "cpmkukky",
            "host": "*************", "port": 5432}
# psql -U trans_id_se_rw -Wcpmkukky -p 5432 -h ************* -d trans_id

#连接poi库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

#连接poicd库
poicdDbConn = psycopg2.connect(database=poicdConf["db"],
                              user=poicdConf["user"],
                              password=poicdConf["pwd"],
                              host=poicdConf["host"],
                              port=poicdConf["port"])
poicdDao = poicdDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()

 # 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接trans库
transDbConn = psycopg2.connect(database=transConf["db"],
                              user=transConf["user"],
                              password=transConf["pwd"],
                              host=transConf["host"],
                              port=transConf["port"])
transDao = transDbConn.cursor()

suffix = '_627'
parkding_table = "temp_parking_bid_todo_lists"
blu_access = "blu_access" + suffix
blu_access_gate_rel = "blu_access_gate_rel" + suffix
blu_access_link_rel = "blu_access_link_rel" + suffix
blu_access_traffic = "blu_access_traffic" + suffix


def process():
    """
    process 正式处理流程
    """
    sql = "select id,bid,node_id,gate_id,type from {} where status = 0".format(parkding_table)
    while True:
        # 342522
        sql = "select id,bid,node_id,gate_id,type from {} where status = 0".format(parkding_table)
        poiDao.execute(sql)
        bids_lists = poiDao.fetchall()
        if bids_lists is None or len(bids_lists) == 0:
            print('todo list none.')
            break
        # 循环 bids_lists  
        for v in bids_lists:
            try:
                status = handlerBid(v)
                print("result:" + str(status))
            except:
                print('try_error...id=%s' % (v[0]))
    return True

def handlerBid(v):
    """
    handler 处理函数
    """
    id = v[0]
    bid = v[1]
    short_node_id = v[2]
    type = v[4]
    node_id_str = ''
    relation_source = 0
    access_data = {}
    # 获取 poi 信息
    print(bid + 'start')
    sql = "select bid,mid,mesh_id,name,status,relation_bid,relation_type, \
        display_x,display_y,relation_bid from poi where bid = '{}'".format(bid)
    print("PoiSql:" + sql)
    poiData = set()
    if type == 'from_de':
        poicdDao.execute(sql)
        poiData = poicdDao.fetchone()
    else:
        poiDao.execute(sql)
        poiData = poiDao.fetchone()
    if poiData is None or len(poiData) == 0:
        updateBidStatusByID(id, 5)
        return 5
    if poiData[2] is None or len(poiData[1]) == 0:
        updateBidStatusByID(id, 12)
        return 12 
    mid = poiData[1]
    if poiData[5] == '' or poiData[5] is None:
        updateBidStatusByID(id, 6)
        return 6
    if type == 'from_de':
        # 获取成果库 nodeid
        relation_source = 7
        sql = "select related_road_node from poi_gate where mid='{}'".format(mid)
        print(sql)
        poicdDao.execute(sql)
        work_data = poicdDao.fetchone()
        print(work_data)
        if work_data is None or len(work_data) == 0:
            updateBidStatusByID(id, 7)
            return 7
        node_id_str = work_data[0]
    else:
        relation_source = 9
        # poi 来源 短node_id 转 长node_id
        sql = "select sid  from image_n where  tid = '{}'".format(short_node_id)
        print(sql)
        transDao.execute(sql)
        nodeid_trans = transDao.fetchone()
        if nodeid_trans is None or len(nodeid_trans) == 0:
            updateBidStatusByID(id, 8)
            return 8
        print(nodeid_trans)
        node_id_str = nodeid_trans[0]
    
    node_id_list = node_id_str.split(',')
    node_id_set = set(node_id_list)
    if len(node_id_set) > 1:
        print("bidGetNodeIDWarning:")
        print(node_id_set)
        updateBidStatusByID(id, 11)
        return 11
    for node_id in node_id_set:
        sql = "select gate_id, in_linkid, out_linkid, node_id, gate_type \
            from nav_gate where  node_id = '{}'".format(node_id)
        print(sql)
        poiDao.execute(sql)
        nav_gate_data = poiDao.fetchall()
        gate_num = len(nav_gate_data)
        if  gate_num == 0:
            updateBidStatusByID(id, 9)
            return 9
        elif gate_num > 2:
            updateBidStatusByID(id, 10)
            return 10

        # 形态 0 未调查，1 物理实体门，2 虚拟门
        form = 2 if nav_gate_data[0][4] == 4 else 1

        mesh_id = poiData[2]
        name = poiData[3]
        status = poiData[4]
        main_bid = poiData[5]
        display_x = poiData[7]
        display_y = poiData[8]

        if isBackExist(bid, main_bid, node_id):
            updateBidStatusByID(id, 3)
            return 3

        face_relate_type =  getFaceTypeByMainBid(main_bid)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 插入 access 表
        access_id = str(uuid4())
        access_id = access_id.replace('-', '')
        print(access_id)
        access_data['access_id'] = access_id
        access_data['main_bid'] = main_bid
        access_data['face_relate_type'] = face_relate_type
        # access_data['geom'] = "st_geomfromtext('POINT(%f %f)',4326)".format(display_x, display_y)
        access_data['geom'] = "st_geomfromtext('POINT({} {})',4326)".format(display_x, display_y)
        access_data['mesh_id'] = mesh_id
        access_data['status'] = status
        access_data['form'] = form
        access_data['kind'] = 2
        access_data['name'] = name
        access_data['name_source'] = 0
        access_data['access_poi_bid'] = bid
        access_data['access_poi_mid'] = mid
        access_data['update_time'] = update_time
        access_data['relation_source'] = relation_source
        access_data['attribute_source'] = relation_source
        # insertData(blu_access, access_data)
        # insert_sql = "INSERT INTO blu_access_627 (status, attribute_source, kind, main_bid, name, form, \
        #      update_time, name_source, mesh_id, access_id, access_poi_mid, relation_source, face_relate_type, \
        #      access_poi_bid, geom) VALUES ('{}', {}, '{}', '{}', '{}', {}, '{}', {}, {}, '{}', '{}', {}, {}, '{}', \
        #      st_geomfromtext('POINT({} {})',4326))".format(access_data['status'], \
        #      access_data['attribute_source'], access_data['kind'], access_data['main_bid'], access_data['name'], \
        #      access_data['form'], access_data['update_time'], access_data['name_source'], access_data['mesh_id'], \
        #      access_data['access_id'], access_data['access_poi_mid'], access_data['relation_source'], \
        #      access_data['face_relate_type'], access_data['access_poi_bid'], display_x, display_y)
        insert_sql = "INSERT INTO blu_access_627 (status, attribute_source, kind, main_bid, name, form, \
            update_time, name_source, mesh_id, access_id, access_poi_mid, relation_source, face_relate_type, \
            access_poi_bid, geom) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, \
            st_geomfromtext(%s,4326))"
        geom_point =  "POINT(" + str(display_x) + " " + str(display_y) + ")"
        poiDao.execute(insert_sql, [access_data['status'], \
            access_data['attribute_source'], access_data['kind'], access_data['main_bid'], access_data['name'], \
            access_data['form'], access_data['update_time'], access_data['name_source'], access_data['mesh_id'], \
            access_data['access_id'], access_data['access_poi_mid'], access_data['relation_source'], \
            access_data['face_relate_type'], access_data['access_poi_bid'], geom_point])
        poiDbConn.commit()

        # 去重
        linkid_set = set()
        for gate in nav_gate_data:  
            access_gate_data = {}  
            gate_id = gate[0]
            access_gate_rel_id = str(uuid4())
            access_gate_rel_id = access_gate_rel_id.replace('-', '')
            access_gate_data['access_gate_rel_id'] = access_gate_rel_id
            access_gate_data['access_id'] = access_id
            access_gate_data['mesh_id'] = mesh_id
            access_gate_data['node_id'] = node_id
            access_gate_data['gate_id'] = gate_id
            access_gate_data['update_time'] = update_time
            access_gate_data['source'] = relation_source
            insertData(blu_access_gate_rel, access_gate_data)

            # 插入 inlink
            in_linkid = gate[1]
            out_linkid = gate[2]
            if  in_linkid not in linkid_set:
                linkid_set.add(in_linkid)
                access_link_data = {}
                access_link_rel_id = str(uuid4())
                access_link_rel_id = access_link_rel_id.replace('-', '')
                access_link_data['access_link_rel_id'] = access_link_rel_id
                access_link_data['access_id'] = access_id
                access_link_data['mesh_id'] = mesh_id
                access_link_data['link_id'] = in_linkid
                access_link_data['link_type'] = 0
                access_link_data['update_time'] = update_time
                access_link_data['source'] = relation_source
                insertData(blu_access_link_rel, access_link_data)
                print("gate_num:" + str(gate_num))
            if gate_num == 2:
                # 插入 outlink
                if  out_linkid not in linkid_set:
                    linkid_set.add(out_linkid)
                    access_link_data = {}
                    access_link_rel_id = str(uuid4())
                    access_link_rel_id = access_link_rel_id.replace('-', '')
                    access_link_data['access_link_rel_id'] = access_link_rel_id
                    access_link_data['access_id'] = access_id
                    access_link_data['mesh_id'] = mesh_id
                    access_link_data['link_id'] = out_linkid
                    access_link_data['link_type'] = 0
                    access_link_data['update_time'] = update_time
                    access_link_data['source'] = relation_source
                    insertData(blu_access_link_rel, access_link_data)
        updateBidStatusByID(id, 2)
        return 2

def isBackExist(bid, main_bid, node_id):
    """
    判断是否已经存在该背景任务
    
    Args:
        bid (str): 背景任务id
        main_bid (str): 主背景任务id
        node_id (str): 节点id
    
    Returns:
        bool: 存在返回True，不存在返回False
    """
    sql = "select 1 \
        from blu_access a \
            left join blu_access_gate_rel b on a.access_id = b.access_id  \
        where  a.access_poi_bid = '{}' and a.main_bid = '{}' and  b.node_id = '{}'".format(bid, main_bid, node_id)
    backgroundDao.execute(sql)
    data = backgroundDao.fetchone()
    if data is None or len(data) == 0:
        return False
    return True    
    

def getFaceTypeByMainBid(bid):
    """
    根据mid获取aoi类型
    Args:
        mid: poi bid
    Returns:
        face_relate_type 类型，若获取失败返回4
        0 未调查，1 关联基础院落，2 关联内部院落，3关联建筑面，4无关联面，5其他
    """
    sql = "select face_id from blu_face_poi where poi_bid = '{}'".format(bid)
    print("getFaceTypeByMainBid:" + sql)
    backgroundDao.execute(sql)
    face_poi_data = backgroundDao.fetchone()
    if face_poi_data is None or len(face_poi_data) == 0:
        return 4
    sql = "select aoi_level from blu_face where face_id = '{}'".format(face_poi_data[0])
    print("getFaceTypeByMainBid:" + sql)
    backgroundDao.execute(sql)
    face_data = backgroundDao.fetchone()
    if face_data is None or len(face_data) == 0:
        return 4
    if face_data[0] == 0:
        return 0
    elif face_data[0] == 1:
        return 5
    elif face_data[0] == 2:
        return 1
    elif face_data[0] == 3:
        return 2
    else:
        return 4


def insertData(table, data):
    """
    向指定表中插入数据
    :param table: 要插入数据的表名
    :param data: 要插入的数据，是一个字典类型
    """
    # 将 data 字典中的键作为列名，值作为列值，构造 SQL 插入语句
    sql = "INSERT INTO {} ({}) VALUES ({})".format(
        table, ', '.join(data.keys()), ', '.join(['%s'] * len(data))
    )
    print("table:" + table)
    print("insertData:" + sql)
    print(tuple(data.values()))
    # 执行 SQL 插入语句
    poiDao.execute(sql, tuple(data.values()))
    poiDbConn.commit()


def updateBidStatusByID(id, status):
    """
    根据id更新状态
    
    Args:
        id (int): 投标id
        status (int): 投标状态
    
    Returns:
        int: 更新成功的行数
    """
    sql = "update {} set status = {} where id = {}".format(parkding_table, status, id)
    print(sql)
    res = poiDao.execute(sql)
    poiDbConn.commit()
    return res


def resetData():
    """
    重置数据的函数,根据不同的suffix执行不同的sql语句
    """
    print("suffix:" + suffix)
    if suffix == '_627':
        sql = "delete from blu_access_627"
        print(sql)
        poiDao.execute(sql)
        poiDbConn.commit()
        sql = "delete from blu_access_gate_rel_627"
        print(sql)
        poiDao.execute(sql)
        poiDbConn.commit()
        sql = "delete from blu_access_link_rel_627"
        print(sql)
        poiDao.execute(sql)
        poiDbConn.commit()
        sql = "delete from blu_access_traffic_627"
        print(sql)
        poiDao.execute(sql)
        poiDbConn.commit()
        sql = "update temp_parking_bid_todo_lists set status = 0 where status != 0"
        print(sql)
        poiDao.execute(sql)
        poiDbConn.commit()


if __name__ == "__main__":
    # resetData()
    print("start" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    process()
    print("end" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))