"""
挖掘AOI主点和大门关联关系缺失
"""
import dataclasses
import sys
import os
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())

import tqdm
import subprocess
import requests
import json
import numpy as np
import traceback
import shapely.wkt
from shapely.geometry import LineString, Point, MultiPoint, Polygon, GeometryCollection

from src.common import pipeline, csv_tools
from src.door_naming.DaoAoiPoi import DaoAoiPoi
from src.door_naming.DaoMis import DaoMis
from src.door_naming.DaoDestTraj import DaoDestTraj
from typing import List
import pandas as pd
import datetime

desc = pipeline.get_desc()

MAX_DIS = 60
MAX_DIS_NODE = 30

today = datetime.date.today().strftime('%Y%m%d')
os.makedirs('all_bids', exist_ok=True)
bid_file = 'all_bids/exp_traj_bids.txt'
bids_strategy_file = 'all_bids/exp_traj_bids_strategy.tsv'


@dataclasses.dataclass
class AoiInfo:
    """
    aoi集合
    """
    poi_bid: str
    poi_geom: str
    poi_std_tag: str
    poi_name: str
    aoi_geom: str
    face_id: str
    uid: str = ''


@dataclasses.dataclass
class Result:
    """
    导出结果集
    """
    aoi_bid: str
    aoi_uid: str
    aoi_std_tag: str
    poi_geom: str
    node_id: str
    node_wkt: str
    msg: str
    final_geom: str
    final_node_dis: float
    node_dis: float = 0


@dataclasses.dataclass
class Context:
    """
    基础数据
    """
    infos: List[AoiInfo] = dataclasses.field(default_factory=list)
    result: List[Result] = dataclasses.field(default_factory=list)
    enableed_bid: List[str] = dataclasses.field(default_factory=list)


def get_pipes():
    """
    构建pipes
    :return:
    """
    pipes = pipeline.Pipeline(
        get_all_aoi_infos,
        mining,
        save,
    )
    return pipes


def run():
    """
    run
    :return:
    """
    ctx = Context()
    # ctx.enableed_bid = ['1826619257630259143']
    pipes = get_pipes()
    desc.attach(pipes)
    pipes(ctx)


@desc("保存数据")
def save(ctx: Context, proceed):
    """
    save
    """
    suffix = ''
    if ctx.enableed_bid:
        suffix = '_' + "debug"
    csv_tools.list_map_to_csv([dataclasses.asdict(x) for x in ctx.result], bids_strategy_file)
    proceed()


def _get_out_nav_link_by_geom(dao: DaoAoiPoi, geom_str):
    """
    根据geom查询外部路
    :param geom_str:
    :return:
    """
    sql = f"with line as (select st_geomfromtext('{geom_str}',4326) as geom_line)" \
          f" select link_id, st_astext(geom) as geom from nav_link, line where kind < 8 " \
          f"and st_intersects(geom, line.geom_line) "
    dao.cursor_road.execute(sql)
    ret = dao.cursor_road.fetchall()
    dao.conn_road.commit()
    return ret


def _get_node_human_handle_unbind(dao: DaoAoiPoi, bid, node_id):
    """
    获取人工作业
    :param dao:
    :param bid:
    :param node_id:
    :return:
    """
    sql = 'select conclusion from gate_aoi_match_check_data where bid = %s and node_id = %s and conclusion=%s limit 1'
    dao.cursor_poi.execute(sql, (bid, node_id, 'N'))
    return dao.cursor_poi.fetchone()


def check_node_in_in(dao: DaoAoiPoi, node_id):
    """
    检查是否为内内大门
    :return:
    """
    sql = 'select form from nav_link where s_nid=%s or e_nid=%s'
    dao.cursor_road.execute(sql, (node_id, node_id))
    ret = dao.cursor_road.fetchall()
    dao.conn_road.commit()
    if len(ret) < 1:
        return False
    for item in ret:
        if '52' not in item:
            return False
    return True


@desc("关联任务")
def mining(ctx: Context, proceed):
    """
    mining
    """
    n = 2000
    group_list = np.array_split(ctx.infos, len(ctx.infos) // n + 1)
    for group in tqdm.tqdm(group_list):
        try:
            with DaoMis() as dao_mis, DaoAoiPoi() as dao_aoi_poi, DaoDestTraj() as dao_dest_traj:
                for item in tqdm.tqdm(group, leave=False):
                    bid, aoi_geom, face_id = item.poi_bid, item.aoi_geom, item.face_id
                    aoi_sp = shapely.wkt.loads(aoi_geom)
                    traj_list, uid = get_exp_traj(dao_mis, bid)
                    item.uid = uid
                    mined_node_ids = []
                    for traj in traj_list:
                        last_point = traj.coords[-1]
                        last_point_geom = Point(last_point)
                        last_point_buf_wkt = last_point_geom.buffer(MAX_DIS_NODE / 110000).wkt
                        node_arr = get_gate_by_poly_wkt(dao_aoi_poi, last_point_buf_wkt)
                        for node_item in node_arr:
                            node_id, node_wkt = node_item[0], node_item[1]
                            node_final_dis = shapely.wkt.loads(node_wkt).distance(last_point_geom) * 1e5
                            if not bool_gate_trust(dao_dest_traj, node_id):
                                continue
                            # 是否已经绑定
                            if not bool_exist_re(dao_aoi_poi, bid, node_id):
                                gate_to_aoi_dist = aoi_sp.distance(shapely.wkt.loads(node_wkt)) * 1e5
                                if gate_to_aoi_dist > MAX_DIS:
                                    continue
                                if node_id not in mined_node_ids:
                                    r = Result(aoi_bid=bid, node_id=node_id, node_wkt=node_wkt,
                                               node_dis=gate_to_aoi_dist,
                                               msg='', poi_geom=item.poi_geom, aoi_std_tag=item.poi_std_tag,
                                               final_geom=last_point_geom, final_node_dis=node_final_dis, aoi_uid=uid)
                                    if gate_to_aoi_dist > 2:
                                        node_shape = shapely.wkt.loads(node_wkt)
                                        poi_shape = shapely.wkt.loads(item.poi_geom)
                                        line1 = f"LINESTRING({node_shape.x} {node_shape.y}, {poi_shape.x} {poi_shape.y})"
                                        is_cover7 = _get_out_nav_link_by_geom(dao_aoi_poi, line1)
                                        if is_cover7:
                                            r.msg = '跨高等级路'
                                            mined_node_ids.append(node_id)
                                        elif _get_node_human_handle_unbind(dao_aoi_poi, bid, node_id):
                                            r.msg = '人工作业结果为N'
                                            mined_node_ids.append(node_id)
                                        elif check_node_in_in(dao_aoi_poi, node_id):
                                            r.msg = '内内大门'
                                            mined_node_ids.append(node_id)
                                        else:
                                            mined_node_ids.append(node_id)
                                    else:
                                        if check_node_in_in(dao_aoi_poi, node_id):
                                            r.msg = '内内大门'
                                            mined_node_ids.append(node_id)
                                        mined_node_ids.append(node_id)
                                    ctx.result.append(r)
        except Exception as e:
            print(f"error: {e} 本次迭代跳过，不影响最终结果")

    proceed()


def bool_gate_trust(pg: DaoDestTraj, node_id):
    """
    bool_gate_trust
    """
    sql = f"select traversability, type, nnww_tag, type_desc from gates_semantic where long_node_id = '{node_id}'"
    pg.cursor.execute(sql)
    semantic_res = pg.cursor.fetchone()
    if semantic_res is None:
        return False

    _, type, nnww_tag, type_desc = semantic_res
    if type == "0":
        return False  # 紧急门
    else:
        if "入口" in nnww_tag:
            return True
        elif "出口" in nnww_tag:
            return False  # 出口门
        else:
            if type in ["2", "3"]:
                return True
            elif type in ["1"]:
                return False  # 出口门
    return False


def bool_exist_re(pg: DaoAoiPoi, aoi_bid, node_id):
    """
    bool_exist_re
    是否node已经存在
    """
    sql = f"""
        select a.access_id
        from blu_access a inner join blu_access_gate_rel b
        on a.access_id = b.access_id
        where b.node_id='{node_id}'
    """
    pg.cursor_aoi.execute(sql)
    res = pg.cursor_aoi.fetchone()
    if res is None:
        return False
    return True


def get_gate_by_poly_wkt(pg: DaoAoiPoi, poly_wkt):
    """
    get_gate_by_poly_wkt
    """
    sql1 = f"""
       select node_id, st_astext(geom)
       from nav_node
       where st_contains(st_geomfromtext('{poly_wkt}', 4326), geom)
    """
    pg.cursor_road.execute(sql1)
    node_res = pg.cursor_road.fetchall()

    node_result = []
    for node in node_res:
        sql2 = f"""
            select *
            from nav_gate where node_id='{node[0]}'
        """
        pg.cursor_road.execute(sql2)
        gate_res = pg.cursor_road.fetchone()
        if gate_res is not None:
            node_result.append(node)

    return node_result


def get_exp_traj(pg: DaoMis, bid):
    """
    get_exp_traj
    """
    uid = get_uid_by_bid(pg, bid)
    sql = f"""
        select st_astext(end_track_line), route_end_percentage
        from exp_traj_monthly where end_poi_uid = '{uid}'
    """
    pg.cursor_mis.execute(sql)
    res = pg.cursor_mis.fetchall()

    results = []
    if len(res) == 0:
        return results, uid
    max_item = max(res, key=lambda x: x[1])
    results.append(shapely.wkt.loads(max_item[0]))
    return results, uid


def get_uid_by_bid(pg: DaoMis, bid):
    """
    get_uid_by_bid
    """
    sql = f"select uid from bid_uid_trans where bid = '{bid}'"
    pg.cursor_mis.execute(sql)
    res = pg.cursor_mis.fetchone()
    if res is None:
        return None
    return res[0]


@desc("获取数据")
def get_all_aoi_infos(ctx: Context, proceed):
    """
    get_all_aoi_infos
    """
    if ctx.enableed_bid:
        # 跑指定case
        bids_list = [{"bid": bid} for bid in ctx.enableed_bid]
        bids = pd.DataFrame(bids_list)
    else:
        bids = csv_tools.read_csv_as_dataframe(bid_file, self_headers={"bid": str})
    batch_size = 2000
    groups = np.array_split(bids, len(bids) // batch_size + 1)
    for group in tqdm.tqdm(groups, desc='加载批量数据'):
        try:
            with DaoAoiPoi(poi_cfg_name=DaoAoiPoi.POI_CFG_SLAVE) as dao:
                for idx, row in tqdm.tqdm(group.iterrows(), total=len(group), leave=False):
                    sql = f"""
                                select b.poi_bid, st_astext(a.geom), a.face_id
                                from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
                                where a.kind != '52' and a.aoi_level = 2 and b.poi_bid = %s
                            """
                    dao.cursor_aoi.execute(sql, [str(row['bid'])])
                    res = dao.cursor_aoi.fetchone()
                    if not res:
                        continue
                    poi_info = dao.get_poi_info_by_bid(res[0])
                    if not poi_info:
                        continue
                    a = AoiInfo(poi_bid=res[0], aoi_geom=res[1], face_id=res[2], poi_std_tag=poi_info.std_tag,
                                poi_name=poi_info.name, poi_geom=poi_info.geom)
                    ctx.infos.append(a)
        except Exception as e:
            print(f"批次执行失败：{e.args},暂时不影响后续处理")
            print(e)
    proceed()


def load_aoi():
    """
    加载aoi
    :return:
    """
    lines = []
    with DaoAoiPoi(poi_cfg_name=DaoAoiPoi.POI_CFG_SLAVE) as dao:
        sql = f"""
            select b.poi_bid
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id 
            where a.kind != '52' and a.aoi_level = 2 and a.src != 'SD'
        """
        df = pd.read_sql(sql, con=dao.conn_aoi)
    batch = 10000
    groups = np.array_split(df, len(df) // batch + 1)
    for group in tqdm.tqdm(groups, desc='加载批量数据'):
        try:
            with DaoAoiPoi(poi_cfg_name=DaoAoiPoi.POI_CFG_SLAVE) as dao:
                for idx, item in tqdm.tqdm(group.iterrows(), leave=False):
                    poi_info = dao.get_poi_info_by_bid(item.poi_bid)
                    if not poi_info or '加油' in poi_info.std_tag or '交通' in poi_info.std_tag:
                        continue
                    lines.append({
                        "bid": poi_info.bid,
                    })
        except Exception as e:
            print(f"批次执行失败：{e.args},暂时不影响后续处理")
    csv_tools.list_map_to_csv(lines, bid_file)


if __name__ == "__main__":
    """
    main
    """
    load_aoi()
    try:
        run()
    except Exception as e:
        traceback.print_exc()
        raise e
