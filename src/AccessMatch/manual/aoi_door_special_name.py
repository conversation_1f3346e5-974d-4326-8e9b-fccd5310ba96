"""
aoi大门特殊名称挖掘
基于竞品+ugc给的无主点的门

这里只关注 x号门
"""
import dataclasses
import datetime
import os.path
import sys
import argparse
from pathlib import Path
import pandas as pd
import shapely.wkt
from src.common import pgsql
from src.door_naming.strategy_v2 import common
import tqdm
from shapely.ops import nearest_points
from shapely.geometry import Point, Polygon, LineString
from src.AccessMatch.manual import aoi_qb_utils

today = datetime.date.today().strftime("%Y%m%d")
WORK_DIR = Path(os.path.abspath(__file__)).parent / "aoi_door_special_name" / today
WORK_DIR.mkdir(parents=True, exist_ok=True)
WORK_FILE = WORK_DIR / "result.csv"
from multiprocessing import Pool, Manager

# 这个策略暂时只针对这些大类,之后再扩充
enable_tag = ['房地产', '医疗', '教育', '公司', '政府']

# 二门以下自动上线tag
auto_tags_2_doors = ("购物,丽人,汽车服务,文化传媒,购物;百货商场,丽人;美发,"
                     "汽车服务;汽车检测场,文化传媒;广播电视,购物;便利店,丽人;美容,"
                     "汽车服务;汽车美容,文化传媒;美术馆,购物;超市,丽人;美体,汽车服务;汽车配件,文化传媒;其他,购物;家电数码,美食,"
                     "汽车服务;汽车维修,文化传媒;文化宫,购物;市场,美食;中餐厅,汽车服务;汽车销售,文化传媒;新闻出版,行政区划,"
                     "美食;茶座,汽车服务;汽车租赁,文化传媒;艺术团体,行政区划;其他,美食;蛋糕甜品店,生活服务,文化传媒;展览馆,"
                     "行政地标,美食;酒吧,生活服务;殡葬服务,行政地标;其他,行政地标;其他,美食;咖啡厅,生活服务;宠物服务,休闲娱乐,"
                     "酒店;星级酒店,美食;其他,生活服务;房产中介机构,休闲娱乐;ktv,美食;外国餐厅,生活服务;家政服务,"
                     "休闲娱乐;电影院,生活服务;其他,休闲娱乐;度假村,生活服务;图文快印店,休闲娱乐;歌舞厅,"
                     "生活服务;维修点,休闲娱乐;剧院,休闲娱乐;农家院,休闲娱乐;其他").split(",")
# 三门以下自动上线
auto_tags_3_doors = "文化传媒;展览馆,休闲娱乐;剧院,运动健身;运动健身;体育场馆".split(',')
# 无限制门自动上线

enable_tag = auto_tags_3_doors + auto_tags_2_doors + enable_tag
ACCESS_CHECK_DIS = 30 * 1.1


@dataclasses.dataclass
class Result:
    """
    结果集
    """
    geom: str
    name: str
    from_type: str
    tag: str
    bid: str = ''
    msg: str = ''
    near_access_dis: float = ACCESS_CHECK_DIS
    near_access_id: str = ''
    near_access_name: str = ''
    near_access_geom: str = ''
    near_access_poi_bid: str = ''
    near_node_dis: float = ACCESS_CHECK_DIS
    near_node_id: str = ''
    method_type: str = ''


@dataclasses.dataclass
class Aoi:
    """
    aoi
    """
    main_bid: str
    main_name: str
    std_tag: str
    aoi_geom: str
    click_pv: int
    special_poi: list[aoi_qb_utils.SpecialUgcPoi] = dataclasses.field(default_factory=list)
    special_intelligence: list[aoi_qb_utils.SpecialIntelligence] = dataclasses.field(default_factory=list)
    accesses: list[aoi_qb_utils.Access] = dataclasses.field(default_factory=list)
    exists_qb: list[aoi_qb_utils.InQbAccess] = dataclasses.field(default_factory=list)
    # 附近的大门
    nearbyGates: list[aoi_qb_utils.NearbyGate] = dataclasses.field(default_factory=list)
    result: list[Result] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class Ctx:
    """
    ctx
    """
    target_bid_list: list[str] = dataclasses.field(default_factory=list)
    taret_bid_file: str = ''
    jp_diff_days: int = 90
    aoi_list: list[Aoi] = dataclasses.field(default_factory=list)


def bool_enable_tag(tag):
    """
    是否是符合要求的tag
    :param tag:
    :return:
    """
    for e in enable_tag:
        if e in tag:
            return True
    return False


def check_has_add(aoi: Aoi, geom):
    """
    # 这个点是否已经加入任务, 避免重复处理
    :param aoi:
    :param geom:
    :return:
    """
    dis = 25
    for item in aoi.result:
        if shapely.wkt.loads(item.geom).distance(shapely.wkt.loads(geom)) * 1.1 * 1e5 < dis:
            return True
    return False


def check_relation(aoi: Aoi, cur_result: Result):
    """
    检查20米内是否有大门,有则判断是否关联,没有则生成新增任务
    去除跨高等级路的数据
    :param aoi:
    :param cur_result:
    :return:
    """
    # 已经有语义化关联
    if cur_result.near_access_id != '':
        return
    for gate in aoi.nearbyGates:
        dis = shapely.wkt.loads(gate.node_geom).distance(shapely.wkt.loads(cur_result.geom)) * 1.1 * 1e5
        if dis < cur_result.near_node_dis:
            cur_result.near_node_dis = dis
            cur_result.near_node_id = gate.node_id

    if cur_result.near_node_id != '':
        cur_result.msg = '疑似缺关联'
        cur_result.method_type = aoi_qb_utils.QB_TYPE_DOOR_RELATION
    else:
        cur_result.msg = '疑似缺门'
        cur_result.method_type = aoi_qb_utils.QB_TYPE_DOOR_MISS


def cross_high_road(aoi: Aoi, geom):
    """
    检查是否跨高等级路
    :param aoi:
    :param geom:
    :return:
    """
    if shapely.wkt.loads(aoi.aoi_geom).contains(shapely.wkt.loads(geom)):
        return False
    else:
        # 找到点到面最近的两个点
        p1, p2 = nearest_points(shapely.wkt.loads(aoi.aoi_geom), shapely.wkt.loads(geom))
        # 创建一条从点到面的最短线
        line = LineString([p1, p2])
        sql = f"with line as (select st_geomfromtext('{line.wkt}',4326) as geom_line)" \
              f" select link_id from nav_link, line where kind < 8 and st_intersects(geom, line.geom_line)"
        ret = pgsql.query_one(pgsql.ENGINE_ROAD, sql)
        if ret:
            print(f"点跨高等级路:{line.wkt},忽略")
            return True
    return False


def match(aoi: Aoi):
    """
    匹配策略
    :param aoi:
    :return:
    """
    if len(aoi.special_intelligence) > 0:
        for sp in aoi.special_intelligence:
            # 去重
            if check_has_add(aoi, sp.geom):
                continue
            if cross_high_road(aoi, sp.geom):
                continue
            result = Result(
                name=sp.name,
                geom=sp.geom,
                tag=sp.tag,
                from_type=aoi_qb_utils.FROM_TYPE_INTELLIGENCE,
            )
            for acc in aoi.accesses:
                dis = shapely.wkt.loads(acc.geom).distance(shapely.wkt.loads(sp.geom)) * 1.1 * 1e5
                if dis < result.near_access_dis:
                    result.near_access_dis = dis
                    result.near_access_id = acc.access_id
                    result.near_access_geom = acc.geom
                    result.near_access_name = acc.name
                    result.near_access_poi_bid = acc.access_poi_bid
                    if '号门' not in acc.name:
                        result.msg = '附近有非特殊名称的门,下发人工'
                        result.method_type = aoi_qb_utils.QB_TYPE_ACCESS_NAME
                    else:
                        result.msg = '附近的出入口已经是特殊名称的门'
                        result.method_type = aoi_qb_utils.QB_TYPE_NOTHING
            # 检查关联关系和大门
            check_relation(aoi, result)
            aoi.result.append(result)

    if len(aoi.special_poi) > 0:
        # 附近的ugc
        for sp in aoi.special_poi:
            # 去重
            if check_has_add(aoi, sp.geom):
                continue
            if cross_high_road(aoi, sp.geom):
                continue
            result = Result(
                name=sp.name,
                geom=sp.geom,
                tag=sp.std_tag,
                from_type=aoi_qb_utils.FROM_TYPE_UGC,
                bid=sp.bid,
            )
            for acc in aoi.accesses:
                dis = shapely.wkt.loads(acc.geom).distance(shapely.wkt.loads(sp.geom)) * 1.1 * 1e5
                if dis < result.near_access_dis:
                    result.near_access_dis = dis
                    result.near_access_id = acc.access_id
                    result.near_access_geom = acc.geom
                    result.near_access_name = acc.name
                    result.near_access_poi_bid = acc.access_poi_bid
                    if '号门' not in acc.name:
                        result.msg = '附近有非特殊名称的门,下发人工'
                        result.method_type = aoi_qb_utils.QB_TYPE_ACCESS_NAME
                    else:
                        result.msg = '附近的出入口已经是特殊名称的门'
                        result.method_type = aoi_qb_utils.QB_TYPE_NOTHING
            check_relation(aoi, result)
            aoi.result.append(result)


def handle(params):
    """
    处理任务
    :param params 参数 第一个是bid, 第二个是result_list
    :return:
    """
    try:
        bid, result_list = params
        poi_info = common.get_poi_info_by_bid(bid)
        aoi_info = common.get_aoi_by_main_bid(bid)
        if not poi_info or not aoi_info or aoi_info.src == 'SD' or aoi_info.aoi_level != 2:
            return
        if not bool_enable_tag(poi_info.std_tag):
            return
        aoi = Aoi(
            main_bid=bid,
            main_name=poi_info.name,
            aoi_geom=aoi_info.geom,
            std_tag=poi_info.std_tag,
            click_pv=poi_info.click_pv,
        )
        # 加载竞品数据
        aoi.special_intelligence = aoi_qb_utils.load_intelligence(aoi.main_bid, aoi.main_name, True)
        # 加载ugc数据
        aoi.special_poi = aoi_qb_utils.load_ugc_poi(aoi.aoi_geom, aoi.main_name, True)

        # 加载已经绑定的出入口
        aoi.accesses = aoi_qb_utils.load_access(bid)
        # 加载aoi附近的大门
        aoi.nearbyGates = aoi_qb_utils.load_covered_gate(aoi.aoi_geom)
        # 加载已经存在的情报数据,避免重复下发
        # 开始匹配
        match(aoi)
        result_list.append(aoi)
    except:
        pass


def handle_multiprocess(ctx: Ctx, process_num):
    """
    采用多进程跑
    :param ctx:
    :param process_num:
    :return:
    """

    bid_list = ctx.target_bid_list
    with Pool(process_num) as p, Manager() as m:
        # 构建一个可在多进程传递参数的list, 用于参数的整合
        result_list = m.list()

        def build_params():
            for bid in bid_list:
                yield bid, result_list

        # 多进程执行任务
        _ = list(tqdm.tqdm(p.imap_unordered(handle, build_params()), total=len(bid_list), desc="多进程执行"))

        for aoi in result_list:
            ctx.aoi_list.append(aoi)


def export(ctx: Ctx):
    """
    导出结果
    :param ctx:
    :return:
    """
    lines = []
    for aoi in ctx.aoi_list:
        for r in aoi.result:
            tmp = dataclasses.asdict(aoi)
            del tmp['aoi_geom']
            del tmp['special_poi']
            del tmp['special_intelligence']
            del tmp['accesses']
            del tmp['result']
            del tmp['nearbyGates']
            tmp.update(dataclasses.asdict(r))
            lines.append(tmp)
    pd.DataFrame(lines).to_csv(WORK_FILE, sep='\t', index=False)


def fill_bids(ctx, bid_type='jp'):
    """
    填充需要处理的bid
    :param ctx:
    :param bid_type:
    :return:
    """
    bid_all = []
    if 'file' in bid_type:
        if ctx.taret_bid_file:
            with open(ctx.taret_bid_file) as f:
                target_bid_list = [x.strip() for x in f]
                bid_all.extend(target_bid_list)
    if 'jp' in bid_type:
        jp_bids = fill_bids_jp(ctx)
        bid_all.extend(jp_bids)
    return bid_all


def fill_bids_jp(ctx: Ctx):
    """
    添加竞品情报
    :return:
    """
    sql = "select distinct(aoi_bid) as bid from apartment_intelligence" \
          f" where updated_at > now() - interval '{ctx.jp_diff_days} days'"
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
    if ret:
        return [x[0] for x in ret]


def save_db(ctx: Ctx):
    """
    保存结果到数据库
    暂时只存储竞品名称核实类型的任务
    :param ctx:
    :return:
    """
    # 这里为了方便面调试,直接读取本地文件
    df = pd.read_csv(WORK_FILE, sep='\t', converters={"main_bid": str, 'near_node_id': str})

    df_access_name = df[df['method_type'] == 'access_name']
    if len(df_access_name) < 0:
        print('没有出入口名称的情报')
    else:
        batch_id = f'出入口竞品diff名称核实_{today}'
        for i, row in tqdm.tqdm(df_access_name.iterrows(), total=len(df_access_name), desc='竞品名称核实'):
            aoi_qb_utils.add_qb_access_name(
                row['main_bid'],
                row['near_access_id'],
                row['geom'],
                row['name'],
                batch_id,
            )


def run(args):
    """
    执行
    :return:
    """
    ctx = Ctx()
    task_type = args.task_type
    if task_type == 'cron':
        ctx.jp_diff_days = args.jp_diff_day
        ctx.target_bid_list = fill_bids(ctx, bid_type='jp')
        process_num = args.process_num
        # 开多进程前需要清空所有链接
        pgsql.clear_all_engine()
        handle_multiprocess(ctx, process_num)
        export(ctx)
    else:
        # 开一个进程执行一次
        ctx.target_bid_list = ['1263149503796477951']
        pgsql.clear_all_engine()
        handle_multiprocess(ctx, 1)
        export(ctx)

    pgsql.clear_all_engine()
    save_db(ctx)


if __name__ == '__main__':
    # 创建一个解析对象
    parser = argparse.ArgumentParser(description="任务处理")

    # 添加你想要的参数
    parser.add_argument("--process_num", default=30, type=int, help="进程数.")
    parser.add_argument("--task_type", default="once", type=str, help="任务类型: once|cron.")
    parser.add_argument("--jp_diff_day", default=90, type=int, help="竞品diff天数.")
    __args = parser.parse_args()

    run(__args)
