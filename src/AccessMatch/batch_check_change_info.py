# !/usr/bin/env python3
# encoding=utf-8
"""
每天例行检查情报diff表是否产出正常，如果失败，重试执行
"""
import sys
import os
import time
import subprocess

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")

from common import common_tool
from pg_tool import PgTool


def process():
    """
    :return: void
    """
    healthy = bool_healthy()
    if not healthy:
        retry()


def bool_healthy():
    """
    判断情报DIFF脚本是否健康运行，非健康状态，重试一次
    """
    with PgTool() as pg:
        date = time.strftime('%Y%m%d', time.localtime(time.time()))
        gate_table_name = f"gate_trigger_{date}_online"
        aoi_table_name = f"aoi_trigger_{date}_online"

        gate_sql = f"select * from pg_tables where tablename='{gate_table_name}'"
        pg.cursor_trajectory.execute(gate_sql)
        gate_res = pg.cursor_trajectory.fetchone()
        if gate_res is None:
            print("例行检查情报diff脚本-大门表异常：", date, )
            return False

        aoi_sql = f"select * from pg_tables where tablename='{aoi_table_name}'"
        pg.cursor_trajectory.execute(aoi_sql)
        aoi_res = pg.cursor_trajectory.fetchone()
        if aoi_res is None:
            print("例行检查情报diff脚本-AOI表异常：", date, )
            return False

        print("例行检查情报diff脚本-正常：", date, )
        return True


def retry():
    """
    diff脚本失败，重试
    """
    date = time.strftime('%Y%m%d', time.localtime(time.time()))
    ret = subprocess.run(['ssh', '<EMAIL>',
                          'cd /home/<USER>/wangchunping/aoi_gate_relate_online/trajectory_relate',
                          'sh update_table.sh >> update_table.log 2>&1'])
    code = ret.returncode
    if code != 0:
        raise Exception('diff脚本失败，重试失败')
    else:
        print("情报diff脚本-重试：", date)
        common_tool.send_hi("情报diff脚本-重试一次", ['lifan14'])


if __name__ == "__main__":
    process()
