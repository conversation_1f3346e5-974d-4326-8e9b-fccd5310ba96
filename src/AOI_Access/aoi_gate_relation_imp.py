# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门,进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42

非常重要！！！
如果是批处理产生的数据, 表gate_bid_change_history中resource一定要赋值6, 否则赋值为1
"""

import psycopg2
import ConfigParser
import os
import urllib2
import json


def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../conf/config.ini')
dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()


def judge_relation_exists(bid, node_id):
    """
    判断bid和node_id是否已经关联
    """
    sql = """
        select
            count(*)
        from 
            blu_access a
            inner join 
            blu_access_gate_rel b 
        on 
            a.access_id = b.access_id
        where
            main_bid = %s
            and b.node_id = %s
    """
    cursor_aoi.execute(sql, [bid, node_id])
    num = cursor_aoi.fetchone()[0]
    sql = """
        select 
            action
        from gate_bid_change_history
        where 
            bid = %s
            and node_id = %s
            and imp_state = 0
            and (
                    action = 'add'
                or 
                    action = 'delete')
        order by
            id desc
        limit 1
    """
    cursor_poi.execute(sql, [bid, node_id])
    res_to_imp = cursor_poi.fetchone()
    
    if res_to_imp is None:
        #没有待入库的关联关系
        if num > 0:
            return True
        else:
            return False
    else:
        #有待入库的关联关系
        if res_to_imp[0] == 'delete':
            return False
        if res_to_imp[0] == 'add':
            return False
    return True

def run(filename):
    """
    读物文件的bid和node，手动入库；
    注意区分数据来源是批处理还是人工
    """
    bid_dict = dict()
    with open(filename, "r") as f:
        lines = f.readlines()
        for line in lines:
            data = line.strip().split('\t')
            bid = data[0]
            node_id = data[1]
            if bid in bid_dict:
                bid_dict[bid].append(node_id)
            else:
                bid_dict[bid] = [node_id]
    
    for bid in bid_dict:
        sql = """
            select 
                a.face_id 
            from 
                blu_face a 
                inner join 
                blu_face_poi b
            on 
                a.face_id = b.face_id
            where 
                poi_bid = %s
        """
        cursor_aoi.execute(sql, [bid])
        res = cursor_aoi.fetchone()
        if res is None:
            continue
        face_id = res[0]
        node_list = bid_dict[bid]
        for node_id in node_list:
            if not judge_relation_exists(bid, node_id):
                #如果数据是人工作业的成果resource应该改成1，因为6代表批处理能关联上，
                # 错误标记成6有可能会在后续批处理的时候被删除
                sql = """
                    insert into 
                        gate_bid_change_history(
                            bid, 
                            node_id,
                            face_id,
                            action,
                            imp_state,
                            src,
                            flow_state,
                            resource
                        )
                    values(
                        %s,
                        %s,
                        %s,
                        'add',
                        0,
                        '批处理数据手动入库',
                        '待入库',
                        6
                    )                
                """
                cursor_poi.execute(sql, [bid, node_id, face_id])
        
    conn_poi.commit()
    conn_poi.close()
    conn_aoi.close()
    conn_road.close()

if __name__ == "__main__":
    filename = ""
    run(filename)