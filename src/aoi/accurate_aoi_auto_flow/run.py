# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AOI情报自动流转闭环
"""
import json
import os
import sys
import uuid
from datetime import datetime

import requests as requests
import tqdm as tqdm

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi import PgQuery
from aoi import PgUpdate
from aoi.aoi_complete import accurate_aoi_info

pg_query_dao = PgQuery.PgQuery()
pg_rw_dao = PgUpdate.PgUpdate()

# 1.0闭环情报路径
ACCURATE_V1_INFO_PATH = "/home/<USER>/aoi-strategy/log/base_data/pv_log/" + datetime.now().strftime("%Y-%m-%d") + "_v1.log"

QB_STATUS_INIT = "INIT"
QB_STATUS_AOI_PENDING = "AOI_PENDING"
QB_STATUS_GATE_PENDING = "GATE_PENDING"
QB_STATUS_GATE_RELATION_PENDING = "GATE_RELATION_PENDING"
QB_STATUS_END = "END"

FLOW_SRC_GAET_RELATION = "2"
FLOW_SRC_AOI = "4"
FLOW_SRC_GATE = "5"

# AOI精准等级
AOI_LEVEL_AGG = 1
AOI_LEVEL_BASIC = 2
AOI_LEVEL_INNER = 3


class AccurateAoiAutoFlow(object):
    """
    1.0闭环信息自动流转
    """

    def __init__(self, bid):
        self.bid = bid
        self.flow_src = {}
        self.src = ""
        self.aoi_level = 0
        self.agg_aoi_son_bid_list = []

    def cal_close_section(self):
        """
        计算待闭环的环节
        Args:
            bid:

        Returns:

        """
        aoi_info = pg_query_dao.get_aoi_by_bid(self.bid)
        # 边框不存在则下发边框-大门-关联关系
        if not aoi_info:
            self.flow_src = {FLOW_SRC_AOI: FLOW_SRC_GATE, FLOW_SRC_GATE: FLOW_SRC_GAET_RELATION}
            self.src = int(FLOW_SRC_AOI)
            return
        self.aoi_level = aoi_info[3]
        # 聚合院落只下发边框
        if aoi_info[3] == 1:
            self.src = int(FLOW_SRC_AOI)
            self.cal_agg_aoi_info()
            self.flow_src = {FLOW_SRC_AOI: "0"}
            return
        # 判断是否存在待关联的大门
        exist_wait_relation_gate = accurate_aoi_info.get_aoi_level_by_bid([self.bid])
        if len(exist_wait_relation_gate) > 0:
            self.src = int(FLOW_SRC_GAET_RELATION)
            self.flow_src = {FLOW_SRC_GAET_RELATION: "0"}
        else:
            self.flow_src = {FLOW_SRC_GATE: FLOW_SRC_GAET_RELATION}
            self.src = int(FLOW_SRC_GATE)

    def cal_agg_aoi_info(self):
        """
        聚合院落情报下发计算,如果缺下发聚合院落补框, 不缺框则下发子AOI精准化
        Returns:

        """
        aoi_list = pg_query_dao.get_agg_aoi(self.bid)
        if not aoi_list:
            self.src = int(FLOW_SRC_AOI)
            return
        son_face_id_list = [aoi[3] for aoi in aoi_list if aoi[3] is not None]
        if len(son_face_id_list) == 0:
            self.src = int(FLOW_SRC_AOI)
            return
        # 查询聚合院落子集信息
        son_aoi_list = pg_query_dao.get_aoi_complete_by_face_id_list(list(set(son_face_id_list)))
        if not son_aoi_list:
            self.src = int(FLOW_SRC_AOI)
            return
        basic_aoi_area = 0
        bid_complete_map = {}
        basic_aoi_num = 0
        accurate_basic_aoi_num = 0
        no_accurate_bid = []
        for son_aoi in son_aoi_list:
            face_id = son_aoi[0]
            bid = son_aoi[1]
            aoi_complete = son_aoi[2]
            aoi_level = son_aoi[4]
            if aoi_level == AOI_LEVEL_BASIC:
                basic_aoi_area += son_aoi[3]
                basic_aoi_num += 1
            if bid is not None:
                bid_complete_map[bid] = aoi_complete
            else:
                bid_complete_map[face_id] = aoi_complete
            if aoi_complete is not None and aoi_level == AOI_LEVEL_BASIC and aoi_complete >= 3:
                accurate_basic_aoi_num += 1
            if aoi_complete and aoi_complete < 3 and aoi_level == AOI_LEVEL_BASIC and bid:
                no_accurate_bid.append(bid)

        # 判断非精准原因
        if float(basic_aoi_area) / aoi_list[0][4] < 0.7:
            self.src = int(FLOW_SRC_AOI)
            return
        self.agg_aoi_son_bid_list = no_accurate_bid


def get_wait_close_info_list():
    """
    1.1获取待闭环情报
    Returns:
    """
    close_info = {}
    with open(ACCURATE_V1_INFO_PATH, 'r') as f:
        for line in f.readlines():
            line = line.strip()
            if not line or line == "":
                continue
            data = line.split("\t")
            if data[2] != '1' or int(data[3]) > 2:
                continue
            close_info[data[0]] = {
                "aoi_complete": data[3],
                "category": data[5]
            }
    return close_info


def auto_flow_info_generate():
    """
    1.0待闭环情报产出
    Returns:

    """
    # 获取待闭环情报信息
    wait_close_info_list = get_wait_close_info_list()
    if len(wait_close_info_list) == 0:
        return
    wait_close_bid_list = [bid for bid, info in wait_close_info_list.items()]

    # 查询闭环中的情报清单
    closing_bid_list = []
    closing_info_list = pg_query_dao.get_accurate_aoi_close_info_by_bids(wait_close_bid_list)
    if closing_info_list:
        closing_bid_list = [item[0] for item in closing_info_list if item[1] != QB_STATUS_END]

    # 下发一体化
    for bid, info in tqdm.tqdm(wait_close_info_list.items()):
        if bid in closing_bid_list:
            continue
        accurate_aoi = AccurateAoiAutoFlow(bid)
        accurate_aoi.cal_close_section()
        req = {
            "from_src": "1.0闭环清单",
            "src": accurate_aoi.src,
            "ref_qb_id": str(uuid.uuid4()).replace("-", ""),
            "ref_qb_batch_id": datetime.now().strftime("%Y%m%d%H%M%S"),
            "qb_type": 2,
            "main_poi_bid": bid,
            "extra": {"flow": accurate_aoi.flow_src}
        }
        qb_id = ""
        # 如果是聚合院落且待闭环子点不为空则下发聚合院落子点信息
        if accurate_aoi.aoi_level == 1 and len(accurate_aoi.agg_aoi_son_bid_list) > 0:
            for agg_son_bid in accurate_aoi.agg_aoi_son_bid_list:
                if agg_son_bid in wait_close_bid_list:
                    continue
                req["main_poi_bid"] = agg_son_bid
                req["ref_qb_id"] = str(uuid.uuid4()).replace("-", "")
                req["extra"] = {"flow": {FLOW_SRC_AOI: FLOW_SRC_GATE, FLOW_SRC_GATE: FLOW_SRC_GAET_RELATION}}
                resp = integration_qb_req(req)
                if "data" in resp and "qb_id" in resp['data']:
                    qb_id = resp['data']['qb_id']
                aoi_info = pg_query_dao.get_aoi_by_bid(agg_son_bid)
                aoi_complete_info = pg_query_dao.get_blu_face_complete_by_bid(bid)
                accurate_info = {
                    "bid": agg_son_bid,
                    "qb_id": qb_id,
                    "aoi_complete": aoi_complete_info[1] if aoi_complete_info else 0,
                    "aoi_level": aoi_info[3] if aoi_info else 0,
                    "category": "",
                    "status": QB_STATUS_INIT,
                    "req": json.dumps(req),
                    "resp": json.dumps(resp),
                    "guid": req["ref_qb_id"]
                }
                pg_rw_dao.save_accurate_aoi_auto_flow(accurate_info)
            continue
        resp = integration_qb_req(req)
        if "data" in resp and "qb_id" in resp['data']:
            qb_id = resp['data']['qb_id']
        accurate_info = {
            "bid": bid,
            "qb_id": qb_id,
            "aoi_complete": info["aoi_complete"],
            "aoi_level": accurate_aoi.aoi_level,
            "category": info["category"],
            "status": QB_STATUS_INIT,
            "req": json.dumps(req),
            "resp": json.dumps(resp),
            "guid": req["ref_qb_id"]
        }
        pg_rw_dao.save_accurate_aoi_auto_flow(accurate_info)


def integration_qb_req(params):
    """
    一体化情报下发
    Args:
        params:

    Returns:

    """
    url = "http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2"
    headers = {
        "Content-Type": "application/json",
    }
    params_json = json.dumps(params)
    res = requests.post(url, data=params_json, headers=headers)
    if res.status_code == 200:
        return res.json()
    print(f"一体化情报下发失败,params:{params}, res:{res.text}")
    return {}


if __name__ == '__main__':
    auto_flow_info_generate()
