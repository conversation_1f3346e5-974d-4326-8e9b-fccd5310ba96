"""
流式任务自动化处理
"""
import json
import os
import sys
from pathlib import Path
import datetime
import tqdm
import numpy as np
import pandas as pd
import requests

root_dir = Path(os.path.abspath(__file__)).parents[4]
sys.path.insert(0, root_dir.as_posix())

from src.AccessMatch.high_pv_not_complete.flow import check_manual, gather_check, utils
from src.common import pgsql, mysql
from src.door_naming.strategy_v2 import common
# 发送风雷任务
from src.AccessMatch.manual import not_complete_door_traj_found, task_manual_fenglei, add_v1_batch_task, \
    apartment_intelligence_task

work_dir = Path(os.path.abspath(__file__)).parent / 'flow_data' / datetime.date.today().strftime("%Y%m%d")
work_dir.mkdir(exist_ok=True, parents=True)
file_name = work_dir / 'aoi_error_file_precise.txt'
today = datetime.datetime.today().strftime("%Y%m%d")

# ugc闭环的任务, 这里单独处理
ugc_file = Path(os.path.abspath(__file__)).parent / 'ugc_gather_completed.txt'

WHITE_TYPE_V1 = 'v1'
WHITE_TYPE_V2 = 'v2'

with open(ugc_file) as f:
    ugc_gather_bids = [x.strip() for x in f]

GATHER_MANUAL_WAIT = 1
GATHER_MANUAL_CREATED = 2
GATHER_MANUAL_HANDLED = 3


def try_download(date):
    """
    尝试下载数据
    :param date:
    :return:
    """
    date_str = date.strftime("%Y-%m-%d")
    data_url = f'http://mapde-poi.baidu-int.com/aoi_quality_check_result/aoi_result/' \
               f'aoi_error_file_precise_{date_str}.txt'
    resp = requests.get(data_url, stream=True)
    if file_name.exists():
        file_name.unlink()
    if resp.status_code == 200:
        with open(file_name, 'wb') as f:
            for chunk in resp.iter_content(1024 * 10 * 10):
                f.write(chunk)
    else:
        print("Failed to get file: status code {}".format(resp.status_code))


def load_data():
    """
    下载数据
    :return:
    """
    if file_name.exists():
        print(f"文件{file_name}已经存在,可直接使用")
        return
    # 优先使用今天的
    date1 = datetime.date.today()
    try_download(date1)
    if file_name.exists():
        print(f"文件{file_name}下载完成")
        return
    # 今天没有,使用昨天的
    date2 = datetime.date.today() - datetime.timedelta(days=1)
    try_download(date2)
    if file_name.exists():
        print(f"文件{file_name}下载完成")
        return


def convert_int(value):
    """
    字符串转整数
    :param value:
    :return:
    """
    try:
        return int(value)
    except ValueError:
        return -1


HANDLE_CODE = ['R0001', 'R0002', 'R0003']


def get_errtype(value):
    """
    错误类型处理
    :param value:
    :return:
    """
    for code in HANDLE_CODE:
        if code in value:
            return 'FLOW'
    if 'R0006' in value:
        return 'R0006'
    if 'R0007' in value:
        return 'R0007'
    return 'OTHER'


def get_not_completed_flow():
    """
    获取未闭环的任务,处理闭环相关的事情
    :return:
    """
    if not file_name.exists():
        raise Exception(f"文件不存在:{file_name}")

    df = pd.read_csv(file_name,
                     converters={"bid": str, 'err_type': get_errtype,
                                 'city': str, 'name': str,
                                 'aoi_level': convert_int, 'nav_pv': convert_int},
                     sep='\t')
    df = df[df['err_type'] == 'FLOW']
    df = df[['bid', 'name', 'city', 'aoi_level', 'nav_pv']].sort_values('nav_pv', ascending=False)
    # df[df['aoi_level'] == 2].to_csv(work_dir / '基础院落.tsv', sep='\t', index=False)
    # df[df['aoi_level'] == 1].to_csv(work_dir / '聚合院落.tsv', sep='\t', index=False)
    # df_gather = df[df['aoi_level'] == 1]
    df_common = df[df['aoi_level'] == 2]
    df_common['add_gate'] = ''
    df_common['v1_strategy'] = ''
    df_common['v1_manual'] = ''
    for idx, row in tqdm.tqdm(df_common.iterrows(), total=len(df_common), desc='处理基础院落'):
        bid = row['bid']
        add_gate, v1_strategy, v1_manual = check_manual.check_common_by_bid(bid)
        df_common.loc[idx, 'add_gate'] = add_gate
        df_common.loc[idx, 'v1_strategy'] = v1_strategy
        df_common.loc[idx, 'v1_manual'] = v1_manual
        utils.add_to_flow(bid, 2)
    df_common.to_csv(work_dir / '基础院落处理.tsv', sep='\t', index=False)


def auto_create_flow():
    """
    自动创建闭环流程
    :return:
    """
    load_data()
    get_not_completed_flow()


def handle_0007():
    """
    处理R0007
    :return:
    """
    # [R0007】母库不是精准化AOI，但是线上是精准化AOI
    load_data()
    if not file_name.exists():
        raise Exception(f"文件不存在:{file_name}")

    df = pd.read_csv(file_name,
                     converters={"bid": str, 'err_type': get_errtype,
                                 'city': str, 'name': str,
                                 'aoi_level': convert_int, 'nav_pv': convert_int},
                     sep='\t')
    df = df[df['err_type'] == 'R0007']
    df = df[['bid', 'aoi_complete', 'aoi_level', 'err_msg']]
    bids = df['bid'].tolist()
    poi_dict = common.multi_get_poi_info_by_bid(bids)
    nav_pv_dict = common.multi_get_pv_by_bid(bids)
    complete_dict = common.multi_get_aoi_complete(bids)
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df)):
        bid = row['bid']
        if bid in poi_dict:
            poi_info = poi_dict[bid]
            df.loc[idx, 'name'] = poi_info.name
            df.loc[idx, 'city'] = poi_info.city
            df.loc[idx, 'std_tag'] = poi_info.std_tag
            df.loc[idx, 'click_pv'] = poi_info.click_pv
        else:
            df.loc[idx, 'name'] = '----- 已失效 ----'
        df.loc[idx, 'nav_pv'] = nav_pv_dict.get(bid, 0)
        df.loc[idx, '现在的aoi_complete'] = complete_dict.get(bid, -1)
    df.to_csv(work_dir / 'R0007_export.tsv', sep='\t', index=False)


def handle_0006():
    """
    处理R0006
    :return:
    """
    # 【R0006】母库是精准化AOI，但是线上不是精准化AOI
    pass


def auto_found_door_task():
    """
    自动挖掘大门任务
    :return:
    """
    sql = 'select bid from ' \
          ' aoi_white_list_flow where aoi_level=2 and can_complete=0 and is_door_add_complete = 0' \
          '  limit 300 '
    wait_list = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
    if not wait_list:
        print("暂时没有任务需要处理")
        return
    bid_list = [x.bid for x in wait_list]
    ctx = not_complete_door_traj_found.Ctx(bid_list=bid_list)
    not_complete_door_traj_found.run(ctx)
    run_file = ctx.result_path
    task_manual_fenglei.run(run_file)
    print("开始添加1.0采集和竞品抓取任务")
    if len(ctx.bid_no_door) > 0:
        for build_bid in ctx.bid_no_door:
            apartment_intelligence_task.add_task(build_bid, apartment_intelligence_task.TYPE_DOOR,
                                                 apartment_intelligence_task.PRIORITY_HIGH)
    # 更新为挖掘成功
    print("更新大门挖掘任务成功")
    sql = 'update aoi_white_list_flow set is_door_add_complete=1 where bid in :bid_list'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple(bid_list)})


def create_v1_batch():
    """
    创建1.0批处理任务
    :return:
    """
    sql = 'select bid from ' \
          ' aoi_white_list_flow where aoi_level=2 and can_complete=0 and is_door_batch_complete = 0' \
          '  limit 600 '
    wait_list = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
    if not wait_list:
        print("暂时没有任务需要处理")
        return
    bid_add_list = []
    for wait in tqdm.tqdm(wait_list, desc='下发1.0关联匹配任务'):
        level = common.get_aoi_complete_level(wait.bid)
        if level >= 3:
            print(f"已经闭环:{wait.bid}")
            sql = 'update aoi_white_list_flow set can_complete=1 where bid in :bid_list'
            pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple([wait.bid])})
            continue
        bid_add_list.append(wait.bid)
        add_v1_batch_task.add_task(wait.bid)
    sql = 'update aoi_white_list_flow set is_door_batch_complete=1 where bid in :bid_list'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple(bid_add_list)})


def insert_white(bid, reason, white_type):
    """
    写入白名单表
    :param bid:
    :param reason:
    :param white_type:
    :return:
    """
    sql = 'select bid from aoi_whitelist where bid=:bid and type=:type'
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, "type": white_type})
    if ret:
        print("已经加入过白名单")
        return
    sql = 'insert into  aoi_whitelist  (bid, reason, type) values (:bid, :reason, :type)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, "reason": reason, "type": white_type})
    print(f"写入-{bid}-{reason}-{white_type}成功")


def check_gather_can_complete(bid, aoi_level, succ_rate=75):
    """
    检查聚合院落是否可闭环
    :param bid:
    :param aoi_level:
    :param succ_rate: 聚合院落面积占比
    :return:
    """
    if aoi_level != 1:
        raise Exception("check_gather_can_complete 不支持aoi_level 非1的数据")

    # 当前aoi_complete >=3 直接闭环
    current_level = common.get_aoi_complete_level(bid)
    if current_level >= 3:
        return True
    sub_aoi = []
    rate = gather_check.get_gather_common(bid, sub_aoi)
    # 下面判断是否闭环
    bid_complete = False
    if bid in ugc_gather_bids:
        # 判断子区域是否闭环
        bid_complete = True
    else:
        # 判断在25年2月1号后如果有作业任务
        date_start = '2025-02-01'
        sql = 'select bid, work_status, update_time from strategy_feature_list' \
              ' where strategy_type=37 and create_time >:ct1 and bid=:bid '
        manual_data = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, {"ct1": date_start, 'bid': bid})
        if manual_data:
            #  闭环三天后再处理,涉及poi回库
            if int(manual_data.work_status) == 3 and \
                    datetime.datetime.today() - manual_data.update_time > datetime.timedelta(days=3):
                bid_complete = True
            else:
                # 人工没做完,不处理
                return False
    if bid_complete or rate > succ_rate:
        # 已经下发人工处理了,人工处理过了,直接按照子区域闭环
        has_not_complete = False
        for item in sub_aoi:
            sub_poi = item.get('sub_poi:', '')
            sub_level = item.get('sub_aoi_level', 0)
            if sub_level != 2 or len(sub_poi) < 5:
                continue
            print(f"检测子区域是否处理: {bid} - {sub_poi}, {sub_level}")
            if not check_manual.check_common_can_complete(sub_poi, sub_level):
                utils.add_to_flow(sub_poi, sub_level)
                has_not_complete = True
        # 存在子区域是否存在任务
        if not has_not_complete:
            return True
    else:
        # 设置下发人工
        sql = 'update aoi_white_list_flow set is_gather_manual_complete=:d1 where bid=:bid'
        pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"d1": GATHER_MANUAL_WAIT, "bid": bid})
        return False
    return False


def check_common():
    """
    检查是否可直接闭环[基础院落]
    :return:
    """
    day_before_4 = datetime.date.today() - datetime.timedelta(days=3)
    sql = 'select bid,is_door_add_complete, is_door_batch_complete from aoi_white_list_flow' \
          ' where' \
          '   aoi_level=2 and can_complete=0 and is_sync = 0 and created_at < :day '
    wait_list = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"day": day_before_4})
    # 如果标记为 can_complete=1 直接闭环
    for wait in tqdm.tqdm(wait_list):
        bid = wait.bid
        if check_manual.check_common_can_complete(bid, 2):
            print(f"{bid} 可自动闭环")
            sql = 'update aoi_white_list_flow set  can_complete=1 where bid=:bid and can_complete!=1'
            pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid})
    # 同步任务
    sql = 'select bid from aoi_white_list_flow where can_complete=1 and is_sync=0 and aoi_level=2 '
    completed_ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
    completed_list = [x.bid for x in completed_ret]
    batch = 'batch_add_' + datetime.date.today().strftime("%Y%m%d")
    if len(completed_list) > 0:
        for completed_bid in completed_list:
            insert_white(completed_bid, batch, WHITE_TYPE_V1)
        sql = 'update aoi_white_list_flow set is_sync=1 where bid in :bid_list and is_sync=0 '
        pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple(completed_list)})


def check_gather():
    """
    1、子区域面积相加时，子区域面积相加（一级tag和聚合一致相加），相加后面积≥70%的，分别闭环子区域，
        子区域全闭环则聚合院落全闭环（避免层级关联错误）
    2、子区域主点和聚合区域一级tag一致的相加后，面积≤75%，下发37 (暂时先不下发,先存着)
    3. 若人工做完后子区域面积相加≥70%（此时不判断tag，避免tag错误），
        按照闭环处理。若人工做完一轮后面积≤70%，按照无法闭环处理（分子分母剔除）。
    :return:
    """
    day_before_4 = datetime.date.today() - datetime.timedelta(days=4)
    sql = 'select bid from aoi_white_list_flow' \
          ' where' \
          '   aoi_level=1 and can_complete=0 and is_sync = 0 and created_at < :day limit 10 '
    wait_list = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"day": day_before_4})
    # 如果标记为 can_complete=1 直接闭环
    for wait in tqdm.tqdm(wait_list):
        bid = wait.bid
        if check_gather_can_complete(bid, 1):
            print(f"{bid} 可自动闭环")
            sql = 'update aoi_white_list_flow set  can_complete=1 where bid=:bid and can_complete!=1'
            pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid})
    # 同步任务
    sql = 'select bid from aoi_white_list_flow where can_complete=1 and is_sync=0 and aoi_level=1 '
    completed_ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
    completed_list = [x.bid for x in completed_ret]
    batch = 'batch_add_' + datetime.date.today().strftime("%Y%m%d")
    if len(completed_list) > 0:
        for completed_bid in completed_list:
            insert_white(completed_bid, batch, WHITE_TYPE_V1)
        sql = 'update aoi_white_list_flow set is_sync=1 where bid in :bid_list and is_sync=0 '
        pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql, {"bid_list": tuple(completed_list)})


def special_city_data_to_flow():
    """
    上海特殊名称闭环
    :return:
    """
    lines_all = []
    sql = 'select main_bid from blu_face_complete '
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql)
    groups = np.array_split(ret, len(ret) // 1000 + 1)
    for group in tqdm.tqdm(groups):
        # 一组数据
        main_bid_list = []
        for (main_bid,) in group:
            main_bid_list.append(main_bid)
        poi_all = common.multi_get_poi_info_by_bid(main_bid_list)
        pv_all = common.multi_get_pv_by_bid(main_bid_list)
        aoi_info = common.multi_get_aoi_by_main_bid(main_bid_list)
        for main_bid in main_bid_list:
            if main_bid in pv_all and main_bid in poi_all and main_bid in aoi_info:
                pv = pv_all[main_bid]
                poi_detail = poi_all[main_bid]
                # 暂时只增加上海市pv10到30的闭环流程
                if poi_detail.city == '上海市' and 30 > pv >= 10 and aoi_info[main_bid].aoi_level == 2:
                    lines_all.append({"main_bid": main_bid})
                    utils.add_to_flow(main_bid, aoi_info[main_bid].aoi_level)


# 任务入口
FUNC_MAP = {
    "create_flow": auto_create_flow,
    'handle_0006': handle_0006,
    'handle_0007': handle_0007,
    "found_door": auto_found_door_task,
    "create_v1_batch": create_v1_batch,
    'check_common': check_common,
    "check_gather": check_gather,
    "special_city_flow": special_city_data_to_flow,
}

if __name__ == '__main__':
    args = sys.argv
    if len(args) > 1:
        action = args[1]
        if not action or action not in FUNC_MAP:
            print("无效操作")
        else:
            FUNC_MAP[action]()
