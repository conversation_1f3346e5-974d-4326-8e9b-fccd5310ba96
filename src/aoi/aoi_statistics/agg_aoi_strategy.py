# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚合院落策略
"""
import copy

import json
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi import PgQuery

dao = PgQuery.PgQuery()


def agg_aoi_statistics(bid_file, result_file):
    """
    聚合院落AOI统计
    :return:
    """
    number = 0
    statistics_data_list = []
    with open(bid_file, 'r') as file_data:
        statistics_map = {"agg_aoi_bid": "", "agg_aoi_tag": "", "son_aoi_info": "", "accurate_aoi_rate": "",
                          "is_accurate_aoi": "否"}
        for line_data in file_data:
            number += 1
            print(f"当前进度：{number}, 成果：{len(statistics_data_list)}")
            temp_statistics_map = copy.deepcopy(statistics_map)
            line_data = line_data.strip("\n")
            line_data = line_data.strip("\r")
            items = line_data.split("\t")
            bid = items[0]
            aoi_list = dao.get_agg_aoi(bid)
            if aoi_list is None or len(aoi_list) == 0:
                continue
            temp_statistics_map['agg_aoi_bid'] = bid

            # 查询POI信息
            poi_info = dao.get_poi_by_bid(bid)
            if poi_info is None:
                continue
            temp_statistics_map['agg_aoi_tag'] = poi_info[0]

            son_face_id_list = [aoi[3] for aoi in aoi_list if aoi[3] is not None]
            if len(son_face_id_list) == 0:
                continue
            # 查询聚合院落子集信息
            son_aoi_list = dao.get_aoi_complete_by_face_id_list(list(set(son_face_id_list)))
            if son_aoi_list is None or len(son_aoi_list) == 0:
                continue
            basic_aoi_area = 0
            bid_complete_map = {}
            basic_aoi_num = 0
            accurate_basic_aoi_num = 0
            for son_aoi in son_aoi_list:
                face_id = son_aoi[0]
                bid = son_aoi[1]
                aoi_complete = son_aoi[2]
                aoi_level = son_aoi[4]
                if aoi_level == 2:
                    basic_aoi_area += son_aoi[3]
                    basic_aoi_num += 1
                if bid is not None:
                    bid_complete_map[bid] = aoi_complete
                else:
                    bid_complete_map[face_id] = aoi_complete
                if aoi_level == 2 and aoi_complete in (3, 4) and son_aoi[5] != 'SD':
                    accurate_basic_aoi_num += 1
            # 判断是否精准
            if float(basic_aoi_area) / aoi_list[0][4] > 0.7 and accurate_basic_aoi_num == basic_aoi_num:
                temp_statistics_map["is_accurate_aoi"] = "是"
            temp_statistics_map["aoi_area_rate"] = str(format(float(basic_aoi_area) / aoi_list[0][4], '.3f'))
            temp_statistics_map["son_aoi_info"] = json.dumps(bid_complete_map)
            if basic_aoi_num > 0:
                temp_statistics_map["accurate_aoi_rate"] = str(float(accurate_basic_aoi_num) / basic_aoi_num)
            else:
                temp_statistics_map["accurate_aoi_rate"] = "0"
            statistics_data_list.append(temp_statistics_map)
            print(temp_statistics_map)

    # 成果写入
    file = open(result_file, 'w')
    for statistics_data in statistics_data_list:
        values = statistics_data.values()
        strs = '\t'.join(values) + "\n"
        file.write(strs)
    file.close()


if __name__ == '__main__':
    agg_aoi_statistics("./bid.txt", "./result.txt")
