#!/bin/bash
if [ $# -ne 3 && $# -ne 2 ];then
    echo "Param Error"
    echo "sh access.sh <PROVNAME_EN> <CITYNAME_EN> "
    echo "exit now"
    exit 1
fi

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../../conf/globalConf.sh

PROVNAME_EN=$1
CITYNAME_EN=$2
BACK="Back"

ACCESSDATA=$DATA_PATH"/jichudiwu/data_from_image"
mkdir -p $ACCESSDATA

cd $ACCESSDATA
csvpath=`ls -t $NFS_PATH/blade/image_public/backup/china_split_NA_unit* | grep china_split_NA_unit.*0 | sed 's/://' | head -2 | tail -1`

mkdir -p $ACCESSDATA/out/$PROVNAME_EN/$CITYNAME_EN/
cd $ACCESSDATA/out/$PROVNAME_EN/$CITYNAME_EN/
cp ${csvpath}/csv_backup/$PROVNAME_EN/$CITYNAME_EN/blu_face.csv ./
cp ${csvpath}/csv_backup/$PROVNAME_EN/$CITYNAME_EN/blu_face_poi.csv ./
cp ${csvpath}/csv_backup/$PROVNAME_EN/$CITYNAME_EN/bud_face.csv ./
