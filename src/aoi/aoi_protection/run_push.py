"""
推送
"""

import os
import sys
from pathlib import Path
import requests

import tqdm

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())

from src.door_naming.DaoBeeflow import DaoBeeflow
from src.aoi.aoi_protection import cfg

PUSH_URL = cfg.BASE_API_PATH + "/release/poiOfflineProtection"
LOGIN_URL = cfg.BASE_API_PATH + '/account/login'


def get_token():
    """
    获取token
    :return:
    """
    req = requests.post(
        url=LOGIN_URL,
        data={'user_email': '<EMAIL>', 'user_password': 'pw123456'}
    )
    return req.json()['data']['access_token']


def do_push(bid, logid, status):
    """
    推送
    :return:
    """
    resp = requests.get(url=PUSH_URL, params={
        "bid": bid,
        "logId": logid,
        "status": status,
    }, headers={"token": get_token()})
    return resp.json()


def update_callback_status(dao: DaoBeeflow, _id, status):
    """
    更新
    :param dao:
    :param _id:
    :param status:
    :return:
    """
    sql = 'update aoi_protect_information set callback_status=%s  where id=%s '
    dao.cursor_beeflow.execute(sql, [status, _id])
    dao.conn_beeflow.commit()


def run_push(task_id):
    """
    执行推送
    :param task_id, 强制推送一条数据
    :return:
    """
    with DaoBeeflow(rw=True) as dao:
        # 高优任务其实已经下线了,这里只推送普通任务
        if task_id and int(task_id) > 0:
            sql = 'select id, logid, bid, offline_status, src, can_offline from aoi_protect_information' \
                  ' where id=%s and src not in %s and can_offline = %s '
            params = [task_id, cfg.HIGH_LEVEL_TAG, cfg.CAN_OFFLINE]
        else:
            sql = 'select id, logid, bid, offline_status, src,can_offline  from aoi_protect_information' \
                  ' where callback_status=%s and can_offline=%s and src not in %s' \
                  ' limit 200'
            params = [cfg.CALLBACK_STATUS_DEFAULT, cfg.CAN_OFFLINE, cfg.HIGH_LEVEL_TAG]
        print(dao.cursor_beeflow.mogrify(sql, params))
        dao.cursor_beeflow.execute(sql, params)
        ret = dao.cursor_beeflow.fetchall()
        if len(ret) < 1:
            print("无需要推送的任务")
            return
        for (_id, logid, bid, offline_status, src, can_offline) in tqdm.tqdm(ret):
            print(f"干预下线-{_id} - {logid} - {bid} - {offline_status} - {can_offline}")
            ret = do_push(bid, logid, offline_status)
            print(ret)
            update_callback_status(dao, _id, cfg.CALLBACK_STATUS_PUSHED)
            exit(100)


def run_check(task_id):
    """
    执行推送结果检测,暂时先不处理
    :return:
    """
    pass


run_relation = {
    "push": run_push,
    "check": run_check,
}

if __name__ == '__main__':
    args = sys.argv

    if len(args) >= 2:
        action = args[1]
        if action in run_relation:
            _id = None
            if len(args) == 3:
                _id = args[2]
            run_relation[action](_id)
        else:
            print("无效参数")
    else:
        pass
        # print("\n使用方式: py39 run_push.py <action> <id, 可选>\n"
        #       f"  <action> 支持: {','.join(run_relation.keys())}")
