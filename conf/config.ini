[nav_conf]
link_convert_url = http://api.guoke.map.baidu.com/trunk/idmap

[regular]
run_mode = online
road_pathname = /muku/road/master_road_ml
;pg_naming_url = http://rc.guoke.map.baidu.com:8000/rcmanage/naming
pg_naming_url = http://mapde-poi.baidu-int.com/prod/api/rcmanage/naming
poi_afs_cmd = /home/<USER>/afs-api/bin/afsshell --username=map_data_aoi --password=DKGjrtAiXi2r
poi_afs_dir = afs://fenghuang.afs.baidu.com:9902/user/map_data_aoi
pg_manage_url = http://rc.guoke.map.baidu.com:8000/rcmanage/dbmanage

[poi_cd]
host = *************
port = 8032
user = poi_aoi_rw
database = poi_cd_rw
password = poi_aoi_rw

[file]
release_bid_path='/home/<USER>/aoi-strategy/data/bid2releasebid'

[aoi]
aoi_api_save_bid_repeat='https://mapde-poi.baidu-int.com/prod/api/saveBidRepeatData'

[master_back]
host = *************
port = 5432
user = master_back_se_ro
database = master_back
password = mapread

[master_back_rw]
host = *************
port = 5432
user = master_back_se_rw
database = master_back
password = cwduumoj

[master_back_proxy]
host = *************
port = 8033
user = master_back
database = master_back
password = master_back

[poi_online]
host = *************
user = poi_aoi_rw
password = poi_aoi_rw
database = poi_online_rw
port = 8032

[poi_online_slave]
host = *************
user = poi_aoi_r
password = poi_aoi_r
database = poi_online
port = 8032

[poi_online_test]
host = gzbh-map-ssc03.gzbh.baidu.com
user = poi_aoi_rw
password = poi_aoi_rw
database = poi_online
port = 9432

[trans_id]
host = *************
user = trans_id_se_rw
password = cpmkukky
database = trans_id
port = 5532

[eq_aoi_gate_relate]
host = **************
user = eq_aoi_gate_relate_se_rw
password = inhrydsj
database = eq_aoi_gate_relate
port = 7432

[eq_db_gate]
host = *************
port = 7532
user = eq_db_gate_se_rw
password = erwmgdaz
database = eq_db_gate

[aoi_resume]
host = *************
user = poi_aoi_rw
password = poi_aoi_rw
database = aoi_resume_rw
port = 8032

[eq_poi_traj_db]
host = *************
user = eq_poi_traj_db_se_rw
password = ccdeugov
database = eq_poi_traj_db
port = 6432

[bee_flow_rw]
host = *************
user = bee_flow
password = nl4c/mqeTcsgpH
database = bee_flow
port = 5730

[bee_flow_rd]
host = *************
user = bee_flow_r
password = bee_flow_r_2021
database = bee_flow
port = 5730

[stream_batch]
aoi_back_feature_url = http://mapde-poi.baidu-int.com/aoidataprod/stream/backFeature
aoi_back_feature_callback = http://mapde-poi.baidu-int.com/prod/api/onlineTaskCallBack

[master_road]
road_pathname = /muku/road/master_road_ml
pg_naming_url = http://mapde-poi.baidu-int.com/prod/api/rcmanage/naming

[tp_origin]
host = *************
user = poi_read
password = kRzbvc!c4!cXBkmp
database = charge_station_online
port = 8102

[poi_online_rd]
host = *************
user = poi_aoi_r
password = poi_aoi_r
database = poi_online
port = 8032

#  mis 轨迹处理
[trajectory]
host = *************
database = dest_traj_to_aoi
port = 8432
user = dest_traj_to_aoi_se_rw
password = uwymqbnx

[dest_traj]
host = **************
database = dest_traj
port = 7432
user = dest_traj_se_rw
password = erwmbdpm

[dest_traj_to_aoi]
host = *************
port = 8432
user = dest_traj_to_aoi_se_rw
password = uwymqbnx
database = dest_traj_to_aoi

[aoi_dest_traj_db]
host = *************
database = aoi_dest_traj_db
port = 5432
user = aoi_dest_traj_rw
password = aoi_dest_traj_rw

[aoi_dest_traj_db2]
host = *************
database = aoi_dest_traj_db2
port = 6432
user = aoi_dest_traj_rw
password = aoi_dest_traj_rw

[poi_tmp_0421]
host = *************
port = 7432
user = poi_tmp_0421_se_rw
password = mdsaqjjc
database = poi_tmp_0421

# 熄火点
[traj_feature_db]
host = *************
port = 9432
user = traj_feature_rw
password = traj_feature_rw
database = traj_feature_db

# 高精轨迹
[traj_db]
host = gzxj-sys-rpm03jabejv.gzxj.baidu.com
port = 5432
user = traj_rw
password = traj_rw
database = traj_db

[byd_traj]
host = gzxj-sys-rpm03jabejv.gzxj.baidu.com
port = 6432
user = traj_rw
password = traj_rw
database = traj_db2