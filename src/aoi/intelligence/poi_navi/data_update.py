# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
竞品引导点例行更新
"""
import os
import sys

from typing import List

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("..")
from aoi import PgUpdate
from aoi.dao.aoi_resume_field import PoiNaviIntelligence

POI_NAVI_TABLE_NAME = 'poi_navi_intelligence'
NEW_POI_NAVI_TABLE_NAME = 'poi_navi_intelligence_new'

pg_rw_dao = PgUpdate.PgUpdate()


def run(new_poi_navi_file: str):
    """
    竞品引导点例行更新
    :return:
    """
    pg_rw_dao.create_poi_navi_table()
    number = 0
    data_list = []
    with open(new_poi_navi_file, 'r') as file_data:
        for line_data in file_data:
            number += 1
            line_data = line_data.strip("\n")
            line_data = line_data.strip("\r")
            items = line_data.split("\t")
            geom_list = items[9].split("|")
            if items[9] == "" or len(geom_list) == 0:
                continue
            geom_str = f"point({geom_list[0].split(';')[1].replace(',', ' ', 1)})"
            data_list.append(PoiNaviIntelligence(
                bid=items[0],
                gid=items[1],
                name=items[2].replace("'", "", -1),
                end_geom_str=geom_str,
                date1=items[10],
                date2=items[11],
                src=items[12],
            ))
            if number % 1000 == 0:
                pg_rw_dao.insert_poi_navi_intelligence(data_list)
                data_list = []
        pg_rw_dao.insert_poi_navi_intelligence(data_list)
    pg_rw_dao.poi_navi_intelligence_table_switch()


if __name__ == '__main__':
    new_poi_navi_file = sys.argv[1]
    if os.path.exists(new_poi_navi_file) is False:
        raise Exception('文件不存在')
    run(new_poi_navi_file)
