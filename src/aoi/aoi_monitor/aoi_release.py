# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
diff 未上线AOI重新推送
"""
import time
import hashlib

import requests


def run(bid_file, result_file):
    """
    :return:
    """
    number = 0
    url = "https://mapde-poi.baidu-int.com/prod/api/aoiReleaseByBid"
    with open(bid_file, 'r') as file_data:
        for line_data in file_data:
            number += 1
            line_data = line_data.strip("\n")
            line_data = line_data.strip("\r")
            items = line_data.split("\t")
            bid = items[0]
            print(f"当前进度：{number}, sp_id:{bid}")
            if bid != "":
                parmas = {"bids":bid,"src":""}
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
                res_data = requests.post(url, data=parmas, headers=headers)
                print(res_data.text)
            time.sleep(0.1)



if __name__ == '__main__':
    run("./bid.txt", "./result.txt")







