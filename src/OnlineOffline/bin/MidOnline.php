<?php

require_once(dirname(__FILE__) . '/CNsHead.class.php');

/**
 * @param: nahead, arr
 * @return: res
 **/
function send_to_diwu($nshead, $arr)
{
    $di_req = mc_pack_array2pack($arr, PHP_MC_PACK_V2);
    $ret_arr['body_len'] = strlen($di_req);
    $ret_arr['log_id'] = 123;
    $ret_arr['provider'] = "script";

    $socket = fsockopen("*************", 8887);
    $num = 0;
    while ($socket == false && $num < 3) {
        $socket = fsockopen("*************", 8887);
        sleep(1);
        $num++;
    }
    if ($socket == false) {
        return 0;
    }
    $res = $nshead->nshead_write($socket, $ret_arr, $di_req);
    $di_res = $nshead->nshead_read($socket);
    fclose($socket);
    if ($di_res == false) {
        return 0;
    }
    $res = mc_pack_pack2array($di_res['buf']);
    return $res;
}

$nshead = new NsHead();
$file_name = $argv[1];

$num = 0;
$header = "mid,bid,name,address,faceid_list,geometry,type,create_time,update_time,city";
$header = explode(",", $header);
array_walk($header, function (&$v, $k) {
    $v = strtolower($v);
});

$handle = fopen($file_name, "r");
while (!feof($handle)) {
    $send_arr = array("action" => "upd");
    $send_arr["query_type"] = "dw_mpoi_upd";

    $line = fgets($handle);
    $line = str_replace("\n", "", $line);
    $line = str_replace("\r", "", $line);
    $num++;
    if ($line == "" || strpos($line, "mid") === 0) {
        continue;
    }
    $arr = explode("\t", $line);
    foreach ($header as $key => $value) {
        $send_arr[$value] = $arr[$key];
    }

    $res = send_to_diwu($nshead, $send_arr);
    var_dump($res);
    if ($res["errno"] == 0) {
        file_put_contents('ok', $send_arr["mid"] . "\n", FILE_APPEND);
    } else if ($res == false || !isset($res["errno"]) || $res["errno"] != 0) {
        var_dump($res);
        file_put_contents("online.error", $line . "\n", FILE_APPEND);
    }
}
fclose($handle);
