# -*- coding: utf-8 -*-
"""
出入口质检过滤策略
"""
import csv
import os
import sys
from typing import List

from shapely.geometry import Polygon, MultiPolygon, Point, MultiPoint, LineString
from shapely import wkt
from loguru import logger
import shapely.geometry

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.dest_traj_1 import DestTrajOne
from src.aoi.dao.dest_traj_2 import DestTrajTwo
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.road import Road
from src.aoi import utils
from src.common import pipeline
from src.aoi.park_access.gate_front import context
from src.aoi.park_access import park_utils
from src.aoi.park_access.gate_front import geometric
from src.back.build import plane
from src.aoi.park_access.context import ParkingAccess

dest_traj = DestTraj()
dest_traj_dao1 = DestTrajOne()
dest_traj_dao2 = DestTrajTwo()
road_dao = Road()
poi_online_query = PoiOnlineQuery()

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000
AREA_FACTOR = 10000000000
FILTER_DISTANCE_CATEGORY = ['交通设施;飞机场', '交通设施;火车站']


def park_access_qc(ctx: context.Context):
    """
    挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_repeat,
        filter_by_high_link,
        filter_near_intelligence,
        filter_pushed_park_access,
    )
    pipe_list(ctx)


def mis_gate_qc(ctx: context.Context):
    """
    缺路质检
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_near_intelligence,
        filter_pushed_park_access,
        filter_near_intelligence_only_distance,
    )
    pipe_list(ctx)


def aoto_park_access_qc(ctx: context.Context):
    """
    挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_repeat,
        filter_near_intelligence,
        filter_pushed_park_access,
    )
    pipe_list(ctx)


def sync_auto_park_access_qc(ctx: context.Context):
    """
    例行挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_exp_traj_end,
        # filter_near_overlap_link,
        filter_repeat,
        filter_by_high_link,
        filter_by_in_aoi,
        filter_near_intelligence,
        filter_already_relation_access,
        filter_pushed_park_access_verify_face_ids,
    )
    pipe_list(ctx)


def mistake_intelligence_qc(ctx: context.Context):
    """
    挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_mistake_intelligence_repeat,
        filter_pushed_mistake_intelligence,
        filter_pushed_park_access_by_mis_gate,
    )
    pipe_list(ctx)


def filter_repeat(ctx: context.Context, proceed):
    """
    过滤挖掘重复的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_access_list = []
    exist_node_id = dict()
    exist_link_id = dict()
    area_sp = wkt.loads(ctx.area)
    for park_access in sorted(ctx.parking_access_list, key=lambda x: x.confidence, reverse=True):
        if not park_access.is_valid:
            continue
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id not in exist_node_id:
            node_sp = wkt.loads(park_access.node_geom)
            if area_sp.distance(node_sp) > 30 / COORDINATE_FACTOR_NEW:
                logger.warning(f"出入口距离区域中心过远node:{park_access.node_id}")
                continue
            if park_access.node_id != '':
                exist_node_id[park_access.node_id] = 1
            park_access_list.append(park_access)
            continue
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK \
                and (park_access.link_id not in exist_link_id
                     or wkt.loads(park_access.link_geom).length > 30 / COORDINATE_FACTOR_NEW):
            link_sp = wkt.loads(park_access.link_geom)
            if area_sp.distance(link_sp) > 30 / COORDINATE_FACTOR_NEW:
                logger.warning(f"出入口距离区域中心过远link:{park_access.link_id}")
                continue
            exist_link_id[park_access.link_id] = 1
            park_access_list.append(park_access)
            continue
        logger.warning(
            f"重复情报过滤:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
            f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}, "
            f"geom:{park_access.node_geom}")
    ctx.parking_access_list = park_access_list
    proceed()


def filter_near_intelligence(ctx: context.Context, proceed):
    """
    过滤挖掘距离较近的情报点
    Args:
        ctx:
        proceed:

    Returns:

    """
    pushed_park_access_geom_list = []
    for park_access in sorted(ctx.parking_access_list, key=lambda x: x.confidence, reverse=True):
        if not park_access.is_valid:
            continue
        park_access_sp = wkt.loads(park_access.node_geom)
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK \
                and wkt.loads(park_access.link_geom).length <= 20 / COORDINATE_FACTOR_NEW:
            park_access_sp = wkt.loads(park_access.link_geom)

        near_exist_pushed_park_access_list = [item for item in pushed_park_access_geom_list
                                              if item['geom'].distance(park_access_sp) < 20 / COORDINATE_FACTOR_NEW]
        if len(near_exist_pushed_park_access_list) > 0:
            for near_exist_pushed_park_access in near_exist_pushed_park_access_list:
                pushed_geom_sp = near_exist_pushed_park_access['geom']
                if pushed_geom_sp.distance(park_access_sp) < 20 / COORDINATE_FACTOR_NEW:
                    park_access.is_valid = False
                    park_access.filter_reason = (f"距离已推送的情报点太近:{near_exist_pushed_park_access};"
                                                 f"{pushed_geom_sp.wkt};{park_access_sp.wkt}")
                    break
                # 如果大于5m, 取最远点距离, 如果超过50m, 则都保留
                # 查询已推送的Node 信息
                pushed_node_ids = []
                pushed_node_sp_list = []
                if near_exist_pushed_park_access['type'] == context.PARKING_ACCESS_TYPE_NODE \
                        and near_exist_pushed_park_access['node_id'] != '':
                    pushed_node_ids.append(near_exist_pushed_park_access['node_id'])
                if near_exist_pushed_park_access['type'] == context.PARKING_ACCESS_TYPE_NODE \
                        and near_exist_pushed_park_access['node_id'] == '':
                    pushed_node_sp_list.append(wkt.loads(near_exist_pushed_park_access['geom']))
                if near_exist_pushed_park_access['type'] == context.PARKING_ACCESS_TYPE_LINK:
                    link_list = road_dao.get_nav_link_by_link_ids_new([near_exist_pushed_park_access['link_id']])
                    pushed_node_ids += [link_list[0]['s_nid'], link_list[0]['e_nid']]
                pushed_node_list = road_dao.get_node_geom(pushed_node_ids)
                pushed_node_sp_list += [wkt.loads(item[0]) for item in pushed_node_list]

                # 查询当前出入口Node信息
                current_node_ids = []
                current_node_sp_list = []
                if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id != '':
                    current_node_ids.append(park_access.node_id)
                if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id == '':
                    current_node_sp_list.append(wkt.loads(park_access.node_geom))
                if park_access.type == context.PARKING_ACCESS_TYPE_LINK:
                    link_list = road_dao.get_nav_link_by_link_ids_new([park_access.link_id])
                    current_node_ids += [link_list[0]['s_nid'], link_list[0]['e_nid']]
                current_node_list = road_dao.get_node_geom(current_node_ids)
                current_node_sp_list += [wkt.loads(item[0]) for item in current_node_list]
                # 计算最大距离
                max_distance = max([pushed_sp.distance(current_sp) * COORDINATE_FACTOR_NEW
                                    for pushed_sp in pushed_node_sp_list for current_sp in current_node_sp_list])
                if max_distance < 50:
                    park_access.is_valid = False
                    park_access.filter_reason = f"距离待推送的情报点太近:{max_distance}"
                    break

            if not park_access.is_valid:
                continue
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.is_valid:
            pushed_park_access_geom_list.append({
                "type": context.PARKING_ACCESS_TYPE_NODE,
                "geom": wkt.loads(park_access.node_geom),
                "node_id": park_access.node_id,
            })
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK and park_access.is_valid:
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids_new([park_access.link_id])[0]
            if link_info['len'] > 15:
                pushed_park_access_geom_list.append({
                    "type": context.PARKING_ACCESS_TYPE_NODE,
                    "geom": wkt.loads(park_access.node_geom),
                    "node_id": park_access.node_id,
                })
                continue
            pushed_park_access_geom_list.append({
                "type": context.PARKING_ACCESS_TYPE_LINK,
                "geom": wkt.loads(park_access.link_geom),
                "link_id": park_access.link_id,
            })
    proceed()


def filter_near_intelligence_only_distance(ctx: context.Context, proceed):
    """
    仅根据距离计算较近的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    pushed_park_access_geom_list = []
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        park_access_sp = wkt.loads(park_access.node_geom) if park_access.type == context.PARKING_ACCESS_TYPE_NODE \
            else wkt.loads(park_access.link_geom)
        near_exist_pushed_park_access_list = [item for item in pushed_park_access_geom_list
                                              if item['geom'].distance(park_access_sp) < 10 / COORDINATE_FACTOR]
        if len(near_exist_pushed_park_access_list) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"距离待推送的情报点太近:{near_exist_pushed_park_access_list}"
            continue
        pushed_park_access_geom_list.append({
            "type": context.PARKING_ACCESS_TYPE_NODE,
            "geom": wkt.loads(park_access.node_geom),
            "node_id": park_access.node_id,
        })
    proceed()


def filter_already_relation_access(ctx: context.Context, proceed):
    """
    过滤已关联出入口成果
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 查询关联子点的Node
    if not ctx.bid:
        return proceed()
    long_node_ids, long_link_ids = park_utils.get_relation_node_and_link_by_park_bid(ctx.bid)
    # 查询关联Node信息
    node_geom_list = []
    if len(long_node_ids) > 0:
        node_geom_res = road_dao.get_node_geom(list(set(long_node_ids)))
        node_geom_list = [item[0] for item in node_geom_res] if node_geom_res else []
    # 查询关联子link
    link_geom_list = []
    if len(long_link_ids) > 0:
        link_geom_res = road_dao.get_nav_link_by_link_ids(list(set(long_link_ids)))
        link_geom_list = [item[0] for item in link_geom_res if item[5] < 20] if link_geom_res else []

    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        # link去重
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK:
            # link已关联不在关联
            if park_access.link_id in long_link_ids:
                park_access.is_valid = False
                park_access.filter_reason = f"link已关联:{long_link_ids}"
                continue

            # link附近存在精准出入口则过滤
            link_sp = wkt.loads(park_access.link_geom)
            exist_relation_gate = [node_geom for node_geom in node_geom_list + link_geom_list
                                   if link_sp.distance(wkt.loads(node_geom)) < 20 / COORDINATE_FACTOR_NEW]
            if len(exist_relation_gate) > 0:
                park_access.is_valid = False
                park_access.filter_reason = f"link 20m附近存在已关联出入口过滤:{exist_relation_gate}"
                continue

        # node去重, node 已关联不在关联
        if park_access.node_id in long_node_ids:
            park_access.is_valid = False
            park_access.filter_reason = f"node已关联:{long_node_ids}"
            continue
        # node 附近存在已关联的Node
        node_sp = wkt.loads(park_access.node_geom)
        exist_relation_gate = [item for item in node_geom_list + link_geom_list
                               if node_sp.distance(wkt.loads(item)) < 25 / COORDINATE_FACTOR_NEW]
        if len(exist_relation_gate) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"node 20m附近存在已关联的Node过滤:{exist_relation_gate}"
            continue
    proceed()


def filter_pushed_park_access(ctx: context.Context, proceed):
    """
    过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        is_pushed_intelligence = poi_online_query. \
            get_pushed_intelligence_by_geom(node_sp.buffer(20 / COORDINATE_FACTOR_NEW).wkt, 'park_access_gate_front',
                                            [ctx.park_intelligence_id])
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"已推送的情报 buffer 20范围存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_pushed_park_access_by_mis_gate(ctx: context.Context, proceed):
    """
    过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        is_pushed_intelligence = poi_online_query.get_pushed_intelligence_by_geom(
            geom=node_sp.buffer(10 / COORDINATE_FACTOR).wkt, type='park_access_mis_gate')
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"已推送的情报 buffer 20范围存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_pushed_park_access_verify_face_ids(ctx: context.Context, proceed):
    """
    例行流程通过核实面过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"过滤已推送的出入口情报:{ctx.bid}")
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        is_pushed_intelligence = poi_online_query.get_pushed_intelligence_by_geom(
            node_sp.buffer(30 / COORDINATE_FACTOR_NEW).wkt, type='park_access_gate_front',
            bids=[ctx.bid])
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"已推送的情报 buffer 20范围存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_by_high_link(ctx: context.Context, proceed):
    """
    高等级道路过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    area_sp = wkt.loads(ctx.area)
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        park_access_node_id = park_access.node_id
        park_access_sp = wkt.loads(park_access.node_geom)
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK:
            link_info = road_dao.get_nav_link_by_link_ids_new([park_access.link_id])[0]
            s_node_list = road_dao.get_node_geom([link_info['s_nid']])
            s_nid_sp = wkt.loads(s_node_list[0][0])
            e_node_list = road_dao.get_node_geom([link_info['e_nid']])
            e_nid_sp = wkt.loads(e_node_list[0][0])
            park_access_sp = s_nid_sp if s_nid_sp.distance(area_sp) < e_nid_sp.distance(area_sp) else e_nid_sp
            park_access_node_id = link_info['s_nid'] \
                if s_nid_sp.distance(area_sp) < e_nid_sp.distance(area_sp) else link_info['e_nid']
        # 判断是否横跨高等级道路
        high_level_road_list = utils.is_over_high_road_park_access(park_access_sp.wkt, ctx.area)
        if not high_level_road_list:
            continue
        node_ids = [item['s_nid'] for item in high_level_road_list] + \
                   [item['e_nid'] for item in high_level_road_list]
        if park_access_node_id in node_ids:
            continue
        if park_access.confidence < context.INTELLIGENCE_CONFIDENCE_LOW:
            park_access.is_valid = False
            park_access.filter_reason = f"横跨高等级道路过滤"
            continue
        # 高置信情报距离LINK超过5m则过滤
        link_info = road_dao.get_nav_link_by_link_ids_new([high_level_road_list[0]['link_id']])[0]
        link_sp = wkt.loads(link_info['geom'])
        if link_sp.distance(park_access_sp) > 5 / COORDINATE_FACTOR_NEW:
            park_access.is_valid = False
            park_access.filter_reason = f"横跨高等级道路过滤"
            continue

    proceed()


def filter_mistake_intelligence_repeat(ctx: context.Context, proceed):
    """
    过滤挖掘重复的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_access_list = []
    exist_node_id = dict()
    exist_link_id = dict()
    for park_access in ctx.parking_access_list:
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id not in exist_node_id:
            exist_node_id[park_access.node_id] = 1
            park_access_list.append(park_access)
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK and park_access.link_id not in exist_link_id:
            exist_link_id[park_access.link_id] = 1
            park_access_list.append(park_access)
    ctx.parking_access_list = park_access_list
    proceed()


def filter_pushed_mistake_intelligence(ctx: context.Context, proceed):
    """
    过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        is_pushed_intelligence = poi_online_query.get_pushed_intelligence_by_geom(
            node_sp.buffer(5 / COORDINATE_FACTOR_NEW).wkt, 'park_access_gate_front_mistake', parent_id=ctx.bid)
        if is_pushed_intelligence:
            park_access.is_valid = False
            park_access.filter_reason = f"已推送的情报 buffer 5范围存在\t{is_pushed_intelligence[0]}"
    proceed()


def filter_by_in_aoi(ctx: context.Context, proceed):
    """
    通过AOI过滤在停车面内部的情报点
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"过滤在停车面内部的情报点")
    park_sp = wkt.loads(ctx.area)
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        park_access_sp = wkt.loads(park_access.node_geom)
        park_access_buffer_100m_sp = park_access_sp.buffer(200 / COORDINATE_FACTOR_NEW)
        # 判断情报点是否在AOI内部
        aoi_list = ctx.dao.master_back_dao.get_intersects_aoi_by_geom(park_access.node_geom)
        if not aoi_list:
            logger.info(f"情报点不在AOI内部:{park_access.node_geom}")
            continue
        aoi_info = aoi_list[0]
        # 获取情报点距离最近的高等级道路
        high_level_link_list = ctx.dao.road_dao.is_over_high_road_link(park_access_buffer_100m_sp.wkt)
        if not high_level_link_list:
            logger.info(f"情报点附近不存在高等级道路:{park_access.node_geom}")
            continue
        nearest_high_level_link = min(high_level_link_list,
                                      key=lambda item: park_access_sp.distance(wkt.loads(item['geom'])))
        nearest_high_link_sp = wkt.loads(nearest_high_level_link['geom'])
        # 获取情报点在高等级LINK的投影点
        foot_point = park_utils.get_foot_point(park_access_sp, nearest_high_link_sp)
        if not foot_point:
            logger.info(f"情报点在同等级道路投影失败:{park_access.node_geom},{nearest_high_level_link['link_id']}")
            continue
        # 投影点与情报点的连线如果穿过停车场面,则认为情报点在停车场面内部
        park_access_foot_point_sp = LineString([park_access_sp.coords[0], foot_point])
        intersects_poly = park_sp.intersection(park_access_foot_point_sp)
        logger.info(f"情报点连线:{park_access_foot_point_sp.wkt},intersects_poly:{intersects_poly}")
        if not intersects_poly:
            continue
        if intersects_poly.geom_type != 'LineString':
            continue
        if intersects_poly.length > 10 / COORDINATE_FACTOR_NEW \
                and park_access_sp.distance(park_sp) > 5 / COORDINATE_FACTOR_NEW:
            park_access.is_valid = False
            park_access.filter_reason = f"在停车场面内部过滤"
    return proceed()


def _print_info(ctx: context.Context, proceed):
    print(111111)
    for idx, access in enumerate(ctx.parking_access_list):
        access: ParkingAccess
        print(access.node_geom)
    print(222222)
    return proceed()


def filter_near_overlap_link(ctx: context.Context, proceed):
    """
    过滤岔路口
    """
    for idx, access in enumerate(ctx.parking_access_list):
        access: ParkingAccess
        if not access.is_valid:
            continue
        if access.strategy != context.GATE_FRONT_AREA_NEAR_LINK:
            continue
        link_sp = wkt.loads(access.link_geom)
        park_access_sp = link_sp.interpolate(0.5, normalized=True)
        over_roads = utils.is_over_high_road_park_access(park_access_sp.wkt, ctx.area)
        if len(over_roads) > 0:
            access.is_valid = False
            access.filter_reason = "对面马路的岔路口"
            logger.warning(f"{access.node_geom} 被过滤了：{access.filter_reason}")
    return proceed()


def filter_exp_traj_end(ctx: context.Context, proceed):
    """
    过滤经验轨迹终点
    """
    park_infos = ctx.dao.poi_online_query.get_park_storefront_result(bids=[ctx.bid])
    if len(park_infos) != 1:
        return proceed()
    park_info = park_infos[0]

    filtered_ids = []
    geoms = []
    for idx, access in enumerate(ctx.parking_access_list):
        access: ParkingAccess
        if not access.is_valid:
            continue
        if 'GATE_FRONT_EXP_TRAJ_END' not in access.strategy:
            continue
        _intelligence = _access_to_intelligence(access, idx)
        intelligences = _accesses_to_intelligences(ctx.parking_access_list, filtered_ids)
        filtered, reason = exp_traj_end_can_filter(_intelligence, ctx.traj.exp_traj_list, park_info, intelligences)
        if not filtered:
            continue
        logger.info(f"i_geom:{_intelligence['geom']}; reason: {reason}")
        access.is_valid = False
        access.filter_reason = reason
        filtered_ids.append(idx)
        logger.warning(f"{access.node_geom} 被过滤了：{access.filter_reason}")
        geoms.append(_intelligence['geom'])

    end_csv = './tmp/filter_exp_traj_end.csv'
    with open('%s' % end_csv, 'w') as hdw:
        writer = csv.writer(hdw)
        for g in geoms:
            writer.writerow([g])
    print(end_csv)
    return proceed()


def _accesses_to_intelligences(accesses: List[ParkingAccess], black_ids=[]) -> list:
    resp = []
    for _idx, _access in enumerate(accesses):
        if _idx in black_ids:
            continue
        resp.append(_access_to_intelligence(_access, _idx))
    return resp


def _access_to_intelligence(access: ParkingAccess, idx) -> dict:
    return {
        'id': idx,
        'geom': access.node_geom,
    }


def exp_traj_end_can_filter(intelligence: dict, exp_traj_list: list, park: dict, intelligences) -> tuple:
    """
    过滤经验轨迹终点是否可以过滤
    """
    area = park['geom']
    area_sp = shapely.wkt.loads(area)
    info_sp = shapely.wkt.loads(intelligence['geom'])
    info_buffer = info_sp.buffer(1e-5 * 15)
    central_line = shapely.wkt.loads(park['central_line'])
    # if not area_sp.info_buffer(5 * 1e-5).contains(info_sp):

    if not area_sp.contains(info_sp):
        return False, "情报点没有在面内"

    def _has_other_intelligence_nearby(int_pt, nearby_dist) -> bool:
        """
        附近有情报返回 True
        FIXME 两个点，可能会互相判断
        """
        info_with_dist = []
        for i in intelligences:
            i_pt = shapely.wkt.loads(i['geom'])
            info_with_dist.append({
                'info': i,
                'dist': i_pt.distance(int_pt)
            })
        info_with_dist.sort(key=lambda x: x['dist'])
        nearby_info = info_with_dist[0]

        if nearby_info['info']['id'] == intelligence['id']:
            # 交割点最近的点就是本身
            return False
        if nearby_info['dist'] < nearby_dist:
            return True
        return False

    def _get_nearby_traj() -> tuple:
        """
        获取最近的轨迹
        """
        traj_with_dis = []
        for _exp_traj in exp_traj_list:
            _end_pt = shapely.geometry.Point(list(_exp_traj['traj'].coords)[-1])
            if not area_sp.contains(_end_pt):
                # 不再面范围内
                continue
            if not info_buffer.intersects(_end_pt):
                # 终点不在当前情报范围内
                continue
            traj_with_dis.append({
                'traj': _exp_traj,
                'dist': _end_pt.distance(info_sp)
            })
        traj_with_dis.sort(key=lambda x: x['dist'])
        if len(traj_with_dis) == 0:
            return None, 0
        return traj_with_dis[0]['traj'], len(traj_with_dis)

    def _around_the_corner(_area_sp) -> bool:
        """
        位于拐角处返回 True
        """
        int_central_line = _area_sp.intersection(central_line)
        l_coords = list(int_central_line.coords)
        if len(l_coords) <= 2:
            return False
        for idx, coord in enumerate(l_coords):
            if idx == len(l_coords) - 2:
                break
            _pt1 = shapely.geometry.Point(coord)
            _pta = shapely.geometry.Point(l_coords[idx + 1])
            _pt2 = shapely.geometry.Point(l_coords[idx + 2])
            _ang = plane.cal_angle(_pta, _pt1, _pt2)
            if 45 < _ang.degrees() < 90 + 45:
                return True
        return False

    nearby_traj, traj_num = _get_nearby_traj()
    if not nearby_traj:
        return False, "一定范围内没有轨迹"
    # if traj_num >= 2:
    #     return False, f"经验轨迹量级过多：{traj_num}"

    traj = nearby_traj['traj']
    print(traj)
    int_sp = area_sp.boundary.intersection(traj)
    traj_end = shapely.geometry.Point(list(traj.coords)[-1])
    int_buffer_size = 20 * 1e-5

    traj_end_buffer = traj_end.buffer(int_buffer_size)
    if _around_the_corner(traj_end_buffer):
        # 经验之谈，拐角出大概率有入口
        return False, "终点位于拐角处"

    # end_distance_intersection = 25 * 1e-5
    end_distance_intersection = 5 * 1e-5
    int_has = False
    reasons = []
    for _pt in geometric.flat_point(int_sp):
        distance = traj_end.distance(_pt)
        if distance < end_distance_intersection:
            reasons.append(f'轨迹终点和交割点离的很近: {distance / 1e-5}')
            continue

        _nearby_sp = _pt.buffer(int_buffer_size)
        # print(_nearby_sp)
        if _has_other_intelligence_nearby(_pt, int_buffer_size):
            int_has = True  # 有交割点情报
            break
    if int_has:
        return True, "经验轨迹终点被过滤，交割点有情报"
    return False, "交割点附近无情报" if len(reasons) == 0 else ';'.join(reasons)
