# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDD商单数据输出
"""
import os
import sys

import psycopg2

pdd_sh_conf = {"db": "pdd_sd", "user": "pdd_sd_se_rw", "pwd": "i<PERSON><PERSON><PERSON><PERSON>",
               "host": "**************",
               "port": 6432}

pdd_sd_conn = psycopg2.connect(database=pdd_sh_conf["db"],
                               user=pdd_sh_conf["user"],
                               password=pdd_sh_conf["pwd"],
                               host=pdd_sh_conf["host"],
                               port=pdd_sh_conf["port"])
pdd_sd_dao = pdd_sd_conn.cursor()

root_dir = "/home/<USER>/gengyong/business_order/huolala"
bud_path = root_dir + "/background/00-building"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-divison"
bad_county_path = root_dir + "/background/04-divison/county"
bad_city_path = root_dir + "/background/04-divison/city"
city_list = ["wuxi", "nanchang", "shenzhen"]


def run():
    """
    成果输出
    Returns:

    """
    print(f"开始blu导出")
    blu_export()
    print(f"开始bud导出")
    bud_export()
    print(f"开始blc导出")
    blc_export()
    print(f"开始brw导出")
    brw_export()
    print(f"开始bad导出")
    bad_export()
    print(f"导出完成")


def bud_export():
    """
    建筑物数据输出
    Returns:

    """
    for city in city_list:
        export_dir = bud_path + "/" + city
        sql = f"""
            select 
                bf.face_id,bf.struct_id,case when bs.name_ch='虚拟POI' then '' else bs.name_ch end as name_ch,bf.aoi_id,bf.height,
                0.0 as floors,0.0 as sub_floors,bf.area,bf.perimeter,bf.geom  
            from bud_face bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
                left join bud_struct_muku bs on bf.struct_id=bs.struct_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/bud_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """

        os.system(cmd)


def blu_export():
    """
    AOI数据输出
    Returns:

    """
    for city in city_list:
        export_dir = blu_path + "/" + city
        sql = f"""
            select 
                face_id,poi_bid,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,name_ch as name,bf.geom
            from blu_face bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)


def blc_export():
    """
    BLC数据输出
    Returns:

    """
    for city in city_list:
        export_dir = blc_path + "/" + city
        sql = f"""
            select 
                face_id,name_ch,name_ph,kind,admin_id,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,bf.geom 
            from blc_green bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_lvdi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)
        sql = f"""
            select 
                face_id,name_ch,name_ph,kind,admin_id,st_area(bf.geom::geography) as area,ST_Perimeter(bf.geom::geography) as perimeter,bf.geom  
            from blc_water bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_shuixi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)


def brw_export():
    """
    BRW数据输出
    Returns:

    """
    for city in city_list:
        export_dir = brw_path + "/" + city
        sql = f"""
            select 
                link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,bf.geom 
            from brw_link bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/brw_link.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)
        sql = f"""
            select 
                node_id,kind,form
            from brw_node bf 
                inner join mesh_conf mc on bf.mesh_id = mc.mesh_id 
            where city_en = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/brw_node.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)


def bad_export():
    """
    行政区划数据输出
    Returns:

    """
    for city in city_list:
        export_dir = bad_city_path + "/" + city
        sql = f"""
            select 
                 guid as face_id,city_id as admin_id,city_name as name_ch,(case when st_isempty(location)='t' then Null else location end) as geom  
            from bad_city where city_en = '{city}' and level = '2'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/division_city.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)
        export_dir = bad_county_path + "/" + city
        sql = f"""
            select 
                 guid as face_id,countycode as admin_id,countyname as name_ch,(case when st_isempty(geom)='t' then Null else geom end) as geom  
            from bad_county where lower(cityenname) = '{city}'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/division_county.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" -lco encoding=gb18030
        """
        os.system(cmd)


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/background")
    make_dir(bud_path)
    make_dir(blu_path)
    make_dir(blc_path)
    make_dir(brw_path)
    make_dir(bad_path)
    make_dir(bad_county_path)
    make_dir(bad_city_path)
    for province in city_list:
        make_dir(bud_path + "/" + province)
        make_dir(blu_path + "/" + province)
        make_dir(blc_path + "/" + province)
        make_dir(brw_path + "/" + province)
        make_dir(bad_county_path + "/" + province)
        make_dir(bad_city_path + "/" + province)


def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


if __name__ == '__main__':
    data_version = sys.argv[1]
    cmd = "rm -rf ./background"
    os.system(cmd)
    init_dir()
    print("目录初始化完成,开始数据导出")
    run()
    zip_cmd = f"zip -r {data_version} background"
    os.system(zip_cmd)
