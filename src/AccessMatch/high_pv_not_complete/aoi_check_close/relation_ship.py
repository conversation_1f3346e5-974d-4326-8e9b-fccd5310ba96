"""
关联关系闭环

https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/ffhqYLGGkXtWSx
【R2001】关联的node/link失效
【R2003】关联node距离边框较远(距离边界超过100m)
【R2004】高等级道路(>=5 & <= 7)与AOI边框交割点附近(30m内)无关联大门(月导航pv>30&面积>5000)
【R2005】符合命名规范的AOI大门，没有父点
【R2007】AOI一个大门也没有(月导航pv>30&面积>5000)
【R2008】不同基础院落&非商单的AOI关联相同的大门
"""
from pathlib import Path
import sys
import os
import sys
import datetime
import pandas as pd
import re

import tqdm

root_dir = Path(os.path.abspath(__file__)).parents[4]
sys.path.insert(0, root_dir.as_posix())

from src.AccessMatch.high_pv_not_complete.aoi_check_close import utils
from src.AccessMatch.high_pv_not_complete.flow import check_manual
from src.common import pgsql
from src.door_naming.strategy_v2 import common
from src.door_naming.poi_offline_strategy import offline_task
from src.AccessMatch.manual import task_manual_fenglei

work_dir = Path(os.path.abspath(__file__)).parent / 'flow_data' / datetime.date.today().strftime("%Y%m%d")
work_dir.mkdir(exist_ok=True, parents=True)
file_name = work_dir / 'aoi_error_file_relationship.txt'
today = datetime.datetime.today().strftime("%Y%m%d")

SOURCE_CHECK_DIS = '[R2003]距离边界超过100m'
SOURCE_CHECK_MORE_NODES = '[R2002]关联node>10个'
SOURCE_CHECK_NO_NODE = '[R2007]AOI没有大门'
SOURCE_NODE_MORE_BID = '[R2008]不同基础院落关联相同的大门'


def load_data():
    """
    下载数据
    :return:
    """
    if file_name.exists():
        print(f"文件{file_name}已经存在,可直接使用")
        return
    # 优先使用今天的
    date1 = datetime.date.today()
    data_url = f'http://mapde-poi.baidu-int.com/aoi_quality_check_result/aoi_result/' \
               f'aoi_error_file_relationship_{date1.strftime("%Y-%m-%d")}.txt'
    utils.try_download(data_url, file_name)
    if file_name.exists():
        print(f"文件{file_name}下载完成")
        return
    # 今天没有,使用昨天的
    date2 = datetime.date.today() - datetime.timedelta(days=1)
    data_url = f'http://mapde-poi.baidu-int.com/aoi_quality_check_result/aoi_result/' \
               f'aoi_error_file_relationship_{date2.strftime("%Y-%m-%d")}.txt'
    utils.try_download(data_url, file_name)
    if file_name.exists():
        print(f"文件{file_name}下载完成")
        return


def check_handled(bid, node_id, source):
    """
    检查是否下发过
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    sql = 'select id from gate_aoi_match_check_data where bid=:bid and node_id=:node_id and source=:source '
    return pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {"bid": bid, 'node_id': node_id, "source": source})


def check_has_conclusion(bid, node_id, source):
    """
    是否有结论
    非对应批次的结果,只看结论是Y的,
    对应批次的任务,Y和N,无法核实都算有结论
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    sql = 'select id, conclusion from gate_aoi_match_check_data ' \
          'where bid=:bid and node_id=:node_id and conclusion =:conclusion '
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql, {'bid': bid, 'node_id': node_id, 'conclusion': 'Y'})
    if ret:
        return True, ret.conclusion
    sql = 'select id, conclusion from gate_aoi_match_check_data where bid=:bid and' \
          ' node_id=:node_id and source=:source and conclusion !=:conclusion'
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql,
                          {'bid': bid, 'node_id': node_id, 'source': source, "conclusion": ''})
    if ret:
        return True, ret.conclusion
    return False, ''


def add_manual_task(bid, node_id, source):
    """
    下发任务
    :param bid:
    :param node_id:
    :param source:
    :return:
    """
    uid = f"{node_id}_{bid}_{today}"
    sql = 'insert into gate_aoi_match_check_data (bid, node_id, uid, source, ways, wait_create)' \
          ' values (:bid, :node_id, :uid, :source, :ways, :wait_create)'
    pgsql.try_execute(pgsql.ENGINE_POI_ONLINE, sql,
                      {"bid": bid, "node_id": node_id, "uid": uid, "source": source,
                       "ways": '清查提准', "wait_create": 1})


def check_aoi_level(level):
    """
    aoi_level 转化
    :param level:
    :return:
    """
    try:
        level = int(level)
    except Exception as e:
        level = -1
    return level


def handle_R2003():
    """
    下发R2002
    :return:
    """
    if not file_name.exists():
        raise Exception("无数据")
    print("开始处理:[R2003]")
    manual_task_lines = []
    df = pd.read_csv(file_name,
                     converters={"bid": str, "err_type": str, "err_msg": str, "aoi_level": check_aoi_level},
                     sep='\t')
    bid_wait = set()
    bid_all = set()
    for idx, row in df.iterrows():
        bid = row['bid']
        aoi_level = row['aoi_level']
        err_type = row['err_type']
        err_msg = row['err_msg']
        s = err_msg
        if int(aoi_level) != 2 or 'R2003' not in err_type:
            continue
            # 2002 - 2003 处理, R2002先不做
        try:
            # bid对应的node_id 在message里面,为了保持一致,暂时先用正则提取
            node_ids = re.search("node_ids=(.*?),distances", s).group(1)
            node_ids = (eval(node_ids))
            source = SOURCE_CHECK_DIS
            if len(node_ids) > 0:
                bid_all.add(bid)
                for node_id in node_ids:
                    checked, conclusion = check_has_conclusion(bid, node_id, source)
                    if checked:
                        print(f"[R2003]无需创建任务,已经闭环:{bid}-{node_id}-{conclusion}")
                        continue
                    bid_wait.add(bid)
                    if check_handled(bid, node_id, source):
                        print(f"[R2003]已经创建任务:{bid}-{node_id}")
                        continue
                    print(f"[R2003]投放任务-{bid}-{node_id}")
                    add_manual_task(bid, node_id, source)
                    manual_task_lines.append({
                        "bid": bid,
                        "node_id": node_id,
                        "source": source,
                        'uid': f"{node_id}_{bid}_{today}",
                        "batch_no": source + "_" + today,
                    })
        except Exception as e:
            print(f"[R2003]下发失败, {bid}, {e.args}")
    pd.DataFrame(manual_task_lines).to_csv(work_dir / 'manual_task_R2003.csv', index=False, sep='\t')
    # 检查已经完成的,丢完成记录
    completed = bid_all - bid_wait
    for x in tqdm.tqdm(completed, desc='闭环结果入库:[R2003]'):
        utils.add_bid_complete(x, 'R2003')


def handle_R2007():
    """
    闭环无门场景
    :return:
    """
    if not file_name.exists():
        raise Exception("无数据")
    print("开始处理:[R2007]")
    df = pd.read_csv(file_name,
                     converters={"bid": str, "err_type": str, "err_msg": str, "aoi_level": check_aoi_level},
                     sep='\t')
    bid_complete = set()
    lines_2007 = []
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df), desc='开始处理R2007'):
        bid = row['bid']
        aoi_level = row['aoi_level']
        err_type = row['err_type']
        if int(aoi_level) != 2 or 'R2007' not in err_type:
            continue
        if check_manual.check_common_can_complete(bid, aoi_level, auto_add_to_flow=True):
            bid_complete.add(bid)
        else:
            lines_2007.append({
                "bid": bid,
            })
    pd.DataFrame(lines_2007).to_csv(work_dir / 'manual_task_R2007.csv', index=False, sep='\t')
    for x in tqdm.tqdm(bid_complete, desc='闭环结果入库:[R2007]'):
        utils.add_bid_complete(x, 'R2007')


def handle_R2008():
    """
    处理R2008
    :return:
    """
    if not file_name.exists():
        raise Exception("无数据")
    print("开始处理:[R2008]")
    df = pd.read_csv(file_name,
                     converters={"bid": str, "err_type": str, "err_msg": str, "aoi_level": check_aoi_level},
                     sep='\t')
    bid_all = set()
    bid_wait = set()
    manual_task_lines = []
    for idx, row in df.iterrows():
        bid = row['bid']
        aoi_level = row['aoi_level']
        err_type = row['err_type']
        err_msg = row['err_msg']
        source = SOURCE_NODE_MORE_BID
        if int(aoi_level) != 2 or 'R2008' not in err_type:
            continue
        err_msg_split = err_msg.split('关联相同的node_id:')
        if len(err_msg_split) != 2:
            continue
        bid_all.add(bid)
        node_id = err_msg_split[-1]
        checked, conclusion = check_has_conclusion(bid, node_id, source)
        if checked:
            # manual_task_lines.append({
            #     "bid": bid,
            #     "node_id": node_id,
            #     "source": source,
            #     'conclusion': conclusion,
            #     "batch_no": source + "_" + today,
            # })
            print(f"[R2008]无需创建任务,已经闭环:{bid}-{node_id}-{conclusion}")
            continue
        bid_wait.add(bid)
        if check_handled(bid, node_id, source):
            print(f"[R2008]已经创建任务:{bid}-{node_id}")
            continue
        print(f"[R2008]投放任务-{bid}-{node_id}")
        add_manual_task(bid, node_id, source)
        manual_task_lines.append({
            "bid": bid,
            "node_id": node_id,
            "source": source,
            'uid': f"{node_id}_{bid}_{today}",
            "batch_no": source + "_" + today,
        })
    pd.DataFrame(manual_task_lines).to_csv(work_dir / 'manual_task_R2008.csv', index=False, sep='\t')
    # 检查已经完成的,丢完成记录
    completed = bid_all - bid_wait
    print('R2008闭环数量', len(completed))
    for x in tqdm.tqdm(completed, desc='闭环结果入库:[R2008]'):
        utils.add_bid_complete(x, 'R2008')


def handle_R2005():
    """
    处理R2005质检项
    :return:
    """
    if not file_name.exists():
        raise Exception("无数据")
    day_of_week = datetime.date.today().weekday()  # 获取今天是一周中的第几天，周一为0，周二为1，依此类推
    if day_of_week not in (0, 3):  # 一周只处理两次
        print("非周一和周四,跳过执行R2005")
        return
    print("开始处理:[R2005]")
    df = pd.read_csv(file_name,
                     converters={"bid": str, "err_type": str, "err_msg": str, "aoi_level": check_aoi_level},
                     sep='\t')
    bid_all = set()
    for idx, row in df.iterrows():
        bid = row['bid']
        err_type = row['err_type']
        if 'R2005' not in err_type:
            continue
        bid_all.add(bid)
    poi_dict = common.multi_get_poi_info_by_bid(list(bid_all))
    nav_dict = common.multi_get_pv_by_bid(list(bid_all))
    lines_all = []
    for bid, poi_item in tqdm.tqdm(poi_dict.items(), desc='生成文件中'):
        need_find_door = nav_dict.get(bid, -1) >= 30 or poi_item.click_pv >= 210 or '幼儿园' in poi_item.name
        bound_bid = None
        if need_find_door:
            bound_bid = task_manual_fenglei.get_buffered_aoi(poi_item.geom, 200)
        need_find_door = bound_bid is not None
        tmp = {
            'main_bid': bound_bid,
            'bid': bid,
            'name': poi_item.name,
            'std_tag': poi_item.std_tag,
            'parent_bid': poi_item.relation_bid,
            'geom': poi_item.geom,
            '导航pv': nav_dict.get(bid, -1),
            '算路pv': poi_item.click_pv,
            '需要保留': need_find_door,
            'near_10_door_node_id': '',
        }
        lines_all.append(tmp)
    # 导出需要处理的poi
    df = pd.DataFrame(lines_all)
    df.to_csv(work_dir / 'R2005_poi_all.tsv', index=False, sep='\t')
    df[df['需要保留']].to_csv(work_dir / 'R2005_poi_need_bound_door.tsv', index=False, sep='\t')
    task_manual_fenglei.run(work_dir / 'R2005_poi_need_bound_door.tsv')
    # 删除的那部分,全删除
    df[['bid']].to_csv(work_dir / 'R2005_poi_not_need.tsv', index=False, sep='\t', header=None)
    offline_task.create(work_dir / 'R2005_poi_not_need.tsv')


def handle(check='all'):
    """
    人工处理
    :param check:
    :return:
    """
    funcs = {
        'R2003': handle_R2003,
        'R2007': handle_R2007,
        'R2008': handle_R2008,
        'R2005': handle_R2005,
    }
    if check == 'all':
        [fn() for _, fn in funcs.items()]
    else:
        if check not in funcs:
            print(f'{check}不是支持的类型')
            return
        funcs[check]()


if __name__ == '__main__':
    if len(sys.argv) < 2:
        print('参数不足')
        exit(-1)
    action = sys.argv[1]
    if len(sys.argv) >= 3:
        _check = sys.argv[2]
    else:
        _check = 'all'
    if action == 'create':
        load_data()
        handle(_check)
    else:
        print('参数不支持')
