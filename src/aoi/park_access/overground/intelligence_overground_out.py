# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
import tqdm
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from sklearn.cluster import DBSCAN
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.traj_db import TrajDB
from src.common import pipeline
from src.aoi.park_access import context
from src.aoi.park_access.context import Context
from src.aoi.park_access import park_utils
from src.aoi import utils
from src.aoi.park_access import park_access_qc as qc
import traj_navigate

traj_db = TrajDB()
# 定义 QPS 限制
QPS = 10  # 轨迹接口每秒最多 10 次请求
ONE_SECOND = 1

COORDINATE_FACTOR = 100000

# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

BUILD_GATE_CATEGORY = ['医疗;综合医院', '医疗;专科医院', '教育培训;高等院校', '房地产;住宅区', '房地产;其他', '房地产', '公司企业;公司', '公司企业;园区', '公司企业',
                       '公司企业;厂矿', '公司企业;农林园艺', '教育培训;中学', '教育培训;小学', '教育培训;幼儿园', '教育培训;培训机构', '教育培训;其他', '教育培训;科研机构',
                       '教育培训;科技馆', '教育培训;成人教育', '教育培训;特殊教育学校', '教育培训', '政府机构;各级政府', '政府机构;行政单位', '政府机构;公检法机构',
                       '政府机构;政治教育机构', '政府机构;中央机构', '政府机构;涉外机构', '汽车服务;汽车销售', '汽车服务;汽车配件', '汽车服务;汽车维修', '汽车服务;汽车检测场',
                       '文化传媒;广播电视', '绿地;高尔夫球场', '医疗;疗养院', '医疗;其他', '医疗', '生活服务;物流公司']
NOT_BUILD_CATEGORY = ['交通设施;加油加气站', '交通设施;飞机场', '交通设施;火车站', '购物;购物中心']
AREA_FIRST_CATEGORY = ['交通设施;飞机场', '交通设施;火车站', '旅游景点;风景区', '旅游景点;公园', '旅游景点;人文景观']
IS_TEST = True


def create_intelligence(ctx: context.Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        end_traj_area_intersects,
        parking_traj_area_intersects,
        park_accept_traj_area_intersects,
        competitor_diff,
        aoi_blu_access,
        high_link_and_out_traj,
        aoi_near_open_gate_and_traj,
        doubly_link_and_traj,
        aoi_out_gate_and_traj,
        # hd_traj_area_intersects,
        park_access_filter,
        save_park_access_intelligence_to_db
    )
    pipe_list(ctx)


def end_traj_area_intersects(ctx: context.Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info("begin end_traj_area_intersects")
    area_sp, intersect_point_list = get_intersects_points(ctx, ctx.traj.dest_traj_list, ctx.traj.park_dest_traj_list)
    if IS_TEST == '0':
        # for traj_sp in ctx.traj.filter_aoi_dest_traj_list:
        #     line_start_point = traj_sp.coords[0]
        #     line_end_point = traj_sp.coords[-1]
        #     area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
        #     if area_buffer_10m_sp.contains(Point(line_start_point)) and not area_buffer_10m_sp.contains(
        #             Point(line_end_point)):
        #         print(traj_sp.wkt)
        logger.info(f"\n终点轨迹与AOI交割点集: {intersect_point_list}")
    if not intersect_point_list:
        return proceed()
    # 查询竞品出口数据
    competitor_out_gate_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    logger.info(f"竞品出口数据: {[item['geom'] for item in competitor_out_gate_list]}")
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    high_traj_pass = True  # 默认高通量PV场景
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
        high_traj_pass = False
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    top_num = 0
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        top_num += 1
        # 非top2|高通量PV场景|聚类点数量小于10|附近20m仅存在封闭大门跳过
        if top_num > 2 and len(points) < 10 and high_traj_pass \
                and park_utils.near_only_exist_urgent_gate(convex_hull.buffer(10 / COORDINATE_FACTOR).wkt):
            logger.info(f"非TOP2聚类点附近仅紧急大门,直接过滤")
            continue
        # 非top3|高通量PV场景|聚类点数量小于5|附近不存在竞品数据|聚类范围内不存在出口大门
        near_competitor_gate = [item for item in competitor_out_gate_list
                                if convex_hull.buffer(20 / COORDINATE_FACTOR).contains(wkt.loads(item['geom']))]
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(10 / COORDINATE_FACTOR).wkt)
        near_out_gate_list = [item for item in node_gate_list if utils.is_out_gate(item[0])] if node_gate_list else []
        if top_num > 2 and len(points) <= 5 and len(near_competitor_gate) == 0 \
                and len(near_out_gate_list) == 0:
            logger.info(f"非TOP3聚类点附近不存在竞品数据,直接过滤")
            continue

        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.centroid
        parking_access = context.ParkingAccess()
        if top_num == 1:
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
        if top_num == 2:
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_MEDIUM
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(5 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list if utils.is_out_gate(x[0])]
            if not node_ids:
                node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.OVERGROUND_OUT_END_TRAJ_AREA_INTERSECT_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 没有大门,就近绑Link
        nearst_link = ctx.dao.road_dao.get_near_link_by_node_geom_new(convex_hull.buffer(5 / COORDINATE_FACTOR).wkt,
                                                                      convex_center_sp.wkt)
        if nearst_link:
            link_node_id = park_utils.get_relation_node_by_link(nearst_link['link_id'], convex_hull.centroid.wkt)
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_hull.centroid.wkt
            parking_access.link_id = nearst_link['link_id']
            parking_access.link_geom = nearst_link['geom']
            parking_access.strategy = context.OVERGROUND_OUT_END_TRAJ_AREA_INTERSECT_LINK
            ctx.parking_access_list.append(parking_access)
            continue
        # 直接取聚类中心点
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = convex_hull.centroid.wkt
        parking_access.strategy = context.OVERGROUND_OUT_END_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(parking_access)
    proceed()


def parking_traj_area_intersects(ctx: context.Context, proceed):
    """
    众源轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    def traj_link_relation(pg, aoi_info, park_bid, filtered_traj_list, sub_pois):
    """
    logger.info(f"begin parking_traj_area_intersects_and_dead_link")
    # 获取停车场轨迹, 地下根据主点buffer 100m & AOI面交集
    park_buffer_100m_sp = wkt.loads(ctx.parking_info.park_wkt).buffer(100 / COORDINATE_FACTOR)
    if ctx.aoi_info.wkt:
        aoi_sp = wkt.loads(ctx.aoi_info.wkt)
        park_buffer_100m_sp = park_buffer_100m_sp.intersection(aoi_sp)
    park_area = ctx.parking_info.park_area_gcj_wkt if ctx.parking_info.park_area_gcj_wkt else park_buffer_100m_sp.wkt
    # 取停车轨迹
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-180)).strftime("%Y-%m-%d")
    parking_traj_list = ctx.dao.traj_feature_dao.get_parking_points_traj_by_wkt(park_area, traj_time)
    if not parking_traj_list:
        logger.info(f"没有停车轨迹")
        return proceed()
    parking_traj_list = [wkt.loads(item[0]) for item in parking_traj_list]
    # 取轨迹与AOI面 or 停车航面交点
    area_sp, intersect_point_list = get_intersects_points(ctx, parking_traj_list, parking_traj_list)
    logger.info(f"\n停车轨迹与AOI交割点集: {intersect_point_list}")
    if not intersect_point_list:
        return proceed()
    # 获取竞品数据
    competitor_out_gate_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=10 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    high_traj_pass = True  # 默认高通量PV场景
    if len(points) < 30:
        db = DBSCAN(eps=10 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
        high_traj_pass = False
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    top_num = 0
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        logger.info(f"停车轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        top_num += 1
        # 非top2|高通量PV场景|聚类点数量小于10|附近20m仅存在封闭大门跳过
        if top_num > 2 and len(points) < 10 and high_traj_pass \
                and park_utils.near_only_exist_urgent_gate(convex_hull.buffer(10 / COORDINATE_FACTOR).wkt):
            logger.info(f"非TOP2聚类点附近仅紧急大门,直接过滤")
            continue
        # 非top3|高通量PV场景|聚类点数量小于5|附近不存在竞品数据|聚类范围内不存在出口大门
        near_competitor_gate = [item for item in competitor_out_gate_list
                                if convex_hull.buffer(20 / COORDINATE_FACTOR).contains(wkt.loads(item['geom']))]
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(10 / COORDINATE_FACTOR).wkt)
        near_out_gate_list = [item for item in node_gate_list if utils.is_out_gate(item[0])] if node_gate_list else []
        if top_num > 2 and len(points) <= 5 and len(near_competitor_gate) == 0 \
                and len(near_out_gate_list) == 0:
            logger.info(f"非TOP3聚类点附近不存在竞品数据,直接过滤")
            continue
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.centroid
        parking_access = context.ParkingAccess()
        if top_num == 1:
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
        if top_num == 2:
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_MEDIUM
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(5 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list if utils.is_out_gate(x[0])]
            if not node_ids:
                node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.OVERGROUND_OUT_PARKING_TRAJ_AREA_INTERSECT_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 没有大门,就近绑Link
        nearst_link = ctx.dao.road_dao.get_near_link_by_node_geom_new(convex_hull.buffer(5 / COORDINATE_FACTOR).wkt,
                                                                      convex_center_sp.wkt)
        if nearst_link:
            link_node_id = park_utils.get_relation_node_by_link(nearst_link['link_id'], convex_hull.centroid.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_hull.centroid.wkt
            parking_access.link_id = nearst_link['link_id']
            parking_access.link_geom = nearst_link['geom']
            parking_access.strategy = context.OVERGROUND_OUT_PARKING_TRAJ_AREA_INTERSECT_LINK
            ctx.parking_access_list.append(parking_access)
            continue
        # 直接取聚类中心点
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = convex_hull.centroid.wkt
        parking_access.strategy = context.OVERGROUND_OUT_PARKING_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(parking_access)
    proceed()


def park_accept_traj_area_intersects(ctx: context.Context, proceed):
    """
    采纳轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin park_accept_traj_area_intersects")
    aoi_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
    park_accept_traj_list = []
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-30)).strftime("%Y-%m-%d")
    # 如果停车场面不在AOI面内 或AOI面不存在, 取停车场面采纳轨迹
    if (aoi_sp and park_area_sp and not aoi_sp.contains(park_area_sp)) or (not aoi_sp and park_area_sp):
        park_accept_traj_list = traj_db.get_park_accept_traj(geom=park_area_sp.wkt, accept_time=traj_time)
        if not park_accept_traj_list:
            logger.info(f"没有采纳轨迹")
            return proceed()
    if not park_accept_traj_list and aoi_sp:
        park_accept_traj_list = traj_db.get_park_accept_traj(geom=aoi_sp.wkt, accept_time=traj_time)
        if not park_accept_traj_list:
            logger.info(f"没有采纳轨迹")
            return proceed()
    traj_list = [wkt.loads(item['arrived_traj']) for item in park_accept_traj_list]
    # 轨迹截取
    range_sp = aoi_sp if aoi_sp else park_area_sp
    if aoi_sp and park_area_sp:
        range_sp = aoi_sp.union(park_area_sp)
    if not range_sp:
        return proceed()
    traj_sp_list = park_utils.traj_intercept(traj_list, range_sp.wkt)
    # 杂乱轨迹过滤
    traj_list = park_utils.filter_noise_trace_by_angle_and_intersects(traj_sp_list)
    area_sp, intersect_point_list = get_intersects_points(ctx, traj_list, traj_list, area_buffer_distance=-10)
    if IS_TEST == '0':
        logger.info(f"\n采纳轨迹与AOI交割点集: {intersect_point_list}")
    if not intersect_point_list:
        return proceed()
    # 查询竞品出口数据
    competitor_out_gate_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"采纳轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        convex_buffer_10m_sp = multipoint.convex_hull.buffer(10 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        exist_valid_gate = False
        parking_access = context.ParkingAccess()
        for aoi_access in ctx.aoi_info.blu_access_list:
            node_sp = wkt.loads(aoi_access.node_geom)
            if not utils.is_out_gate(aoi_access.node_id):
                continue
            if not convex_buffer_10m_sp.contains(node_sp):
                continue
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = aoi_access.node_id
            parking_access.node_geom = aoi_access.node_geom
            parking_access.strategy = context.OVERGROUND_OUT_ACCEPT_TRAJ_AREA_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            exist_valid_gate = True
            break
        if exist_valid_gate:
            continue
        # 判断是否存在竞品
        near_competitor_list = [item for item in competitor_out_gate_list
                                if convex_buffer_10m_sp.contains(wkt.loads(item['geom']))]
        if not near_competitor_list:
            continue
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = near_competitor_list[0]['geom']
        parking_access.strategy = context.OVERGROUND_OUT_ACCEPT_TRAJ_AREA_INTERSECT_GATE
        ctx.parking_access_list.append(parking_access)
    proceed()


def hd_traj_area_intersects(ctx: context.Context, proceed):
    """
    高精与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin hd_traj_area_intersects")
    range_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
    if range_sp and park_area_sp:
        range_sp = range_sp.union(park_area_sp)
    if not range_sp and park_area_sp:
        range_sp = park_area_sp
    if not range_sp:
        return proceed()
    hd_traj_list = traj_db.get_hd_traj(geom=range_sp.wkt)
    if not hd_traj_list:
        logger.info(f"没有高精轨迹")
        return proceed()
    traj_list = [wkt.loads(item['geom']) for item in hd_traj_list]
    # 轨迹截取
    traj_sp_list = park_utils.traj_intercept(traj_list, range_sp.wkt)

    area_sp, intersect_point_list = get_intersects_points(ctx, traj_sp_list, traj_sp_list)
    if IS_TEST == '0':
        logger.info(f"\n高精与AOI交割点集: {intersect_point_list}")
    if not intersect_point_list:
        return proceed()
    # 查询竞品出口数据
    competitor_out_gate_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"采纳轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        convex_buffer_10m_sp = multipoint.convex_hull.buffer(10 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        exist_valid_gate = False
        parking_access = context.ParkingAccess()
        for aoi_access in ctx.aoi_info.blu_access_list:
            node_sp = wkt.loads(aoi_access.node_geom)
            if not utils.is_out_gate(aoi_access.node_id):
                continue
            if not convex_buffer_10m_sp.contains(node_sp):
                continue
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = aoi_access.node_id
            parking_access.node_geom = aoi_access.node_geom
            parking_access.strategy = context.OVERGROUND_OUT_ACCEPT_TRAJ_AREA_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            exist_valid_gate = True
            break
        if exist_valid_gate:
            continue
        # 判断是否存在竞品
        near_competitor_list = [item for item in competitor_out_gate_list
                                if convex_buffer_10m_sp.contains(wkt.loads(item['geom']))]
        if not near_competitor_list:
            continue
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = near_competitor_list[0]['geom']
        parking_access.strategy = context.OVERGROUND_OUT_ACCEPT_TRAJ_AREA_INTERSECT_GATE
        ctx.parking_access_list.append(parking_access)
    proceed()


def competitor_diff(ctx: context.Context, proceed):
    """
    竞品情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin competitor_diff")
    park_access_navi_competitor_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    logger.info(f"竞品数据抓取:{park_access_navi_competitor_list}")
    if not park_access_navi_competitor_list:
        return proceed()
    sub_pois = ctx.dao.poi_online_query.get_park_access_by_kv(park_bid=ctx.parking_info.bid)
    sub_pois_wkt_list = [wkt.loads(item['geom']) for item in sub_pois
                         if item['road_relation']
                         and "link_info" in item['road_relation']
                         and item['road_relation']['link_info']
                         and ("出入口" in item['name'] or '出口' in item['name'])] if sub_pois else []
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        # 竞品引导点附近30m存在精准出入口子点, 过滤
        if len([item for item in sub_pois_wkt_list if poi_navi_sp.distance(item) < 30 / COORDINATE_FACTOR]) > 0:
            logger.info(f"竞品引导点附近30m存在精准出入口子点, 过滤")
            continue
        # 判断子点附近是否存在大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list if utils.is_out_gate(x[0])]
            if not node_ids:
                node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: poi_navi_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.OVERGROUND_OUT_COMPETITOR_GATE
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                ctx.parking_access_list.append(parking_access)
                continue
        # 没有大门,就近绑Link
        nearst_link = ctx.dao.road_dao.get_near_link_by_node_geom_new(poi_navi_sp.buffer(10 / COORDINATE_FACTOR).wkt,
                                                                      poi_navi_sp.wkt)
        if nearst_link:
            link_node_id = park_utils.get_relation_node_by_link(nearst_link['link_id'], poi_navi_sp.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = poi_navi_sp.wkt
            parking_access.link_id = nearst_link['link_id']
            parking_access.link_geom = nearst_link['geom']
            parking_access.strategy = context.OVERGROUND_OUT_COMPETITOR_LINK
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            ctx.parking_access_list.append(parking_access)
            continue
        # 直接取聚类中心点
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = poi_navi_sp.wkt
        parking_access.strategy = context.OVERGROUND_OUT_COMPETITOR_NODE
        parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
        ctx.parking_access_list.append(parking_access)

    return proceed()


def aoi_blu_access(ctx: Context, proceed):
    """
    AOI出口大门 + 轨迹通过
    Args:
        ctx:

    Returns:

    """
    logger.info("begin aoi blu access")
    traj_list = ctx.dao.poi_online_query.get_navigate_traj(bids=[ctx.parking_info.bid])
    logger.info(f"导航轨迹总量:{len(traj_list)}")
    out_aoi_traj_list = list(set([wkt.loads(item['geom']) for item in traj_list]))
    if len(out_aoi_traj_list) == 0:
        return proceed()
    aoi_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else None
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) if ctx.parking_info.park_area_gcj_wkt else None
    range_wkt = ctx.aoi_info.wkt
    if (not aoi_sp and park_area_sp) or (aoi_sp and park_area_sp and aoi_sp.distance(park_area_sp) > 0):
        range_wkt = park_area_sp.wkt
    out_aoi_traj_list = traj_navigate.filter_out_traj_by_wkt(range_wkt, out_aoi_traj_list)

    # 交割点聚类
    cluster_sp_list = cluster_traj_intersects_points(ctx, list(out_aoi_traj_list))
    if not cluster_sp_list:
        return proceed()
    for item in ctx.aoi_info.blu_access_list:
        if not utils.is_out_gate(item.node_id):
            continue
        node_sp = wkt.loads(item.node_geom)
        # 如果聚类存在距离出口20m范围内的轨迹,则认为是出口
        near_cluster_sp_list = [cluster_sp for cluster_sp in cluster_sp_list
                                if cluster_sp.distance(node_sp) <= 20 / COORDINATE_FACTOR]
        if len(near_cluster_sp_list) == 0:
            continue
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_id = item.node_id
        parking_access.node_geom = item.node_geom
        parking_access.strategy = context.OVERGROUND_OUT_BLU_ACCESS_AND_NAVIGATE_TRAJ
        ctx.parking_access_list.append(parking_access)
    proceed()


def high_link_and_out_traj(ctx: context.Context, proceed):
    """
    高通量LINK + 轨迹
    Args:
        ctx:

    Returns:

    """
    logger.info(f"begin high_link_and_out_traj")
    # 获取停车场高通量LINK
    if not ctx.parking_info.road_relation:
        return proceed()
    road_relation = ctx.parking_info.road_relation
    if "link_info" not in road_relation or len(road_relation["link_info"]) == 0:
        return proceed()
    short_link_ids = [item['link_id'] for item in road_relation['link_info'] if item['type'] == 3]
    if not short_link_ids:
        return proceed()
    short_high_link_id = short_link_ids[0]
    long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids([short_high_link_id])
    if not long_link_list:
        return proceed()
    link_list = ctx.dao.road_dao.get_nav_link_by_link_ids_new([long_link_list[0][0]])
    if not link_list:
        return proceed()
    link_info = link_list[0]
    link_sp = wkt.loads(link_info['geom'])
    link_buffer_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
    link_buffer_30m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
    # 获取高通量link轨迹
    traj_list = park_utils.get_high_link_traj(short_high_link_id, ctx.aoi_info.wkt, ctx.parking_info.park_area_gcj_wkt)
    if not traj_list:
        return proceed()
    # 取起点在AOI范围内, 终点在外的轨迹
    out_traj_list = ctx.get_out_park_aoi_traj(traj_list, ctx.aoi_info.wkt, ctx.parking_info.park_area_gcj_wkt)
    # 取与高通量LINK相交轨迹
    intersects_traj_list = [traj_sp for traj_sp in out_traj_list if link_buffer_sp.intersects(traj_sp)]
    if not intersects_traj_list:
        return proceed()
    # 轨迹通过非精准子点,直接取子点
    sub_poi_wkt_list = []
    sub_poi_list = park_utils.get_not_accurate_out_park_access(ctx.parking_info.bid)
    for park_access in sub_poi_list:
        park_access_sp = wkt.loads(park_access['geom'])
        park_access_buffer_sp = park_access_sp.buffer(5 / COORDINATE_FACTOR)
        traj_list = [traj_sp for traj_sp in intersects_traj_list if park_access_buffer_sp.intersects(traj_sp)]
        if len(traj_list) >= 3:
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_geom = park_access['geom']
            parking_access.strategy_value = f"轨迹通量:{len(traj_list)}"
            parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            parking_access.strategy = context.OVERGROUND_OUT_HIGH_LINK_TRAJ_AND_SUB_POI
            ctx.parking_access_list.append(parking_access)
            sub_poi_wkt_list.append(wkt.loads(park_access['geom']))
    # 存在非精准子点, 且在LINK附近直接过
    if sub_poi_wkt_list and len([item for item in sub_poi_wkt_list if item.intersects(link_buffer_30m_sp)]) > 0:
        return proceed()
    # 取停车场在LINK投影
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    foot_point = utils.get_foot_point(park_sp, link_sp)
    if not foot_point:
        node_ids = [link_info['s_nid'], link_info['e_nid']]
        node_list = ctx.dao.road_dao.get_node_geom(node_ids)
        if not node_list:
            return proceed()
        foot_point = min([wkt.loads(item[0]) for item in node_list], key=lambda x: x.distance(park_sp))
    parking_access = context.ParkingAccess()
    parking_access.type = context.PARKING_ACCESS_TYPE_LINK
    parking_access.node_id = link_info['s_nid']
    parking_access.node_geom = foot_point.wkt
    parking_access.link_id = link_info['link_id']
    parking_access.link_geom = link_info['geom']
    parking_access.strategy = context.OVERGROUND_OUT_HIGH_LINK_TRAJ
    ctx.parking_access_list.append(parking_access)
    return proceed()


def aoi_near_open_gate_and_traj(ctx: context.Context, proceed):
    """
    AOI 边框附近出口大门 + 轨迹
    Args:
        ctx:

    Returns:

    """
    logger.info(f"begin aoi near open gate and traj")
    out_traj_list = ctx.traj.out_navigate_traj_list + ctx.traj.out_park_accept_traj_list + ctx.traj.out_end_traj_list
    # 获取AOI附近LINK出口大门, 出口轨迹通过
    if not ctx.aoi_info.wkt or not out_traj_list:
        return proceed()
    # 获取AOI边框linestring
    link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt_new(ctx.aoi_info.wkt)
    if not link_list:
        return proceed()
    out_node_list = []
    for link_info in link_list:
        if link_info['kind'] == 10 or link_info['kind'] <= 7:
            continue
        link_sp = wkt.loads(link_info['geom'])
        link_buffer_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
        # 获取LINK上出口大门
        nav_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(link_buffer_sp.wkt)
        if not nav_gate_list:
            continue
        for nav_gate in nav_gate_list:
            gate_traffic_list = ctx.dao.dest_traj_dao.get_node_passage_by_long_node_id(nav_gate[0])
            if not gate_traffic_list:
                continue
            if gate_traffic_list[0]['passage'] != 'OUT':
                continue
            out_node_list.append(nav_gate[0])
    if not out_node_list:
        return proceed()
    node_list = ctx.dao.road_dao.get_node_list(out_node_list)
    if not link_list:
        return proceed()
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    aoi_boundary_sp = aoi_sp.exterior

    for node_info in node_list:
        node_sp = wkt.loads(node_info['geom'])
        node_buffer_sp = node_sp.buffer(3 / COORDINATE_FACTOR)
        if node_sp.distance(aoi_boundary_sp) > 20 / COORDINATE_FACTOR:
            continue
        intersects_traj_list = [traj_sp for traj_sp in out_traj_list if node_buffer_sp.intersects(traj_sp)]
        if len(intersects_traj_list) < 5:
            continue
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_id = node_info['node_id']
        parking_access.node_geom = node_info['geom']
        parking_access.strategy_value = f"轨迹通过:{len(intersects_traj_list)}"
        parking_access.strategy = context.OVERGROUND_OUT_AOI_NEAR_OUT_GATE_AND_TRAJ
        ctx.parking_access_list.append(parking_access)
    return proceed()


def doubly_link_and_traj(ctx: context.Context, proceed):
    """
    双向LINK + 轨迹
    Args:
        ctx:

    Returns:

    """
    logger.info(f"begin doubly_link_and_traj")
    # 获取停车场关联出入口双向路LINK
    park_access_list = ctx.dao.poi_online_query.get_park_access_by_kv(park_bid=ctx.parking_info.bid)
    out_traj_list = ctx.traj.out_navigate_traj_list + ctx.traj.out_park_accept_traj_list + ctx.traj.out_end_traj_list
    for park_access in park_access_list:
        if not park_access['road_relation'] or "link_info" not in park_access['road_relation']:
            continue
        link_infos = park_access['road_relation']['link_info']
        if len(link_infos) == 0:
            continue
        out_link_ids = [item['link_id'] for item in link_infos if item['type'] == 2 and item['orientation'] == 2]
        if len(out_link_ids) > 0:
            continue
        link_ids = [item['link_id'] for item in link_infos if item['type'] == 2]
        if not link_ids:
            continue
        long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids(link_ids)
        if not long_link_list:
            continue
        link_list = ctx.dao.road_dao.get_nav_link_by_link_ids_new([long_link_list[0][0]])
        if not link_list or link_list[0]['dir'] != 1:
            continue
        # 计算LINK轨迹通量
        park_link_traj = park_utils.cal_match_link_traj_num(out_traj_list, link_list[0]['link_id'])
        if len(park_link_traj) < 3:
            continue
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.link_id = link_list[0]['link_id']
        parking_access.node_id = link_list[0]['s_nid']
        parking_access.node_geom = park_access['geom']
        parking_access.strategy_value = f"轨迹通过:{len(park_link_traj)}"
        parking_access.strategy = context.OVERGROUND_OUT_DOUBLE_LINK_AND_TRAJ
        ctx.parking_access_list.append(parking_access)
    return proceed()


def aoi_out_gate_and_traj(ctx: context.Context, proceed):
    """
    AOI关联纯出口 + 轨迹
    Args:
        ctx:

    Returns:

    """
    logger.info(f"begin aoi_out_gate_and_traj")
    out_traj_list = ctx.traj.out_navigate_traj_list + ctx.traj.out_park_accept_traj_list + ctx.traj.out_end_traj_list
    for item in ctx.aoi_info.blu_access_list:
        gate_traffic_list = ctx.dao.dest_traj_dao.get_node_passage_by_long_node_id(item.node_id)
        if not gate_traffic_list:
            continue
        if gate_traffic_list[0]['passage'] != 'OUT':
            continue
        # 获取大门关联的outlink
        nav_gate_list = ctx.dao.road_dao.get_nav_gate_by_node_id(item.node_id)
        if not nav_gate_list:
            continue
        out_link_id = nav_gate_list[0]['in_linkid']
        # 计算LINK轨迹通量
        park_link_traj = park_utils.cal_match_link_traj_num(out_traj_list, out_link_id)
        if len(park_link_traj) < 3:
            continue
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_id = item.node_id
        parking_access.node_geom = item.node_geom
        parking_access.strategy_value = f"轨迹通过:{len(park_link_traj)}"
        parking_access.strategy = context.OVERGROUND_OUT_AOI_OUT_GATE_AND_TRAJ
        ctx.parking_access_list.append(parking_access)
    return proceed()


def cluster_traj_intersects_points(ctx: context.Context, traj_list):
    """
    导航轨迹交割点聚类
    Args:
        ctx:

    Returns:

    """
    area_sp, intersect_point_list = get_intersects_points(ctx, traj_list, traj_list)
    if not intersect_point_list:
        return []
    if IS_TEST == '0':
        logger.info(f"\n导航轨迹与AOI交割点集: {intersect_point_list}")
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    if len(points) < 30:
        db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=3).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    valid_convex_hull_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.95:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"导航轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        valid_convex_hull_wkt_list.append(convex_hull)
    return valid_convex_hull_wkt_list


def park_access_filter(ctx: Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.out_park_access_qc(ctx)
    return proceed()


def get_intersects_points(ctx: Context, parent_traj_list: list, park_traj_list: list, area_buffer_distance=10):
    """
    获取轨迹交割点
    常规垂类直接用AOI面与轨迹交割,景区&交通枢纽用停车场面做交割,无父点也用停车场面做交割
    Args:
        ctx:
        parent_traj_list:
        park_traj_list:
        area_buffer_distance:

    Returns:

    """
    # 取轨迹与AOI面 or 停车航面交点

    park_area_gcj_wkt = ctx.parking_info.park_area_gcj_wkt
    if not park_area_gcj_wkt:
        park_area_gcj_wkt = ctx.parking_info.park_area_aoi_30m_wkt
    area_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt else wkt.loads(park_area_gcj_wkt)
    intersect_point_list = park_utils.calcul_intersect_points_by_out_traj(area_sp.wkt, parent_traj_list,
                                                                          area_buffer_distance=area_buffer_distance)
    # 交通枢纽 & 景区 & 无父点 直接用停车场面做交割
    if (ctx.aoi_info.std_tag in AREA_FIRST_CATEGORY or '航站楼' in ctx.parking_info.name or not ctx.aoi_info.wkt) \
            and ctx.parking_info.park_area_gcj_wkt:
        area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
        # 取与停车场面相交最多的轨迹(父点轨迹 or 停车场轨迹)
        traj_list = [traj_sp for traj_sp in park_traj_list if traj_sp.intersects(area_sp)]
        park_intersects_traj_list = [traj_sp for traj_sp in park_traj_list if traj_sp.intersects(area_sp)]
        traj_list = park_intersects_traj_list if len(park_intersects_traj_list) > len(traj_list) else traj_list
        intersect_point_list = park_utils.calcul_intersect_points_by_out_traj(area_sp.wkt, traj_list,
                                                                              area_buffer_distance=area_buffer_distance)
        logger.info(f"交通枢纽 & 景区 & 无父点 直接用停车场面做交割")
    return area_sp, intersect_point_list


def save_park_access_intelligence_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_access_intelligence_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type}, 是否有效:{item.is_valid}, node_id位置\t{item.node_id},"
                        f"node_geom位置\t{item.node_geom}, 策略:{item.strategy}, confidence:{item.confidence},"
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, strategy_values:{item.strategy_value}")
        valid_geom_list = [item.node_geom for item in ctx.parking_access_list if item.is_valid]
        invalid_geom_list = [item.node_geom for item in ctx.parking_access_list if not item.is_valid]
        logger.info(f"有效:{valid_geom_list}, 无效:{invalid_geom_list}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, 是否有效:{park_access.is_valid},node_id位置\t{park_access.node_id}, "
                        f"策略:{park_access.strategy}, strategy_value:{park_access.strategy_value}, "
                        f"link_id\t{park_access.link_id}, 过滤原因:{park_access.filter_reason}")
            continue
        guid = str(uuid.uuid4()).replace("-", "")
        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': ctx.intelligence_type,
            'batch_number': f"park_access_overground_out_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': f"park_access_overground_out_add_{guid}",
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
            'park_access_bid': '',
            'confidence': park_access.confidence,
            'remark': park_access.remark,

        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)

    proceed()


def main(bid_list: list):
    """
    地上停车场情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场', '停车场', '立体停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"开始挖掘地上停车场情报:{parking[7]}")
            ctx = Context(parking)
            ctx.intelligence_type = "park_access_overground_out_add"
            ctx.init_context()
            ctx.get_park_sub_pois()
            # AOI 场景
            if ctx.aoi_info.bid:
                # 获取出入口信息
                ctx.get_aoi_blu_access_list()
                # 轨迹过滤AOI范围外的
                ctx.filter_traj_by_aoi()
                # 计算AOI出入口轨迹通量
                ctx.cal_dest_traj_pass_blu_access()
            ctx.traj.filter_aoi_dest_traj_list = ctx.traj.dest_traj_list
            ctx.init_out_traj()
            ctx.traj.out_park_accept_traj_list = park_utils.filter_noise_trace_by_angle_and_intersects(
                ctx.traj.out_park_accept_traj_list, self_intersects_num=5)
            # traj_list = park_utils.get_high_link_traj('16731439130', ctx.aoi_info.wkt,
            #                                           ctx.parking_info.park_area_gcj_wkt)
            # for traj in traj_list:
            #     print(traj.geom)
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[7]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    # 取四大垂类停车场BID
    if bid_file == '0' and bids == '0':
        poi_online_query = PoiOnlineQuery()
        park_import_list = poi_online_query.get_park_import_list()
        bid_list = [item['bid'] for item in park_import_list]
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
