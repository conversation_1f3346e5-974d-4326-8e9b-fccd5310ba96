<?php

require_once(dirname(__FILE__) . "/utils.php");

$file = $argv[1];
run_gl_service($file);

/**
 * @param �����ص������ļ�
 * @return ��������
 */
function run_gl_service($file)
{
	$fp = fopen($file, 'r');
	if ( $fp === false ) 
	{
		return array(
			'errno' => 1,
			'error' => sprintf('open %s fail', $file),
		);
	}

	$count = 0;
	while ( !feof($fp) ) 
	{
		$count ++;
		$line = fgets($fp);
		if($line==""){
				continue;
		}
		$ret = parser_line($line, $data);
		if ($ret['errno'] == 1)
		{
			write_msg("log_error_file_format\n", "stderr");
			continue;
		}

		$raw_uid=$data['id'];
		run_gl($data, $res, 2);

		if ($res['errno'] != 0) 
		{
			print  "$raw_uid\t0\n";
		} 
		else 
		{
			$ret_cnt = count($res['list']);
			if ($ret_cnt < 1)
			{
				print  "$raw_uid\t1\n";
			}
			else
			{
				print "$raw_uid";
				foreach($res['list'] as $value) 
				{
					$ret_id   = $value['relate_bid'];
					$me_score = $value['me_score'];
					$lstm_gbdt_score = $value['lstm_gbdt_score'];

					if ($ret_id != $data['id']){
						print "\t" . $ret_id . ':' . $me_score . ':' . $lstm_gbdt_score;
					}
				} 
				print "\n";
			}
		}
	}
	
	fclose($fp);

	return array('errno'=>0, 'error'=>'OK');
}
