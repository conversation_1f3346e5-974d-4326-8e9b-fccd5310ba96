#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source $CUR_DIR"/conf.sh"

function GenDiff(){
	awk -F "\t" '
	ARGIND==1{
		mid = $1;
		shape = $6;
		old[mid] = shape;
	}
	ARGIND==2{
		mid = $1;
		shape = $6;
		if(mid in old){
			if(shape != old[mid])
				print mid "\t" old[mid] "\t" shape > "'${DataPath}'/shape.match";
		}
		else
			print mid > "'${OutputPath}'/building.add";
		new[mid];
	}
	END{
		for(i in old){
			if(!(i in new))
				print i > "'${OutputPath}'/building.del";
		}
	}' ${DATA_PATH}/jichudiwu/data.building ${DATA_PATH}/building/online/new.building;

	${PYTHONBIN} ${BinPath}/GenDiff.py ${DataPath}/shape.match ${OutputPath}/building.change;

	awk -F "\t" '
	ARGIND==1{
		mid = $1;
		shape = $6;
		old[mid] = shape;
	}
	ARGIND==2{
		mid = $1;
		shape = $6;
		if(mid in old){
			if(shape != old[mid])
				print mid "\t" old[mid] "\t" shape > "'${DataPath}'/aoi.match";
		}
		else
			print mid > "'${OutputPath}'/aoi.add";
		new[mid];
	}
	END{
		for(i in old){
			if(!(i in new))
				print i > "'${OutputPath}'/aoi.del";
		}
	}' ${DATA_PATH}/jichudiwu/data.aoi ${DATA_PATH}/aoi/online/mid.aoi.online;

	${PYTHONBIN} ${BinPath}/GenDiff.py ${DataPath}/aoi.match ${OutputPath}/aoi.change;
	cat ${OutputPath}/*add ${OutputPath}/*change > ${OutputPath}/diwu.change.all;
}

function Main(){
	GenDiff;
}

Main;
