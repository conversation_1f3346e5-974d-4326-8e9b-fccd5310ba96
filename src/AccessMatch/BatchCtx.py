"""
批次处理的context文件
"""
import psycopg2

from src.AccessMatch.pg_tool import PgTool


class BatchContext:
    """
    1.0批处理的context, 用于连接公用与连接检测
    """

    def __init__(self, bid=None):
        """
        初始化
        """
        self.bid = bid
        self.debug = False
        if self.bid:
            self.debug = True
        self.strategy_node_max = 50
        self.pg_tool = PgTool(aoi_use_proxy=True)
        # 调试信息
        self.debug_ret = {}

    def pg_reconnect(self):
        """
        尝试重新连接
        :return:
        """
        self.pg_tool.close()
        self.pg_tool = PgTool(aoi_use_proxy=True)

    def pg_close(self):
        """
        关闭连接
        :return:
        """
        self.pg_tool.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.pg_close()

    def check_pg_connection(self, conn):
        """
        检查conn 是否有效
        :param conn:
        :raises ConnectionError
        :return:
        """
        sql = 'select 1'
        with conn.cursor() as cursor:
            try:
                cursor.execute(sql)
                cursor.fetchone()
                conn.commit()
                return True
            except Exception as e:
                print("check_conn error", e)
                return False

    def check_is_inner_road(self, form):
        """
        判断是否为内部路
        :param form:
        :return:
        """
        return '52' in form.split(',')

    def get_intersects_nav_gate_by_aoi(self, aoi_geom):
        """
        获取AOI范围内的道路大门，过滤内内大门
        :param aoi_geom:
        :return [node_id]:
        """
        intersects_gate_node_id = []
        # 查询相交Node
        intersects_node_list = self.get_intersects_node_by_geom(aoi_geom)
        if intersects_node_list is None or len(intersects_node_list) == 0:
            return intersects_gate_node_id
        intersects_node_id_list = [intersects_node[0] for intersects_node in intersects_node_list]

        # 根据Node查询道路大门
        intersects_nav_gate_list = self.get_nav_gate_by_node(intersects_node_id_list)
        if intersects_nav_gate_list is None or len(intersects_nav_gate_list) == 0:
            return intersects_gate_node_id

        # 通过判断nav_link form过滤相交大门的内内大门
        link_id_list = set()
        link_id_to_node_map = {}
        for intersects_nav_gate in intersects_nav_gate_list:
            link_id_list.add(intersects_nav_gate[1])
            link_id_list.add(intersects_nav_gate[2])
            link_id_to_node_map[intersects_nav_gate[1]] = intersects_nav_gate[0]
            link_id_to_node_map[intersects_nav_gate[2]] = intersects_nav_gate[0]
        nav_link_list = self.get_nav_link_by_link_id_form(list(link_id_list))
        if nav_link_list is None or len(nav_link_list) == 0:
            return intersects_gate_node_id

        for nav_link in nav_link_list:
            link_id = nav_link[0]
            intersects_gate_node_id.append(link_id_to_node_map[link_id])
        return intersects_gate_node_id

    def get_intersects_node_by_geom(self, geom):
        """
        获取指定范围内的Node
        :param geom:
        :return:
        """
        sql = "select node_id from nav_node nn where st_intersects(st_geomfromtext('{}',4326), nn.geom)".format(geom)
        self.pg_tool.cursor_road.execute(sql)
        res = self.pg_tool.cursor_road.fetchall()
        self.pg_tool.conn_road.commit()
        return res

    def get_nav_gate_by_node(self, node_id_list):
        """
        获取道路大门
        :param node_id_list:
        :return:
        """
        sql = "select node_id,in_linkid,out_linkid,type from nav_gate where node_id in('{}')".format(
            "','".join(node_id_list))
        self.pg_tool.cursor_road.execute(sql)
        res = self.pg_tool.cursor_road.fetchall()
        self.pg_tool.conn_road.commit()
        return res

    def get_nav_link_by_link_id_form(self, link_id_list):
        """
        获取nav_link
        :param link_id_list:
        :return:
        """
        link_ids = "','".join(link_id_list)
        sql = f"""
                select link_id, form, kind, st_astext(geom) from nav_link 
                where form != '52' and link_id in('{link_ids}')
            """
        self.pg_tool.cursor_road.execute(sql)
        res = self.pg_tool.cursor_road.fetchall()
        self.pg_tool.conn_road.commit()
        return res
