#!/bin/bash
LocalPath=`dirname $0`;
source "$LocalPath"/conf.sh;
#### 竞品抓取AOI数据融合上线
## 1.  初始化: 获取线上全量的AOI数据 从抓取的竞品数据中过滤掉
## 2.  标准化数据格式: uid + name + city + address + x + y + phone + tag + catalog_id + src + gaode_id + shape + pc_bid
## 3.  拆分结果: 删除or非发布状态or缺失bid -> ${TMPPATH}/data.no_pc_bid  剩余 -> ${TMPPATH}/data.with_pc_bid
## 4.  无判重结果部分判重
## 5.  去除线上已有AOI的bid
## 6.  线上压盖去除
## 7.  自交多边形去除 大面积投毒AOI去除
## 8.  自身压盖去除
## 9.  去除主点不在AOI内数据
## 10. 去除STD_TAG是出入口门的数据 
## 11. 整理上线格式

TodayDate=$(date -d "1 days ago" +"%Y-%m-%d");

#拉全量 aoi 及高德 aoi 数据
function Initial(){
	cp "$DATA_PATH/aoi/$TodayDate.csv" "$DATAPATH/aoi.data"
	sh "$LocalPath/"getGD.sh; # get Gaode Data from Hadoop
}

#对高德 aoi 数据进行格式转换
function GetStdData(){
	${PYTHONBIN} ${PythonScript}/GetStdData.py ${DATAPATH}/gaode.aoi ${DATAPATH}/data.input.temp;
	cat ${DATAPATH}/data.input.temp | sort -u > ${DATAPATH}/data.input;
	rm ${DATAPATH}/data.input.temp;
}

#判重
function PC(){
	awk -F "\t"	'
	BEGIN{
		print "uid\tname\tcity\taddress\tpoint_x\tpoint_y\tphone\tstd_tag\tuid_src"
	}
	{
		uid = $1;
		name = $2;
		city = $3;
		address = $4;
		point_x = $5;
		point_y = $6;
		phone = "";
		std_tag = "";
		uid_src = "gaode";
		print uid "\t" name "\t" city "\t" address "\t" point_x "\t" point_y "\t" phone "\t" std_tag "\t" uid_src 
	}' ${DATAPATH}/data.input   > ${TMPPATH}/AOI_PC.input;

	wget_addr_file="$TMPPATH/wget_addr"
	rm $wget_addr_file
	touch $wget_addr_file
	#curl -T "$TMPPATH/AOI_PC.input" "$FTP_SERVER/"
	cp "$TMPPATH/AOI_PC.input" "$FTP_PATH/"
	#inFilePath=${InFilePath}
	now_date=$(date +"%Y%m%d");
	${PHPBIN} ${PhpScript}/send_lixing.php "$FTP_SERVER/AOI_PC.input" "$now_date" "$wget_addr_file";
	downloadPath=$(head -1 "$wget_addr_file" | cut -f 2)
	wget --no-passive-ftp $downloadPath -O ${OUTPUTPATH}/AOI_PC.out
	
	awk -F "\t" '
	{
	  cnt=$1;	
	  res_type=$10;
	  if( res_type == "gl"){
	      print $0  > "'${TMPPATH}'/data.pc_gl"
	  } else {
	      print $0 > "'${TMPPATH}'/data.add_and_ns"
	  }
	}' ${OUTPUTPATH}/AOI_PC.out 
	echo $"PC process done .................................."
}

function Process_add_and_ns(){
	#step1. process as_hash format
	awk -F "\t" '
	ARGIND==1{
        city=$1;
	    city_id=$2;
	    map[city] = city_id;
	}
	ARGIND==2{
	    print $0;
	}
	ARGIND==3{
	    
	    cnt = $1;
	    name = $2;
	    city = $3;
	    if(city in map){
		city_id = map[city]
	        print cnt "\t" city_id "\t" name  
	    }
	}' ${AS_HASH}/city2city_id.conf ${AS_HASH}/title ${TMPPATH}/data.add_and_ns > ${TMPPATH}/as_hash.input
	# step2. use as_hash service
	cat  ${TMPPATH}/as_hash.input | ${AS_HASH}/as_hash -d ${AS_HASH}/conf/ -f hash.conf > ${TMPPATH}/as_hash.out	
   

	#  as_hash.out format e.g.: '盐城市质量技术监督局|223' 100000(cnt)  1355269572568309679(bid)     127     -1
	# step3. lookup t_poi_res for bid's name and format the data
	awk -F "\t" '
	ARGIND==1{
	    bid=$3;
	    map[bid]=$0;
	}
	ARGIND==2{
	    bid=$3;
	    baidu_name=$4;
	    if(bid in map){
		split(map[bid],arr,"\t");
		split(arr[1],b,"|");
		gd_name=b[1];
		cnt = arr[2];
		print cnt "\t" bid "\t" substr(gd_name,2,length(gd_name)) "\t" baidu_name;
	     }
    }' ${TMPPATH}/as_hash.out ${POIDATAPATH}/t_poi_res > ${TMPPATH}/nameComp.Input
	# step4. use LSTM name Compare model to calculate the two POIs' name similarity
	rm ${TEXT_CLASSIFICATION}/infer/*;
    cp ${TMPPATH}/nameComp.Input ${TEXT_CLASSIFICATION}/infer/test;
	cd ${TEXT_CLASSIFICATION};
	sh infer.sh;
	mv ${TEXT_CLASSIFICATION}/output ${TMPPATH}/nameComp.output;
	${PYTHONBIN} ${PythonScript}/mergeResult.py ${TMPPATH}/nameComp.output ${TMPPATH}/as_hash.gl


	#step5. merge as_hash.gl to data.pc_gl
	awk -F "\t" '
	ARGIND==1{
		cnt=$1;
		bid=$2;
		map[cnt]=bid;
	}
	ARGIND==2{
		cnt = $1;
		if(cnt in map){
			temp=$1;
			for(i=2;i<=10;i++){
				temp=temp"\t"$i
			}
			print temp "\t" map[cnt] > "'${TMPPATH}'/as_hash.gl.formated"
		}
	}
	ARGIND==3{
		cnt = $1;
		if(cnt in map){
			next
		} else {
			print $0 > "'${TMPPATH}'/data.add_and_ns.AS_Falied"
		}
	}
	' ${TMPPATH}/as_hash.gl ${OUTPUTPATH}/AOI_PC.out ${TMPPATH}/data.add_and_ns
	cat ${TMPPATH}/as_hash.gl.formated >> ${TMPPATH}/data.pc_gl;

}

#将高德 aoi 非两类，一类百度 aoi 库中已存在，产出 diff aoi，一类百度库中不存在
function FilterExistedAoi(){
	awk -F "\t" '
	ARGIND==1{
		cnt = $1;
		gl_bid=$11;
		map[cnt]=$11;
	}
	ARGIND==2{
		cnt = $1;
		if(cnt in map){
			temp=$1
			for(i=2;i<=NF-1;i++){
				temp=temp "\t" $i
			}
			temp=temp"\t"map[cnt]
			print temp
		}
	}
	' ${TMPPATH}/data.pc_gl ${DATAPATH}/data.input > ${TMPPATH}/data.gl

	cat ${TMPPATH}/data.gl | awk -F "\t" '
	ARGIND==1{
		bid = $3;
		map[bid];
	}
	ARGIND==2{
		bid = $13;
		if(bid in map){
			print $0 > "'${OUTPUTPATH}'/data.bid_existed";
			next;
		}
		print $0 > "'${TMPPATH}'/data.FilterExistedAoi.out";
	}' ${DATAPATH}/aoi.data -;
	
	
	awk -F "\t" '
	ARGIND==1{
		bid = $13;
		map[bid];
	}
	ARGIND==2{
		bid = $3;
		shape = $2;
		if(bid in map)
			map[bid] = shape;
	}
	ARGIND==3{
		bid = $13;
		gid = $11;
		shape = $12;
		print gid "\t" shape "\t" bid "\t" map[bid];
	}' ${OUTPUTPATH}/data.bid_existed ${DATAPATH}/aoi.data ${OUTPUTPATH}/data.bid_existed > ${TMPPATH}/AoiDiff.input;
	${PYTHONBIN} ${PythonScript}/AoiDiff.py ${TMPPATH}/AoiDiff.input ${OUTPUTPATH}/AoiDiff.output;
	echo $"FilterExistedAoi done....................."
}

#将百度不存在的 aoi，分为压盖 aoi 和不压盖 aoi ，压盖率超过 50%，则认为压盖
function FilterGlandAoi(){
    rm -r ${CITYPATH}/*;
    
	for city in `cat ${TMPPATH}/data.FilterExistedAoi.out | awk -F "\t" '{if($3 != "")print $3}' | sort -u`; do
        mkdir -p ${CITYPATH}/${city};
    done
    
	for city in `cat ${DATAPATH}/aoi.data | awk -F "\t" '{if($11 != "")print $11}' | sort -u`; do
        mkdir -p ${CITYPATH}/${city};
    done
	
    cat ${DATAPATH}/aoi.data | awk -F "\t" '{
        city = $11;
        if(city == "")
            next;
        print $3 "\t" $2 > "'${CITYPATH}/'" city "/aoi.baidu";
    }'
    cat ${TMPPATH}/data.FilterExistedAoi.out | awk -F "\t" '{
        city = $3;
        if(city == "")
            next;
        print $1 "\t" $12 > "'${CITYPATH}/'" city "/aoi.gaode";
    }'
    for city in `ls ${CITYPATH}`; do
    	if [ -s ${CITYPATH}/${city}/aoi.baidu -a -s ${CITYPATH}/${city}/aoi.gaode ]; then
        	${PYTHONBIN} ${PythonScript}/intersectionFilter.py ${CITYPATH}/${city}/aoi.baidu ${CITYPATH}/${city}/aoi.gaode ${CITYPATH}/${city}/no_intersection ${CITYPATH}/${city}/intersection > ${CITYPATH}/${city}/log.intersection &
        fi
    done
    wait
   
    cat ${CITYPATH}/*/intersection > ${TMPPATH}/intersection
    cat ${CITYPATH}/*/no_intersection > ${TMPPATH}/no_intersection
    awk -F "\t" '
    ARGIND==1{
        cnt = $1;
        map[cnt]
    }
    ARGIND==2{
        cnt = $1;
        if(cnt in map){
            print $0 > "'${TMPPATH}'/FilterGlandAoi.intersection"
	} else {
	    print $0 > "'${TMPPATH}'/FilterGlandAoi.out"
	}
    }' ${TMPPATH}/intersection ${TMPPATH}/data.FilterExistedAoi.out 
    echo $"FilterGlandAoi done..............................."
}

#对高德 aoi 面积超过 10000000 的数据进行错误检测（过滤不是简单多边形的数据）
function FilterErrorData(){
    ${PYTHONBIN} ${PythonScript}/GetArea.py ${TMPPATH}/FilterGlandAoi.out ${TMPPATH}/FilterGlandAoi.out.area;
    cat ${TMPPATH}/FilterGlandAoi.out.area | awk -F "\t" '{
    	area = $NF;
    	if(area >= 10000000)
    		next;
    	print $0;
    }' | cut -f 1-13 > ${TMPPATH}/FilterErrorData.input.1;
     
    #  check the geometry is a simple shape
    ${PYTHONBIN} ${PythonScript}/SelfIntersectionFilter.py ${TMPPATH}/FilterErrorData.input.1 ${TMPPATH}/data.FilterErrorData.out;
    echo $"FilterErrorData done........................"
}

function SelfGlandFilter(){
    cat ${TMPPATH}/data.FilterErrorData.out | awk -F "\t" '{ 
        city = $3;
        if(city == "")
            next;
        print $1 "\t" $12 > "'${CITYPATH}/'" city "/aoi.SelfGlandFilter";
    }'
    for city in `ls ${CITYPATH}`; do
		if [ -s ${CITYPATH}/${city}/aoi.SelfGlandFilter ]; then
			${PYTHONBIN} ${PythonScript}/ShapeMap.py ${CITYPATH}/${city}/aoi.SelfGlandFilter ${CITYPATH}/${city}/aoi.SelfGlandFilter ${CITYPATH}/${city}/result.SelfFilter  > ${CITYPATH}/${city}/log.SelfFilter &
		fi
	done    
    wait
    cat ${CITYPATH}/*/result.SelfFilter | awk -F "\t" '
    ARGIND==1{
        mid = $1;
        map[mid] = $NF;
    }
    ARGIND==2{
        id = $1;
        if(!(id in map))
            next;
        for(i = 2;i <= NF;i++){
            split($i, list, "|");
            tmp_id = list[1];
            if(!(tmp_id in map) || id == tmp_id)
                continue;            
            if(map[id] >= map[tmp_id] && !(id in del))
                del[tmp_id];
        }
    }
    ARGIND==3{
        mid = $1;
        if(mid in del)
            print $0 > "'${TMPPATH}'/SelfGlandFilter.Intersection";
        print $0 > "'${TMPPATH}'/SelfGlandFilter.out";
    }' ${TMPPATH}/FilterGlandAoi.out.area - ${TMPPATH}/data.FilterErrorData.out;
    echo $"SelfGlandFilter done................"
}

function GetOnlineFormat(){
    echo $"GetOnlineFOrmat!!!!"	

	${PYTHONBIN} ${PythonScript}/GetOnlineFormat.py ${TMPPATH}/SelfGlandFilter.out ${OUTPUTPATH}/online.all.tmp1;

	awk -F "\t" '
	{gid=$14; if(gid in map){next} else {print $0 ; map[gid]} } ' ${OUTPUTPATH}/online.all.tmp1 > ${OUTPUTPATH}/online.all.tmp2
	
	rm ${OUTPUTPATH}/online.all.tmp1;
	mv ${OUTPUTPATH}/online.all.tmp2 ${OUTPUTPATH}/online.all.tmp1
	
	awk -F "\t" '
	ARGIND==1{
		bid = $1;
		releaseBid = $2;
		map[bid] = releaseBid;
	}
	ARGIND==2{
		bid = $3;
		if (bid in map){
			bid = map[bid]
			tmp = $1 "\t" $2 "\t" bid
			for(i=4; i<=NF; i++){
				tmp =tmp "\t" $i;
			}
			print tmp;
			next
		} else {
			print $0
		}
		
	}' ${DATA_PATH}/bid2releasebid ${OUTPUTPATH}/online.all.tmp1 > ${OUTPUTPATH}/online.all.releaseBid

	awk -F "\t" '
	ARGIND==1{
		bid = $3;
		city = $11;
		map[bid] = $0 ;
	}
	ARGIND==2{
		uid = $3;
		std_tag = $24;
		x = $12;
		y = $13;
		city = $20;
		status = $30;
		if (uid in map){
			l=split(map[uid],a,"\t");
			temp=a[1];
			for(i=2;i<=10;i++){
				temp = temp "\t" a[i]
			}
			temp = temp "\t" city "\t" a[12] "\t" a[13] "\t" a[14];
			
			if( std_tag ~"出入口|商圈|行政地标|道路|丽人|美食|门址|铁路|行政地标|街"){
				print temp "\t" std_tag "\t" status "\t" x "\t" y > "'${OUTPUTPATH}'/std_tag.filtered"
			} else {
				if (status ==1 || status ==15){
					print temp "\t" std_tag "\t" status "\t" x "\t" y > "'${OUTPUTPATH}'/online.all.tmp2";
				} else {
					print temp "\t" std_tag "\t" status "\t" x "\t" y > "'${OUTPUTPATH}'/status.filtered"
				}
			}
		
		}
	}' ${OUTPUTPATH}/online.all.releaseBid ${DATA_PATH}/poi/poi_res.utf8;


	${PYTHONBIN} ${PythonScript}/poiNotInAoi.py ${OUTPUTPATH}/online.all.tmp2 ${OUTPUTPATH}/data.poiNotInAoi ${OUTPUTPATH}/online.all;


	awk -F "\t" '{
		bid = $3;
		if(bid in map){
			print $0 > "'${OUTPUTPATH}/bid.duplicated'";
			next;
		}else{
			print $0 > "'${OUTPUTPATH}/online.final'";
			map[bid];
		}
	}' ${OUTPUTPATH}/online.all;

	awk -F "\t" '
	ARGIND==1{
		bid=$3;
		map[bid]
	}
	ARGIND==2{
		bid=$3;
		name = $4;
		city=$11;
		
		if(bid in map){
			next
		} else {
			last_name = substr(name, length(name)-1, length(name));
			if( city ~"香港|澳门"){
				next
			}
			
			if( std_tag ~"出入口|道路|门址|铁路|街|商圈|行政"){
				next
			}
			
			print $0
		}
		
	}' ${DATAPATH}/aoi.data ${OUTPUTPATH}/online.final > ${OUTPUTPATH}/online.final.checked
	
	cat ${OUTPUTPATH}/online.final.checked | cut -f 1-14 > ${OUTPUTPATH}/online.final;
	echo $"DONE!!!!!!!!!!!!!!!!!!!!!!!"
	now_date=`date -d "1 days ago" +"%Y%m%d"`;
	now_date=${now_date};
	file_name="tasc_stat_"$now_date;
	echo -e "AOI新增数量" "\t" "number_of_new_aoi_data" "\t"  `wc -l ${OUTPUTPATH}/online.final | awk -F " " '{print $1}'`  >  ${OUTPUTPATH}/report;
	cat ${OUTPUTPATH}/report | iconv -f gb18030 -t utf8 -c > ${OUTPUTPATH}/$file_name;
	rm ${OUTPUTPATH}/report;

	#$HADOOP fs -rm /app/lbs/lbs-poi/AOI/gaode/online.final
	#$HADOOP fs -put ${OUTPUTPATH}/online.final  /app/lbs/lbs-poi/AOI/gaode/online.final

	mv "${OUTPUTPATH}/online.final" "${DATA_PATH}/gd_online.final"

}


function main(){
	echo "one"
	#拉全量 aoi 及高德 aoi 数据
	Initial;
	echo "two"
	#对高德 aoi 数据进行格式转换
	GetStdData;
	echo "three"
	#判重
	PC;
	echo "four"
	#Process_add_and_ns;
	#将高德 aoi 非两类，一类百度 aoi 库中已存在，产出 diff aoi，一类百度库中不存在
	FilterExistedAoi;
	echo "five"
	#将百度不存在的 aoi，分为压盖 aoi 和不压盖 aoi ，压盖率超过 50%，则认为压盖 
	FilterGlandAoi;
	echo "six"
	#对高德 aoi 面积超过 10000000 的数据进行错误检测（过滤不是简单多边形的数据）
	FilterErrorData;
	echo "seven"
	SelfGlandFilter;
	echo "eight"
	GetOnlineFormat;
	echo "nine"
}

main;
