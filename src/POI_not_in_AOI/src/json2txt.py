"""
json to text
"""
import sys


def main():
    """
    :return:
    """
    file = open(sys.argv[1])
    for line in file:
        line = line.strip("\n")
        items = line.split("\t")
        try:
            info = eval(items[4])
            src = info['xy']
            print items[0] + "\t" + items[1] + "\t" + items[2] + "\t" + items[3] + "\t" + src
        except Exception as e:
            continue


main()
