/*记录匹配成功的大门
node_id 
face_id 
bid 
geom_node 大门的范围框信息
geom_aoi AOI的范围框信息

*/
create table gate_aoi_match_batch_success (
    id serial primary key,
    node_id varchar(128) not null default '',
    face_id varchar(128) not null default '',
    bid varchar(128) not null default '',
    src int not null default 0 COMMENT '0: 批处理策略 1:轨迹匹配来源',
    status int not null default 0 COMMENT '0: 待转换出入口 1:已转换成出入口',
    mesh_id varchar(6) not null default '' COMMENT 'AOI的图幅id',
    geom_node geometry(Geometry,4326),
    geom_aoi geometry(Geometry,4326),
    start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
)

-- create table gate_aoi_match_batch_success (
--     id serial primary key,
--     node_id varchar(128) not null default '',
--     face_id varchar(128) not null default '',
--     bid varchar(128) not null default '',
--     src int not null default 0 ,
--     status int not null default 0 ,
--     mesh_id varchar(6) not null default '',
--     geom_node geometry(Geometry,4326),
--     geom_aoi geometry(Geometry,4326),
--     start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
--     update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP 
-- )

/*记录匹配失败的大门
node_id 
face_id 
bid 
geom_node 大门的范围框信息
geom_aoi AOI的范围框信息

*/
create table gate_aoi_match_batch_fail (
    id serial primary key,
    node_id varchar(128) not null default '',
    face_id varchar(128) not null default '',
    bid varchar(128) not null default '',
    status int not null default 0 COMMENT '0: 不需要轨迹匹配, 1 待进行轨迹匹配, 2 轨迹匹配完成',
    mesh_id varchar(6) not null default '' COMMENT 'AOI的图幅id',
    geom_node geometry(Geometry,4326),
    geom_aoi geometry(Geometry,4326),
    start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    key `gate_aoi_match_batch_fail_node_id_idx` (`node_id`)
)

-- create table gate_aoi_match_batch_fail (
--     id serial primary key,
--     node_id varchar(128) not null default '',
--     face_id varchar(128) not null default '',
--     bid varchar(128) not null default '',
--     status int not null default 0,
--     mesh_id varchar(6) not null default '',
--     geom_node geometry(Geometry,4326),
--     geom_aoi geometry(Geometry,4326),
--     start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
--     update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
--     key `gate_aoi_match_batch_fail_node_id_idx` (`node_id`)
-- )

/*记录AOI的匹配信息
*/
create table aoi_gate_match_batch_result (
    id serial primary key,
    face_id varchar(128) not null default '' primary key,
    bid varchar(128) not null default '',
    success_node_id text not null default '' COMMENT '匹配成功的大门',
    fail_node_id text not null default '' COMMENT '匹配失败的大门', 
    mesh_id varchar(6) not null default '' COMMENT 'AOI的图幅id',
    level int not null default 0 COMMENT '0:未匹配到大门的AOI 1:匹配到大门的AOI 2:L1级别的AOI 3:L2级别的AOI',
    status int not null default 0 COMMENT '0:未计算LEVEL字段  1:已计算level字段',
    valid int  not null default 1 COMMENT '0: 失效 1: 有效',
    start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    key `aoi_gate_match_batch_result_bid_idx` (`bid`)
    key `aoi_gate_match_batch_result_bid_idx` (`face_id`)
)

-- create table aoi_gate_match_batch_result (
--     id serial primary key,
--     face_id varchar(128) not null default '',
--     bid varchar(128) not null default '' ,
--     success_node_id text not null default '',
--     fail_node_id text not null default '', 
--     mesh_id varchar(6) not null default '',
--     level int not null default 0 ,
--     status int not null default 0 ,
--     valid int  not null default 1 
--     start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
--     update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP 
-- )

/*
记录需要进行关联关系的大门和aoi情报来源
*/

create table gate_match_intel_src (
    id serial primary key,
    node_id varchar(128) not null default '',/*暂时不用*/
    face_id  varchar(128) not null default '',
    bid varchar(128) not null default '',
    status int not null default 0 COMMENT '0: 待匹配 1:匹配中 2:匹配完成 3:匹配失败',
    src varchar(1000) not null default '',
    fail_reason varchar(1000) not null default '',
    start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
)
create index gate_match_intel_src_bid_index on gate_match_intel_src(bid)

-- create table gate_match_intel_src (
--     id serial primary key,
--     node_id varchar(128) not null default '',/*暂时不用*/
--     face_id  varchar(128) not null default '',
--     bid varchar(128) not null default '',
--     status int not null default 0 ,
--     src varchar(1000) not null default '',
--     fail_reason varchar(1000) not null default '',
--     start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
--     update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
-- )

/*
记录道路大门变更历史
*/
create table gate_change_history (
    node_id varchar(128) not null default '',
    start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    gate_update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '道路大门的更新时间'
    )

/*
关联关系变更情报库设计
*/
create table gate_bid_change_history (
    id serial primary key,
    node_id varchar(128) not null default '',
    bid varchar(128) not null default '',
    face_id  varchar(128) not null default '', /*aoi关联的bid可能会变，记录aoi的主点*/
    action varchar(16) not null default '',/*add delete*/
    imp_state int  not null default 0, /*0 未入库  1 入库中 2 已入库*/
    src varchar(128) not null default '',/*记录情报数据的来源*/
    flow_state varchar(1000) not null default '',/*记录当前的环节*/
    mesh_id varchar(6) not null default '', /*赋值为空，由入库脚本赋值*/
    resource int  not null default 0, /*规格同出入口的赋值规格，6：批处理生成  2：人工作业生成*/
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
)
create index gate_bid_change_history_bid_index on gate_bid_change_history(bid);

/*
关联关系例行更新中间成果库
*/
create table gate_bid_match_tmp_res(
    id serial primary key,
    node_id varchar(128) not null default '',
    bid varchar(128) not null default '',
    face_id  varchar(128) not null default '', /*aoi关联的bid可能会变，记录aoi的主点*/
    action varchar(16) not null default '',/*add delete*/
    status int not null default 0, /*0:未处理，1 处理成功 2 处理失败*/
    src varchar(1000) not null default '', /*来源*/
    process_type varchar(256) not null default '' /*batch 批处理， manu-check 人工核实*/,
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,/* 创建时间*/
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP /* '更新时间'*/
)
create index gate_bid_match_tmp_res_bid_idx on gate_bid_match_tmp_res(bid);
-- create table gate_bid_match_tmp_res(
--     id serial primary key,
--     node_id varchar(128) not null default '',
--     bid varchar(128) not null default '',
--     face_id  varchar(128) not null default '', 
--     action varchar(16) not null default '',
--     status int not null default 0, 
--     src varchar(1000) not null default '', 
--     process_type varchar(256) not null default '',
--     create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
--     update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP 
-- )

/*
人工作业的履历库
*/
create table gate_aoi_match_check_data(
    id serial primary key,
    bid varchar(64) not null default '',
    node_id varchar(64) not null default '',
    conclusion varchar(64) not null default '',
    source varchar(1000) not null default '',
    ways varchar(1000) not null default '清查提准',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP /* '更新时间'*/
)
create index gate_aoi_match_check_data_bid_index on gate_aoi_match_check_data(bid)


create table bid_gate_distribution(
    id bigserial primary key,
    bid varchar(64) not null default '',
    data varchar(1000) not null default '',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP /* '更新时间'*/
)
create index bid_gate_distribution_bid_index on bid_gate_distribution(bid)

create table gate_match_sync_integration(
    uid varchar(100) primary key,
    main_bid varchar(128) not null default '',
    node_id varchar(128) not null default '',
    qb_id int not null default 0,
    sync_status int not null default 2,/*与语义化保持一致*/
    is_manual int not null default 0,
    action varchar(64) not null default '',
    batch_id varchar(128) not null default '',
    batch_name varchar(128) not null default '',
    node_geom geometry(Geometry,4326),
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
)