# -*- coding: utf-8 -*-
"""
地下停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
import math
import re
import tqdm
import requests
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon, MultiLineString
from shapely.affinity import translate
from shapely import wkt
from loguru import logger
from rapidfuzz import fuzz

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.mistake import context
from src.aoi import utils
from src.aoi.park_access import park_utils
from src.aoi.park_access.mistake import park_access_qc as qc
from collections import defaultdict, deque


COORDINATE_FACTOR = 110000
DEAD_LINK_DISTANCE_LIMIT = 100


# logger.remove()
log_path = "./log/access_mistake_intelligence.log"
logger.add(log_path, rotation="100 MB")

IS_TEST = True
NODE_DISTANCE_IN_PARK_LIMIT = 20
same_access_type_arr = ['PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK', \
                        'PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE', \
                        'PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE']

white_bid_list = ['2823441590738076139']

def create_intelligence(ctx: context.Context):
    """
    门前出入口挖掘
    Args:
        ctx:
    Returns:
    """
    pipe_list = pipeline.Pipeline(
        get_park_std_tag,
        get_park_access_list,

        # 冗余
        check_node_distance_by_park,
        park_access_filter,
        save_park_to_db,
    )
    pipe_list(ctx)


def get_park_std_tag(ctx: context.Context, proceed):
    """
    获取停车场标准标签
    """
    park_category_data = ctx.dao.poi_online_query.get_park_category(ctx.parking_info.bid)
    if park_category_data:
        ctx.parking_info.park_category = park_category_data[0]
    logger.info(f"park_category:{ctx.parking_info.park_category}")
    return proceed()


def get_park_access_list(ctx: context.Context, proceed):
    """
    获取停车场出入口
    Returns:
    """
    print(f"star-park_id：{ctx.parking_info.bid}")
    sub_pois = ctx.dao.poi_online_query.get_sub_pois(ctx.parking_info.bid)
    if not sub_pois:
        logger.error(f"{ctx.parking_info.bid}没有子poi")
        return
    for sub_poi in sub_pois:
        if not sub_poi[3] or sub_poi[4] != 1:
            continue
        park_sub_poi = context.ParkingSubPOI()
        park_sub_poi.bid = sub_poi[0]
        park_sub_poi.point_gcj = sub_poi[2]
        road_relation = sub_poi[3]
        park_sub_poi.road_relation = road_relation
        park_sub_poi.name = sub_poi[5]
        park_sub_poi.open_limit_new = int(sub_poi[6]) if sub_poi[6] else 0
        if road_relation is not None and "link_info" in road_relation:
            # 关联node
            link_info_list = road_relation["link_info"]
            if len(link_info_list) == 0:
                continue
            link_info = link_info_list[0]
            if 'type' in link_info and link_info["type"] == 1:
                short_node_ids = link_info['node_id']
                long_node_list = ctx.dao.trans_dao.get_long_node_by_short_node_ids([short_node_ids])
                long_node_ids = [item[0] for item in long_node_list if long_node_list]
                if len(long_node_ids) == 0:
                    continue
                park_sub_poi.relation_node_id = long_node_ids[0]
                park_sub_poi.relation_short_node_id = short_node_ids
                park_sub_poi.type = 'NODE'
            # 关联link
            if 'type' in link_info and link_info["type"] == 2:
                short_link_ids = link_info['link_id']
                long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids([short_link_ids])
                long_link_ids = [item[0] for item in long_link_list if long_link_list]
                if len(long_link_ids) == 0:
                    continue
                park_sub_poi.relation_link_id = long_link_ids[0]
                park_sub_poi.type = 'LINK'
            ctx.sub_pois.append(park_sub_poi)
    if len(ctx.sub_pois) == 0:
        logger.error(f"{ctx.parking_info.bid}没有有效子poi")
        return
    return proceed()



def buffer_line_sides_only(line, distance):
    """
    # 返回线段的2侧扩展后的多边形
    """
    # 创建一个左右两边扩展的多边形
    left_offset = translate(line.parallel_offset(distance, 'left'), 0, 0)
    right_offset = translate(line.parallel_offset(distance, 'right'), 0, 0)
    # 组合左右多边形，但去掉两端
    sides_polygon = Polygon([*left_offset.coords] + [*right_offset.coords[::-1]])
    return sides_polygon
    

def angle_between_lines(p1_start, p1_end, p2_start, p2_end ):
    """
    # 计算有向线段之间的夹角
    """
    # 获取线段的起点和终点
    # 计算两个向量的方向
    vec1 = (p1_end[0] - p1_start[0], p1_end[1] - p1_start[1])
    vec2 = (p2_end[0] - p2_start[0], p2_end[1] - p2_start[1])
    # 计算向量的叉积
    cross_product = vec1[0] * vec2[1] - vec1[1] * vec2[0]
    # 计算向量的点积
    dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
    # 计算两个向量之间的夹角的余弦值
    cos_angle = dot_product / (math.sqrt(vec1[0]**2 + vec1[1]**2) * math.sqrt(vec2[0]**2 + vec2[1]**2))
    # 防止浮点运算误差导致的值超出范围[-1, 1]
    cos_angle = max(-1.0, min(1.0, cos_angle))
    # 计算角度
    angle = math.degrees(math.acos(cos_angle))
    # print(angle)
    # 使用叉积的符号来确定角度的方向
    if cross_product > 0:
        return angle
    elif cross_product < 0:
        return 360 - angle
    else:
        # 两条线段共线
        return 0


def get_node_next_link_angel(ctx, node_id, check_node_id):
    """
    计算node连线的角度
    """
    node_link = ctx.dao.road_dao.get_nav_link_by_s_node([node_id])
    check_node_link = ctx.dao.road_dao.get_nav_link_by_s_node([check_node_id])
    if not node_link or not check_node_link:
        return -1
    line1 = wkt.loads(node_link[0][1])
    line2 = wkt.loads(check_node_link[0][1])
    if node_link[0][2] == 3:
        p1_start, p1_end = line1.coords[-1], line1.coords[0]
    else:
        p1_start, p1_end = line1.coords[0], line1.coords[-1]
    
    if check_node_link[0][2] == 3:
        p2_start, p2_end = line2.coords[-1], line2.coords[0]
    else:
        p2_start, p2_end = line2.coords[0], line2.coords[-1]
    # print(p1_start)
    # print(p1_end)
    # print(p2_start)
    # print(p2_end)
    return angle_between_lines(p1_start, p1_end, p2_start, p2_end)


def get_node_area_relation(point_gcj_wkt, park_area_sp):
    """
    获取节点与区域的关系
    Args:
        node_id:
        park_area_sp:
    Returns:
    """
    if park_area_sp.contains(wkt.loads(point_gcj_wkt)):
        node_area_relation = 'IN_AREA'
    else:
        node_area_relation = 'OUT_AREA'
    return node_area_relation


def calculate_angle_between_vectors(point, node_point1, node_point2):
    """
    计算停车场到2个门的夹角
    """
    point = wkt.loads(point)
    node_point1 = wkt.loads(node_point1)
    node_point2 = wkt.loads(node_point2)
    # 计算向量
    vector1 = (node_point1.x - point.x, node_point1.y - point.y)
    vector2 = (node_point2.x - point.x, node_point2.y - point.y)
    # 计算每个向量的角度（相对于x轴正方向，逆时针为正，顺时针为负）
    angle1 = math.degrees(math.atan2(vector1[1], vector1[0]))
    angle2 = math.degrees(math.atan2(vector2[1], vector2[0]))
    # 计算两个角度之间的差
    angle_difference = abs(angle1 - angle2)
    # 如果角度差大于180度，则使用360度减去角度差来得到较小的夹角
    if angle_difference > 180:
        angle_difference = 360 - angle_difference
    return angle_difference


def check_link_to_multi_node(ctx: context.Context, proceed):
    """
    检查单条链路上是否有多个节点
    Args:
        ctx:
        proceed:
    Returns:
    """
    node_to_access = {}
    node_all_list = []
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) \
        if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt)
    if not park_area_sp:
        return proceed()
    park_area_sp.buffer(30 / COORDINATE_FACTOR)
    park_point_wkt = ctx.parking_info.park_wkt
    # 数据准备
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t\
            {sub_park_access.relation_node_id}\t{sub_park_access.relation_link_id}")
        if sub_park_access.type == 'LINK':
            link_arr = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_arr:
                continue
            s_node_id = link_arr[0][1]
            e_node_id = link_arr[0][2]
            if s_node_id not in node_all_list:
                node_all_list.append(s_node_id)
                node_to_access[s_node_id] = sub_park_access
            elif e_node_id not in node_all_list:
                node_all_list.append(e_node_id)
                node_to_access[e_node_id] = sub_park_access
            else:
                continue
        elif sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            node_to_access[node_id] = sub_park_access
            if node_id not in node_all_list:
                node_all_list.append(node_id)
    if len(node_all_list) <= 1:
        logger.warning(f"{ctx.parking_info.bid}\tnode ≤ 1")
        return proceed()
    node_set = set()
    for node_id in node_all_list:
        logger.info(f"start_access-node\t{node_id}")
        access = node_to_access[node_id]
        # node_area_relation = get_node_area_relation(access.point_gcj, park_area_sp)
        node_point_wkt = access.point_gcj
        node_buffer_wkt = wkt.loads(access.point_gcj).buffer(100 / COORDINATE_FACTOR).wkt
        nav_links = ctx.dao.road_dao.get_nav_link_by_geom(node_buffer_wkt, "52", 7)
        if not nav_links:
            logger.warning(f"{ctx.parking_info.bid}\t{node_id}\t没有找到对应的导航链路")
            continue
        # filter_nav_link = [row for row in nav_links if row[3] != 1]
        # if len(filter_nav_link) == 0:
            # logger.warning(f"{ctx.parking_info.bid}\t{node_id}\tfilter后没有找到对应的导航链路")
            # continue
        check_node_lists = []
        for check_node in node_all_list:
            if check_node == node_id:
                continue
            distance = wkt.loads(access.point_gcj).distance(
                wkt.loads(node_to_access[check_node].point_gcj)) * COORDINATE_FACTOR
            logger.info(f"距离 当前bid:{ctx.parking_info.bid}\tnode_id:{node_id}\tcheck_node:{check_node}\t{distance}")
            if distance < 20 and distance > 120:
                # 距离过远过近的排除
                continue
            check_node_access_name = node_to_access[check_node].name
            # nav_gate_semantic = utils.get_nav_gate_semantic(check_node)
            # if nav_gate_semantic not in ['入口', '出入口']:
            if not check_node_access_name.endswith('入口'):
                logger.info(f"通行性非入口：{ctx.parking_info.bid}\tnode_id:{node_id}\t\
                    check_node:{check_node}\t{check_node_access_name}")
                continue
            check_node_lists.append(check_node)
        if len(check_node_lists) == 0:
            logger.warning(f"{ctx.parking_info.bid}\t{node_id}\t没有找到可匹配的node")
            continue
        # 递归深度 4级
        logger.info(f"check_node_lists{check_node_lists}")
        reachable_nodes = find_reachable_nodes_with_paths(node_id, check_node_lists, nav_links, 6)
        logger.info(f"reachable_nodes{reachable_nodes}")
        if reachable_nodes:
            is_reachable_target = False
            for reachable_node_id, reachable_value in reachable_nodes.items():
                if reachable_node_id in node_set:
                    logger.warning(f"node_set:{node_set}\r\
                        eachable_node_id:{reachable_node_id} reachable_node_id in node_set")
                    continue
                sub_park_access = node_to_access[reachable_node_id]
                check_node_point_wkt = sub_park_access.point_gcj
                line_angle = calculate_angle_between_vectors(park_point_wkt, node_point_wkt, check_node_point_wkt)
                if line_angle > 90 and line_angle < 270:
                    logger.warning(f"node_id:{node_id}\tcheck_node:{reachable_node_id}\t\
                        node_id:{reachable_node_id}\t角度超过90度{line_angle}")
                    continue
                # check_node_area_relation = get_node_area_relation(sub_park_access.point_gcj, park_area_sp)
                # if check_node_area_relation == node_area_relation:
                #     logger.info(f"node位置与面关系一致node_id\treachable_nodes:{reachable_node_id}\tnode_id:{node_id}")
                #     continue
                is_reachable_target = True
                logger.warning(f"reachable_nodes:{reachable_node_id}，有问题的node")
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.type = "park_access_rongyu"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE
                park_access.strategy_value = f"node:{node_id},reachable_nodes:{reachable_node_id}"
                ctx.parking_access_list.append(park_access)
            if node_id in node_set:
                continue
            if is_reachable_target:
                node_set.add(node_id)
                logger.warning(f"node:{node_id}，有问题的node")
                sub_park_access = node_to_access[node_id]
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.type = "park_access_rongyu_auto"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE
                park_access.strategy_value = f"node_start:{node_id}"
                ctx.parking_access_list.append(park_access)
    return proceed()
    

def find_reachable_nodes_with_paths(node_id, check_node_list, links, max_depth=7):
    """
    寻找从给定节点出发可以到达的其他节点及其路径。
    """
    # 构建邻接表，考虑方向性
    graph = defaultdict(list)
    for link in links:
        # 单向边：只添加从 s_node 到 e_node 的路径
        if link[3] == 3:
            graph[link[2]].append((link[1], link[0]))
        elif link[3] == 2:
            graph[link[1]].append((link[2], link[0]))
        # 双向边：添加从 s_node 到 e_node 和从 e_node 到 s_node 的路径
        else:
            graph[link[1]].append((link[2], link[0]))
            graph[link[2]].append((link[1], link[0]))
    # 将 check_node_list 转为集合，便于快速查找
    target_nodes = set(check_node_list)
    # 使用 BFS 查找从 node_id 可到达的节点和路径，限制深度
    reachable_paths = {}
    queue = deque([(node_id, [], 0)])  # 队列中的每一项是 (当前节点, 到当前节点的路径, 当前深度)
    visited = set([node_id])

    while queue:
        current_node, path, depth = queue.popleft()
        # 如果深度超过限制，则停止搜索
        if depth > max_depth:
            continue
        
        # 检查是否是目标节点
        if current_node in target_nodes:
            reachable_paths[current_node] = path  # 保存到达该节点的路径
        
        # 遍历相邻节点
        for neighbor, link_id in graph[current_node]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append((neighbor, path + [link_id], depth + 1))  # 更新路径并增加深度
    return reachable_paths if reachable_paths else None


def find_reachable_nodes_single(node_id, check_node_lists, links):
    """
    寻找从给定节点出发可以到达的其他节点。
    """
    # 构建邻接表
    graph = defaultdict(list)
    for link in links:
        if link[3] == 3:
            s_node = link[2]
            e_node = link[1]
        else:
            s_node = link[1]
            e_node = link[2]
        graph[s_node].append(e_node)
    # 将 check_node_lists 转为集合，便于快速查找
    target_nodes = set(check_node_lists)
    # 使用 BFS 查找从 node_id 可到达的节点
    reachable_nodes = set()
    queue = deque([node_id])
    visited = set([node_id])
    while queue:
        node = queue.popleft()
        # 检查是否是目标节点
        if node in target_nodes:
            reachable_nodes.add(node)
            # 找到一个就停止
            break
        # 遍历相邻节点
        for neighbor in graph[node]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append(neighbor)
    return list(reachable_nodes) if reachable_nodes else None



def get_root_info(ctx, park_id):
    """
    获取主点信息
    """
    root_info = ctx.dao.poi_online_query.get_park_from_4category(park_id)
    if not root_info:
        root_info = ctx.dao.poi_online_query.get_park_from_4category_near(park_id)
        if not root_info:
            return None
    return root_info[0]
    

def check_node_distance_by_park(ctx: context.Context, proceed):
    """
    同一个停车场，包含多个出入口，且node距离过近20米以内 认为异常
    """
    logger.info('start-check_node_distance_by_park')
    show_tag = ctx.parking_info.show_tag
    points_with_bids = []
    bid_info = {}
    # 提取bid和点坐标  
    for sub_park_access in ctx.sub_pois:
        points_with_bids.append(sub_park_access)
        bid_info[sub_park_access.bid] = sub_park_access

    if len(points_with_bids) <= 1:
        logger.info("仅一个出入口，过滤")
        return proceed()
    # 计算所有点之间的距离  
    del_access = []
    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    park_name = ctx.parking_info.name
    for i in range(len(points_with_bids)):  
        for j in range(i + 1, len(points_with_bids)):  
            access1 = points_with_bids[i]
            access2 = points_with_bids[j]
            bid1, point1, node_id1, link_id1 = access1.bid, access1.point_gcj, \
                access1.relation_node_id, access1.relation_link_id
            bid2, point2, node_id2, link_id2 = access2.bid, access2.point_gcj, \
                access2.relation_node_id, access2.relation_link_id
            logger.info(f"bid1:{bid1},point1:{point1},node_id1:{node_id1},bid2:{bid2},\
                point2:{point2},node_id2:{node_id2}")
            if node_id1 != '' and node_id1 == node_id2:
                # 同一个node 需要删掉一个
                logger.info(f"node_same:node_id1:{node_id1},node_id2:{node_id2}")
                del_access.append([access2, bid2, bid1, 'node_same'])
                continue
            point1_sp = wkt.loads(point1)
            point2_sp = wkt.loads(point2)
            distance = point1_sp.distance(point2_sp) * 110000
            logger.info(f"distance:{distance}")
            if distance < 40:
                print(node_id1, node_id2)
                print(link_id1, link_id2)
                is_dead_node1, _, _ = utils.is_node_on_dead_road(node_id=node_id1, form=None) if node_id1 != '' \
                    else utils.is_link_on_dead_road(link_id=link_id1, form=None)
                is_dead_node2, _, _ = utils.is_node_on_dead_road(node_id=node_id2, form=None) if node_id2 != '' \
                    else utils.is_link_on_dead_road(link_id=link_id2, form=None)
                logger.info(f"node_id1:{node_id1},is_dead_node1:{is_dead_node1},node_id2:{node_id2},\
                    distance{distance},is_dead_node2:{is_dead_node2}")
                if show_tag in ('地上停车场'): 
                    if is_dead_node1 and not is_dead_node2:
                        del_access.append([access1, bid1, bid2, f"地上绑地下"])
                        continue
                    elif not is_dead_node1 and is_dead_node2:
                        del_access.append([access2, bid2, bid1, f"地上绑地下"])
                        continue
            # if distance < 30:
            #     link_sp1 = get_link_sp(ctx, node_id1, link_id1)
            #     link_sp2 = get_link_sp(ctx, node_id2, link_id2)
            #     if link_sp1 is None or link_sp2 is None:
            #         continue
            #     link_dis = link_sp1.distance(link_sp2) * 110000
            #     if link_dis > 0.5:
            #         continue
            #     dis1 = point1_sp.distance(park_sp)
            #     dis2 = point1_sp.distance(park_sp)
            #     logger.info(f"dis1:{dis1},dis2{dis2}")
            #     if dis1 < dis2:
            #         del_access.append([access2, bid2, bid1, '出入口距离近，删除较远的口'])
            #         # if  '东侧' in park_name or '南侧' in park_name or '西侧' in park_name or '北侧' in park_name:
            #         #     # 路侧删远的
            #         #     del_access.append([access2, bid2, bid1, '距离较远'])
            #         # else:
            #         #     del_access.append([access1, bid1, bid2, '距离较远'])
            #     else:
            #         del_access.append([access1, bid1, bid2, '距离较远'])
            #         # if  '东侧' in park_name or '南侧' in park_name or '西侧' in park_name or '北侧' in park_name:
            #         #     # 路侧删远的
            #         #     del_access.append([access1, bid1, bid2, '距离较远'])
            #         # else:
            #         #     del_access.append([access2, bid2, bid1, '距离较远'])
                    
                   
    if len(del_access) > 0:
        poi_info = ctx.dao.poi_online_query.get_poi_info_by_bid(ctx.parking_info.parent_id)
        click_pv = 0
        if poi_info:
            click_pv = poi_info[5]
        access_set = set()
        for temp_park_access, bid1, bid2, reason in del_access:
            if bid1 in white_bid_list:
                continue
            sub_park_access = bid_info[bid1]
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = "park_access_rongyu_auto"
            park_access.strategy = context.PARK_ACCESS_MISTAKE_RONGYU_AUTO
            park_access.strategy_value = bid2
            ctx.parking_access_list.append(park_access)
            logger.error(f"\t出入口冗余待删除\t{reason}\t{bid1}\t{bid2}\t{click_pv}")
    return proceed()


def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_qc(ctx)
    return proceed()


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type},access_bid:{item.bid}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 是否有效:{item.is_valid},过滤原因:{item.filter_reason}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type},access_bid:{park_access.bid}, \
                    node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                    f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        jdq_batch = 'JQDCWP2024101810'
        resp = ''
        guid = str(uuid.uuid4()).replace("-", "")

        intelligence_type = 'park_access_mistake'
        if park_access.strategy in same_access_type_arr:
            intelligence_type = "park_access_rongyu"

        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': intelligence_type,
            'batch_number': f"park_access_mistake_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': park_access.strategy + guid,
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': resp,
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': jdq_batch,
            'confidence': 0,
            'remark': '',
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
            'park_access_bid': park_access.bid
        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)
    proceed()


def push(point_access_geom, source_id, ref_qb_batch_id, park_bid, park_access_id):
    """
    一体化下发
    Args:
        point_xy:
        source_id:

    Returns:
    """
    data = {
        "src": 10,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ref_qb_batch_id,  # 必填，批次号
        # 停车场bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "main_poi_bid": park_bid,  
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "JQD",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "核实灌入出入口是否是工单停车场的出入口，是则不处理，不是则删除",
                "gates": [{'wkt': '', 'desc': '', 'gate_bid': park_access_id}]
            }
        }
    }
    logger.info(f"推送数据:{data}")
    req = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{req.text}")
    return req.text


def main(bid_list: list):
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    if len(bid_list) == 0:
        return
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), 
        show_tags=['地上停车场', '地下停车场', '立体停车场', '停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    logger.info(f"开始挖掘关联错误出入口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        ctx = context.Context(parking)
        logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
        create_intelligence(ctx)
        # try:
        #     ctx = context.Context(parking)
        #     logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
        #     create_intelligence(ctx)
        # except Exception as e:
        #     print(f"情报挖掘失败:{e}{parking}\n{traceback.format_exc()}")


def dead_traj_and_dead_link(ctx, geom_wkt, aoi_geom_wkt, dbscan_eps_num, dbscan_min_sample_num, distance_min=10):
    """
    断头轨迹聚类 + 断头路
    Args:
        ctx:
        proceed:
    Returns:

    """
    logger.info(f"begin dead_traj_and_dead_link")
    if aoi_geom_wkt == "":
        logger.info(f"dead_link_traj_list aoi_geom_wkt is none")
        return False
    # 查找断头路轨迹点
    dead_link_traj_list = ctx.dao.traj_feature_dao.get_broken_points_by_geom(aoi_geom_wkt)
    if not dead_link_traj_list:
        logger.info(f"dead_link_traj_list is none")
        return False
    broken_points = [item['geom'] for item in dead_link_traj_list]
    # logger.info(f"聚类轨迹终点: {broken_points}")
    clusters = park_utils.clusters_by_point_list(broken_points, 
        dbscan_eps=dbscan_eps_num, dbscan_min_sample=dbscan_min_sample_num)
    if not clusters:
        logger.info(f"dead_link_traj_list 无 clusters 点")
        return False
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        multipoint = MultiPoint(points)
        # logger.error(multipoint.wkt)
        # logger.info(f"断头轨迹终点聚类: {multipoint.wkt}")
        convex_hull_sp = multipoint.convex_hull.buffer(3 / COORDINATE_FACTOR)
        # logger.error(convex_hull_sp)
        if convex_hull_sp.distance(wkt.loads(geom_wkt)) < distance_min / COORDINATE_FACTOR:
            return True
    logger.info(f"dead_link_traj_list clusters 点 未命中")
    return False


def get_bid_lists():
    """
    获取停车场任务列表
    Returns:
    """
    poi_online_query = PoiOnlineQuery()
    bid_list = []
    parking_rongyu_lists = poi_online_query.get_rongyu_park_bids()
    bid_set = set()
    if parking_rongyu_lists:
        for cate in parking_rongyu_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
    logger.info(f"总停车场任务数：{len(bid_list)}")
    return bid_list


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file == "1":
        # 跑sql
        bid_list = get_bid_lists()
    elif bid_file == "0":
        # 单个测试 不做处理
        bid_list = bids.split(",")
    else:
        # 跑文件
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if len(bids) > 1:
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)

