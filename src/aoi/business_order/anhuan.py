# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P安环业务数据导出商单数据输出
"""
import os
import psycopg2

pdd_sh_conf = {"db": "pdd_sd", "user": "pdd_sd_se_rw", "pwd": "iayyd<PERSON><PERSON>",
               "host": "**************",
               "port": 6432}

pdd_sd_conn = psycopg2.connect(database=pdd_sh_conf["db"],
                               user=pdd_sh_conf["user"],
                               password=pdd_sh_conf["pwd"],
                               host=pdd_sh_conf["host"],
                               port=pdd_sh_conf["port"])
pdd_sd_dao = pdd_sd_conn.cursor()

root_dir = "/home/<USER>/gengyong/business_order/anhuan"
bud_path = root_dir + "/background/00-building"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-divison"
bad_county_path = root_dir + "/background/04-divison/county"
bad_town_path = root_dir + "/background/04-divison/town"
province_list = {
    'yulin': "polygon((109.64313907863712 38.0892858952673,109.64314791531358 38.176491670219946, 109.52130678018476 "
             "38.176595522197054, 109.52129783275913 38.08938975837249, 109.64313907863712 38.0892858952673))"
}


def run():
    """
    安环业务数据导出
    Returns:

    """
    # print(f"开始bud导出")
    # bud_export()
    print(f"开始blu导出")
    blu_export()
    # print(f"开始blc导出")
    # blc_export()
    print(f"开始brw导出")
    brw_export()
    print(f"开始bad导出")
    bad_export()
    print(f"导出完成")


def bud_export():
    """
    建筑物数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = bud_path + "/" + province
        sql = f"""
            select 
                bf.face_id,bf.struct_id,case when bs.name_ch='虚拟POI' then '' else bs.name_ch end as name_ch,bf.aoi_id,bf.height,
                0.0 as floors,0.0 as sub_floors,bf.area,bf.perimeter,bf.geom  
            from bud_face_muku bf 
                left join bud_struct_muku bs on bf.struct_id=bs.struct_id 
            where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """

        os.system(cmd)


def blu_export():
    """
    AOI数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = blu_path + "/" + province
        sql = f"""
            select 
                face_id,poi_bid,area,perimeter,name_ch as name,gcj2ll(geom) as geom from blu_face bf
            where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blu_face.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)


def blc_export():
    """
    BLC数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = blc_path + "/" + province
        sql = f"""
            select 
                face_id,name_ch,name_ph,kind,admin_id,area,perimeter,bf.geom 
            from blc_green bf 
            where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_lvdi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        sql = f"""
                    select 
                        face_id,name_ch,name_ph,kind,admin_id,area,perimeter,bf.geom  
                    from blc_water bf
                    where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/blc_face_shuixi.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def brw_export():
    """
    BRW数据输出
    Returns:

    """
    for province, wkt in province_list.items():
        export_dir = brw_path + "/" + province
        sql = f"""
            select 
                link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,gcj2ll(geom)  as geom
            from brw_link bf where st_intersects(bf.geom, st_geomfromtext('{wkt}', 4326))
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/brw_link.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        pdd_sd_dao.execute(sql)
        brw_link_list = pdd_sd_dao.fetchall()
        if brw_link_list is None or len(brw_link_list) == 0:
            continue
        node_id_list = []
        for brw_link in brw_link_list:
            node_id_list.append(brw_link[1])
            node_id_list.append(brw_link[2])
        node_id_str = "','".join(node_id_list)
        sql = f"""
                    select 
                        node_id,kind,form
                    from brw_node  where node_id in('{node_id_str}')
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/brw_node.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def bad_export():
    """
    行政区划数据输出
    Returns:

    """
    for province in province_list:
        export_dir = bad_county_path + "/" + province
        sql = f"""
            select 
                guid as face_id,countycode as admin_id,countyname as name_ch,(case when st_isempty(geom)='t' then Null else gcj2ll(geom) end) as geom  
            from bad_county where cityname = '榆林市'
        """
        cmd = f"""
            ogr2ogr -f 'MapInfo File' {export_dir}/division_county.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
        """
        os.system(cmd)
        export_dir = bad_town_path + "/" + province
        sql = f"""
                    select 
                        guid as face_id,town_code as admin_id,town_name as name_ch,(case when st_isempty(geom)='t' then Null else gcj2ll(geom) end) as geom 
                    from bad_admin_town where city_name = '榆林市'
                """
        cmd = f"""
                    ogr2ogr -f 'MapInfo File' {export_dir}/division_town.mif -dsco farmat=mif PG:"host='**************' dbname='pdd_sd' port='6432' user='pdd_sd_se_rw' password='iayydyva'" -a_srs wgs84 -sql "{sql}" encoding=UTF-8
                """
        os.system(cmd)


def init_dir():
    """
    初始化目录
    Returns:

    """
    make_dir(root_dir + "/background")
    make_dir(blu_path)
    make_dir(brw_path)
    make_dir(bad_path)
    make_dir(bad_county_path)
    make_dir(bad_town_path)
    for province, wkt in province_list.items():
        make_dir(blu_path + "/" + province)
        make_dir(brw_path + "/" + province)
        make_dir(bad_county_path + "/" + province)
        make_dir(bad_town_path + "/" + province)


def make_dir(dir):
    """
    创建目录
    Args:
        dir:

    Returns:

    """
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


if __name__ == '__main__':
    init_dir()
    print("目录初始化完成,开始数据导出")
    run()
