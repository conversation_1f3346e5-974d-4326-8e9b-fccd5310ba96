# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地上下文
"""
import math
import os

from shapely.validation import make_valid
from shapely import wkt, geometry
import numpy as np


def split_multi_polygon(origin_shapely_wkt_list):
    """
    分割multipolygon
    :param origin_shapely_wkt_list:
    :return:
    """
    split_wkt_shapely_list = []
    for wkt_shapely in origin_shapely_wkt_list:
        if wkt_shapely.is_empty:
            continue
        wkt_shapely = check_valid_shapely(wkt_shapely)

        if wkt_shapely.geom_type in ["GeometryCollection", "MultiPolygon"]:
            for current_wkt_shapely in wkt_shapely.geoms:
                if current_wkt_shapely.geom_type == 'LineString':
                    continue
                split_wkt_shapely_list.append(current_wkt_shapely)
        elif wkt_shapely.geom_type == "Polygon":
            split_wkt_shapely_list.append(wkt_shapely)
        else:
            print("当前类型无效：{}".format(wkt_shapely))
    return split_wkt_shapely_list


def check_valid_shapely(wkt_shapely):
    """
    校验WKT是否自相交或者异常，修正返回
    :param wkt_shapely:
    :return:
    """
    if wkt_shapely.is_valid:
        return wkt_shapely
    return make_valid(wkt_shapely).buffer(0)


def merge_polygon(polygon1, polygon2):
    """
    buffer 合并
    :param polygon1:
    :param polygon2:
    :return:
    """
    # 两个几何体分别buffer 后去交集
    polygon1 = check_valid_shapely(polygon1)
    polygon2 = check_valid_shapely(polygon2)
    distance = polygon1.distance(polygon2)

    buffer_wkt1 = polygon1
    buffer_wkt2 = polygon2
    init_buffer_distance = distance / 5
    if distance < 0.000001:
        init_buffer_distance = 0.000005
    elif distance < 0.000005:
        init_buffer_distance = 0.000006
    elif distance < 0.00001:
        init_buffer_distance = 0.000007
    while True:
        buffer_distance = init_buffer_distance
        buffer_wkt1 = check_valid_shapely(buffer_wkt1.buffer(buffer_distance, 16, 2, 2))
        while not buffer_wkt1.intersects(polygon2):
            buffer_distance = buffer_distance + distance / 5
            buffer_wkt1 = buffer_wkt1.buffer(buffer_distance, 16, 2, 2)
            buffer_wkt1 = check_valid_shapely(buffer_wkt1)
        buffer_distance = init_buffer_distance
        buffer_wkt2 = buffer_wkt2.buffer(buffer_distance, 16, 2, 2)
        while not buffer_wkt2.intersects(polygon1):
            buffer_distance = buffer_distance + distance / 5
            buffer_wkt2 = buffer_wkt2.buffer(buffer_distance, 16, 2, 2)
            buffer_wkt2 = check_valid_shapely(buffer_wkt2)
        # 获取两个几何体buffer之后交集然后合并
        buffer_wkt1 = check_valid_shapely(buffer_wkt1)
        intersects_wkt = buffer_wkt1.intersection(buffer_wkt2)
        if (
                intersects_wkt.boundary.length * 100000 > 100 and intersects_wkt.area * 10000000 < 0.1) or intersects_wkt.area * 10000000 < 0.01:
            init_buffer_distance = init_buffer_distance + init_buffer_distance
            continue
        break
    intersects_wkt = check_valid_shapely(intersects_wkt)
    middle_merge_polygon_shaple = check_valid_shapely(intersects_wkt.union(polygon1))
    last_merge_wkt_shapely = check_valid_shapely(middle_merge_polygon_shaple.union(polygon2))
    if last_merge_wkt_shapely.geom_type == "MultiPolygon":
        multi_wkt_shapely_list = last_merge_wkt_shapely.geoms
        filter_small_wkt = None
        for wkt_shapely in multi_wkt_shapely_list:
            if filter_small_wkt is None:
                filter_small_wkt = wkt_shapely
                continue
            if wkt_shapely.area * 10000000 < 0.1:
                continue
            filter_small_wkt = filter_small_wkt.union(wkt_shapely)
        return filter_small_wkt.simplify(0.00001, preserve_topology=True)

    return last_merge_wkt_shapely.simplify(0.00001, preserve_topology=True)


def angle_straightening(filter_angle, new_filter_angle, wkt_shapely):
    """
    拉直夹角
    :param filter_angle:
    :param wkt_shapely:
    :return:
    """
    # 拉直面积过滤与比例过滤，如果小于限制值则不拉直，防止失真太多
    straight_area_limit = 0.0000000500
    straight_area_rate_limit = 0.2
    wkt_shapely_area = wkt_shapely.area
    coords_list = wkt_shapely.exterior.coords
    point_list = []
    is_exists_filter_angle = False
    del_coords_keys = []
    for key, coords in enumerate(coords_list):
        if (len(coords_list) - len(del_coords_keys)) <= 4:
            point_list.append(geometry.Point(coords[0], coords[1]))
            continue
        # 如果起点与第二点向量 起点与倒数第二个点的向量夹角小于给定夹角，也过滤
        if key == 0 and len(coords_list) > 4:
            bgein_end_line_a = np.array([coords[0] - coords_list[1][0], coords[1] - coords_list[1][1]])
            bgein_end_line_b = np.array(
                [coords[0] - coords_list[len(coords_list) - 2][0], coords[1] - coords_list[len(coords_list) - 2][1]])
            bgein_end_angle = angle_cal(bgein_end_line_a, bgein_end_line_b)

            # 计算过滤掉的面积
            current_deleted_coords_list = get_deleted_point_wkt_shapely(
                [geometry.Point(coord[0], coord[1]) for coord in coords_list], del_coords_keys + [key])
            if geometry.Polygon(current_deleted_coords_list).is_valid:
                filter_wkt_area = wkt_shapely.difference(
                    check_valid_shapely(geometry.Polygon(current_deleted_coords_list))).area
                if bgein_end_angle < filter_angle and filter_wkt_area < straight_area_limit and filter_wkt_area / wkt_shapely_area < straight_area_rate_limit:
                    is_exists_filter_angle = True
                    del_coords_keys.append(key)
                    continue
        if key == 0 or key == len(coords_list) - 1 or len(coords_list) == 4:
            point_list.append(geometry.Point(coords[0], coords[1]))
            continue
        # 求向量夹角，拉直后如果产生新的较小夹角则不拉，四个点 p1 p2 p3 p4；line_a:p2->p1  line_b:p2->p3  linc  p3->p1  line_d p3->p4
        line_a = np.array([coords[0] - coords_list[key - 1][0], coords[1] - coords_list[key - 1][1]])
        line_b = np.array([coords[0] - coords_list[key + 1][0], coords[1] - coords_list[key + 1][1]])
        angele = angle_cal(line_a, line_b)

        # 求拉直后产生的新夹角
        new_angle = 180
        if key < (len(coords_list) - 1):
            line_c = np.array(
                [coords_list[key + 1][0] - coords_list[key - 1][0], coords_list[key + 1][1] - coords_list[key - 1][1]])

            if key == len(coords_list) - 2:
                line_d = np.array([coords_list[key + 1][0] - coords_list[1][0],
                                   coords_list[key + 1][1] - coords_list[1][1]])
            else:
                line_d = np.array([coords_list[key + 1][0] - coords_list[key + 2][0],
                                   coords_list[key + 1][1] - coords_list[key + 2][1]])
            new_angle = angle_cal(line_c, line_d)
        # 计算过滤掉的面积
        current_deleted_coords_list = get_deleted_point_wkt_shapely(
            [geometry.Point(coord[0], coord[1]) for coord in coords_list], del_coords_keys + [key])
        if geometry.Polygon(current_deleted_coords_list).is_valid:
            filter_wkt_area = wkt_shapely.difference(geometry.Polygon(current_deleted_coords_list)).area
            if (math.isnan(angele) or angele < filter_angle) \
                    and new_angle > new_filter_angle \
                    and filter_wkt_area < straight_area_limit \
                    and filter_wkt_area / wkt_shapely_area < straight_area_rate_limit:
                is_exists_filter_angle = True
                del_coords_keys.append(key)
                continue
        point_list.append(geometry.Point(coords[0], coords[1]))
    if len(point_list) < 3:
        return wkt_shapely, False
    return geometry.Polygon(point_list), is_exists_filter_angle


def angle_cal(a, b):
    a_norm = np.sqrt(np.sum(a * a))
    b_norm = np.sqrt(np.sum(b * b))
    cos_value = np.dot(a, b) / (a_norm * b_norm)
    arc_value = np.arccos(cos_value)
    angle_value = arc_value * 180 / np.pi
    return angle_value


def get_deleted_point_wkt_shapely(coords_list, del_key_list):
    """
    删除polygon coords_list 的点
    :param coords_list:
    :param del_key_list:
    :return:
    """
    del_key_list.sort(reverse=True)
    for del_key in del_key_list:
        coords_list.pop(del_key)
    return coords_list


def vertical_gun_acute_angle(wkt_shapely):
    # 拉直面积过滤与比例过滤，如果小于限制值则不拉直，防止失真太多
    straight_area_limit = 0.0000000500
    straight_area_rate_limit = 0.2
    coords_list = wkt_shapely.exterior.coords
    replace_coords_list = [[coord[0], coord[1]] for coord in coords_list]
    for key, coords in enumerate(coords_list):
        if key > len(coords_list) - 2:
            continue
        current_coords = coords
        next_coords = coords_list[key + 1]
        if key == 0:
            pre_coords = coords_list[len(coords_list) - 2]
        else:
            pre_coords = coords_list[key - 1]
        # 计算夹角
        line_a = np.array([current_coords[0] - pre_coords[0], current_coords[1] - pre_coords[1]])
        line_b = np.array([current_coords[0] - next_coords[0], current_coords[1] - next_coords[1]])
        angele = angle_cal(line_a, line_b)
        if angele < 40:
            # 计算垂足
            wkt_foot = get_foot_by_point_and_line(pre_coords, current_coords, next_coords)
            wkt_foot_shapley = geometry.Point(wkt_foot[0], wkt_foot[1])
            veritify_line_shapely = geometry.LineString([current_coords, next_coords])
            # 判断垂足是否在线段内
            print(
                f"是否在框内：{wkt_shapely.intersects(wkt_foot_shapley)}, foot:{veritify_line_shapely.distance(wkt_foot_shapley)}, foot:{wkt_foot_shapley}")
            if veritify_line_shapely.distance(wkt_foot_shapley) > 0.000001:
                wkt_foot = get_foot_by_point_and_line(next_coords, pre_coords, current_coords)
                wkt_foot_shapley = geometry.Point(wkt_foot[0], wkt_foot[1])
                veritify_line_shapely = geometry.LineString([pre_coords, current_coords])
                if veritify_line_shapely.distance(wkt_foot_shapley) > 0.000001:
                    continue
            # 判断截取后面积变更
            temp_coords_list = replace_coords_wkt_shapely(replace_coords_list, [wkt_foot[0], wkt_foot[1]], key)
            filter_shapely = geometry.Polygon(
                geometry.Point(temp_coords[0], temp_coords[1]) for temp_coords in temp_coords_list)
            filter_shapely = check_valid_shapely(filter_shapely)
            filter_wkt_area = check_valid_shapely(wkt_shapely.difference(filter_shapely)).area
            if filter_wkt_area > straight_area_limit or filter_wkt_area / wkt_shapely.area > straight_area_rate_limit:
                print("过滤面积超出限制")
                continue
            replace_coords_list = temp_coords_list
    return geometry.Polygon(
        geometry.Point(temp_coords[0], temp_coords[1]) for temp_coords in replace_coords_list)


def gemetry_collection_split_and_union(wkt_shapely):
    """
    gemetry collection 合并
    :param wkt_shapely:
    :return:
    """
    wkt_union_shapely = None
    wkt_shapely_list = split_multi_polygon([wkt_shapely])
    for current_wkt_shapely in wkt_shapely_list:
        if wkt_union_shapely is None:
            wkt_union_shapely = current_wkt_shapely
        else:
            wkt_union_shapely = wkt_union_shapely.union(current_wkt_shapely)
    return wkt_union_shapely


def replace_coords_wkt_shapely(coords_list, coords, replace_key):
    """
    替换 coords_list
    :param coords_list:
    :param coords:
    :param replace_key:
    :return:
    """
    coords_list[replace_key][0] = coords[0]
    coords_list[replace_key][1] = coords[1]
    if replace_key == 0:
        coords_list[len(coords_list) - 1][0] = coords[0]
        coords_list[len(coords_list) - 1][1] = coords[1]
    return coords_list


def make_dir(dir):
    isExist = os.path.exists(dir)
    if not isExist:
        # Create a new directory because it does not exist
        os.makedirs(dir)


def get_foot(point, line):
    """
    获取直线 与 点的垂足
    :param point: (x,y)
    :param line: ((x1,y1),(x2,y2))
    :return:  (x,y)
    """
    start_x, start_y, end_x, end_y = line
    pa_x, pa_y = point

    p_foot = [0, 0]
    if line[0] == line[3]:
        p_foot[0] = line[0]
        p_foot[1] = point[1]
        return p_foot
    k = (end_y - start_y) * 1.0 / (end_x - start_x)
    a = k
    b = -1.0
    c = start_y - k * start_x
    p_foot[0] = (b * b * pa_x - a * b * pa_y - a * c) / (a * a + b * b)
    p_foot[1] = (a * a * pa_y - a * b * pa_x - b * c) / (a * a + b * b)
    return p_foot

def get_foot_by_point_and_line(point, line_start, line_end):
    """
    计算垂足
    Args:
        point:
        line_start:
        line_end:

    Returns:

    """
    # Convert points to numpy arrays
    P = np.array(point)
    A = np.array(line_start)
    B = np.array(line_end)

    # Vector from A to B
    AB = B - A
    # Vector from A to P
    AP = P - A

    # Projection parameter t
    t = np.dot(AP, AB) / np.dot(AB, AB)

    # Calculate the projection point F
    F = A + t * AB

    return F


def deal_green_hole_wkt(wkt_shapely_list: list, filter_hole_area: float) -> list:
    """
    空洞去除
    :param wkt_shapely_list:  wkt list
    :param filter_hole_area: 空洞过滤面积
    :return:
    """
    # 空洞过滤面积
    wkt_shapely_list = split_multi_polygon(wkt_shapely_list)
    # 开始拆分空洞
    filter_hole_shapely_list = []
    for key, wkt_shapely in enumerate(wkt_shapely_list):
        print("空洞过滤处理：边框总量：{}, 当前进度：{}".format(len(wkt_shapely_list), key))
        wkt_str = wkt_shapely.wkt
        wkt_str = wkt_str.replace('POLYGON ((', '').replace('))', '')
        wkt_str_list = wkt_str.split('), (')
        no_hole_shapely = wkt.loads("POLYGON ((" + wkt_str_list[0] + "))")
        for key, item in enumerate(wkt_str_list):
            if key == 0:
                continue
            hole_wkt_shapely = wkt.loads("POLYGON ((" + item + "))")
            if hole_wkt_shapely.area < filter_hole_area:
                continue
            no_hole_shapely = no_hole_shapely.difference(hole_wkt_shapely)
        filter_hole_shapely_list.append(no_hole_shapely)
    return filter_hole_shapely_list
