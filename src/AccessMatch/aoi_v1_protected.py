"""
 aoi_v1 数据保护策略
"""

from pg_tool import PgTool


def load_protected_main_bids(pg_tool: PgTool):
    """
    加载主点保护字段名单， 数据存储在 poi数据库的 aoi_v1_protect 表中
    :return:
    """
    # 构建保护主点的 map 集合
    protected_map = {}
    sql = """
           select 
               main_bid
           from 
               aoi_v1_protect
           where 
               key = 'main' 
       """
    pg_tool.cursor_poi.execute(sql)
    ret = pg_tool.cursor_poi.fetchall()
    for v in ret:
        protected_map[v[0]] = True
    return protected_map
