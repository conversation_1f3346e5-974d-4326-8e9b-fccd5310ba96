# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场出入口上下文信息
"""
from datetime import datetime, timedelta
import sys
import os
import json
from loguru import logger
from dataclasses import dataclass, field
from mapio.utils import bid2uid
from shapely import wkt

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.dest_traj_to_aoi import DestTrajToAOI
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.aoi_resume_query import AoiResumeQuery
from src.aoi.BFQuery import BFQuery
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi.dao.traj_feature import TrajFeatureDao
from src.aoi import utils

log_path = "./log/choose_img.log"
logger.add(log_path, rotation="10 MB")


@dataclass
class Dao:
    """
    数据库操作对象
    """
    road_dao = Road()
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    trans_dao = Trans()

@dataclass
class LightCheck:
    """
    停车场出入口挖掘成果
    """
    id: int = field(default=0, init=False)
    intelligence_id: int = field(default=0, init=False)
    park_bid: str = field(default="", init=False)  # 类型, 关联Node还是link
    long_node_id: str = field(default="", init=False)
    short_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    node_passage: str = field(default="", init=False)
    long_link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    batch: str = field(default="", init=False)
    msg: str = field(default="", init=False)
    img_content: str = field(default="", init=False)
    img_list: str = field(default="", init=False)
    img_num: int = field(default=0, init=False)
    new_img_num: int = field(default=0, init=False)
    status: int = field(default=0, init=False)
    img_download_num: int = field(default=0, init=False)
    intelligence_list: list = field(default_factory=list, init=False)


@dataclass 
class ProcessInfo:
    """
    流程状态信息
    """
    total_light_num: int = field(default=0, init=False) # 总待轻核实数
    now_light_num: int = field(default=0, init=False)   # 当前任务数
    batch: str = field(default="", init=False)          # 批次
    batch_num: int = field(default=0, init=False)       # 每个批次包含停车场数量
    process_status: int = field(default=0, init=False)  # 流程状态
    total_img_num: int = field(default=0, init=False)   # 总图片数
    light_check_id_list: list = field(default_factory=list, init=False) # 下载图片成功的id 打包成功并下发后轻核实 统一更新状态
    workplace_dir: str = field(default="", init=False)  # 工作目录
    img_dir: str = field(default="", init=False)        # 图片目录
    tar_path: str = field(default="", init=False)       # 压缩包完整文件路径

@dataclass
class ParkingInfo:
    """"
    停车场信息
    """
    bid: str = field(default="", init=False)
    name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    park_wkt: str = field(default="", init=False)
    park_area_wkt: str = field(default="", init=False)
    park_area_aoi_30m_wkt: str = field(default="", init=False)
    park_area_gcj_wkt: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    road_relation_childrens: str = field(default="", init=False)
    traj_dbname: str = field(default="", init=False)
    address: str = field(default="", init=False)
    park_category: str = field(default="", init=False)


class Context:
    """
    处理上下文
    """
    def __init__(self, light_check: list, process_info: ProcessInfo):
        """
        初始化
        """
        self.light_check = LightCheck()
        self.parking_info = ParkingInfo()
        self.process_info = process_info
        self.dao = Dao()
        # self.init_parking_info()
        self.init_light_check(light_check)
        self.debug = True


    def init_parking_info(self):
        """
        初始化停车场信息
        Args:
            ctx:
            parking:

        Returns:

        """
        paring_info = ParkingInfo()
        parking = self.origin_parking_list
        paring_info.name = parking[0]
        paring_info.show_tag = parking[4]
        paring_info.parent_id = parking[5] if parking[5] and parking[5] != '0' else ""
        paring_info.bid = parking[7]
        paring_info.park_wkt = parking[10]
        paring_info.park_area_wkt = parking[6]
        paring_info.road_relation = parking[9]
        paring_info.address = parking[11]
        paring_info.open_limit_new = parking[12]
        paring_info.road_relation_childrens = ""
        if len(parking) > 13:
            paring_info.road_relation_childrens = parking[13]
        if parking[6]:
            paring_info.park_area_gcj_wkt = utils.mc_to_gcj(parking[6])
        self.parking_info = paring_info

    def init_light_check(self, light_check):
        """
        初始化智能信息
        Args:
            ctx:
        Returns:
        """
        light_check_info = LightCheck()
        light_check_info.id = int(light_check['id'])
        light_check_info.intelligence_id = int(light_check['intelligence_id'])
        light_check_info.park_bid = light_check['park_bid']
        light_check_info.img_content = light_check['img_content']
        light_check_info.status = int(light_check['status'])
        light_check_info.status = 20
        light_check_info.long_node_id = light_check['long_node_id']
        light_check_info.img_num = light_check['img_num']
        light_check_info.img_download_num = 0
        light_check_info.img_list = light_check['img_content']
        self.light_check = light_check_info
