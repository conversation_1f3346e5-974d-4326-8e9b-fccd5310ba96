# -*- coding: utf-8 -*-
"""
出入口和冗余情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
import math
import re
import tqdm
import requests
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import nearest_points
from shapely.affinity import translate
from shapely import wkt
from loguru import logger
from rapidfuzz import fuzz

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.mistake import context
from src.aoi import utils
from src.aoi.park_access import park_utils
from src.aoi.park_access.mistake import park_access_qc as qc
from collections import defaultdict, deque


COORDINATE_FACTOR = 110000
DEAD_LINK_DISTANCE_LIMIT = 100


# logger.remove()
log_path = "./log/access_mistake_intelligence.log"
logger.add(log_path, rotation="100 MB")

IS_TEST = True
NODE_DISTANCE_IN_PARK_LIMIT = 20
same_access_type_arr = ['PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK', \
                        'PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE', \
                        'PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE']


def create_intelligence(ctx: context.Context):
    """
    门前出入口挖掘
    Args:
        ctx:
    Returns:
    """
    pipe_list = pipeline.Pipeline(
        # get_complete_position,
        # get_complete_position_detail
        get_park_std_tag,
        get_park_access_list,

        # # online
        # 冗余
        check_node_distance_by_park,           # 大门距离近
        check_link_to_multi_node,              # 大门有link可联通
        check_traj_line_pass_two_node,         # 终点轨迹穿过2个node
        # 关联错
        check_by_distance,                     # 距离远
        check_access_by_low_traj,              # 低通量
        check_underground_by_not_dead_road,    # 地下非断头路
        check_underground_by_not_dead_road_min_area,   # 地下非断头路 小面场景
        check_main_bid_from_node_and_park,     # 绑了附近aoi的大门
        check_link_on_urgert_gate,             # 出入口在紧急门上
        check_node_relation_by_other_park,     # node被其他停车场关联
        check_overground_by_dead_road,         # 地上断头路
        check_node_line_has_other_node,        # node和停车场连线上还有其他node 
        check_in_gate_only_out_traj,           # 入门只有出的轨迹
        check_underground_not_on_dead_road_but_near, # 地下非断头路附近有断头路
        check_pass_high_road,                   # 跨高等级道路
        check_exp_traj_not_arrive,              # 经验轨迹未到达
        check_by_complete,                      # 竞品diff
        # diff_parent_exp_and_plan_traj   # 规划轨迹和经验轨迹不一致 todo 迭代提有效，有qps限制，注意频率

        # 无父点的单独写
        # check_traj_has_no_intersection_with_park,
        # check_underground_by_dead_road,        # 忽略 
        is_strategy_already_recall,
        # is_manual_work,
        park_access_filter,
        save_park_to_db,
    )
    pipe_list(ctx)


def diff_parent_exp_and_plan_traj(ctx: context.Context, proceed):
    """
    对比父点预期轨迹和规划轨迹是否一致
    """
    parent_id = ctx.parking_info.parent_id
    logger.info(f"parent_id: {parent_id},\tshow_tag{ctx.parking_info.show_tag}\t{ctx.parking_info.name}")
    park_parent_exp_trajs = utils.get_exp_traj_lists_by_bid(parent_id, 3, 0.9)
    for sub_park_access in ctx.sub_pois:
        # if sub_park_access.bid != '2948915693551791976':
        #     continue
        logger.info(f"strat-access_bid {sub_park_access.bid}")
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(80 / COORDINATE_FACTOR)
        else:
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(70 / COORDINATE_FACTOR, cap_style=1)

        # 判断是否有经验轨迹在node附近，保留起点和终点
        exp_filter_arr = []
        for exp_traj_dict in park_parent_exp_trajs:
            exp_traj_sp = exp_traj_dict['traj']
            # 相交
            exp_traj_end_point_sp = Point(exp_traj_sp.coords[-1])
            if access_geom_sp.intersects(exp_traj_end_point_sp):
                exp_filter_arr.append([exp_traj_sp.coords[0], exp_traj_sp.coords[-1], exp_traj_sp])
                # logger.warning(f"{ctx.parking_info.bid}的父节点{parent_id}存在经验轨迹{exp_traj_sp}在{access_geom_sp}附近")
        if len(exp_filter_arr) == 0:
            logger.info(f"没有经验轨迹在{access_geom_sp}附近")
            continue
        for exp_traj in exp_filter_arr:
            exp_traj_sp = exp_traj[2]
            start_coord = exp_traj[0]
            start_coord_str = str(start_coord[0]) + ',' + str(start_coord[1])
            point_gcj_str = str(wkt.loads(sub_park_access.point_gcj).x) + \
                ',' + str(wkt.loads(sub_park_access.point_gcj).y)
            # 测试 node终点
            # aq_py3 run_test.py  0 4178534571592538465  0 1 0
            # point_gcj_str = "116.50331239516818,39.915346348151225"
            # Hausdorff Distance: = 0.002208393080945758
            # point_gcj_str = "108.95127941928135,34.2702672132402"
            # Hausdorff Distance: 0.005923280631161648
            logger.info("起终点")
            logger.info(start_coord_str)
            logger.info(point_gcj_str)
            linestring = utils.get_nav_route(start_coord_str, point_gcj_str, "baidu")
            if not linestring:
                continue
            logger.info(linestring)
            logger.info(exp_traj_sp)
            # 计算 Hausdorff 距离
            hausdorff_distance = linestring.hausdorff_distance(exp_traj_sp)
            logger.info(f"Hausdorff Distance: {hausdorff_distance}")
            if hausdorff_distance < 0.002:
                logger.info(f"经验轨迹和规划轨迹相似度小于阈值，hausdorff_distance:{hausdorff_distance}")
                continue
            # 计算 l
            exp_len = exp_traj_sp.length * 110000
            plan_len = linestring.length * 110000
            logger.info(f"exp_traj_sp length: {exp_len}, plan_len: {plan_len}")
            traj_dis = abs(exp_len - plan_len)
            if traj_dis < 200:
                logger.info(f"经验轨迹和规划轨迹长度差异不大，经验轨迹长度：{exp_len}，规划轨迹长度：{plan_len}")
                continue
            
            if not ctx.aoi_info.wkt:
                logger.info(f"缺少aoi面")
                continue
            logger.info(ctx.aoi_info.wkt)
            intersect_point = find_first_intersection_or_nearest(linestring, wkt.loads(ctx.aoi_info.wkt))
            logger.info(intersect_point)
            node_and_intersect_point_dis = access_geom_sp.distance(Point(intersect_point)) * 110000
            logger.info(f"node_and_intersect_point_dis:{node_and_intersect_point_dis}")
            if node_and_intersect_point_dis < 30:
                logger.info(f"node与交点距离小于100m，节点坐标：{node_sp.coords[0]}，\
                    交点坐标：{intersect_point}，{node_and_intersect_point_dis}")
                continue

            logger.warning("疑似有问题的大门")
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = sub_park_access.type
            park_access.strategy = context.PARK_ACCESS_PLAN_AND_PARENT_EXP_TRAJ_DIFF
            park_access.strategy_value = f"轨迹长度差：{traj_dis}，轨迹相似度{hausdorff_distance}"
            ctx.parking_access_list.append(park_access)
            break
    return proceed()


def find_first_intersection_or_nearest(linestring, polygon):
    """
    检查 LineString 和 Polygon 是否有交点
    """
    # 检查 LineString 和 Polygon 是否有交点
    intersection = linestring.intersection(polygon)
    if not intersection.is_empty:
        # 如果有交点，找出第一个交点
        # intersection 可能是一个 MultiPoint, LineString, 或者其他几何类型
        # 这里假设我们想要第一个点，可以迭代 intersection 的几何集合
        if intersection.geom_type == 'Point':
            # 如果直接是一个点，返回它
            return intersection
        elif intersection.geom_type == 'MultiPoint':
            # 如果是多点，返回第一个点
            return intersection[0]
        elif intersection.geom_type == 'LineString':
            # 如果是线段，返回线段的起点（或者可以根据需求选择其他策略）
            return intersection.coords[0]  # 这里简单返回起点作为代表
        elif intersection.geom_type == 'MultiLineString':
            # 如果是多线段，返回第一段线段的起点
            return intersection[0].coords[0]
        else:
            # 处理其他可能的交集类型（根据需要可以扩展）
            return False
    else:
        # 如果没有交点，找到 LineString 终点到 Polygon 最近的点
        line_end_point = Point(linestring.coords[-1])
        nearest_point = nearest_points(polygon, line_end_point)[1]
        return nearest_point


def get_park_std_tag(ctx: context.Context, proceed):
    """
    获取停车场标准标签
    """
    park_category_data = ctx.dao.poi_online_query.get_park_category(ctx.parking_info.bid)
    if park_category_data:
        ctx.parking_info.park_category = park_category_data[0]
    logger.info(f"park_category:{ctx.parking_info.park_category}")
    return proceed()


def get_park_access_list(ctx: context.Context, proceed):
    """
    获取停车场出入口
    Returns:
    """
    print(f"star-park_id：{ctx.parking_info.bid}")
    sub_pois = ctx.dao.poi_online_query.get_sub_pois(ctx.parking_info.bid)
    if not sub_pois:
        logger.error(f"{ctx.parking_info.bid}没有子poi")
        return
    for sub_poi in sub_pois:
        if not sub_poi[3] or sub_poi[4] != 1:
            continue
        park_sub_poi = context.ParkingSubPOI()
        park_sub_poi.bid = sub_poi[0]
        park_sub_poi.point_gcj = sub_poi[2]
        road_relation = sub_poi[3]
        park_sub_poi.road_relation = road_relation
        park_sub_poi.name = sub_poi[5]
        park_sub_poi.open_limit_new = int(sub_poi[6]) if sub_poi[6] else 0
        if road_relation is not None and "link_info" in road_relation:
            # 关联node
            link_info_list = road_relation["link_info"]
            if len(link_info_list) == 0:
                continue
            link_info = link_info_list[0]
            if 'type' in link_info and link_info["type"] == 1:
                short_node_ids = link_info['node_id']
                long_node_list = ctx.dao.trans_dao.get_long_node_by_short_node_ids([short_node_ids])
                long_node_ids = [item[0] for item in long_node_list if long_node_list]
                if len(long_node_ids) == 0:
                    continue
                park_sub_poi.relation_node_id = long_node_ids[0]
                park_sub_poi.relation_short_node_id = short_node_ids
                park_sub_poi.type = 'NODE'
            # 关联link
            if 'type' in link_info and link_info["type"] == 2:
                short_link_ids = link_info['link_id']
                long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids([short_link_ids])
                long_link_ids = [item[0] for item in long_link_list if long_link_list]
                if len(long_link_ids) == 0:
                    continue
                park_sub_poi.relation_link_id = long_link_ids[0]
                park_sub_poi.type = 'LINK'
            ctx.sub_pois.append(park_sub_poi)
    if len(ctx.sub_pois) == 0:
        logger.error(f"{ctx.parking_info.bid}没有有效子poi")
        return
    return proceed()


def get_min_dis_link(park_wkt, links):
    """
    计算与停车场最近的道路
    """
    min_link_id = None
    min_dis = None
    for link in links:
        link_id = link[0]
        link_wkt = link[6]
        link_sp = wkt.loads(link_wkt)
        dis = link_sp.distance(wkt.loads(park_wkt))
        if min_dis is None:
            min_link_id = link_id
            min_dis = dis
        elif dis < min_dis:
            min_link_id = link_id
            min_dis = dis
    return min_link_id



def get_complete_position(ctx: context.Context, proceed):
    """
    # 引导点信息导出
    """
    # 引导点
    print(ctx.parking_info.road_relation_childrens)
    exit(1)
    poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
    poi_navi_num = 0
    poi_navi_wkt_arr = []
    if poi_navi_list:
        poi_nav_str = ""
        for poi_nav in poi_navi_list:
            # 提取坐标的正则表达式
            point_str = poi_nav[2]
            if not point_str:
                continue
            pattern = re.compile(r"(\d+\.\d+) (\d+\.\d+)")
            # 使用 findall 提取所有坐标
            coordinates_1 = pattern.findall(point_str)
            # 转换为浮点数并输出
            for lon, lat in coordinates_1:
                poi_navi_wkt_arr.append
                poi_nav_str = poi_nav_str + f"POINT({float(lon)} {float(lat)}),"
                poi_navi_num += 1


def get_nav_diff_num(poi_nav_wkt_arr, we_nav_wkt_arr, buffer_meter=20):
    """
    计算两个导航点之间的差异数量
    """
    diff_num = 0
    for poi_nav_wkt in poi_nav_wkt_arr:
        poi_nav_sp = wkt.loads(poi_nav_wkt).buffer(buffer_meter / COORDINATE_FACTOR)
        for we_nav_wkt in we_nav_wkt_arr:
            if poi_nav_sp.contains(wkt.loads(we_nav_wkt)):
                continue
            else:
                diff_num += 1
    return diff_num


def get_complete_position_detail(ctx: context.Context, proceed):
    """
    获取完整的导航点
    """
    # 引导点
    poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
    we_navi_num, poi_navi_num = 0, 0
    open_limit_new = ctx.parking_info.open_limit_new
    bid = ctx.parking_info.bid
    we_nav_wkt_arr = []
    poi_nav_wkt_arr = []
    if ctx.parking_info.road_relation_childrens:
        for key, val in ctx.parking_info.road_relation_childrens.items():
            for link_info in val:
                if "orientation" in link_info and link_info['orientation'] == 1 and link_info['point'] != "":
                    point_arr = link_info['point'].split(',')
                    gcj_wkt = utils.mc_to_gcj(f"POINT({float(point_arr[0])} {float(point_arr[1])})")
                    we_navi_num += 1
                    we_nav_wkt_arr.append(gcj_wkt)
    elif ctx.parking_info.road_relation:
        if "link_info" in ctx.parking_info.road_relation:
            for link_info in ctx.parking_info.road_relation:
                if "orientation" in link_info and link_info['orientation'] == 1 and \
                    link_info['type'] == 3 and link_info['point'] != "":
                    point_arr = link_info['point'].split(',')
                    gcj_wkt = utils.mc_to_gcj(f"POINT({float(point_arr[0])} {float(point_arr[1])})")
                    we_navi_num += 1
                    we_nav_wkt_arr.append(gcj_wkt)
    gid = ""
    name = ""
    if poi_navi_list:
        for poi_nav in poi_navi_list:
            # 提取坐标的正则表达式
            # bid,name,st_astext(geom) as geom,gid
            bid = poi_nav[0]
            name = poi_nav[1]
            point_str = poi_nav[2]
            gid = poi_nav[3]
            if not point_str:
                continue
            pattern = re.compile(r"(\d+\.\d+) (\d+\.\d+)")
            # 使用 findall 提取所有坐标
            coordinates_1 = pattern.findall(point_str)
            # 转换为浮点数并输出
            for lon, lat in coordinates_1:
                poi_nav_wkt_arr.append(f"POINT({float(lon)} {float(lat)})")
                poi_navi_num += 1
    poi_info = ctx.dao.poi_online_query.get_poi_info_by_bid(ctx.parking_info.bid)
    if poi_info:
        click_pv = poi_info[5]
    else:
        click_pv = ""
    # 判断引导点数量是否一致
    # gid name 竞品显示坐标 匹配百度bid 百度停车场名字 百度显示坐标 百度算路pv  竞品引导位置  百度引导位置  不同原因
    name = ""
    if gid:
        competitor_infos = ctx.dao.poi_online_query.get_competitor_info_by_gids([gid])
        if len(competitor_infos) > 0:
            name = competitor_infos[0][1]

    temp_arr = [gid, name, poi_nav_wkt_arr, bid, ctx.parking_info.name, we_nav_wkt_arr, click_pv, open_limit_new]
    msg = ""
    if we_navi_num == poi_navi_num:
        if we_navi_num == 0:
            msg = "引导点数量都为0个"
        else:
            diff_num = get_nav_diff_num(poi_nav_wkt_arr, we_nav_wkt_arr, 30)
            if diff_num > 0:
                msg = f"引导点位置不同，不包含竞品个数：{diff_num}"
            else:
                if open_limit_new not in ["1", "14"] and "内部" in name:
                    msg = "BD不对外，竞品对内"
                else:
                    msg = "数据一致"
    else:
        msg = "引导点个数不一致"
    temp_arr.append(msg)
    output_str = '\t'.join(map(str, temp_arr))
    logger.error(output_str)
    return proceed()


def make_line_by_point(point1, point2):
    """
    2点连线
    """
    match = re.match(r"POINT\s*\(([\d\.]+)\s+([\d\.]+)\)", point1)
    point1_x = float(match.group(1))
    point1_y = float(match.group(2))
    match2 = re.match(r"POINT\s*\(([\d\.]+)\s+([\d\.]+)\)", point2)
    point2_x = float(match2.group(1))
    point2_y = float(match2.group(2))
    return f"LINESTRING ({point1_x} {point1_y}, {point2_x} {point2_y})"


def get_confidence_by_park_click_pv(park_click_pv):
    """
    根据停车场点击量计算置信度
    """
    confidence = 0
    if park_click_pv > 500:
        confidence = 100
    elif park_click_pv > 100:
        confidence = 95
    elif park_click_pv > 50:
        confidence = 90
    return confidence


def check_exp_traj_not_arrive(ctx: context.Context, proceed):
    """
    经验轨迹未到达
    """
    logger.info(f"check_exp_traj_not_arrive-start:park_bid\t{ctx.parking_info.bid}")
    park_bid = ctx.parking_info.bid
    parent_id = ctx.parking_info.parent_id
    logger.info(f"parent_id: {parent_id},\tshow_tag{ctx.parking_info.show_tag}\t{ctx.parking_info.name}")
    park_area_gcj_wkt = ctx.parking_info.park_area_gcj_wkt \
        if ctx.parking_info.park_area_gcj_wkt else ctx.parking_info.park_area_aoi_30m_wkt
    if str(ctx.parking_info.open_limit_new) in ['1', '14']:
        logger.info(f"内部停车场，过滤\t{ctx.parking_info.open_limit_new}")
        return proceed()
    exp_traj = utils.get_exp_traj_by_bid_more(parent_id, 3, 0.9)
    park_parent_exp_traj_dict = utils.get_exp_traj_lists_by_bid(parent_id, 3, 0.9)
    park_exp_traj_dict = utils.get_exp_traj_lists_by_bid(park_bid, 3, 0.9)
    park_exp_traj_dict = park_exp_traj_dict + park_parent_exp_traj_dict
    # if exp_traj:
    #     for geom in exp_traj:
    #         print(geom)
    # exit(1)
    # if park_exp_traj_dict:
    #     for val in park_exp_traj_dict:
    #         print(val['traj'])
    #         print(val['num_track'])
    #         print(val['num_yaw'])
    #         print(val['yaw_rate'])
    #         print(val['cuid_repetition_rate'])
    if not exp_traj:
        logger.info("停车场父点无经验轨迹，过滤")
        return proceed()
    exp_traj_num = len(exp_traj)
    park_click_pv = -1

    poi_info = ctx.dao.poi_online_query.get_poi_info_by_bid(park_bid)
    if poi_info:
        park_click_pv = poi_info[5]
    if park_click_pv <= 2:
        logger.info("停车场算路pv 小于3 过滤")
        return proceed()

    for sub_park_access in ctx.sub_pois:
        # if sub_park_access.bid != '2948915693551791976':
        #     continue
        logger.info(f"strat-access_bid {sub_park_access.bid}")
        confidence = get_confidence_by_park_click_pv(park_click_pv)
        error_msg = ''
        if str(sub_park_access.open_limit_new) in ['1', '14']:
            logger.info(f"内部出入口，过滤\t{ctx.parking_info.open_limit_new}")
            continue
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(50 / COORDINATE_FACTOR)
            access_geom_5_sp = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR)
        else:
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(60 / COORDINATE_FACTOR, cap_style=1)
            access_geom_5_sp = node_sp.buffer(5 / COORDINATE_FACTOR, cap_style=1)
        exp_res, exp_msg = exp_traj_diff_with_polygon(exp_traj, access_geom_sp, is_end_point=True)
        if exp_res == 1:
            # 0 无轨迹 1 有偏航  有一个就是偏航 2 未偏航  node 附近有轨迹 并且都没有偏航
            park_exp_res, park_exp_msg = exp_traj_diff_with_polygon_and_yaw(
                park_exp_traj_dict, access_geom_sp, 30, 0.4)
            if park_exp_res == 1:
                # 有轨迹且偏航率偏高
                error_msg = f"父点经验轨迹达到，偏航率低过高 {park_exp_msg}"
                logger.warning(f"exp_traj_not_arrive,exp_traj_num:{exp_traj_num}\tpark_click_pv:{park_click_pv}\t\
                    yaw_percent >  0.4, res:{park_exp_res},yam_rate{park_exp_msg}")
                continue
            else:
                # exp_traj一致
                logger.info(f"access_bid\t{sub_park_access.bid}\t{exp_msg}\t 父点经验轨迹达到，且偏航率低过滤")
                continue
        else:
            park_show_tag = ctx.parking_info.show_tag
            if park_show_tag in ['地下停车场', '立体停车场']:
                if ctx.parking_info.park_category != '机场&火车站':
                    # 地下判断终点聚合点  buffer 20 ,  函数内部有10米 判断  实际是 30米 内有断头路聚合点
                    aoi_res = ctx.dao.master_back_dao.get_aoi_info_by_bid(ctx.parking_info.parent_id)
                    if not aoi_res:
                        logger.info(f"sub_park_access_bid:{sub_park_access.bid}\t缺少aoi信息 过滤")
                        continue
                    aoi_geom_wkt = aoi_res[1]
                    if dead_traj_and_dead_link(ctx, access_geom_5_sp.wkt, aoi_geom_wkt, 40, 5, 10):
                        # 是断头路 过滤
                        logger.info(f"sub_park_access_bid:{sub_park_access.bid}\t是断头轨迹聚合点 过滤")
                        continue
                    else:
                        error_msg = "无断头轨迹聚合点直接输出"
                        logger.info(f"sub_park_access_bid:{sub_park_access.bid}无断头轨迹聚合点直接 过滤")
                        continue
                else:
                    error_msg = "交通枢纽断头轨迹不可用直接输出"
            else:
                # 地上判断终点轨迹进入停车场面数量
                traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
                traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_end_all, ctx, False, 30)
                if traj_list and len(traj_list) > 4:
                    # 判断终点轨迹是否经过
                    all_end_traj_num = len(traj_list)
                    park_area_gcj_sp = wkt.loads(park_area_gcj_wkt)
                    node_pass_num, park_pass_num = get_dest_traj_pass_node_num_and_pass_park_num(
                        access_geom_5_sp, traj_list, park_area_gcj_sp)
                    logger.info(f"sub_park_access_bid:{sub_park_access.bid}\tall_end_num:{all_end_traj_num},\
                        park_pass_num:{park_pass_num},node_pass_num:{node_pass_num}")
                    if node_pass_num > 0 and all_end_traj_num > 0:
                        node_percent = node_pass_num / all_end_traj_num
                        logger.info(f"sub_park_access_bid:{sub_park_access.bid}\tall_end_num:{all_end_traj_num},\
                            park_pass_num:{park_pass_num},node_pass_num:{node_pass_num},node_percent:{node_percent}")
                        if node_percent > 0.15 or park_pass_num > 3:
                            # 通量大于30% 或者 停车场面进入数量大于5
                            logger.info(f"sub_park_access_bid:{sub_park_access.bid}\t地上大门进入park轨迹多 过滤")
                            continue
                        else:
                            error_msg = f"终点轨迹通量不够 node_percent {node_percent} all_end_traj_num {all_end_traj_num} \
                                node_pass_num {node_pass_num} park_pass_num {park_pass_num}"
                    else:
                        error_msg = f"终点轨迹无大门通量 all_end_traj_num {all_end_traj_num} node_pass_num {node_pass_num}"
                else:
                    error_msg = '地上无终点轨迹或者轨迹数小于5输出'
                    logger.info(f"地上无终点轨迹或者轨迹数小于5：{traj_list}")
                    continue

        logger.warning(f"exp_traj_not_arrive,exp_traj_num:{exp_traj_num}\tpark_click_pv:{park_click_pv}\t{error_msg}")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.confidence = confidence
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_EXP_TRAJ_NOT_ARRIVE
        park_access.strategy_value = f"{park_click_pv} {exp_traj_num} {error_msg}"
        ctx.parking_access_list.append(park_access)

        # # 0 无轨迹 1 有偏航  有一个就是偏航 2 未偏航  node 附近有轨迹 并且都没有偏航
        # park_exp_res, park_exp_msg = exp_traj_diff_with_polygon_and_yaw(park_exp_traj_dict, access_geom_sp, 5, 0.3)
        # logger.info(f"access_bid\t{sub_park_access.bid}\t{park_exp_msg}\t{park_exp_res}\t exp_traj_not_yaw 过滤")
        # if park_exp_res == 1:
        #     # 有轨迹且偏航率偏高
        #     logger.warning(f"exp_traj_not_arrive,exp_traj_num:{exp_traj_num}\t\
        #           park_click_pv:{park_click_pv}\tpark_exp_res:{park_exp_res}")
        #     park_access = context.ParkingAccess()
        #     park_access.bid = sub_park_access.bid
        #     park_access.node_geom = sub_park_access.point_gcj
        #     park_access.node_id = sub_park_access.relation_node_id
        #     park_access.link_id = sub_park_access.relation_link_id
        #     park_access.type = sub_park_access.type
        #     park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_EXP_TRAJ_NOT_ARRIVE
        #     park_access.strategy_value = f"{parent_click_pv} {exp_traj_num} {park_exp_res}"
        #     ctx.parking_access_list.append(park_access)

    return proceed()
    

def check_pass_high_road(ctx: context.Context, proceed):
    """
    跨高级道路的
    """
    logger.info(f"check_pass_high_road-start:park_bid\t{ctx.parking_info.bid}")
    park_point_wkt = ctx.parking_info.park_wkt
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        access_point_wkt = sub_park_access.point_gcj
        line_wkt = make_line_by_point(access_point_wkt, park_point_wkt)
        link_form_arr = ["10", "11", "15", "16", "17", "31", "32"]
        nav_links = ctx.dao.road_dao.get_high_nav_link_by_geom(line_wkt, link_form_arr, 8)
        if not nav_links:
            continue
        check_num = 0
        check_link_id = ""
        for link in nav_links:
            # print(link)
            if "," not in link[1]:
                check_num += 1
            form_arr = link[1].split(',')
            for form in form_arr:
                if form not in link_form_arr:
                    check_num += 1
                    check_link_id = link[0]
                    break
            if check_num > 0:
                break
        if check_num == 0:
            continue

        # 引导点
        poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
        park_access_navi_competitor_list = get_build_compete_lists(ctx)
        traj_list = None
        if ctx.parking_info.show_tag not in ['地下停车场', '立体停车场'] and len(ctx.parking_info.parent_id) > 1:
            traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
            traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_end_all, ctx, False, 30)
        parent_id = ctx.parking_info.parent_id
        park_bid = ctx.parking_info.bid
        exp_traj_parent = utils.get_exp_traj_by_bid(parent_id, 3, 0.3)
        exp_traj_park = utils.get_exp_traj_by_bid(park_bid, 3, 0.3)
        exp_traj = exp_traj_parent + exp_traj_park

        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp_10m = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR)
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(25 / COORDINATE_FACTOR)
        else:
            node_id = sub_park_access.relation_node_id
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp_10m = node_sp.buffer(10 / COORDINATE_FACTOR, cap_style=1)
            access_geom_sp = node_sp.buffer(30 / COORDINATE_FACTOR, cap_style=1)

        if poi_navi_list:
            poi_nav_res, poi_nav_msg = check_poi_nav(ctx.parking_info, poi_navi_list, access_geom_sp.wkt, 5)
            if poi_nav_res == 1:
                # jpydd一致
                logger.info(f"{poi_nav_res}\t{poi_nav_msg}\t jp_point_nav过滤\taccess_bid:{sub_park_access.bid}")
                continue
        if park_access_navi_competitor_list:
            competitor_diff_res, competitor_diff_msg = competitor_diff_by_park_bid_with_access(
                park_access_navi_competitor_list, access_geom_sp.wkt, 5)
            if competitor_diff_res == 1:
                # jp小区一致
                logger.info(f"{competitor_diff_res}\t{competitor_diff_msg}\t jp_competitor_diff过滤")
                continue
        exp_res, exp_msg = exp_traj_diff_with_polygon(exp_traj, access_geom_sp)
        if exp_res == 1:
            # exp_traj一致
            logger.info(f"{exp_res}\t{exp_msg}\t exp_traj_diff过滤")
            continue

        # if ctx.dao.poi_online_query.is_history_manual_worked(sub_park_access.bid):
        #     # 历史人工作业过，跳过
        #     logger.info(f"access_bid\t{sub_park_access.bid}\t历史人工作业过，过滤")
        #     continue

        if traj_list:
            # 判断终点轨迹是否经过
            pass_num = get_dest_traj_pass_node_num(access_geom_sp, traj_list)
            print("pass_num" + str(pass_num))
            if pass_num > 0:
                logger.info(f"{pass_num}\t终点轨迹穿过过滤")
                continue

        logger.info(f"check_pass_high_road:{nav_links[0]}")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_PASS_HIGH_ROAD
        park_access.strategy_value = check_link_id
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_traj_line_pass_two_node(ctx: context.Context, proceed):
    """
    轨迹穿越2个node
    """
    park_show_tag = ctx.parking_info.show_tag
    # if park_show_tag in ['地下停车场', '立体停车场']:
    #     # 地下轨迹乱易穿过2个 加断头路过滤
    #     return proceed()
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) \
    if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt)
    if not park_area_sp:
        return proceed()
    aoi_area_sp = wkt.loads(ctx.aoi_info.wkt) if ctx.aoi_info.wkt  else park_area_sp
    traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
    access_geom_sp_dict = {}
    dead_road_bid = []
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR)
            res, _, _ = utils.is_dead_link_road_by_link(sub_park_access.relation_link_id, 100)
            if res:
                dead_road_bid.append(sub_park_access.bid)
        if sub_park_access.type == 'NODE':
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(5 / COORDINATE_FACTOR, cap_style=1)
            res, _, _ = utils.is_dead_link_road(access_geom_sp.wkt, 100)
            if res:
                dead_road_bid.append(sub_park_access.bid)

        access_geom_sp_dict[sub_park_access.bid] = [access_geom_sp, sub_park_access]
    
    pass_access_bid_dict = {} 
    node_end_point_num_dict = {}
    # 计算终点轨迹是否有穿过2个node
    for traj_sp in traj_list_end_all:
        traj_end_point_sp = Point(list(traj_sp.coords)[-1])
        for access_bid, val in access_geom_sp_dict.items():
            # 5 + 5 10米内无终点
            # if val[0].distance(traj_end_point_sp) * COORDINATE_FACTOR < 5:
            #     if access_bid not in node_end_point_num_dict:
            #         node_end_point_num_dict[access_bid] = 1
            #     node_end_point_num_dict[access_bid] += 1
            #     continue
            if access_bid in pass_access_bid_dict:
                continue
            # if access_bid != '15849641201082665335':
            #     continue
            if not aoi_area_sp.contains(traj_end_point_sp):
                # 终点不在aoi范围内
                continue
            if traj_sp.intersects(val[0]):
                for access_bid_diff, val_diff in access_geom_sp_dict.items():
                    if access_bid == access_bid_diff:
                        continue
                    node_dis = val[0].distance(val_diff[0]) * COORDINATE_FACTOR
                    # logger.info(f"node距离： {node_dis},access_bid{access_bid},access_bid_diff:{access_bid_diff}")
                    if traj_sp.intersects(val_diff[0]) and node_dis < 120 and node_dis > 20:
                        # 连接通过2个node 且node间距离小于 + 20米nodebuffer
                        if park_show_tag in ['地下停车场', '立体停车场']:
                            # 地下有一个不是断头路
                            if access_bid not in dead_road_bid or access_bid_diff not in dead_road_bid:
                                pass_access_bid_dict[access_bid] = val[1]
                                pass_access_bid_dict[access_bid_diff] = val_diff[1]
                                break
                        else:
                            # 地上有一个是断头路
                            if access_bid in dead_road_bid or access_bid_diff in dead_road_bid:
                                pass_access_bid_dict[access_bid] = val[1]
                                pass_access_bid_dict[access_bid_diff] = val_diff[1]
                                break
    
    push_access_list = []
    if len(pass_access_bid_dict) > 0:
        # end_traj_sum = sum(node_end_point_num_dict.values())
        # if end_traj_sum >= 5:
        # node_percent_dict = {key: value / end_traj_sum for key, value in node_end_point_num_dict.items()}
        for access_id, val in pass_access_bid_dict.items():
            # if access_id in node_end_point_num_dict and access_id in node_percent_dict \
            #     and node_percent_dict[access_id] > 0.2 and node_percent_dict[access_id] < 0.8:
            #     logger.info(f"{access_id}终点在node范围内数量及占比\t{node_end_point_num_dict[access_id]},\
            #           {node_percent_dict[access_id]}")
            #     continue
            if access_id in push_access_list:
                continue
            push_access_list.append(access_id)
            sub_park_access = val
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = "park_access_rongyu"
            park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_END_TRAJ_PASS_MULI_NODE
            park_access.strategy_value = f"traj_pass_2_node"
            ctx.parking_access_list.append(park_access)
    return proceed()


def check_by_complete(ctx: context.Context, proceed):
    """
    检查是否完成
        Args:
            ctx:
            proceed:

    Returns:
    """
    logger.info(f"check_by_complete-start:park_bid\t{ctx.parking_info.bid}")
    park_bid = ctx.parking_info.bid
    parent_id = ctx.parking_info.parent_id
    logger.info(f"parent_id: {parent_id},\tshow_tag{ctx.parking_info.show_tag}\t{ctx.parking_info.name}")
    park_area_gcj_wkt = ctx.parking_info.park_area_gcj_wkt \
        if ctx.parking_info.park_area_gcj_wkt else ctx.parking_info.park_area_aoi_30m_wkt

    # 引导点
    poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
    # if poi_navi_list:
    #     for a in poi_navi_list:
    #         print(a)
    # exit(10)
    # 出入口竞品
    park_access_navi_competitor_list = get_build_compete_lists(ctx)
    # if park_access_navi_competitor_list:
    #     for a in park_access_navi_competitor_list:
    #         print(a[0])
    # exit(1)
    if not park_access_navi_competitor_list:
        logger.info(f"竞品数据不存在\t{ctx.parking_info.bid}")
        return proceed()
    park_jp_num = len(poi_navi_list) if poi_navi_list else 0
    access_jp_num = len(park_access_navi_competitor_list) if park_access_navi_competitor_list else 0
    # if ctx.parking_info.show_tag not in ['地下停车场', '立体停车场'] and len(ctx.parking_info.parent_id) > 1:
        # traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
    #     # for a in traj_list_end_all:
    #     #     print(a)
        # traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_end_all, ctx, False, 30)
    # #     for a in traj_list:
    # #         print(a)
    parent_id = ctx.parking_info.parent_id
    park_bid = ctx.parking_info.bid
    exp_traj = utils.get_exp_traj_by_bid(parent_id, 3, 0.7)
    # for a in exp_traj:
    #     print(a)
    # exit(1)
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_check_access_bid:{sub_park_access.bid}")
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                logger.info(f"link 失效{sub_park_access.relation_link_id}")
                continue
            access_geom_sp = wkt.loads(link_info[0][0])
            access_geom_sp_30 = wkt.loads(link_info[0][0]).buffer(30 / COORDINATE_FACTOR)
        else:
            node_id = sub_park_access.relation_node_id
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp
            access_geom_sp_30 = node_sp.buffer(30 / COORDINATE_FACTOR, cap_style=1)
        
        park_min_dis = get_park_nav_mis_dis(poi_navi_list, access_geom_sp)
        if park_min_dis < 50:
            # jpydd一致
            logger.info(f"park_min_dis:{park_min_dis}\t jp_point_nav < 50过滤\taccess_bid:{sub_park_access.bid}")
            continue
        access_min_dis = get_competitor_diff_by_park_bid_with_access_min_dis(
            park_access_navi_competitor_list, access_geom_sp)
        if access_min_dis < 50:
            # jp出入口一致
            logger.info(f"access_min_dis:{access_min_dis}\t jp_competitor_access_diff < 50过滤\taccess_bid:\
                {sub_park_access.bid}")
            continue
        min_dis = min(park_min_dis, access_min_dis)

        exp_res, exp_msg = exp_traj_diff_with_polygon(exp_traj, access_geom_sp_30, True)
        if exp_res == 1:
            # exp_traj一致
            logger.info(f"{exp_res}\t{exp_msg}\t exp_traj_diff到达过滤")
            continue

        if ctx.parking_info.show_tag in ['地下停车场', '立体停车场']:
            if sub_park_access.type == 'NODE':
                # 关联node
                node_id = sub_park_access.relation_node_id
                node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(3 / COORDINATE_FACTOR).wkt
                res, _, _ = utils.is_dead_link_road(node_buffer_wkt, 50)
                if res:
                    logger.info(f"{node_id}\tnode是断头路")
                    continue
            elif sub_park_access.type == 'LINK':
                link_id = sub_park_access.relation_link_id
                res, _, _ = utils.is_dead_link_road_by_link(link_id, 50)
                if res:
                    logger.info(f"{link_id}\tlink是断头路过滤")
                    continue
        
        if ctx.parking_info.show_tag in ['地上停车场', '停车场']:
            # 地上判断终点轨迹进入停车场面数量
            traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
            traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_end_all, ctx, False, 30)
            if traj_list and len(traj_list) > 3:
                # 判断终点轨迹是否经过
                all_end_traj_num = len(traj_list)
                park_area_gcj_sp = wkt.loads(park_area_gcj_wkt)
                node_pass_num, park_pass_num = get_dest_traj_pass_node_num_and_pass_park_num(
                    access_geom_sp_30, traj_list, park_area_gcj_sp)
                logger.info(f"sub_park_access_bid:{sub_park_access.bid}\tall_end_num:{all_end_traj_num},\
                    park_pass_num:{park_pass_num},node_pass_num:{node_pass_num}")
                if node_pass_num > 0 and all_end_traj_num > 0:
                    node_percent = node_pass_num / all_end_traj_num
                    logger.info(f"sub_park_access_bid:{sub_park_access.bid}\tall_end_num:{all_end_traj_num},\
                        park_pass_num:{park_pass_num},node_pass_num:{node_pass_num},node_percent:{node_percent}")
                    if node_percent > 0.20 or park_pass_num > 3:
                        # 通量大于30% 或者 停车场面进入数量大于3
                        logger.info(f"sub_park_access_bid:{sub_park_access.bid}\t地上大门进入park轨迹多 过滤")
                        continue
                    else:
                        error_msg = f"终点轨迹通量不够 node_percent {node_percent} all_end_traj_num {all_end_traj_num} \
                            node_pass_num {node_pass_num} park_pass_num {park_pass_num}"
                else:
                    error_msg = f"终点轨迹无大门通量 all_end_traj_num {all_end_traj_num} node_pass_num {node_pass_num}"
            else:
                error_msg = '地上无终点轨迹或者轨迹数小于5输出'
                logger.info(f"地上无终点轨迹或者轨迹数小于5：{traj_list}")
                continue

        logger.info(f"access_geom_sp:{access_geom_sp}\t jp_diff_output\tmin_dis:{min_dis}")
        park_access = context.ParkingAccess()
        park_access.confidence = 90
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_COMPLETE_DIFF_LESS_100
        park_access.strategy_value = f"park_jp_num:{park_jp_num}\taccess_jp_num:{access_jp_num}\t{min_dis}"
        ctx.parking_access_list.append(park_access)
    return proceed()


def exp_traj_diff_with_polygon(exp_traj, geom_sp, is_end_point=False):
    """
    判断经验轨迹和node是否有交集
    """
    if not exp_traj:
        return 0, "exp_traj_len_is_0"
    # print(f"exp_geom_sp\t{geom_sp}")
    for exp_sp in exp_traj:
        # print(exp_sp)
        if is_end_point:
            # 终点在范围内
            if geom_sp.contains(Point(list(exp_sp.coords)[-1])):
                # print(exp_sp)
                return 1, "exp_traj_diff_with_polygon_has_intersects"
        else:
            if exp_sp.intersects(geom_sp):
                return 1, "exp_traj_diff_with_polygon_has_intersects"
    return 0, "exp_traj_diff_with_polygon_no_intersects"


def exp_traj_diff_with_polygon_and_yaw(exp_traj_list, geom_sp, min_yaw_num=30, min_yaw_rata=0.3):
    """
    判断经验轨迹是否偏航
    0 无轨迹
    1 有偏航  有一个就是偏航
    2 未偏航  node 附近有轨迹 并且都没有偏航
    """
    if not exp_traj_list:
        logger.info(f"park_exp_traj_len_is_0")
        return 0, "park_exp_traj_len_is_0"
    is_not_yaw = 0
    # logger.info(geom_sp)
    for exp_traj in exp_traj_list:
        exp_sp = exp_traj['traj']
        num_yaw = exp_traj['num_yaw']
        yaw_rate = exp_traj['yaw_rate']
        # logger.info(exp_sp)
        if geom_sp.contains(Point(list(exp_sp.coords)[-1])):
            # logger.info(exp_sp)
            if num_yaw > min_yaw_num and yaw_rate > min_yaw_rata:
                logger.info(f"num_yaw:{num_yaw},\t{min_yaw_num},yaw_rate:{yaw_rate}\t{min_yaw_rata}")
                logger.info(exp_sp)
                return 1, yaw_rate
            else:
                is_not_yaw += 1
    if is_not_yaw > 0:
        return 2, "exp_traj_diff_with_polygon_no_intersects"
    return 0, "exp_traj_diff_with_polygon_no_intersects"


def check_out_link(ctx, park_wkt, link_ids):
    """
    判断是否是出方向link
    """
    is_out_link = False
    for link_id in link_ids:
        link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([link_id])
        if not link_info:
            continue
        link_sp = wkt.loads(link_info[0][0])
        link_dir = link_info[0][6]
        if link_dir not in [2, 3]:
            continue
        if link_dir == 2:
            first_sp = Point(list(link_sp.coords)[0])
            last_sp = Point(list(link_sp.coords)[-1])
        elif link_dir == 3:
            last_sp = Point(list(link_sp.coords)[0])
            first_sp = Point(list(link_sp.coords)[-1])
        first_dis = first_sp.distance(wkt.loads(park_wkt)) * 111000
        last_dis = last_sp.distance(wkt.loads(park_wkt)) * 111000
        # link 夹角大于45度 且是出方向
        if first_dis < last_dis and (last_dis - first_dis) > 1:
            angel = calculate_angle_between_vectors(park_wkt, first_sp.wkt, last_sp.wkt)
            logger.warning(f"park_bid:{ctx.parking_info.bid}\t{link_id}\t{link_dir}\tangel：{angel}")
            if angel < 45:
                is_out_link = True
                logger.warning(f"park_bid:{ctx.parking_info.bid}\t{link_id}\t{link_dir}")
                break
    return is_out_link


def is_closer_in_intersection_with_direction(linestring_wkt, point_wkt, polygon_wkt):
    """
    判断轨迹进出方向
    """
    # logger.debug(f"{linestring_wkt}\t{point_wkt}")
    # logger.debug(f"{polygon_wkt}")
    # 解析WKT字符串为shapely对象
    linestring = wkt.loads(linestring_wkt)
    polygon = wkt.loads(polygon_wkt)
    point = wkt.loads(point_wkt)
 
    # 找到LineString与Polygon的相交部分
    intersection = linestring.intersection(polygon)
    
    # 检查相交部分是否为线段，并且确保它是简单的（没有自相交）
    if intersection.is_simple and intersection.geom_type == 'LineString':
        # 获取相交线段的起点和终点坐标
        intersection_coords = list(intersection.coords)
        start, end = intersection_coords[0], intersection_coords[-1]
        
        # 在linestring上找到相交线段的起始索引和结束索引
        start_idx = None
        end_idx = None
        for i, coord in enumerate(linestring.coords):
            if tuple(coord) == start:
                start_idx = i
            if tuple(coord) == end:
                end_idx = i
        
        # 确保我们找到了起始和结束索引，并且它们是相邻的（或正确的顺序）
        if start_idx is not None and end_idx is not None:
            # 检查索引的顺序以确定方向
            if (start_idx < end_idx and (end_idx - start_idx == len(intersection_coords) - 1)) or \
               (start_idx > end_idx and (start_idx - end_idx == len(intersection_coords) - 1)):
                # 获取相交线段的起点和终点Point对象
                start_point = Point(start)
                end_point = Point(end)
                
                # 计算起点和终点到指定Point的距离
                start_distance = start_point.distance(point)
                end_distance = end_point.distance(point)
                
                # 判断距离是增大还是减小（基于linestring的方向）
                if end_distance < start_distance:
                    return "IN"
                elif end_distance > start_distance:
                    return "OUT"
                else:
                    return "UNKNOWN"
            else:
                # 如果索引顺序不正确，可能是相交部分不是简单的线段，或者linestring与polygon的边界复杂
                return "UNKNOWN"
        else:
            # 如果没有找到起始和结束索引，可能是因为相交部分太复杂
            return "UNKNOWN"
    else:
        # 如果相交部分不是线段，或者相交部分太复杂（例如，多个线段或点），则返回None
        return "UNKNOWN"
 

def get_end_traj_in_out_num(traj_list, park_wkt, access_geom_sp):
    """
    统计轨迹进出数量
    """
    num_dict = {
        "traj_all": 0,
        "traj_in": 0,
        "traj_out": 0,
    }
    for traj_sp in traj_list:
        if not access_geom_sp.intersects(traj_sp):
            continue
        dir = is_closer_in_intersection_with_direction(traj_sp.wkt, park_wkt, access_geom_sp.wkt) 
        if dir == "IN":
            num_dict["traj_in"] += 1
        elif dir == "OUT":
            num_dict["traj_out"] += 1
        num_dict["traj_all"] += 1
    return num_dict


def check_in_gate_only_out_traj(ctx: context.Context, proceed):
    """
    入口只有出方向轨迹，或者单向出link
    """
    # 目前轨迹召回0个 跑的很慢注释了
    # traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
    # traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_end_all, ctx)
    traj_list = None
    park_show_tag = ctx.parking_info.show_tag
    if park_show_tag in ['地下停车场', '立体停车场']:
        # 地下反方向进入特多 忽略地下场景
        return proceed()

    for sub_park_access in ctx.sub_pois:
        check_links = []
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(10 / COORDINATE_FACTOR)
            check_links.append(sub_park_access.relation_link_id)
        else:
            node_id = sub_park_access.relation_node_id
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(10 / COORDINATE_FACTOR, cap_style=1)
            links = ctx.dao.road_dao.get_nav_link_by_node([node_id], None)
            link_id = get_min_dis_link(ctx.parking_info.park_wkt, links)
            check_links.append(link_id)
        if check_links is None or len(check_links) == 0:
            logger.warning(f"{ctx.parking_info.bid}\t没有找到link")
            continue
        logger.info(f"check_links{check_links}")
        # 判断是否是仅出去的link
        is_single_out_link = check_out_link(ctx, ctx.parking_info.park_wkt, check_links)
        logger.info(f"check_links{check_links}\tis_single_out_link:{is_single_out_link}")
        # 判断终点轨迹是否只有出去的
        is_single_out_by_traj = False
        if traj_list:
            num_dict = get_end_traj_in_out_num(traj_list, ctx.parking_info.park_wkt, access_geom_sp)
            # 判断出去通量占比
            if num_dict['traj_all'] > 10:
                ratio_out = num_dict['traj_out'] / num_dict['traj_all']
                ratio_in = num_dict['traj_in'] / num_dict['traj_all']
                logger.info(f"node_id:{sub_park_access.relation_node_id}\t终点数量:{num_dict['traj_all']}\t\
                    出数量:{num_dict['traj_out']}\t进数量:{num_dict['traj_in']}\t出占比:{ratio_out}\t进占比:{ratio_in}")
                if num_dict['traj_all'] > 10 and ratio_out >= 0.50 and ratio_in <= 0.05:
                    logger.info(f"{ctx.parking_info.bid}\t终点轨迹只有出去的")
                    is_single_out_by_traj = True

        if is_single_out_link or is_single_out_by_traj:
            logger.error(f"bid:{sub_park_access.bid}\tnode_id:{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{is_single_out_link}\t{is_single_out_by_traj}")
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = sub_park_access.type
            if sub_park_access.type == 'LINK':
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_LINK
            else:
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_IN_GATE_OUT_TRAJ_NODE
            park_access.strategy_value = f"is_single_out_link:{is_single_out_link},\
                is_single_out_by_traj:{is_single_out_by_traj}"
            ctx.parking_access_list.append(park_access)
        return proceed()
       

def get_dest_traj_pass_node_num(geom_sp, dest_traj_list, is_end_point=False):
    """"
    计算终点轨迹通过AOI出入口的量级
    is_end_point: 终点是否在面内
    """
    if not geom_sp:
        return 0
    pass_num = 0
    # logger.info(f"geom_sp\t{geom_sp.wkt}")
    for traj_sp in dest_traj_list:
        # print(f"{traj_sp}")
        if is_end_point:
            # 终点在范围内
            if geom_sp.contains(Point(list(traj_sp.coords)[-1])):
                # print(traj_sp.wkt)
                pass_num = pass_num + 1
        else:
            # 轨迹穿过范围
            if traj_sp.intersects(geom_sp):
                # print(traj_sp.wkt)
                pass_num = pass_num + 1
    return pass_num


def get_dest_traj_pass_node_num_and_pass_park_num(geom_sp, dest_traj_list, park_area_gcj_sp):
    """"
    计算终点轨迹通过AOI出入口的量级
    is_end_point: 终点是否在面内
    """
    if not geom_sp:
        return 0
    node_pass_num = 0
    park_pass_num = 0
    # logger.info(f"geom_sp\t{geom_sp.wkt}")
    # logger.info(f"park_area_gcj_sp\t{park_area_gcj_sp.wkt}")
    for traj_sp in dest_traj_list:
        # 终点在node范围内
        # logger.info(traj_sp)
        if geom_sp.intersects(traj_sp):
            # print(traj_sp.wkt)
            node_pass_num = node_pass_num + 1
            # 终点在park范围内
            if park_area_gcj_sp.contains(Point(list(traj_sp.coords)[-1])):
                # print(traj_sp.wkt)
                park_pass_num = park_pass_num + 1
    return node_pass_num, park_pass_num



def check_traj_has_no_intersection_with_park(ctx: context.Context, proceed):
    """
    检查终点轨迹是否与停车场相交  
    """
    logger.info(f"check_traj_has_no_intersection_with_park\t{ctx.parking_info.bid}")
    traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
    traj_list_stop_all = utils.get_park_stop_traj(ctx.parking_info, ctx.aoi_info, ctx)
    traj_list_all = traj_list_end_all + traj_list_stop_all
    traj_end_num = len(traj_list_end_all)
    traj_stop_num = len(traj_list_stop_all)
    traj_all_num = len(traj_list_all)
    logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\taoi终点轨迹数量:{traj_end_num}\t\
        停留轨迹数量:{traj_stop_num}\t总轨迹数量:{traj_all_num}")
    if not traj_list_all:
        logger.warning(f"{ctx.parking_info.bid}\t没有找到终点轨迹")
        return proceed()
    
    traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_all, ctx)

    if not traj_list:
        logger.warning(f"{ctx.parking_info.bid}\t没有找到node终点轨迹")
        return proceed()
    traj_num = len(traj_list)
    logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\t终点node_filter轨迹数量:{traj_num}")
    if len(traj_list) < 10:
        logger.warning(f"{ctx.parking_info.bid}\t终点轨迹数量 < 10")
        return proceed()
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(10 / COORDINATE_FACTOR)
        if sub_park_access.type == 'NODE':
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(10 / COORDINATE_FACTOR, cap_style=1)
        pass_num = get_dest_traj_pass_node_num(access_geom_sp, traj_list)
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}")
        percent = pass_num / traj_num
        if percent < 0.05 and pass_num < 3:
            logger.error(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}\t{percent}")
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = sub_park_access.type
            park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_ACCESS_BY_LOW_TRAJ
            park_access.strategy_value = f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}\t{percent}"
            ctx.parking_access_list.append(park_access)
    return proceed()


def buffer_line_sides_only(line, distance):
    """
    # 返回线段的2侧扩展后的多边形
    """
    # 创建一个左右两边扩展的多边形
    left_offset = translate(line.parallel_offset(distance, 'left'), 0, 0)
    right_offset = translate(line.parallel_offset(distance, 'right'), 0, 0)
    # 组合左右多边形，但去掉两端
    sides_polygon = Polygon([*left_offset.coords] + [*right_offset.coords[::-1]])
    return sides_polygon
    

def angle_between_lines(p1_start, p1_end, p2_start, p2_end ):
    """
    # 计算有向线段之间的夹角
    """
    # 获取线段的起点和终点
    # 计算两个向量的方向
    vec1 = (p1_end[0] - p1_start[0], p1_end[1] - p1_start[1])
    vec2 = (p2_end[0] - p2_start[0], p2_end[1] - p2_start[1])
    # 计算向量的叉积
    cross_product = vec1[0] * vec2[1] - vec1[1] * vec2[0]
    # 计算向量的点积
    dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
    # 计算两个向量之间的夹角的余弦值
    cos_angle = dot_product / (math.sqrt(vec1[0]**2 + vec1[1]**2) * math.sqrt(vec2[0]**2 + vec2[1]**2))
    # 防止浮点运算误差导致的值超出范围[-1, 1]
    cos_angle = max(-1.0, min(1.0, cos_angle))
    # 计算角度
    angle = math.degrees(math.acos(cos_angle))
    # print(angle)
    # 使用叉积的符号来确定角度的方向
    if cross_product > 0:
        return angle
    elif cross_product < 0:
        return 360 - angle
    else:
        # 两条线段共线
        return 0


def get_node_next_link_angel(ctx, node_id, check_node_id):
    """
    计算node连线的角度
    """
    node_link = ctx.dao.road_dao.get_nav_link_by_s_node([node_id])
    check_node_link = ctx.dao.road_dao.get_nav_link_by_s_node([check_node_id])
    if not node_link or not check_node_link:
        return -1
    line1 = wkt.loads(node_link[0][1])
    line2 = wkt.loads(check_node_link[0][1])
    if node_link[0][2] == 3:
        p1_start, p1_end = line1.coords[-1], line1.coords[0]
    else:
        p1_start, p1_end = line1.coords[0], line1.coords[-1]
    
    if check_node_link[0][2] == 3:
        p2_start, p2_end = line2.coords[-1], line2.coords[0]
    else:
        p2_start, p2_end = line2.coords[0], line2.coords[-1]
    # print(p1_start)
    # print(p1_end)
    # print(p2_start)
    # print(p2_end)
    return angle_between_lines(p1_start, p1_end, p2_start, p2_end)


def check_node_line_has_other_node(ctx: context.Context, proceed):
    """
    检查node连线是否有其他节点
    """
    node_set = set()
    for sub_park_access in ctx.sub_pois:
        if sub_park_access.type == 'NODE':
            node_set.add(sub_park_access.relation_node_id)

    park_sp = wkt.loads(ctx.parking_info.park_wkt)
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}\t{sub_park_access.type}")
        if sub_park_access.type == 'NODE':
            node_sp = wkt.loads(sub_park_access.point_gcj)
            line_sp = LineString([park_sp, node_sp])
            # print(line_sp,park_sp,node_sp)
            line_buffer_sp = buffer_line_sides_only(line_sp, 30 / COORDINATE_FACTOR)
            # print(line_buffer_sp)
            node_list = ctx.dao.road_dao.get_nav_node_by_geom(line_buffer_sp.wkt)
            
            if not node_list:
                logger.info(f"{sub_park_access.bid}\tno_nodes_in_geom")
                continue
            for node in node_list:
                check_node_id = node[0]
                if check_node_id in node_set:
                    logger.info(f"check_node_id:{check_node_id}\t pass")
                    continue
                nav_gate_semantic = utils.get_nav_gate_semantic(check_node_id)
                if nav_gate_semantic not in ['入口', '出入口']:
                    logger.info(f"check_node_id:{check_node_id},{nav_gate_semantic}\t过滤非入口")
                    continue
                node_angle = get_node_next_link_angel(ctx, sub_park_access.relation_node_id, check_node_id)
                logger.info(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                    {check_node_id}\t{node_angle}")
                if node_angle < 150 or node_angle > 250:
                    # 非反反向的过滤掉
                    logger.info(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                        {check_node_id}\t{node_angle}\tnode夹角不相反")
                    continue
                node_dis = node_sp.distance(wkt.loads(node[1])) * COORDINATE_FACTOR
                if node_dis > 30:
                    logger.info(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                        {check_node_id}\t{node_dis}")
                    continue
                gate_info = ctx.dao.road_dao.get_nav_gate_by_node_id(check_node_id)
                if not gate_info:
                    logger.info(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                        {check_node_id}\t{node_dis}\tno_gate")
                    continue
                logger.error(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t{check_node_id}\t{node_dis}")
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.type = sub_park_access.type
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_NODE_LINE_HAS_OTHER_NODE
                park_access.strategy_value = f"node:{sub_park_access.relation_node_id}\t\
                    ot_node:{check_node_id}\t{node_dis}"
                ctx.parking_access_list.append(park_access)
    return proceed()


def check_access_by_low_traj(ctx: context.Context, proceed):
    """
    检查低轨迹
    :return:
    """
    logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}")
    if len(ctx.sub_pois) < 2:
        logger.warning(f"{ctx.parking_info.bid}\t出入口poi数量 < 2")
        return proceed()
    traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
    traj_list_stop_all = utils.get_park_stop_traj(ctx.parking_info, ctx.aoi_info, ctx)
    # for traj_sp in traj_list_stop_all:
    #     print(f"{traj_sp}")
    # exit(1)
    # traj_list_all = traj_list_end_all
    traj_list_all = traj_list_end_all + traj_list_stop_all
    traj_end_num = len(traj_list_end_all)
    traj_stop_num = len(traj_list_stop_all)
    traj_all_num = len(traj_list_all)
    logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\taoi终点轨迹数量:{traj_end_num}\t\
        停留轨迹数量:{traj_stop_num}\t总轨迹数量:{traj_all_num}")
    if not traj_list_all:
        logger.warning(f"{ctx.parking_info.bid}\t没有找到终点轨迹")
        return proceed()
    traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_all, ctx)

    if not traj_list:
        logger.warning(f"{ctx.parking_info.bid}\t没有找到node终点轨迹")
        return proceed()
    traj_num = len(traj_list)
    logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\t终点node_filter轨迹数量:{traj_num}")
    if len(traj_list) < 10:
        logger.warning(f"{ctx.parking_info.bid}\t终点轨迹数量 < 10")
        return proceed()
    for sub_park_access in ctx.sub_pois:
        logger.info(f"access_bid:\t{sub_park_access.bid}\taccess_node_id:{sub_park_access.relation_node_id}\t\
            taccess_link_id{sub_park_access.relation_link_id}")
        if sub_park_access.type == 'LINK':
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                continue
            access_geom_sp = wkt.loads(link_info[0][0]).buffer(10 / COORDINATE_FACTOR)
        if sub_park_access.type == 'NODE':
            node_sp = wkt.loads(sub_park_access.point_gcj)
            access_geom_sp = node_sp.buffer(10 / COORDINATE_FACTOR, cap_style=1)
        pass_num = get_dest_traj_pass_node_num(access_geom_sp, traj_list)
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}")
        percent = pass_num / traj_num
        if percent < 0.07 and pass_num < 50:
            logger.error(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}\t{percent}")
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.node_id = sub_park_access.relation_node_id
            park_access.link_id = sub_park_access.relation_link_id
            park_access.type = sub_park_access.type
            park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_ACCESS_BY_LOW_TRAJ
            park_access.strategy_value = f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}\t{percent}"
            ctx.parking_access_list.append(park_access)
    return proceed()


def get_node_area_relation(point_gcj_wkt, park_area_sp):
    """
    获取节点与区域的关系
    Args:
        node_id:
        park_area_sp:
    Returns:
    """
    if park_area_sp.contains(wkt.loads(point_gcj_wkt)):
        node_area_relation = 'IN_AREA'
    else:
        node_area_relation = 'OUT_AREA'
    return node_area_relation


def calculate_angle_between_vectors(point, node_point1, node_point2):
    """
    计算停车场到2个门的夹角
    """
    point = wkt.loads(point)
    node_point1 = wkt.loads(node_point1)
    node_point2 = wkt.loads(node_point2)
    # 计算向量
    vector1 = (node_point1.x - point.x, node_point1.y - point.y)
    vector2 = (node_point2.x - point.x, node_point2.y - point.y)
    # 计算每个向量的角度（相对于x轴正方向，逆时针为正，顺时针为负）
    angle1 = math.degrees(math.atan2(vector1[1], vector1[0]))
    angle2 = math.degrees(math.atan2(vector2[1], vector2[0]))
    # 计算两个角度之间的差
    angle_difference = abs(angle1 - angle2)
    # 如果角度差大于180度，则使用360度减去角度差来得到较小的夹角
    if angle_difference > 180:
        angle_difference = 360 - angle_difference
    return angle_difference


def check_link_to_multi_node(ctx: context.Context, proceed):
    """
    检查单条链路上是否有多个节点

    Args:
        ctx (context.Context): 上下文对象
        proceed (function): 回调函数

    Returns:
        function: 回调函数

    """
    """
    检查单条链路上是否有多个节点
    Args:
        ctx:
        proceed:
    Returns:
    """
    node_to_access = {}
    node_all_list = []
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) \
        if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt)
    if not park_area_sp:
        return proceed()
    park_area_sp.buffer(30 / COORDINATE_FACTOR)
    park_point_wkt = ctx.parking_info.park_wkt
    # 数据准备
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t\
            {sub_park_access.relation_node_id}\t{sub_park_access.relation_link_id}")
        if sub_park_access.type == 'LINK':
            link_arr = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_arr:
                continue
            s_node_id = link_arr[0][1]
            e_node_id = link_arr[0][2]
            if s_node_id not in node_all_list:
                node_all_list.append(s_node_id)
                node_to_access[s_node_id] = sub_park_access
            elif e_node_id not in node_all_list:
                node_all_list.append(e_node_id)
                node_to_access[e_node_id] = sub_park_access
            else:
                continue
        elif sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            node_to_access[node_id] = sub_park_access
            if node_id not in node_all_list:
                node_all_list.append(node_id)
    if len(node_all_list) <= 1:
        logger.warning(f"{ctx.parking_info.bid}\tnode ≤ 1")
        return proceed()
    node_set = set()
    for node_id in node_all_list:
        logger.info(f"start_access-node\t{node_id}")
        access = node_to_access[node_id]
        # node_area_relation = get_node_area_relation(access.point_gcj, park_area_sp)
        node_point_wkt = access.point_gcj
        node_buffer_wkt = wkt.loads(access.point_gcj).buffer(100 / COORDINATE_FACTOR).wkt
        nav_links = ctx.dao.road_dao.get_nav_link_by_geom(node_buffer_wkt, "52", 7)
        if not nav_links:
            logger.warning(f"{ctx.parking_info.bid}\t{node_id}\t没有找到对应的导航链路")
            continue
        # filter_nav_link = [row for row in nav_links if row[3] != 1]
        # if len(filter_nav_link) == 0:
            # logger.warning(f"{ctx.parking_info.bid}\t{node_id}\tfilter后没有找到对应的导航链路")
            # continue
        check_node_lists = []
        for check_node in node_all_list:
            if check_node == node_id:
                continue
            distance = wkt.loads(access.point_gcj).distance(
                wkt.loads(node_to_access[check_node].point_gcj)) * COORDINATE_FACTOR
            logger.info(f"距离 当前bid:{ctx.parking_info.bid}\tnode_id:{node_id}\tcheck_node:{check_node}\t{distance}")
            if distance < 20 and distance > 120:
                # 距离过远过近的排除
                continue
            check_node_access_name = node_to_access[check_node].name
            # nav_gate_semantic = utils.get_nav_gate_semantic(check_node)
            # if nav_gate_semantic not in ['入口', '出入口']:
            if not check_node_access_name.endswith('入口'):
                logger.info(f"通行性非入口：{ctx.parking_info.bid}\tnode_id:{node_id}\t\
                    check_node:{check_node}\t{check_node_access_name}")
                continue
            check_node_lists.append(check_node)
        if len(check_node_lists) == 0:
            logger.warning(f"{ctx.parking_info.bid}\t{node_id}\t没有找到可匹配的node")
            continue
        # 递归深度 4级
        logger.info(f"check_node_lists{check_node_lists}")
        reachable_nodes = find_reachable_nodes_with_paths(node_id, check_node_lists, nav_links, 6)
        logger.info(f"reachable_nodes{reachable_nodes}")
        if reachable_nodes:
            is_reachable_target = False
            for reachable_node_id, reachable_value in reachable_nodes.items():
                if reachable_node_id in node_set:
                    logger.warning(f"node_set:{node_set}\r\
                        eachable_node_id:{reachable_node_id} reachable_node_id in node_set")
                    continue
                sub_park_access = node_to_access[reachable_node_id]
                check_node_point_wkt = sub_park_access.point_gcj
                line_angle = calculate_angle_between_vectors(park_point_wkt, node_point_wkt, check_node_point_wkt)
                if line_angle > 90 and line_angle < 270:
                    logger.warning(f"node_id:{node_id}\tcheck_node:{reachable_node_id}\t\
                        node_id:{reachable_node_id}\t角度超过90度{line_angle}")
                    continue
                # check_node_area_relation = get_node_area_relation(sub_park_access.point_gcj, park_area_sp)
                # if check_node_area_relation == node_area_relation:
                #     logger.info(f"node位置与面关系一致node_id\treachable_nodes:{reachable_node_id}\tnode_id:{node_id}")
                #     continue
                is_reachable_target = True
                logger.warning(f"reachable_nodes:{reachable_node_id}，有问题的node")
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.type = "park_access_rongyu"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE
                park_access.strategy_value = f"node:{node_id},reachable_nodes:{reachable_node_id}"
                ctx.parking_access_list.append(park_access)
            if node_id in node_set:
                continue
            if is_reachable_target:
                node_set.add(node_id)
                logger.warning(f"node:{node_id}，有问题的node")
                sub_park_access = node_to_access[node_id]
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.type = "park_access_rongyu"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE
                park_access.strategy_value = f"node_start:{node_id}"
                ctx.parking_access_list.append(park_access)
    return proceed()
    

def find_reachable_nodes_with_paths(node_id, check_node_list, links, max_depth=7):
    """
    寻找从给定节点出发可以到达的其他节点及其路径。
    """
    # 构建邻接表，考虑方向性
    graph = defaultdict(list)
    for link in links:
        # 单向边：只添加从 s_node 到 e_node 的路径
        if link[3] == 3:
            graph[link[2]].append((link[1], link[0]))
        elif link[3] == 2:
            graph[link[1]].append((link[2], link[0]))
        # 双向边：添加从 s_node 到 e_node 和从 e_node 到 s_node 的路径
        else:
            graph[link[1]].append((link[2], link[0]))
            graph[link[2]].append((link[1], link[0]))
    # 将 check_node_list 转为集合，便于快速查找
    target_nodes = set(check_node_list)
    # 使用 BFS 查找从 node_id 可到达的节点和路径，限制深度
    reachable_paths = {}
    queue = deque([(node_id, [], 0)])  # 队列中的每一项是 (当前节点, 到当前节点的路径, 当前深度)
    visited = set([node_id])

    while queue:
        current_node, path, depth = queue.popleft()
        # 如果深度超过限制，则停止搜索
        if depth > max_depth:
            continue
        
        # 检查是否是目标节点
        if current_node in target_nodes:
            reachable_paths[current_node] = path  # 保存到达该节点的路径
        
        # 遍历相邻节点
        for neighbor, link_id in graph[current_node]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append((neighbor, path + [link_id], depth + 1))  # 更新路径并增加深度
    return reachable_paths if reachable_paths else None


def find_reachable_nodes_single(node_id, check_node_lists, links):
    """
    寻找从给定节点出发可以到达的其他节点。
    """
    # 构建邻接表
    graph = defaultdict(list)
    for link in links:
        if link[3] == 3:
            s_node = link[2]
            e_node = link[1]
        else:
            s_node = link[1]
            e_node = link[2]
        graph[s_node].append(e_node)
    # 将 check_node_lists 转为集合，便于快速查找
    target_nodes = set(check_node_lists)
    # 使用 BFS 查找从 node_id 可到达的节点
    reachable_nodes = set()
    queue = deque([node_id])
    visited = set([node_id])
    while queue:
        node = queue.popleft()
        # 检查是否是目标节点
        if node in target_nodes:
            reachable_nodes.add(node)
            # 找到一个就停止
            break
        # 遍历相邻节点
        for neighbor in graph[node]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append(neighbor)
    return list(reachable_nodes) if reachable_nodes else None



def get_root_info(ctx, park_id):
    """
    获取主点信息
    """
    root_info = ctx.dao.poi_online_query.get_park_from_4category(park_id)
    if not root_info:
        root_info = ctx.dao.poi_online_query.get_park_from_4category_near(park_id)
        if not root_info:
            return None
    return root_info[0]
    

def check_by_distance(ctx: context.Context, proceed):
    """
    根据距离判断出入口准确性, 距离停车场超出150认为异常
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) \
        if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt)
    if not park_area_sp:
        return proceed()
    
    is_scenic = utils.is_scenic_park(ctx.parking_info.bid, False)

    park_access_distance_park_min = 150
    if is_scenic:
        park_access_distance_park_min = 75
    
    for sub_park_access in ctx.sub_pois:
        sub_park_access_sp = wkt.loads(sub_park_access.point_gcj)
        # 出入口距离停车场面距离
        park_access_distance_park = sub_park_access_sp.distance(park_area_sp) * COORDINATE_FACTOR
        if park_access_distance_park < park_access_distance_park_min:
            logger.info(f"{sub_park_access.bid}距离停车场面:{park_access_distance_park}")
            continue
        park_access_distance_aoi = 0
        if ctx.aoi_info and ctx.aoi_info.bid \
                and ('旅游景点' not in ctx.aoi_info.std_tag or '地下停车场' in ctx.parking_info.show_tag):
            aoi_sp = wkt.loads(ctx.aoi_info.wkt)
            park_access_distance_aoi = sub_park_access_sp.distance(aoi_sp) * COORDINATE_FACTOR
            if sub_park_access_sp.distance(aoi_sp) < 50 / COORDINATE_FACTOR:
                logger.info(f"aoi面过滤距离：{sub_park_access_sp.distance(aoi_sp)}")
                continue
        elif ctx.aoi_info.bid == '' and park_access_distance_park < 500:
            logger.info(f"ctx.aoi_info.bid:{ctx.aoi_info.bid}")
            continue
        logger.info(f"{sub_park_access.bid}距离停车场过远:{park_access_distance_park}")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_DISTANCE
        park_access.strategy_value = f"park_access_distance_park:{park_access_distance_park}," \
                                     f"park_access_distance_aoi:{park_access_distance_aoi}"
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_node_distance_by_park(ctx: context.Context, proceed):
    """
    同一个停车场，包含多个出入口，且node距离过近20米以内 认为异常
    """
    logger.info('start-check_node_distance_by_park')
    points_with_bids = []
    bid_info = {}
    # 提取bid和点坐标  
    for sub_park_access in ctx.sub_pois:
        if sub_park_access.type  == 'LINK':
            points_with_bids.append([sub_park_access.bid, sub_park_access.point_gcj])
            bid_info[sub_park_access.bid] = sub_park_access
        elif sub_park_access.type == 'NODE':
            points_with_bids.append([sub_park_access.bid, sub_park_access.point_gcj])
            bid_info[sub_park_access.bid] = sub_park_access
        else:
            continue

    if len(points_with_bids) <= 1:
        logger.info("仅一个出入口，过滤")
        return proceed()
    # 存储距离近的bid对  
    close_bids = []
    # 计算所有点之间的距离  
    for i in range(len(points_with_bids)):  
        for j in range(i + 1, len(points_with_bids)):  
            bid1, point1 = points_with_bids[i]
            bid2, point2 = points_with_bids[j]
            logger.info(f"bid1:{bid1},point1:{point1},bid2:{bid2},point2:{point2}")
            point1_sp = wkt.loads(point1)
            point2_sp = wkt.loads(point2)
            distance = point1_sp.distance(point2_sp) * 110000
            logger.info(f"distance:{distance}")
            if distance < 55:  
                close_bids.append((bid1, point1, bid2, point2, distance))  

    if len(close_bids) > 0:
        access_set = set()
        for bid1, point1, bid2, point2, distance in close_bids:
            logger.error(f"出入口距离过近：{bid1},{point1},{bid2},{point2},{distance}")
            if bid1 not in access_set:
                temp_park_access = bid_info[bid1]
                park_access = context.ParkingAccess()
                park_access.bid = temp_park_access.bid
                park_access.node_geom = temp_park_access.point_gcj
                park_access.node_id = temp_park_access.relation_node_id
                park_access.link_id = temp_park_access.relation_link_id
                park_access.type = "park_access_rongyu"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK
                park_access.strategy_value = f"{bid1},{point1},{bid2},{point2},{distance}"
                ctx.parking_access_list.append(park_access)
            if bid2 not in access_set:
                temp_park_access = bid_info[bid2]
                park_access = context.ParkingAccess()
                park_access.bid = temp_park_access.bid
                park_access.node_geom = temp_park_access.point_gcj
                park_access.node_id = temp_park_access.relation_node_id
                park_access.link_id = temp_park_access.relation_link_id
                park_access.type = "park_access_rongyu"
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_NODE_DISTANCE_NEAR_IN_PARK
                park_access.strategy_value = f"{bid1},{point1},{bid2},{point2},{distance}"
                ctx.parking_access_list.append(park_access)
    return proceed()


def check_node_relation_by_other_park(ctx: context.Context, proceed):
    """
    判断 同一个node 关联多个停车场的  
    Args:
        ctx:
        proceed:
    Returns:

    """
    park_bid = ctx.parking_info.bid
    park_parent_id = ctx.parking_info.parent_id
    park_name = ctx.parking_info.name
    park_address = ctx.parking_info.address
    if not ctx.parking_info.park_area_gcj_wkt:
        return proceed()
    park_area_gcj_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt)
    if not park_area_gcj_sp:
        return proceed()
    for sub_park_access in ctx.sub_pois:
        if sub_park_access.type != 'NODE':
            return proceed()
        short_node_id = sub_park_access.relation_short_node_id
        node_sp = wkt.loads(sub_park_access.point_gcj)
        node_park_dis = node_sp.distance(park_area_gcj_sp)
        if park_area_gcj_sp.contains(node_sp):
            logger.info(f"access_bid:{sub_park_access.bid}\tnode在当前停车场范围内")
            continue
        if node_park_dis * COORDINATE_FACTOR < 3:
            logger.info(f"short_node_id:{short_node_id},node_park_dis:{node_park_dis}\tnode距离停车场3米内忽略")
            continue
        node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(100 / COORDINATE_FACTOR).wkt
        check_res, error_park_bid_str = utils.check_node_in_other_park(
            node_sp, node_buffer_wkt, short_node_id, ctx.parking_info, node_park_dis)
        if not check_res:
            logger.info(f"access_bid:{sub_park_access.bid}\tnode不在其他停车场中存在")
            return proceed()
        logger.error(f"short_node_id:{short_node_id}\tpark_bid:{park_bid}\terror_park_bid_str:\
            {error_park_bid_str}\tnode在其他停车场中存在")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_NODE_RELATION_BY_OTHER_PARK
        park_access.strategy_value = error_park_bid_str
        ctx.parking_access_list.append(park_access)
        break
    return proceed()


def check_relation_link_exist_close_gate(ctx: context.Context, proceed):
    """
    判断关联link buffer 3m 是否存在封闭大门
    Args:
        ctx:
        proceed:

    Returns:

    """
    for sub_park_access in ctx.sub_pois:
        if sub_park_access.type != 'LINK':
            continue
        link_id = sub_park_access.relation_link_id
        link_list = ctx.dao.road_dao.get_nav_link_by_link_ids([link_id])
        if not link_list:
            logger.info(f"{link_id}\t信息不存在")
            continue
        link_sp = wkt.loads(link_list[0][0])
        nav_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(link_sp.buffer(3 / COORDINATE_FACTOR).wkt)
        if not nav_gate_list:
            logger.info(f"{link_id}\t附近没有关联大门")
            continue
        node_ids = [item[0] for item in nav_gate_list]
        close_gate_list = [node_id for node_id in node_ids if utils.is_close_gate(node_id)]
        if len(close_gate_list) == 0:
            continue
        logger.error(f"link:{link_id}\tnode:{node_ids}\tlink存在封禁node")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_NEAR_CLOSE_GATE
        park_access.strategy_value = close_gate_list[0]
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_overground_by_dead_road(ctx: context.Context, proceed):
    """
    地上停车场 结合断头路信息，断头路下发情报
    Args:
        ctx:
        proceed:
    Returns:
    """
    park_show_tag = ctx.parking_info.show_tag
    if park_show_tag in ['地下停车场', '立体停车场']:
        return proceed()
    
    if len(ctx.parking_info.parent_id) < 5:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过无父点停车场")
        return proceed()

    has_both_type_park, has_overground_park, has_underground_park = utils.is_overground_and_underground_in_same_aoi(
        ctx.parking_info.parent_id)
    if not has_both_type_park:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过非地上地下都有")
        return proceed()
    
    if utils.is_scenic_park(ctx.parking_info.bid):
        logger.info(f"{ctx.parking_info.bid}\t跳过景区停车场")
        return proceed()

    for sub_park_access in ctx.sub_pois:
        strategy_value = ''
        access_geom_wkt = ""
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        if sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            res, _, _ = utils.is_node_on_dead_road(node_id, DEAD_LINK_DISTANCE_LIMIT)
            if not res:
                logger.info(f"node_id:{node_id}\tnode非断头路")
                continue
            strategy_value = node_id
            access_geom_wkt = wkt.loads(sub_park_access.point_gcj).buffer(10 / COORDINATE_FACTOR).wkt
        elif sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            res, _, _  = utils.is_link_on_dead_road(link_id, DEAD_LINK_DISTANCE_LIMIT)
            if not res:
                logger.info(f"link:{link_id}\tlink非断头路")
                continue
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                logger.info(f"link:{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR).wkt
            strategy_value = link_id
        else:
            continue

        # 地上场景扩招 
        # aoi_res = ctx.dao.master_back_dao.get_aoi_info_by_bid(ctx.parking_info.parent_id)
        # if not aoi_res:
        #     logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t缺少aoi信息")
        #     continue
        # aoi_geom_wkt = aoi_res[1]
        
        # # 非断头路 使用断头点聚合过滤  node buffer 10米  link buffer 5米   
        # if not dead_traj_and_dead_link(ctx, access_geom_wkt, aoi_geom_wkt, 45, 5):
        #     # 不是断头路
        #     logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t非断头轨迹聚合点")
        #     continue

        # poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
        # poi_nav_res, poi_nav_msg = check_poi_nav(ctx.parking_info, poi_navi_list, access_geom_wkt)
        # if poi_nav_res == 1:
        #     # jp一致
        #     logger.info(f"{poi_nav_res}\t{poi_nav_msg}\t jp_point_nav过滤")
        #     continue
        
        # competitor_diff_res, competitor_diff_msg = competitor_diff_by_park_bid(
        #   ctx, ctx.parking_info.bid, access_geom_wkt)
        # if competitor_diff_res == 1:
        #     # jp一致
        #     logger.info(f"{competitor_diff_res}\t{competitor_diff_msg}\t jp_competitor_diff过滤")
        #     continue

        logger.error(f"park_bid:{ctx.parking_info.bid}\tnode_id:{sub_park_access.relation_node_id}\t\
            link_id:{sub_park_access.relation_link_id}\t是断头轨迹聚合点")
        # 是断头路 竞品也不存在
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.node_id = sub_park_access.relation_node_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_OVERGROUND_DEAD_ROAD
        park_access.strategy_value = strategy_value
        ctx.parking_access_list.append(park_access)
    return proceed()


def competitor_diff_by_park_bid(ctx, park_bid, access_geom_wkt):
    """
    0, "小区竞品数据不存在"
    1, "竞品与出入口距离小于20米"
    2, "小区竞品未匹配到停车场"
    3, "小区竞品未匹配到出入口"
    4, "停车场存在且距离大于20"
    """
    park_access_navi_competitor_list = get_build_compete_lists(ctx)
    return competitor_diff_by_park_bid_with_access(park_access_navi_competitor_list, access_geom_wkt)


def competitor_diff_by_park_bid_with_access(park_access_navi_competitor_list, access_geom_wkt, distance=20):
    """
    出入口diff
    """
    if not park_access_navi_competitor_list:
        return 0, "小区竞品数据不存在"
    # print(f"competitor_diff_by_park_bid_with_access_node_geom\t{access_geom_wkt}")
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        # print(poi_navi_sp.wkt)
        if poi_navi_sp.distance(wkt.loads(access_geom_wkt)) < distance / COORDINATE_FACTOR:
            return 1, f"竞品与出入口距离小于{distance}m"
    return 4, f"停车场存在且距离大于{distance}m"


def get_competitor_diff_by_park_bid_with_access_min_dis(park_access_navi_competitor_list, access_geom_sp):
    """
    竞品出入口距离
    """
    min_dis = 100000
    if not park_access_navi_competitor_list:
        return min_dis
    for item in park_access_navi_competitor_list:
        poi_navi_sp = wkt.loads(item["geom"])
        now_dis = poi_navi_sp.distance(access_geom_sp) * COORDINATE_FACTOR
        min_dis = min(min_dis, now_dis)
    return min_dis


def get_build_compete_lists(ctx):
    """
    竞品出入口匹配
    """
    park_access_navi_competitor_list = ctx.dao.poi_online_query.get_competitor_access_nav_by_park_bids(
        [ctx.parking_info.bid])
    if not park_access_navi_competitor_list:
        logger.info(f"小区竞品未匹配到出入口:{ctx.parking_info.bid}")
        return False
    return park_access_navi_competitor_list


def get_build_compete_lists_old(ctx):
    """
    小区竞品数据
    """
    gids = match_bid_gids(ctx, ctx.parking_info.bid)
    if not gids:
        logger.info(f"小区竞品未匹配到gids:{ctx.parking_info.bid}")
        return False
    # 获取gids下所有出入口
    park_competitor_list = ctx.dao.poi_online_query.get_competitor_park(gid=gids[0],
                                                                        space_attr=ctx.parking_info.show_tag,
                                                                        poi_tag='交通设施;停车场')
    if not park_competitor_list:
        logger.info(f"小区竞品未匹配到停车场:{ctx.parking_info.bid},{gids[0]}")
        return False
    # 查找竞品出入口
    park_access_navi_competitor_list = ctx.dao.poi_online_query.get_competitor_park(
        relation_bid=park_competitor_list[0]['relation_bid'],
        poi_tag='出入口;停车场出入口')
    if not park_access_navi_competitor_list:
        logger.info(f"小区竞品未匹配到出入口:{ctx.parking_info.bid},{park_competitor_list[0]['relation_bid']}")
        return False
    return park_access_navi_competitor_list


def match_bid_gids(ctx, park_bid):
    """
    1、根据gid、bid映射表找匹配
    2、如1失败，根据名称相似度、空间属性、距离匹配
    """
    gid_res = ctx.dao.poi_online_query.get_gid_from_bid(park_bid)
    if gid_res is not None:
        # competitor_infos = get_competitor_info_by_gids([gid_res[0]])
        competitor_infos = ctx.dao.poi_online_query.get_competitor_info_by_gids([gid_res[0]])
        if len(competitor_infos) > 0:
            return [gid_res[0]]

    # park_info = get_park_info(park_bid)
    park_info = ctx.dao.poi_online_query.get_park_info(park_bid)
    if park_info is None:
        return []
    bd_name = park_info[0]
    bd_show_tag = park_info[1]
    bd_gcj_wkt = park_info[2]
    bd_std_tag = park_info[5]

    # nearby_competitor_infos = get_nearby_competitor_infos(bd_gcj_wkt)
    nearby_competitor_infos = ctx.dao.poi_online_query.get_nearby_competitor_infos(bd_gcj_wkt)
    gid_infos = []
    for info in nearby_competitor_infos:
        gid, gd_name, gd_gcj_wkt, gd_space_attr, gd_std_tag = info

        # std_tag属性不一样，匹配失败
        if bd_std_tag != gd_std_tag:
            continue

        # 空间属性不一样，匹配失败
        if gd_space_attr != bd_show_tag:
            continue

        # 方位不一样，匹配失败
        bd_dir = extract_directions(bd_name)
        gd_dir = extract_directions(gd_name)
        if bd_dir != "" and gd_dir != "" and bd_dir != gd_dir:
            continue

        # 数字不一样，匹配失败
        bd_nums = extract_and_convert_numbers(bd_name)
        gd_nums = extract_and_convert_numbers(gd_name)
        if len(set(bd_nums) - set(gd_nums)) > 0:
            continue

        # 名称相似高，位置相近，匹配成功
        replace_keys = ["地面", "地上", "地下", "停车场", "购物中心"]
        bd_clean_name = replace_multiple_keys(bd_name, replace_keys)
        gd_clean_name = replace_multiple_keys(gd_name, replace_keys)
        name_similarity = fuzz.ratio(bd_clean_name, gd_clean_name) / 100
        dist = wkt.loads(bd_gcj_wkt).distance(wkt.loads(gd_gcj_wkt))
        if name_similarity >= 0.4 and dist < 150 / 110000:
            gid_infos.append({
                "gid": gid,
                "name_similarity": name_similarity,
                "dist": -1,
            })
            # print(bd_name, gd_name, name_similarity)
            continue

    sorted_gid_infos = sorted(gid_infos, key=lambda x: x["name_similarity"], reverse=True)
    gids = [info["gid"] for info in sorted_gid_infos]
    return gids


def extract_and_convert_numbers(text):
    """
    extract_and_convert_numbers
    Args:
        text:

    Returns:

    """
    chinese_numerals = {
        '零': 0,
        '一': 1,
        '二': 2,
        '三': 3,
        '四': 4,
        '五': 5,
        '六': 6,
        '七': 7,
        '八': 8,
        '九': 9,
        '十': 10,
    }
    # 提取阿拉伯数字
    arabic_numbers = re.findall(r'\d+', text)

    # 提取中文数字
    chinese_numbers = re.findall(r'[零一二三四五六七八九十]+', text)

    # 转换中文数字为阿拉伯数字
    for cn in chinese_numbers:
        num = 0
        for char in cn:
            if char in chinese_numerals:
                num = num * 10 + chinese_numerals[char]
        arabic_numbers.append(num)

    return arabic_numbers
    

def extract_directions(text):
    """
    提取东西南北四个字
    Args:
        text:

    Returns:

    """
    # 使用正则表达式提取东西南北四个字
    directions = re.findall(r'[东西南北]', text)
    return directions


def replace_multiple_keys(name, keys):
    """
    去除名称中的关键字
    Args:
        name:
        keys:

    Returns:

    """
    for key in keys:
        name = name.replace(key, "")
    return name


def check_poi_nav(parking_info, poi_navi_list, access_geom_wkt, buffer_meter=20):
    """
    竞品引导点
    0 无 jp
    1 有且一致
    2 有且不一致
    """
    if not poi_navi_list:
        return 0, "没有jp停车场导航信息"
    # print(f"check_poi_nav\t{access_geom_wkt}")
    for poi_nav in poi_navi_list:
        # 提取坐标的正则表达式
        point_str = poi_nav[2]
        if not point_str:
            continue
        pattern = re.compile(r"(\d+\.\d+) (\d+\.\d+)")
        # 使用 findall 提取所有坐标
        coordinates_1 = pattern.findall(point_str)
        # 转换为浮点数并输出
        for lon, lat in coordinates_1:
            # print("经度：", float(lon))
            # print("纬度：", float(lat))
            poi_nav_point_sp = wkt.loads(f"POINT({float(lon)} {float(lat)})")
            # print(poi_nav_point_sp.wkt)
            poi_nav_point_buffer_sp = poi_nav_point_sp.buffer(buffer_meter / COORDINATE_FACTOR)
            access_geom_wkt_sp = wkt.loads(access_geom_wkt)
            # logger.info(f"jp: {poi_nav_point_buffer_sp}\taccess_buffer: {access_geom_wkt}")
            if poi_nav_point_buffer_sp.intersection(access_geom_wkt_sp):
                return 1, f"jp停车场导航位置一致"
    return 2, "jp停车场导航位置不一致"


def get_park_nav_mis_dis(poi_navi_list, access_geom_sp):
    """
    竞品引导点
    最小距离
    """
    min_dis = 100000
    if not poi_navi_list:
        min_dis
    # print(f"check_poi_nav\t{access_geom_sp}")
    for poi_nav in poi_navi_list:
        # 提取坐标的正则表达式
        point_str = poi_nav[2]
        if not point_str:
            continue
        pattern = re.compile(r"(\d+\.\d+) (\d+\.\d+)")
        # 使用 findall 提取所有坐标
        coordinates_1 = pattern.findall(point_str)
        # 转换为浮点数并输出
        for lon, lat in coordinates_1:
            # print("经度：", float(lon))
            # print("纬度：", float(lat))
            poi_nav_point_sp = wkt.loads(f"POINT({float(lon)} {float(lat)})")
            # print(poi_nav_point_sp.wkt)
            now_dis = poi_nav_point_sp.distance(access_geom_sp) * COORDINATE_FACTOR
            min_dis = min(min_dis, now_dis)
    return min_dis


def check_build_complete(parking_info, poi_navi_list, access_geom_wkt):
    """
    竞品引导点
    0 无 jp
    1 有且一致
    2 有且不一致
    """
    if not poi_navi_list:
        return 0, "没有jp停车场导航信息"
    for poi_nav in poi_navi_list:
        # 提取坐标的正则表达式
        point_str = poi_nav[2]
        if not point_str:
            continue
        pattern = re.compile(r"(\d+\.\d+) (\d+\.\d+)")
        # 使用 findall 提取所有坐标
        coordinates_1 = pattern.findall(point_str)
        # 转换为浮点数并输出
        for lon, lat in coordinates_1:
            # print("经度：", float(lon))
            # print("纬度：", float(lat))
            poi_nav_point_sp = wkt.loads(f"POINT({float(lon)} {float(lat)})")
            poi_nav_point_buffer_sp = poi_nav_point_sp.buffer(20 / COORDINATE_FACTOR)
            access_geom_wkt_sp = wkt.loads(access_geom_wkt)
            # logger.info(f"jp: {poi_nav_point_buffer_sp}\taccess_buffer: {access_geom_wkt}")
            if poi_nav_point_buffer_sp.intersection(access_geom_wkt_sp):
                return 1, f"{parking_info.bid}\t{parking_info.parent_id}\tjp停车场导航位置一致"
    return 2, "jp停车场导航位置不一致"


def check_underground_by_dead_road(ctx: context.Context, proceed):
    """
    地下停车场断头路判断
    """
    park_show_tag = ctx.parking_info.show_tag
    logger.info(f"{park_show_tag}")
    if park_show_tag not in ['地下停车场', '立体停车场']:
        return proceed()
    if len(ctx.parking_info.parent_id) < 5:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过无父点停车场")
        return proceed()
    has_both_type_park, has_overground_park, has_underground_park = utils.is_overground_and_underground_in_same_aoi(
        ctx.parking_info.parent_id)
    if not has_both_type_park:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过非地上地下都有")
        return proceed()
    
    if utils.is_scenic_park(ctx.parking_info.bid):
        logger.info(f"{ctx.parking_info.bid}\t跳过景区停车场")
        return proceed()

    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        strategy_value = ''
        access_geom_wkt = ""
        if sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(3 / COORDINATE_FACTOR).wkt
            res, _, _ = utils.is_dead_link_road(node_buffer_wkt, 100)
            if not res:
                logger.info(f"{node_id}\tnode不是断头路")
                continue
            strategy_value = node_id
            access_geom_wkt = wkt.loads(sub_park_access.point_gcj).buffer(10 / COORDINATE_FACTOR).wkt
        elif sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            res, _, _ = utils.is_dead_link_road_by_link(link_id, 100)
            if not res:
                logger.info(f"{link_id}\tlink不是断头路")
                continue
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                logger.info(f"{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR).wkt
            strategy_value = link_id
        else:
            continue
        # if sub_park_access.relation_node_id != '3473894181673070626':
        #     logger.info(f"{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t不是3473894181673070626")
        #     continue
        logger.warning(sub_park_access.relation_node_id)
        aoi_res = ctx.dao.master_back_dao.get_aoi_info_by_bid(ctx.parking_info.parent_id)
        if not aoi_res:
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t缺少aoi信息")
            continue
        aoi_geom_wkt = aoi_res[1]
        # 非断头路 使用断头点聚合过滤  node buffer 10米  link buffer 5米
        if dead_traj_and_dead_link(ctx, access_geom_wkt, aoi_geom_wkt, 50, 4):
            # 是断头路
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t是断头轨迹聚合点")
            continue
        poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
        poi_nav_res, poi_nav_msg = check_poi_nav(ctx.parking_info, poi_navi_list, access_geom_wkt)
        if poi_nav_res == 1:
            # jp一致
            logger.info(f"{poi_nav_res}\t{poi_nav_msg}\t jp_point_nav过滤")
            continue
        competitor_diff_res, competitor_diff_msg = competitor_diff_by_park_bid(
            ctx, ctx.parking_info.bid, access_geom_wkt)
        if competitor_diff_res == 1:
            # jp一致
            logger.info(f"{competitor_diff_res}\t{competitor_diff_msg}\t jp_competitor_diff过滤")
            continue

        # 终点轨迹过滤   
        traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
        # traj_list_stop_all = utils.get_park_stop_traj(ctx.parking_info, ctx.aoi_info, ctx)
        traj_list_all = traj_list_end_all
        traj_end_num = len(traj_list_end_all)
        traj_all_num = len(traj_list_all)
        logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\taoi终点轨迹数量:{traj_end_num}\t总轨迹数量:{traj_all_num}")
        if not traj_list_all:
            logger.warning(f"{ctx.parking_info.bid}\t没有找到终点轨迹")
            return proceed()
        traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_all, ctx, True)
        if not traj_list:
            logger.warning(f"{ctx.parking_info.bid}\t没有找到node终点轨迹")
            return proceed()
        traj_num = len(traj_list)
        logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\t终点node_filter轨迹数量:{traj_num}")
        pass_num = get_dest_traj_pass_node_num(wkt.loads(access_geom_wkt), traj_list, True)
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t过滤终点node_filter轨迹数量:{pass_num}")
        if pass_num > 30:
            continue
        percent = pass_num / traj_num
        if percent > 0.35:
            logger.info(f"passnum_filter\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
                {sub_park_access.relation_link_id}\t{pass_num}\t{traj_num}\t{percent}")
            continue
        logger.error(f"park_bid:{ctx.parking_info.bid}\tnode_id:{sub_park_access.relation_node_id}\t\
            link_id:{sub_park_access.relation_link_id}\t非断头路也不是断头轨迹聚合点")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.node_id = sub_park_access.relation_node_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD
        park_access.strategy_value = strategy_value
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_underground_by_not_dead_road(ctx: context.Context, proceed):
    """
    地下停车场 结合断头路信息，非断头路下发情报
    """
    park_show_tag = ctx.parking_info.show_tag
    if park_show_tag not in ['地下停车场', '立体停车场']:
        return proceed()
        
    if len(ctx.parking_info.parent_id) < 5:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过无父点停车场")
        return proceed()

    if utils.is_scenic_park(ctx.parking_info.bid):
        logger.info(f"{ctx.parking_info.bid}\t跳过景区停车场")
        return proceed()

    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        strategy_value = ''
        access_geom_wkt = ""
        if sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(3 / COORDINATE_FACTOR).wkt
            res, _, _ = utils.is_dead_link_road(node_buffer_wkt, 100)
            if res:
                logger.info(f"{node_id}\tnode是断头路")
                continue
            strategy_value = node_id
            access_geom_wkt = wkt.loads(sub_park_access.point_gcj).buffer(10 / COORDINATE_FACTOR).wkt
        elif sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            res, _, _ = utils.is_dead_link_road_by_link(link_id, 100)
            if res:
                logger.info(f"{link_id}\tlink是断头路")
                continue
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                logger.info(f"{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR).wkt
            strategy_value = link_id
        else:
            continue
        aoi_res = ctx.dao.master_back_dao.get_aoi_info_by_bid(ctx.parking_info.parent_id)
        if not aoi_res:
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t缺少aoi信息")
            continue
        aoi_geom_wkt = aoi_res[1]

        # 非断头路 使用断头点聚合过滤  node buffer 10米  link buffer 5米
        if dead_traj_and_dead_link(ctx, access_geom_wkt, aoi_geom_wkt, 20, 8):
            # 是断头路 过滤
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t是断头轨迹聚合点")
            continue

        poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
        poi_nav_res, poi_nav_msg = check_poi_nav(ctx.parking_info, poi_navi_list, access_geom_wkt)
        if poi_nav_res == 1:
            # jp一致
            logger.info(f"{poi_nav_res}\t{poi_nav_msg}\t jp_point_nav过滤\taccess_bid:{sub_park_access.bid}")
            continue
        
        competitor_diff_res, competitor_diff_msg = competitor_diff_by_park_bid(
            ctx, ctx.parking_info.bid, access_geom_wkt)
        if competitor_diff_res == 1:
            # jp一致
            logger.info(f"{competitor_diff_res}\t{competitor_diff_msg}\t jp_competitor_diff过滤")
            continue

        logger.error(f"park_bid:{ctx.parking_info.bid}\tpark_pid:{ctx.parking_info.parent_id}\t不是断头轨迹聚合点")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.node_id = sub_park_access.relation_node_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_DEAD_ROAD
        park_access.strategy_value = strategy_value
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_underground_by_not_dead_road_min_area(ctx: context.Context, proceed):
    """
    地下停车场断头路判断小面场景, 排除极大极小场景
    ctx.aoi_info.area < 5000 平方米   ctx.aoi_info.area > 100 
    """
    park_show_tag = ctx.parking_info.show_tag
    logger.info(f"{park_show_tag}")
    if park_show_tag not in ['地下停车场']:
        return proceed()
    if not ctx.aoi_info.area or ctx.aoi_info.area > 5000 or ctx.aoi_info.area < 100:
        return proceed()
    
    if len(ctx.parking_info.parent_id) < 5:
        logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过无父点停车场")
        return proceed()
    # if utils.is_scenic_park(ctx.parking_info.bid):
    #     logger.info(f"{ctx.parking_info.bid}\t跳过景区停车场")
    #     return proceed()

    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        strategy_value = ''
        access_geom_wkt = ""
        if sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(3 / COORDINATE_FACTOR).wkt
            res, _, _ = utils.is_node_on_dead_road(node_id, node_distance_limit=50, aoi_area=5000, form=None)
            if res:
                logger.info(f"{node_id}\tnode是断头路")
                continue
            strategy_value = node_id
            access_geom_wkt = wkt.loads(sub_park_access.point_gcj).buffer(10 / COORDINATE_FACTOR).wkt
        elif sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            res, _, _ = utils.is_link_on_dead_road(link_id, link_distance_limit=50, aoi_area=5000, form=None)
            if res:
                logger.info(f"{link_id}\tlink是断头路")
                continue
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if link_info:
                logger.info(f"{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(5 / COORDINATE_FACTOR).wkt
            strategy_value = link_id
        else:
            continue
        logger.warning(sub_park_access.relation_node_id)
        aoi_res = ctx.dao.master_back_dao.get_aoi_info_by_bid(ctx.parking_info.parent_id)
        if not aoi_res:
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t缺少aoi信息")
            continue
        aoi_geom_wkt = aoi_res[1]
        # 非断头路 使用断头点聚合过滤  node buffer 10米  link buffer 5米
        if dead_traj_and_dead_link(ctx, access_geom_wkt, aoi_geom_wkt, 50, 4):
            # 是断头路
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t是断头轨迹聚合点")
            continue
        poi_navi_list = ctx.dao.poi_online_query.get_competitor_park_nav_list_by_bids([ctx.parking_info.bid])
        poi_nav_res, poi_nav_msg = check_poi_nav(ctx.parking_info, poi_navi_list, access_geom_wkt)
        if poi_nav_res == 1:
            # jp一致
            logger.info(f"{poi_nav_res}\t{poi_nav_msg}\t jp_point_nav过滤")
            continue
        competitor_diff_res, competitor_diff_msg = competitor_diff_by_park_bid(
            ctx, ctx.parking_info.bid, access_geom_wkt)
        if competitor_diff_res == 1:
            # jp一致
            logger.info(f"{competitor_diff_res}\t{competitor_diff_msg}\t jp_competitor_diff过滤")
            continue

        # 终点轨迹过滤   
        traj_list_end_all = utils.get_park_end_traj(ctx.parking_info, ctx.aoi_info, ctx)
        # traj_list_stop_all = utils.get_park_stop_traj(ctx.parking_info, ctx.aoi_info, ctx)
        traj_list_all = traj_list_end_all
        traj_end_num = len(traj_list_end_all)
        traj_all_num = len(traj_list_all)
        logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\taoi终点轨迹数量:{traj_end_num}\t总轨迹数量:{traj_all_num}")
        if not traj_list_all:
            logger.warning(f"{ctx.parking_info.bid}\t没有找到终点轨迹")
            return proceed()
        traj_list = utils.filter_traj_by_node(ctx.sub_pois, traj_list_all, ctx, True)
        if not traj_list:
            logger.warning(f"{ctx.parking_info.bid}\t没有找到node终点轨迹")
            return proceed()
        traj_num = len(traj_list)
        logger.info(f"check_low_traj_access\t{ctx.parking_info.bid}\t终点node_filter轨迹数量:{traj_num}")
        pass_num = get_dest_traj_pass_node_num(wkt.loads(access_geom_wkt), traj_list, True)
        if pass_num > 40:
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t过滤终点node_filter轨迹数量:{pass_num}")
            continue
        logger.error(f"park_bid:{ctx.parking_info.bid}\tnode_id:{sub_park_access.relation_node_id}\t\
            link_id:{sub_park_access.relation_link_id}\t非断头路也不是断头轨迹聚合点")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.node_id = sub_park_access.relation_node_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_UNDERGROUND_DEAD_ROAD_MIN_AREA
        park_access.strategy_value = strategy_value
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_underground_not_on_dead_road_but_near(ctx: context.Context, proceed):
    """
    地下不在断头路上，但是附近有断头路
    """
    park_show_tag = ctx.parking_info.show_tag
    if park_show_tag not in ['地下停车场', '立体停车场']:
        return proceed()
    for sub_park_access in ctx.sub_pois:
        logger.info(f"start_access\t{sub_park_access.bid}\t{sub_park_access.relation_node_id}\t\
            {sub_park_access.relation_link_id}")
        strategy_value = ''
        access_geom_wkt = ""
        if sub_park_access.type == 'NODE':
            # 关联node
            node_id = sub_park_access.relation_node_id
            # 判断自己是不是
            node_buffer_wkt = wkt.loads(sub_park_access.point_gcj).buffer(3 / COORDINATE_FACTOR).wkt
            # res, _, _ = utils.is_node_on_dead_road(node_buffer_wkt, 70)
            res, _, _ = utils.is_dead_link_road(node_buffer_wkt, 70)
            if res:
                logger.info(f"{node_id}\tnode是断头路")
                continue
            strategy_value = node_id
            access_geom_wkt = wkt.loads(sub_park_access.point_gcj).buffer(10 / COORDINATE_FACTOR).wkt
            access_geom_wkt_15 = wkt.loads(sub_park_access.point_gcj).buffer(15 / COORDINATE_FACTOR).wkt
        elif sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            # res, _, _ = utils.is_link_on_dead_road(link_id, 70)
            res, _, _ = utils.is_dead_link_road_by_link(link_id, 70)
            if res:
                logger.info(f"{link_id}\tlink是断头路")
                continue
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([sub_park_access.relation_link_id])
            if not link_info:
                logger.info(f"{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(10 / COORDINATE_FACTOR).wkt
            access_geom_wkt_15 = wkt.loads(link_info[0][0]).buffer(15 / COORDINATE_FACTOR).wkt
            strategy_value = link_id
        
        # 判断范围大一些有没有断头路
        logger.info(access_geom_wkt)
        res, _, _ = utils.is_dead_link_road(access_geom_wkt, 50)
        if not res:
            logger.info(f"node周围没有断头路")
            continue
        logger.error(f"park_bid:{ctx.parking_info.bid}\tpark_pid:{ctx.parking_info.parent_id}\t非断头路附近有断头路")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.node_id = sub_park_access.relation_node_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_UNDERGROUND_NOT_ON_DEAD_ROAD_BUT_NEAR
        park_access.strategy_value = strategy_value
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_node_in_park_bid(node_wkt, aoi_geom_wkt, aoi_buffer_meter=5):
    """
    判断node是否在指定aoi中
    :param node_wkt:
    :param park_parent_id:
    :return:
    """
    if not aoi_geom_wkt:
        return False

    if not node_wkt:
        return False

    aoi_geom_sp = wkt.loads(aoi_geom_wkt).buffer(aoi_buffer_meter / COORDINATE_FACTOR)
    node_sp = wkt.loads(node_wkt)
    if aoi_geom_sp.contains(node_sp):
        return True
    return False


def check_main_bid_from_node_and_park(ctx: context.Context, proceed):
    """
    其他aoi的node
    """
    park_bid = ctx.parking_info.bid
    park_parent_id = ctx.parking_info.parent_id
    park_name = ctx.parking_info.name
    park_address = ctx.parking_info.address
    park_name_pre = park_name.split('-')[0]
    for sub_park_access in ctx.sub_pois:
        access_bid = sub_park_access.bid
        if sub_park_access.type != 'NODE':
            continue
        # 关联node
        if len(park_parent_id) < 5:
            logger.info(f"{ctx.parking_info.bid}\t{ctx.parking_info.parent_id}\t跳过无父点停车场")
            continue
        node_id = sub_park_access.relation_node_id
        short_node_id = sub_park_access.relation_short_node_id
        # 获取node的主点bid
        aoi_main_bid_lists = ctx.dao.master_back_dao.get_main_bid_by_node(node_id)
        if not aoi_main_bid_lists:
            logger.info(f"{node_id}\tnode不在主aoi内")
            continue
        aoi_main_bid_set = set()
        strategy_value_str = ''
        qb_flag = False
        for aoi_main_bid_list in aoi_main_bid_lists:
            logger.info(f"{aoi_main_bid_list[0]}")
            aoi_main_bid_info = ctx.dao.poi_online_query.get_poi_info_by_bid(aoi_main_bid_list[0])
            if not aoi_main_bid_info:
                logger.info(f"{aoi_main_bid_list[0]}\taoi绑定的主点不存在")
                continue
            
            if park_bid == aoi_main_bid_list[0]:
                logger.info(f"park_id:{park_bid}\tnodeid:{node_id}\t过滤当前停车场bid")
                continue

            if aoi_main_bid_info[1] in ['交通设施;停车场', '交通设施;路侧停车位', '出入口;停车场出入口']:
                logger.info(f"park_id:{park_bid}\tnodeid:{node_id}\t过滤停车场tag")
                continue
            
            aoi_main_bid_name = aoi_main_bid_info[3]
            if aoi_main_bid_name.split('-')[0] in park_name:
                logger.info(f"park_name:{park_name}\taoi_main_bid_name:{aoi_main_bid_name}\t过滤停车场名称包含")
                qb_flag = False
                break
            
            if park_name_pre in aoi_main_bid_name:
                logger.info(f"park_name_pre:{park_name_pre}\taoi_main_bid_name:{aoi_main_bid_name}\t过滤停车场名称pre包含")
                qb_flag = False
                break

            if park_address != '' and park_address == aoi_main_bid_info[4]:
                logger.info(f"park_address:{park_address}\taoi_main_bid_address:{aoi_main_bid_info[4]}\t过滤地址一致")
                qb_flag = False
                break

            if aoi_main_bid_list[1] != '':
                aoi_geom_sp = wkt.loads(aoi_main_bid_list[1])
                # 判断是否有交集
                if ctx.parking_info.park_area_gcj_wkt != '' and aoi_geom_sp.intersects(
                        wkt.loads(ctx.parking_info.park_area_gcj_wkt)):
                    logger.info(f"{node_id}\t停车场在aoi内")
                    qb_flag = False
                    break
                else:
                    logger.info(f"{node_id}\tnode不在aoi内")
                    qb_flag = True
                    strategy_value_str = f"{aoi_main_bid_list[0]}"
        if qb_flag:
            logger.error(f"park1:{ctx.parking_info.bid}\taoi_main_bid:{strategy_value_str}\t\
                node_id:{short_node_id}\tPARK_ACCESS_MISTAKE_BY_DIFF_MAIN_BID_FROM_NODE_AND_PARK")
            park_access = context.ParkingAccess()
            park_access.bid = sub_park_access.bid
            park_access.node_geom = sub_park_access.point_gcj
            park_access.link_id = sub_park_access.relation_link_id
            park_access.node_id = sub_park_access.relation_node_id
            park_access.type = sub_park_access.type
            park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_DIFF_MAIN_BID_FROM_NODE_AND_PARK
            park_access.strategy_value = strategy_value_str
            ctx.parking_access_list.append(park_access)
    return proceed()        

            
def check_link_on_urgert_gate(ctx: context.Context, proceed):
    """
    link 挂载封禁门上
    """
    for sub_park_access in ctx.sub_pois:
        access_bid = sub_park_access.bid
        strategy_value = ''
        # logger.info(f"{sub_park_access}\tlink信息")
        if sub_park_access.type == 'LINK':
            link_id = sub_park_access.relation_link_id
            link_info = ctx.dao.road_dao.get_nav_link_by_link_ids([link_id])
            if not link_info:
                logger.info(f"{link_id}\tlink信息不存在")
                continue
            access_geom_wkt = wkt.loads(link_info[0][0]).buffer(2 / COORDINATE_FACTOR).wkt
            node_ids = ctx.dao.road_dao.get_contains_node_by_geom(access_geom_wkt)
            if not node_ids:
                logger.info(f"{access_bid}\t{link_id}\tlink上没有node")
                continue
            # 都是封禁才是
            # is_close_gate = True
            # for long_node_id in node_ids:
            #     passage = utils.is_close_gate(long_node_id)
            #     if not passage:
            #         logger.info(f"{access_bid}\t{link_id}\t{long_node_id}\tnode非封禁门")
            #         is_close_gate = False

            # 有一个封禁门就行
            is_close_gate = False
            for long_node_id in node_ids:
                passage = utils.is_close_gate(long_node_id)
                if passage:
                    logger.info(f"{access_bid}\t{link_id}\t{long_node_id}\tnode封禁门")
                    is_close_gate = True
                    break
            logger.info(f"is_close_gate:{is_close_gate}")
            if is_close_gate:
                logger.info(f"{access_bid}\t{link_id}\tlink挂封禁门上")
                strategy_value = link_id
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.link_id = sub_park_access.relation_link_id
                park_access.node_id = sub_park_access.relation_node_id
                park_access.type = sub_park_access.type
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_ON_URGENT_GATE
                park_access.strategy_value = strategy_value
                ctx.parking_access_list.append(park_access)
    return proceed()


def is_manual_work(ctx: context.Context, proceed):
    """
    判断是否人工干预
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if park_access.strategy not in [context.PARK_ACCESS_MISTAKE_BY_LINK_TO_MULTI_NODE]:
            # 判断出入口是否人工干预
            manual_work_id = ctx.dao.beeflow_dao.is_manual_work(park_access.bid)
            if not manual_work_id:
                continue
            park_access.is_valid = False
            park_access.filter_reason = f"人工干预:{manual_work_id}"
    logger.info(f"end is_manual_work")
    return proceed()


def is_strategy_already_recall(ctx: context.Context, proceed):
    """
    当前策略召回过不投放
    """
    for park_access in ctx.parking_access_list:
        park_intelligence = ctx.dao.poi_online_query.get_park_intelligence_by_access_bid(
            park_access.bid, park_access.strategy)
        if not park_intelligence:
            continue
        park_access.is_valid = False
        park_access.filter_reason = f"情报id:{park_intelligence[0]},{park_access.strategy}"
        logger.info(f"end is_strategy_already_recall,{park_access.filter_reason}")
    return proceed()


def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_qc(ctx)
    return proceed()


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type},access_bid:{item.bid}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 是否有效:{item.is_valid},过滤原因:{item.filter_reason}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type},access_bid:{park_access.bid}, \
                    node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                    f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        jdq_batch = 'JQDCWP2024101810'
        resp = ''
        guid = str(uuid.uuid4()).replace("-", "")

        intelligence_type = 'park_access_mistake'
        if park_access.strategy in same_access_type_arr:
            intelligence_type = "park_access_rongyu"

        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': intelligence_type,
            'batch_number': f"park_access_mistake_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': park_access.strategy + guid,
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': resp,
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': jdq_batch,
            'confidence': 0,
            'remark': '',
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
            'park_access_bid': park_access.bid
        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)
    proceed()


def push(point_access_geom, source_id, ref_qb_batch_id, park_bid, park_access_id):
    """
    一体化下发
    Args:
        point_xy:
        source_id:

    Returns:
    """
    data = {
        "src": 10,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ref_qb_batch_id,  # 必填，批次号
        # 停车场bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "main_poi_bid": park_bid,  
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "JQD",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "核实灌入出入口是否是工单停车场的出入口，是则不处理，不是则删除",
                "gates": [{'wkt': '', 'desc': '', 'gate_bid': park_access_id}]
            }
        }
    }
    logger.info(f"推送数据:{data}")
    req = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{req.text}")
    return req.text


def main(bid_list: list):
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    if len(bid_list) == 0:
        return
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), 
        show_tags=['地上停车场', '地下停车场', '立体停车场', '停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    logger.info(f"开始挖掘关联错误出入口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        ctx = context.Context(parking)
        logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
        # create_intelligence(ctx)
        try:
            ctx = context.Context(parking)
            logger.info(f"main_start_park_bid:\t{ctx.parking_info.bid}")
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking}\n{traceback.format_exc()}")


def dead_traj_and_dead_link(ctx, geom_wkt, aoi_geom_wkt, dbscan_eps_num, dbscan_min_sample_num, distance_min=10):
    """
    断头轨迹聚类 + 断头路
    Args:
        ctx:
        proceed:
    Returns:

    """
    logger.info(f"begin dead_traj_and_dead_link")
    if aoi_geom_wkt == "":
        logger.info(f"dead_link_traj_list aoi_geom_wkt is none")
        return False
    # 查找断头路轨迹点
    dead_link_traj_list = ctx.dao.traj_feature_dao.get_broken_points_by_geom(aoi_geom_wkt)
    if not dead_link_traj_list:
        logger.info(f"dead_link_traj_list is none")
        return False
    broken_points = [item['geom'] for item in dead_link_traj_list]
    # logger.info(f"聚类轨迹终点: {broken_points}")
    clusters = park_utils.clusters_by_point_list(broken_points, 
        dbscan_eps=dbscan_eps_num, dbscan_min_sample=dbscan_min_sample_num)
    if not clusters:
        logger.info(f"dead_link_traj_list 无 clusters 点")
        return False
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        multipoint = MultiPoint(points)
        # logger.error(multipoint.wkt)
        # logger.info(f"断头轨迹终点聚类: {multipoint.wkt}")
        convex_hull_sp = multipoint.convex_hull.buffer(3 / COORDINATE_FACTOR)
        # logger.error(convex_hull_sp)
        if convex_hull_sp.distance(wkt.loads(geom_wkt)) < distance_min / COORDINATE_FACTOR:
            return True
    logger.info(f"dead_link_traj_list clusters 点 未命中")
    return False


def get_bid_lists():
    """
    获取停车场任务列表
    Returns:
    """
    poi_online_query = PoiOnlineQuery()
    bid_list = []
    parking_4cate_lists = poi_online_query.get_4categories_park_bids()
    bid_set = set()
    if parking_4cate_lists:
        for cate in parking_4cate_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
    park_4cate_near_lists = poi_online_query.get_4categories_near_park_bids()
    if park_4cate_near_lists:
        for cate in park_4cate_near_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
        park_pv_change_lists = poi_online_query.get_park_pv_change_bids()
    if park_pv_change_lists:
        for cate in park_pv_change_lists:
            if cate['bid'] not in bid_set:
                bid_set.add(cate['bid'])
                bid_list.append(cate['bid'])
    logger.info(f"总停车场任务数：{len(bid_list)}")
    
    return bid_list


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file == "1":
        # 跑四大全集
        bid_list = get_bid_lists()
    elif bid_file == "0":
        # 单个测试
        bid_list = get_bid_lists()
    else:
        # 跑文件
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if len(bids) > 1:
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    main(np.array(bid_list))
    # with multiprocessing.Pool(progress) as pool:
        # res = pool.map(main, bid_lists)

