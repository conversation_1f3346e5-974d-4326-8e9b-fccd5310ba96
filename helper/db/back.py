# !/usr/bin/env python3
"""
背景服务端数据库模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session

Base = declarative_base()


class BluFace(Base):
    """
    BluFace
    """
    __tablename__ = 'blu_face'

    face_id = Column(String(128), nullable=False, primary_key=True)
    geom = Column(String)
    mesh_id = Column(String(6), nullable=False, default="")
    name_ch = Column(String(120), nullable=False, default="")
    aoi_level = Column(Integer, nullable=False, default=0)
    src = Column(String(120), nullable=False, default="")
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return self.face_id


class BluFacePoi(Base):
    """
     BluFacePoi
    """
    __tablename__ = 'blu_face_poi'

    id = Column(String(128), nullable=False, primary_key=True)
    face_id = Column(String(128), nullable=False, default="")
    poi_id = Column(String(128), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    std_tag = Column(String(128), nullable=False, default="")
    poi_bid = Column(String(128), nullable=False, default="")

    def __str__(self):
        return self.id


class BluFaceComplete(Base):
    """
    语义化2.0 bid池
    """

    __tablename__ = 'blu_face_complete'
    complete_info_id = Column(String, primary_key=True)
    main_bid = Column(String(128), nullable=False, default="")
    face_id = Column(String(128), nullable=False, default="")
    aoi_complete = Column(Integer, nullable=False, default=0)
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    is_white = Column(Integer, nullable=False, default=0)
    version = Column(String(32), nullable=False, default="1.0")

    def __str__(self):
        return self.complete_info_id

class BluAccess(Base):
    """

    """
    __tablename__ = 'blu_access'
    access_id = Column(String(128), nullable=False, primary_key=True)
    face_id = Column(String(128), nullable=False, default="")
    node_id = Column(String(128), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    status = Column(Integer, nullable=False, default=1)
    kind = Column(Integer, nullable=False, default=0)
    name = Column(String(128), nullable=False, default="")
    name_source = Column(Integer, nullable=False, default=0)
    access_poi_bid = Column(String(128), nullable=False, default="")
    access_poi_mid = Column(String(6), nullable=False, default="")
    update_time = Column(DateTime(timezone=False), nullable=False)
    memo = Column(String(1000), nullable=False, default="")
    extra_field1 = Column(Text, nullable=False, default="")
    extra_field2 = Column(Text, nullable=False, default="")
    geom = Column(String)
    main_bid = Column(String(128), nullable=False, default="")
    face_relate_type = Column(Integer, nullable=False, default=0)
    form = Column(Integer, nullable=False, default=0)
    relation_source = Column(Integer, nullable=False, default=0)
    attribute_source = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.access_id


class BluAccessGateRel(Base):
    """

    """
    __tablename__ = 'blu_access_gate_rel'
    access_gate_rel_id = Column(String(128), nullable=False, primary_key=True)
    access_id = Column(String(128), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    node_id = Column(String(128), nullable=False, default="")
    gate_id = Column(String(128), nullable=False, default="")
    update_time = Column(DateTime(timezone=False), nullable=False)
    source = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.access_gate_rel_id


class BluAccessLinkRel(Base):
    """

    """
    __tablename__ = 'blu_access_link_rel'
    access_link_rel_id = Column(String(128), nullable=False, primary_key=True)
    access_id = Column(String(128), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    link_id = Column(String(128), nullable=False, default="")
    node_id = Column(String(128), nullable=False, default="")  # 新增字段
    link_type = Column(Integer, nullable=False, default=0)
    update_time = Column(DateTime(timezone=False), nullable=False)
    source = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.access_link_rel_id


class BluAccessTraffic(Base):
    """

    """
    __tablename__ = 'blu_access_traffic'
    access_traffic_id = Column(String(128), nullable=False, primary_key=True)
    access_id = Column(String(128), nullable=False, default="")
    mesh_id = Column(String(6), nullable=False, default="")
    valid_obj = Column(Integer, nullable=False, default=0)
    obj_restrict = Column(Integer, nullable=False, default=0)
    transit_type = Column(SmallInteger, nullable=False, default=0)
    fee = Column(Integer, nullable=False, default=0)
    valid_time = Column(String(1000), nullable=False, default="")
    update_time = Column(DateTime(timezone=False), nullable=False)
    source = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.access_link_rel_id



class BluAoiRel(Base):

    __tablename__ = 'blu_aoi_rel'
    aoi_rel_id = Column(String(128), nullable=False, primary_key=True)
    face_id1 = Column(String(128), nullable=False, default="")
    face_id2 = Column(String(128), nullable=False, default="")
    rel_type = Column(Integer, nullable=False, default=0)
    mesh_id = Column(String(6), nullable=False, default="")
    memo = Column(String(1000), nullable=False, default="")

    def __str__(self):
        return self.aoi_rel_id


class BadAllAdmin(Base):

    __tablename__ = 'bad_all_admin'

    id = Column(Integer, primary_key=True)
    province_id = Column(String(128), nullable=False, default="")
    province_name = Column(String(64), nullable=False, default="")
    city_id = Column(String(128), nullable=False, default="")
    city_name = Column(String(64), nullable=False, default="")
    county_id = Column(String(128), nullable=False, default="")
    county_name = Column(String(64), nullable=False, default="")
    town_id = Column(String(128), nullable=False, default="")
    town_name = Column(String(128), nullable=False, default="")
    village_id = Column(String(128), nullable=False, default="")
    village_name = Column(String(128), nullable=False, default="")
    location = Column(String)
    level = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return str(self.id)
