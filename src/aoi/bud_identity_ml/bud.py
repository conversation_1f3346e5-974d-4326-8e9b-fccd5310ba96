# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建筑物识别后处理
"""
import uuid

import sys

import os

import tqdm as tqdm
from shapely import wkt
from shapely.strtree import STRtree
from shapely.validation import make_valid

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

from common import pipeline
from aoi.bud_identity_ml import context
from aoi.basic_feature_ml import utils

from aoi.dao.road import Road
from aoi.dao.background_result import BackgroundResultDao

road_dao = Road()
background_dao = BackgroundResultDao()

def pipes():
    """
    导出所有 pipe 并组合为一个 pipeline。
    """
    return pipeline.Pipeline(
        init_vectorize_wkt,
        merge_discern_wkt_by_rtree,
        merge_discern_wkt_by_rtree,
        filter_hole,
        # split_by_inner_road,
        save_result,
    )


def init_vectorize_wkt(ctx: context.Context, proceed):
    """
    初始化矢量化范围框
    :param ctx:
    :param proceed:
    :return:
    """
    vectorize_wkt_list = background_dao.get_bud_identity_by_batch_and_step(ctx.batch, ctx.step.vectored)
    print(f"初始化矢量化范围框")
    if not vectorize_wkt_list:
        vectorize_wkt_list = []
        print("vectorize_wkt_list is empty")
    shape_wkt_list = []
    street_geom_map = ctx.street_info
    for vectorize_wkt in tqdm.tqdm(vectorize_wkt_list):
        vectorize_shape = wkt.loads(vectorize_wkt[0])
        if not vectorize_shape.is_valid:
            vectorize_shape = make_valid(vectorize_shape)
        # 过滤不在原始识别街区范围
        origin_geom_shape = wkt.loads(list(street_geom_map.values())[0])
        if not origin_geom_shape.intersects(vectorize_shape):
            continue
        shape_wkt_list.append(vectorize_shape)
    ctx.shapely_wkt_list = shape_wkt_list
    proceed()

def merge_discern_wkt_by_rtree(ctx: context.Context, proceed):
    """
    地识别框，间距小于5米的绿地面合并，去除间隙
    :param ctx: 识别矢量后的框
    :return: 合并后的shapely list
    """
    discern_wkt_list = ctx.shapely_wkt_list
    merge_result_wkt_list = []
    tree = STRtree(discern_wkt_list)
    index_by_id = dict((id(pt), i) for i, pt in enumerate(discern_wkt_list))

    already_cal_index = []
    print(f"开始合并")
    for key, discern_wkt in tqdm.tqdm(enumerate(discern_wkt_list)):
        index_id = index_by_id[id(discern_wkt)]
        if index_id in already_cal_index:
            continue
        already_cal_index.append(index_id)
        merge_wkt = make_valid(discern_wkt)
        is_continue = False
        current_already_merge_id = []
        while True:
            merge_wkt_buffer = merge_wkt.buffer(ctx.filter_values.merge_distance)
            for wkt_index_id in tree.query(merge_wkt_buffer, "intersects"):
                if wkt_index_id in already_cal_index or wkt_index_id in current_already_merge_id:
                    continue
                current_already_merge_id.append(wkt_index_id)
                is_continue = True
                union_wkt = merge_wkt.union(discern_wkt_list[wkt_index_id]).simplify(0.000001)
                union_wkt = union_wkt.buffer(0.00001).buffer(-0.00001)
                diff_area = (union_wkt.area - merge_wkt.area - discern_wkt_list[wkt_index_id].area) * 10000000000
                union_area = union_wkt.minimum_rotated_rectangle.area
                if union_area == 0:
                    continue
                if ((merge_wkt.area + discern_wkt_list[wkt_index_id].area) / union_area < 0.65) or diff_area > 20:
                    continue
                else:
                    merge_wkt = union_wkt
                    already_cal_index.append(wkt_index_id)
            if not is_continue:
                break
            is_continue = False
        merge_result_wkt_list.append(merge_wkt)
    ctx.shapely_wkt_list = merge_result_wkt_list
    proceed()


def filter_hole(ctx: context.Context, proceed):
    """
    空洞过滤
    :param wkt_shapely:
    :return:
    """
    # 空洞过滤面积
    filter_hole_area = 0.0000000250
    ctx.shapely_wkt_list = utils.split_multi_polygon(ctx.shapely_wkt_list)
    ctx.shapely_wkt_list = utils.deal_green_hole_wkt(ctx.shapely_wkt_list, filter_hole_area)
    proceed()


def split_by_inner_road(ctx: context.Context, proceed):
    """
    通过内部路切割连片建筑物
    :param wkt_shapely_list:
    :return:
    """
    wkt_shapely_list = ctx.shapely_wkt_list
    diff_inner_road_result = []
    print(f"开始通过内部路切割连片建筑物")
    for key, wkt_shapely in tqdm.tqdm(enumerate(wkt_shapely_list)):
        inner_road_list = road_dao.get_inner_road(wkt_shapely.wkt)
        if inner_road_list is None or len(inner_road_list) == 0:
            diff_inner_road_result.append(wkt_shapely)
            continue
        merge_inner_road = wkt.loads(inner_road_list[0][1])
        link_id_list = []
        for inner_road in inner_road_list:
            link_id_list.append(inner_road[2])
            inner_road_shapely = wkt.loads(inner_road[1])
            buffer_width = inner_road[0] * 0.00003
            inner_road_buffer_shape = inner_road_shapely.buffer(buffer_width)
            merge_inner_road = merge_inner_road.union(inner_road_buffer_shape)
        diff_result_wkt = utils.check_valid_shapely(wkt_shapely.difference(merge_inner_road))
        diff_inner_road_result.append(diff_result_wkt)
    ctx.shapely_wkt_list = utils.split_multi_polygon(diff_inner_road_result)
    proceed()


def save_result(ctx: context.Context, proceed):
    """
    成果入库
    :param ctx:
    :param proceed:
    :return:
    """
    print(f"开始入库")
    for wkt_shapely in tqdm.tqdm(ctx.shapely_wkt_list):
        wkt_shapely = wkt_shapely.simplify(ctx.filter_values.simplify_distance, preserve_topology=True).buffer(
            0.00001).buffer(-0.00001)
        for split_shapely in utils.split_multi_polygon([wkt_shapely]):
            if split_shapely.area < ctx.filter_values.filter_area:
                continue
            data = {
                "guid": str(uuid.uuid4()).replace("-", ""),
                "batchno": ctx.batch,
                "step": ctx.step.handled,
                "geom": wkt_shapely.wkt,
            }
            background_dao.insert_bud_after_progress_record(data)
    proceed()
