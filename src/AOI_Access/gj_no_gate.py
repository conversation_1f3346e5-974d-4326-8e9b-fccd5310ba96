#-*- coding: utf-8 -*-
"""
guiji 清单查询无门数据
"""
import time
import psycopg2
import requests
import json
import shapely.wkt
import shapely.geometry
import uuid
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize



curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}
 
# poi测试
# poiConf = {"db": "poi_online",
#             "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
#             "host": "**************", "port": 9432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}

transConf = {"db": "trans_id",
            "user": "trans_id_se_rw", "pwd": "cpmkukky",
            "host": "*************", "port": 5432}

#连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()

# 连接trans库
transDbConn = psycopg2.connect(database=transConf["db"],
                              user=transConf["user"],
                              password=transConf["pwd"],
                              host=transConf["host"],
                              port=transConf["port"])
transDao = transDbConn.cursor()


# 获取pv表名称
pv_table_list_sql = "SELECT tablename FROM pg_tables WHERE tablename \
     like 'ft_nav_gate_202%' and length(tablename) = 20 order by tablename desc limit 2"
pvDao.execute(pv_table_list_sql)
pv_table_list_res = pvDao.fetchall()
if pv_table_list_res is None or len(pv_table_list_res) == 0:
    print("pv查询表名查询失败")
    exit()
pv_table = pv_table_list_res[1][0]
print("pv_table:" + pv_table)

# 获取高置信表名称
high_gate_table_sql =  "SELECT tablename FROM pg_tables WHERE tablename \
     like 'gates_track_confidence_bmf_202%' and length(tablename) = 35 order by tablename desc limit 2"
pvDao.execute(high_gate_table_sql)
high_gate_table_res = pvDao.fetchall()
if high_gate_table_res is None or len(high_gate_table_res) == 0:
    print("高置信大门表不存在")
    exit()
high_gate_table = high_gate_table_res[1][0]
print("high_gate_table:" + high_gate_table)     

school = ['教育培训;幼儿园', '教育培训;小学', '教育培训;中学']

def updateAoiGateByBid(id, level):
    """
    更新 aoi level
    """
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = "update aoi_gate_match_batch_result \
        set level=%s, status=1, update_time='%s' where id=%d" % (level, update_time, id)
    # print("sql：" + sql)
    poiDao.execute(sql)
    poiDbConn.commit()


def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())) 
    # 初始化数据 
    #获取待处理的数据列表
    for bid in open("/home/<USER>/chengqiong/aoi-strategy/aoi_access/data/gj_gate_bid.txt"):
        bid = bid.replace(" ", "").replace("\n", "")
        sql = "select face_id from  aoi_gate_match_batch_result where bid = '{}'".format(bid)
        print(sql)
        poiDao.execute(sql)
        aoi_gate_list = poiDao.fetchone()
        if aoi_gate_list:
            face_id = aoi_gate_list[0]
            print("face_id" + face_id)
            sql = "select st_astext(geom) from blu_face where face_id = '{}'".format(face_id)
            backgroundDao.execute(sql)
            aoi_geom = backgroundDao.fetchone()
            if aoi_geom:
                aoi_line = shapely.wkt.loads(aoi_geom[0]).boundary.wkt
                sql = "select ng.node_id, st_AsText(st_shortestline( \
                        st_geomfromtext(%s, 4326), nd.geom)) as line_geom, \
                        st_astext(nd.geom) from nav_gate ng inner join nav_node nd \
                        on ng.node_id = nd.node_id \
                        where st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0003)"
                roadDao.execute(sql, [aoi_geom[0], aoi_line])
                node_res = roadDao.fetchall()
                num = 0 
                for node_id_tmp in node_res:
                    sql = "select count(*) from nav_link  where \
                        st_intersects(st_geomfromtext(%s, 4326), geom) and kind <= 7"
                    roadDao.execute(sql, [node_id_tmp[1]])
                    res = roadDao.fetchone()
                    if res[0] == 0:
                        sql = "select in_linkid, out_linkid from nav_gate where node_id = %s"
                        roadDao.execute(sql, [node_id_tmp[0]])
                        link_info_res = roadDao.fetchone()
                        if link_info_res is None or len(link_info_res) == 0:
                            continue
                        sql = "select form from nav_link where link_id = %s or link_id = %s"
                        roadDao.execute(sql, [link_info_res[0], link_info_res[1]])
                        form_res = roadDao.fetchall()
                        if (form_res[0][0] == '52' and form_res[1][0] == '52') or (
                            form_res[0][0] != '52' and form_res[1][0] != '52'):
                            num = 1
                            break
                    print("跨高等级道路")        
                if num == 1:
                    print("存在至少一个大门:\t" + bid)
                else:
                    print("bid不存在大门:\t" + bid)
            else:
                print("blu_face不存在aoi:\t" + bid)
        else:
            print("aoi_gate_match_batch_result不存在aoi:\t" + bid)
    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

if __name__ == "__main__":
    process()