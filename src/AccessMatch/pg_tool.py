# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pg库链接接口
"""
import traceback
import psycopg2
import os
import configparser
import requests

# 项目根目录
root_path = os.path.dirname(os.path.dirname(__file__) + "/../../")


class PgTool:
    """Pg库查询接口
    :attributes
        conn_poi: 线上库
        conn_aoi: 背景库
        conn_road: 道路库
    """

    def __init__(self, road_dsn=None, aoi_use_proxy=False):
        self.cursor_traj_intel = None
        self.conn_traj_intel = None
        self.cursor_trajectory = None
        self.conn_trajectory = None
        self.cursor_road = None
        self.conn_road = None
        self.conn_aoi_resume = None
        self.cursor_aoi_resume = None
        self.cursor_poi_write = None
        self.conn_poi_write = None
        self.env = None
        self.cursor_aoi = None
        self.conn_aoi = None
        self.cursor_poi = None
        self.conn_poi = None
        self.cf = None
        self.road_dsn = road_dsn
        self.aoi_use_proxy = aoi_use_proxy

        self.connect()

    def connect(self):
        """
        初始化链接
        """
        if self.road_dsn is None:
            self.road_dsn = {}
        self.cf = configparser.ConfigParser()
        self.cf.read(root_path + "/conf/config.ini")
        self.conn_poi = self.get_db_conn("poi_online")
        self.cursor_poi = self.conn_poi.cursor()

        if self.aoi_use_proxy:
            # 走链接池
            self.conn_aoi = self.get_db_conn("master_back_proxy")
            self.conn_aoi.autocommit = True
            self.cursor_aoi = self.conn_aoi.cursor()
        else:
            self.conn_aoi = self.get_db_conn("master_back")
            self.conn_aoi.autocommit = True
            self.cursor_aoi = self.conn_aoi.cursor()

        self.env = self.cf.get("regular", "run_mode")
        if self.env == "online":
            self.conn_poi_write = self.get_db_conn("poi_online")
            self.conn_poi_write.autocommit = True
            self.cursor_poi_write = self.conn_poi_write.cursor()
        else:
            self.conn_poi_write = self.get_db_conn("poi_online_test")
            self.conn_poi_write.autocommit = True
            self.cursor_poi_write = self.conn_poi_write.cursor()
        self.conn_aoi_resume = self.get_db_conn("aoi_resume")
        self.cursor_aoi_resume = self.conn_aoi_resume.cursor()
        if not self.road_dsn or type(self.road_dsn) != dict:
            # 获取天级更新的道路库连接信息
            pathname = self.cf.get("regular", "road_pathname")
            dbinfo_url = self.cf.get("regular", "pg_naming_url")
            road_dsn = self.get_db_info(pathname, dbinfo_url)
            road_dsn["database"] = road_dsn["db"]
            road_dsn["password"] = road_dsn["passwd"]
            self.road_dsn = road_dsn
        self.conn_road = self.get_db_conn_by_dbinfo(self.road_dsn)
        self.cursor_road = self.conn_road.cursor()

        self.conn_trajectory = self.get_db_conn("eq_aoi_gate_relate")
        self.cursor_trajectory = self.conn_trajectory.cursor()
        try:
            self.conn_traj_intel = self.get_db_conn("eq_poi_traj_db")
            self.cursor_traj_intel = self.conn_traj_intel.cursor()
        except Exception as e:
            traceback.print_exc()

    def reconnect(self):
        """
        重新链接
        :return:
        """
        try:
            self.close()
        except:
            pass
        self.connect()

    def close(self):
        """
        关闭链接
        :return:
        """
        if not self.conn_poi.closed:
            self.conn_poi.commit()
            self.conn_poi.close()

        if not self.conn_aoi.closed:
            self.conn_aoi.commit()
            self.conn_aoi.close()

        if not self.conn_road.closed:
            self.conn_road.commit()
            self.conn_road.close()

        if not self.conn_aoi_resume.closed:
            self.conn_aoi_resume.commit()
            self.conn_aoi_resume.close()

        if not self.conn_poi_write.closed:
            self.conn_poi_write.commit()
            self.conn_poi_write.close()

        if not self.conn_trajectory.closed:
            self.conn_trajectory.commit()
            self.conn_trajectory.close()

        if not self.conn_traj_intel.closed:
            self.conn_traj_intel.commit()
            self.conn_traj_intel.close()

    def get_db_info(self, pathname, requrl):
        """根据传入的pathname获取数据库的连接信息
        Args:
            pathname: 数据库的节点信息
        Return:
            res: list类型，数据库的连接信息
        """
        content = {"pathname": pathname, "action": "query", "type": "dbinfo"}
        response = requests.post(requrl, json=content)
        res = response.json()
        if "status" in res and res["status"] == 0 and "data" in res:
            return res["data"]
        return

    def get_db_info_by_name(self, db_name):
        """根据传入的pathname获取数据库的连接信息
        Args:
            pathname: 数据库的节点信息
        Return:
            res: list类型，数据库的连接信息
        """
        content = {"name": db_name, "action": "getdb"}
        response = requests.post(self.cf.get("regular", "pg_manage_url"), json=content)
        res = response.json()
        if "status" in res and res["status"] == 0 and "data" in res:
            return res["data"]
        return

    def get_db_conn(self, dbname):
        """
        获取db查询
        """
        _host = self.cf.get(dbname, "host")
        _port = self.cf.get(dbname, "port")
        _user = self.cf.get(dbname, "user")
        _database = self.cf.get(dbname, "database")
        _password = self.cf.get(dbname, "password")
        conn = psycopg2.connect(
            database=_database, user=_user, password=_password, host=_host, port=_port
        )
        return conn

    def get_db_conn_by_dbinfo(self, db):
        """
        获取db查询
        """
        conn = psycopg2.connect(
            database=db["database"],
            user=db["user"],
            password=db["password"],
            host=db["host"],
            port=db["port"],
        )
        return conn

    def get_db_conn_by_dbname(self, db_name):
        """
        根据数据库名称获取连接
        """
        db_info = self.get_db_info_by_name(db_name)
        if "database" not in db_info:
            db_info["database"] = db_info["db"]
        if "password" not in db_info:
            db_info["password"] = db_info["passwd"]

        return self.get_db_conn_by_dbinfo(db_info)

    def __enter__(self):
        """
        支持with操作
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持with操作，自动关闭链接
        """
        self.close()

    def get_conn_road(self):
        """
        获取道路链接库
        """
        return self.conn_road

    def get_conn_poi(self):
        """
        获取poi链接（poi库）
        """
        return self.conn_poi

    def get_conn_back(self):
        """
        获取master_back链接(背景库)
        """
        return self.conn_aoi

    def get_road_dsn(self):
        """
        获取道路库的连接方式
        """
        return self.road_dsn

    def commit(self):
        """
        数据提交
        """
        if not self.conn_poi.closed:
            self.conn_poi.commit()

        if not self.conn_aoi.closed:
            self.conn_aoi.commit()

        if not self.conn_road.closed:
            self.conn_road.commit()

        if not self.conn_aoi_resume.closed:
            self.conn_aoi_resume.commit()

        if not self.conn_poi_write.closed:
            self.conn_poi_write.commit()

        if not self.conn_trajectory.closed:
            self.conn_trajectory.commit()

        if self.conn_traj_intel and not self.conn_traj_intel.closed:
            self.conn_traj_intel.commit()

    def rollback(self):
        """
        数据回滚
        """
        if not self.conn_poi.closed:
            self.conn_poi.rollback()

        if not self.conn_aoi.closed:
            self.conn_aoi.rollback()

        if not self.conn_road.closed:
            self.conn_road.rollback()

        if not self.conn_aoi_resume.closed:
            self.conn_aoi_resume.rollback()

        if not self.conn_poi_write.closed:
            self.conn_poi_write.rollback()

        if not self.conn_trajectory.closed:
            self.conn_trajectory.rollback()

        if self.conn_traj_intel and not self.conn_traj_intel.closed:
            self.conn_traj_intel.rollback()
