# -*- coding: utf-8 -*-
"""
出入口新增情报过滤
"""
import datetime
import multiprocessing
import sys
import os
import uuid
import math

import tqdm
import requests
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.affinity import translate
from shapely import wkt
from datetime import datetime
from datetime import timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi import utils

from shapely import wkt
from loguru import logger

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
road_dao = Road()
trans_dao = Trans()

# logger.remove()
log_path = "./log/access_add.log"
logger.add(log_path, rotation="100 MB")


def main():
	"""
	获取待新增的出入口任务
	"""
	limit = 20000
	tasks = poi_online_query.get_wait_add_access_tasks(limit)
	# 带进度的循环
	# task_id, source, gcj_geom,park_bid,l ong_node_id,long_link_id, node_id, link_id, \
	# statu s,source_i d,access_nam e,access_show_t = taskag
	for i in tqdm.tqdm(range(len(tasks))):
		task = tasks[i]
		try:
			task_id = task['task_id']
			res = process_one_task(task)
			logger.info(res)
			filter_access(res)
			if res['status'] == "pushed":
				if res['source'] == '':
					res['source'] = "STRATEGY_FROM_PARK_ACCESS_ADD_TASK_AUTO"
				push_res = push_task(res)
				if not push_res:
					res['status'] = "push_error"
			poi_online_rw.update_park_access_add_task(task_id, res)
		except Exception as e:
			logger.error(e)


def push_task(res):
	"""
	推送出入口
	"""
	request = {
		"source": res['source'],
		"source_id": res['source_id'],
		"parent_id": res['park_bid'],
		"show_tag": res['access_show_tag'],
		"name": res['access_name'],
		"address": res['access_address'],
		"point_x": res['point_x'],
		"point_y": res['point_y'],
		"status": 1,
		"is_add_poi": 1,
		"force_update": 1
	}
	if res['road_relation_type'].lower() == "node":
		request["node_id"] = res['node_id']
	elif res['road_relation_type'].lower() == "link":
		request["link_id"] = res['link_id']
	else:
		return False
	logger.info(request)
	return utils.data_update_request(request)


def filter_access(res):
	"""
	出入口过滤
	"""
	# 线上已存在
	sub_access_info = poi_online_query.get_sub_pois(res['park_bid'])
	if sub_access_info and  res['status'] == 'pushed':
		node_sp = wkt.loads(res['gcj_geom'])
		print(sub_access_info)
		access_min_dis = min(node_sp.distance(wkt.loads(access_info[2])) for access_info in sub_access_info) * 110000
		if access_min_dis < 10:
			res['status'] = "already_exist"
	return


def get_passage(long_node_id, long_link_id):
	"""
	获取通行性
	"""
	if long_node_id != "":
		passage = utils.get_nav_gate_semantic(long_node_id)
	elif long_link_id != "":
		passage = '入口'
	return passage
	

def process_one_task(task):
	"""
	处理一个任务
	"""
	logger.info(f"process_one_task-start{task}")
	if task['type'] == '':
		task['type'] = "park_access_add_task"
	park_bid = task['park_bid']

	long_node_id = task['long_node_id']
	node_id = task['node_id']
	long_link_id = task['long_link_id']
	link_id = task['link_id']
	if long_node_id == '' and len(node_id) > 5:
		trans_node = trans_dao.get_long_node_by_short_node_ids([node_id])
		if trans_node: 
			long_node_id = trans_node[0][0]
	
	if long_node_id == '':
		if long_link_id == '' and len(link_id) > 5:
			trans_link = trans_dao.get_long_link_by_short_link_ids([link_id])
			if trans_link: 
				long_link_id = trans_link[0][0]
	road_relation_type = 'None'
	if long_node_id != '':
		road_relation_type = 'node'
	elif long_link_id != '':
		road_relation_type = 'link'
	res = {
		"status": task['status'],
		"source": task['source'].upper(),
		"source_id": task['type'] + "_" + str(uuid.uuid4()).replace("-", ""),
		"update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
		"park_bid": park_bid,
		"node_id": node_id,
		"link_id": link_id,
		"long_node_id": long_node_id,
		"long_link_id": long_link_id,
		"road_relation_type": road_relation_type
	}

	park_info  = poi_online_query.get_park_info(park_bid)
	if not park_info:
		res['status'] = "park_not_exist"
		return res
	park_name = park_info[0]
	res['access_address'] = park_info[9]

	passage = get_passage(long_node_id, long_link_id)
	if passage not in ['入口', '出入口']:
		res['status'] = "passage_not_in"
		return res
	res['access_show_tag'] = "停车场" + passage
	res['access_name'] = park_name.replace("-", "") + "-" + passage
	

	if long_node_id != '':
		node_info = road_dao.get_node_list([long_node_id])
		if not node_info:
			res['status'] = "node_not_exist"
			return res
		res['gcj_geom'] = node_info[0][0]
		if res['node_id'] == '':
			short_node_list = trans_dao.get_short_node_by_long_node_ids([long_node_id])
			if not short_node_list:
				res['status'] = "node_long_to_short_fail"
				return res
			res['node_id'] = short_node_list[0][0]
	else:
		link_info = road_dao.get_nav_link(link_ids=[long_link_id])
		if not link_info:
			res['status'] = "link_not_exist"
			return res
		res['gcj_geom'] = Point(wkt.loads(link_info[0][1]).coords[0]).wkt
		short_link_list = trans_dao.get_short_link_by_long_link_ids([long_link_id])
		if not short_link_list:
			res['status'] = "link_long_to_short_fail"
			return res
		res['link_id'] = short_link_list[0][0]
		
	print(res['gcj_geom'])
	node_geom_bd = utils.gcj_to_mc(res['gcj_geom'])
	res['bdmc_geom'] = node_geom_bd
	res['point_x'] = wkt.loads(node_geom_bd).x
	res['point_y'] = wkt.loads(node_geom_bd).y
	res['status'] =  "pushed"
	return res


if __name__ == '__main__':
    main()
