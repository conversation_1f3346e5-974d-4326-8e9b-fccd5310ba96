"""
策略自动化处理
"""
from src.door_naming.DaoAoiPoi import DaoAoiPoi
from src.aoi.aoi_protection import context, cfg
from typing import Tuple
import shapely.wkt
from src.back.tool import geo_helper
from typing import Callable, List


def get_aoi_by_main_bid(dao: DaoAoi<PERSON>oi, bid):
    """
    根据main_bid查询aoi
    :param dao:
    :param bid:
    :return:
    """
    sql = 'select st_astext(a.geom) as geom, a.face_id, b.poi_bid, a.aoi_level,b.std_tag, a.area, a.src ' \
          'from blu_face a inner join blu_face_poi b on a.face_id=b.face_id' \
          f" where b.poi_bid='{bid}' limit 1"
    dao.cursor_aoi.execute(sql)
    ret = dao.cursor_aoi.fetchone()
    return ret


def white_tag_handle(dao: DaoAoiPoi, data: context.Data) -> Tuple[bool, bool]:
    """
    白名单tag过滤
    :param dao:
    :param data:
    :return:
    """
    if data.src in cfg.WHITE_TAG:
        data.memo = {
            "msg": '白名单的数据源, 可直接下线',
            "action": cfg.STRATEGY_NOTHING,
        }
        return True, True
    return False, False


def bid_repeat_aoi(dao: DaoAoiPoi, data: context.Data) -> Tuple[bool, bool]:
    """
    检测判重
    :param dao:
    :param data:
    :return: 是否自动处理, 是否可下线
    """
    sql = 'select release_bid from aoi_repeat_bid where invalid_bid=%s'
    dao.cursor_poi.execute(sql, [data.bid])
    ret = dao.cursor_poi.fetchone()
    if not ret:
        # 查不到判重的数据
        return False, False
    new_bid = ret.release_bid
    old_face = get_aoi_by_main_bid(dao, data.bid)
    if not old_face:
        data.memo = {
            "msg": '无边框数据可自动下线',
            "action": cfg.STRATEGY_NOTHING,
        }
        return True, True
    new_face = get_aoi_by_main_bid(dao, new_bid)
    if old_face and not new_face:
        new_poi_info = dao.get_poi_info_by_bid(new_bid)
        if not new_poi_info:
            print(f"{data.bid}-{new_bid}: 未获取到新poi信息,下发人工")
            return False, False
        new_geom = new_poi_info.geom
        if not shapely.wkt.loads(old_face.geom).distance(shapely.wkt.loads(new_geom)):
            print(f"{data.bid}-{new_bid}: 判重后的数据不在边框内,下发人工")
            return False, False
        # 判重后可以自动处理
        data.memo = {
            "msg": '判重,新bid无边框,旧bid有边框, 换绑主点',
            "action": cfg.STRATEGY_REPEAT_CHANGE_MAIN_BID,
            "data": {
                "blu_face_poi_update_bid": [old_face.face_id, old_face.src, new_bid, new_poi_info.mid],
                # 暂时不用改src
                # "blu_face_update_src": [old_face.face_id, 'CD'],
                "face_id": old_face.face_id,
                "old_bid": data.bid,
                "new_bid": new_bid,
            }
        }
        return True, True

    if old_face and new_face:
        # 新旧数据都有,但是新数据不是商单
        if new_face.src != "SD":
            return False, False
        else:
            # 新数据是商单
            old_geom = shapely.wkt.loads(old_face.geom)
            new_geom = shapely.wkt.loads(new_face.geom)
            rate = geo_helper.cal_iou(old_geom, new_geom)
            if rate < 0.8:
                return False, False
            else:
                # 交并比大于80%, 删除B端框, 当前主点下线, 当前C端框使用新的判重点
                data.memo = {
                    "msg": "判重后的主点为B端框,并且交并比大于0.8,B端框上线",
                    "action": cfg.STRATEGY_B_TO_C,
                    "data": {
                        # 直接改新的src为旧的值, 保障判重后的边框能上线
                        "blu_face_update_src": [new_face.face_id, old_face.src]
                        # 暂时不考虑旧边框删除
                    }
                }
                return True, True
    return False, False


def aoi_cover_poi_check(dao: DaoAoiPoi, data: context.Data) -> Tuple[bool, bool]:
    """
    aoi 覆盖bid的检测
    :param dao:
    :param data:
    :return: 是否自动处理,是否可下线
    """
    face_info = get_aoi_by_main_bid(dao, data.bid)
    if not face_info:
        data.memo = {
            "msg": '无边框数据',
            "action": cfg.STRATEGY_NOTHING,
        }
        return True, True
    geom = face_info.geom
    sql = ' with tmp as (select st_geomfromtext(%s, 4326) as aoi_geom)' \
          ' select bid, std_tag from poi, tmp where st_intersects(tmp.aoi_geom, poi.geometry) and bid !=%s '
    dao.cursor_poi.execute(sql, [geom, data.bid])
    ret = dao.cursor_poi.fetchall()
    if len(ret) < 1:
        data.memo = {
            "msg": "aoi下无可换绑的poi: 无POI, 无法下线",
            "action": cfg.STRATEGY_NOTHING,
        }
        return True, False
    can_build_aoi_bid = []
    for item in ret:
        std_tag = item.std_tag
        if std_tag not in cfg.ENABLE_TAGS:
            continue
        can_build_aoi_bid.append(item.bid)
    if len(can_build_aoi_bid) < 1:
        data.memo = {
            "msg": "aoi下无可换绑的poi: 区域内POI不能作为主点, 无法下线",
            "action": cfg.STRATEGY_NOTHING,
        }
        return True, False

    return False, False


FUNCS: List[Callable] = [
    # 白名单数据自动下线
    white_tag_handle,
    # bid判重处理
    bid_repeat_aoi,
    # aoi压盖处理
    aoi_cover_poi_check,
]


def run(dao: DaoAoiPoi, data: context.Data):
    """
    策略处理
    :param dao:
    :param data:
    :return:
    """
    for func in FUNCS:
        is_auto, can_offline = func(dao, data)
        if is_auto:
            return is_auto, can_offline
    return False, False
