# !/usr/bin/env python3
"""
一体化情报模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session
from sqlalchemy.dialects.postgresql import JSONB
Base = declarative_base()


class IntegrationQb(Base):
    """
    一体化情报表
    """

    SRC_UNKNOWN = 0  # 未知类型
    SRC_SEMANTICS = 1  # 语义化
    SRC_GATE_RELATE = 2  # 大门关联
    SRC_PARK_ACCESS = 3  # 停车场出入口关联
    SRC_AOI = 4  # AOI边框
    SRC_GATE = 5  # 大门

    WORK_TYPE_UNKNOWN = 0  # 未知作业类型
    WORK_TYPE_AUTO = 1  # 自动批处理作业
    WORK_TYPE_MANUAL = 2  # 人工作业

    STATUS_PENDING = 0  # 任务待创建：情报生成的初始状态
    STATUS_WORK_TODO = 1  # 任务已创建
    STATUS_WORK_DONE = 2  # 制作完成 制作完成，成果待回库，其他环节不可用
    STATUS_BACK_DB_DONE = 3  # 回库完成 制作完成，且成果回库，其他环节可拉取 为终态
    STATUS_DATA_CORRECT = 4  # 数据正确：情报核实数据是正确的无需修改，为终态
    STATUS_CANNOT_WORK = 5  # 无法制作 情报无效 当前工艺水平下无法判断或不需要制作 为终态
    STATUS_CHECK_TODO = 6  # 无法核实-待采集 资料未覆盖，无法核实
    STATUS_GATHER = 7  # 无法核实-投放采集 无法核实，已投放路淘、众源等采集资料 为终态

    __tablename__ = 'integration_qb'

    id = Column(Integer, nullable=False, primary_key=True)  # 要素情报id
    aoi_qb_id = Column(Integer, nullable=False, default=0)  # 一体化情报id

    src = Column(Integer, default=SRC_UNKNOWN)  # 情报来源(语义化, 大门关联, 停车场出入口关联...)
    work_type = Column(Integer, default=WORK_TYPE_UNKNOWN)  # 情报来源类型
    strategy_type = Column(String(100), nullable=False, default="")  # 策略类型

    ref_qb_batch_id = Column(String(100), nullable=False, default="")  # 具体情报批次id,可为空
    ref_qb_batch_name = Column(String(100), nullable=False, default="")  # 具体情报批次名称,可为空
    ref_qb_id = Column(String(100), nullable=False, default="")  # 具体情报id(各自业务线情报唯一可定位id)

    work_factors = Column(String(100), nullable=False, default="")  # 生产要素
    ref_factors = Column(String(100), nullable=False, default="")  # 参考要素

    main_poi_bid = Column(String(100), nullable=False, default="")  # 情报关联主点poi的bid
    main_poi_name = Column(String(100), nullable=False, default="")  # 情报关联主点poi的名称
    main_poi_pv = Column(String(100), nullable=False, default="0")  # 情报关联主点poi的pv
    mesh_id = Column(String(20), nullable=False, default="")  # 情报关联主点poi所在城市图幅号

    city_name = Column(String(100), nullable=False, default="")  # 情报关联主点poi所在城市
    city_code = Column(String(100), nullable=False, default="")  # 情报关联主点poi所在城市编码
    city_top = Column(Integer, default=0)  # 情报关联主点poi所在城市等级

    mark_geom = Column(String, nullable=False)  # 情报标记位置
    priority = Column(Integer, nullable=False, default=0)  # 情报优先级(紧急[90，100] 高[70，90) 中[50，70) 低[70，80))
    nav_risk = Column(Integer, nullable=False, default=0)  # 导航风险等级(0 未调查, 1 高风险, 2 中风险, 3 低风险, 4 无风险)

    status = Column(Integer, default=STATUS_PENDING)
    section_id = Column(Integer, default=0)  # 处理环节id
    flow_status = Column(Integer, default=0)  # 0待处理 1待下发 2下发成功
    memo = Column(String(200), nullable=False, default="")
    extra = Column(JSONB, nullable=False, default=lambda: {})

    ref_id = Column(String(100), nullable=False, default="")  # 参考要素ID
    action = Column(String(100), nullable=False, default="")  # 动作类型
    from_src = Column(String(255), nullable=False, default="")  # 来源

    created_at = Column(DateTime(timezone=False), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=False), nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return str(self.id)
