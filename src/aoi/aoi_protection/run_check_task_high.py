"""
任务核实,高优

高优任务暂时没有策略判断, 直接下发人工
"""
import os
import sys
from pathlib import Path
import datetime
import dataclasses
from typing import List
import tqdm
import json

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())

from src.aoi.aoi_protection import cfg, context
from src.door_naming.DaoBeeflow import DaoBeeflow


@dataclasses.dataclass
class Ctx:
    """
    ctx值
    """
    data_list: List[context.Data] = dataclasses.field(default_factory=list)


def load_data(ctx: Ctx):
    """
    数据加载
    :param ctx:
    :return:
    """
    with DaoBeeflow() as dao:
        sql = 'select id, bid, src, pv, created_at, updated_at, std_tag ' \
              'from aoi_protect_information where handle_status=%s and src in %s limit 3000'
        print(dao.cursor_beeflow.mogrify(sql, [cfg.HANDLE_STATUS_DEFAULT, cfg.HIGH_LEVEL_TAG]))
        dao.cursor_beeflow.execute(sql, [cfg.HANDLE_STATUS_DEFAULT, cfg.HIGH_LEVEL_TAG])
        ret = dao.cursor_beeflow.fetchall()
        for item in tqdm.tqdm(ret, desc='数据加载'):
            _id, bid, src, pv, created_at, updated_at, std_tag = item
            data = context.Data(
                id=_id,
                bid=bid,
                src=src,
                pv=pv,
                created_at=created_at,
                updated_at=updated_at,
                std_tag=std_tag,
            )
            ctx.data_list.append(data)


def check_bid_repeat(dao: DaoBeeflow, bid: str):
    """
    设置是否重复下发
    规则:
         近两天内重复出现, 并且被下发人工,且在高优的集合中
    :param dao: dao查询对象
    :param bid: 需要检查的bid
    :return:
    """
    dao.conn_beeflow.ping(reconnect=True)
    sql = 'select id from aoi_protect_information where bid=%s and updated_at > %s  and work_type in %s' \
          ' and handle_status in %s and batch_type = %s '
    # 近两天内处理过. 无论是策略还是人工,都是重复,
    yesterday = datetime.date.today() - datetime.timedelta(days=1)
    work_type = [cfg.WORK_TYPE_MANUAL, cfg.WORK_TYPE_STRATEGY]
    handle_status = [cfg.HANDLE_STATUS_SUCCESS,
                     cfg.HANDLE_STATUS_WAIT_PREVIEW,
                     cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE,
                     cfg.HANDLE_STATUS_STRATEGY,
                     cfg.HANDLE_STATUS_FAIL
                     ]
    dao.cursor_beeflow.execute(sql, [bid, yesterday.strftime("%Y-%m-%d"), work_type, handle_status, cfg.MANUAL_HIGH])
    ret = dao.cursor_beeflow.fetchone()
    if ret:
        return ret[0]

    return 0


def set_need_manual(dao: DaoBeeflow, data: context.Data, batch_id):
    """
    设置人工处理
    :param dao:
    :param data: 处理对象
    :param batch_id: 指定对应的batch_id
    :return:
    """
    dao.conn_beeflow.ping(reconnect=True)
    sql = 'update aoi_protect_information set work_type=%s, handle_status=%s , batch_id=%s, batch_type = %s' \
          ' where id=%s'
    dao.cursor_beeflow.execute(sql, [cfg.WORK_TYPE_MANUAL, cfg.HANDLE_STATUS_WAIT_PREVIEW, batch_id, cfg.MANUAL_HIGH,
                                     data.id])
    dao.conn_beeflow.commit()


def set_repeat(dao: DaoBeeflow, data: context.Data):
    """
    设置为重复
    :param dao:
    :param data:
    :return:
    """
    sql = 'update aoi_protect_information set work_type=%s, handle_status=%s, memo=%s where id=%s limit 1'
    dao.cursor_beeflow.execute(sql, [
        cfg.WORK_TYPE_REPEAT,
        cfg.HANDLE_STATUS_SUCCESS,
        json.dumps(data.memo, ensure_ascii=False), data.id])
    dao.conn_beeflow.commit()


def run(ctx: Ctx):
    """
    高优任务下发
    高优任务策略不处理,直接转人工
    :param ctx:
    :return:
    """
    batch_id = "aoi_protection_high_" + datetime.datetime.now().strftime("%Y%m%d%H")
    with DaoBeeflow(rw=True) as dao_bee_flow:
        for item in tqdm.tqdm(ctx.data_list, desc='数据处理'):
            item: context.Data
            repeat_id = check_bid_repeat(dao_bee_flow, item.bid)
            if repeat_id > 0:
                item.memo = {
                    "msg": '数据重复',
                    "data": {
                        "repeat_id": repeat_id
                    }
                }
                print(f"近期重复下发,设置重复{item.bid}-{item.id}-{repeat_id}")
                set_repeat(dao_bee_flow, item)
                continue
            print(f"高优任务设置人工:{item.id} - {item.bid} - {batch_id}")
            set_need_manual(dao_bee_flow, item, batch_id)


if __name__ == '__main__':
    _ctx = Ctx()
    load_data(_ctx)
    run(_ctx)
