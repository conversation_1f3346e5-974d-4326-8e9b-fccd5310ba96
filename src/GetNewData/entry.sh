#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../../conf/globalConf.sh

CityFile=$1;

#function GetVersion(){
#	cd version;
#	wget -r -l 2 ftp://10.113.177.33/home/<USER>/NFS_data/map_data_data_release/data_thunder/release/Back/*;
#	Version=`ls 10.113.177.33/home/<USER>/NFS_data/map_data_data_release/data_thunder/release/Back | sort -n | tail -1`;
#	cd -;
#}
SPLIT_DATA="$TMP_PATH/jichudiwu/split"

function Process(){
  infile=$1;
	while read line; do
    echo "sh -x access.sh $line > ${infile}.log"
	done<${infile}
}

function Main(){
  mkdir -p "$SPLIT_DATA";
  rm -r $SPLIT_DATA/*;

#	GetVersion;

  split_line_cnt=` wc -l < ${CityFile} | awk '{print sprintf("%.0f", ($1 / 20))}' `
  split -l ${split_line_cnt} ${CityFile} "$SPLIT_DATA/city.part."

  for ONE_PART in `ls $SPLIT_DATA/city.part.*`; do
    Process ${ONE_PART} &
  done
  wait
}

Main;
