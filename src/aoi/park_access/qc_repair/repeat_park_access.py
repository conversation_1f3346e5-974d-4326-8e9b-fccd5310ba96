# -*- coding: utf-8 -*-
"""
冗余出入口下线
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
from math import ceil

import tqdm
import requests
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi.dao.master_back import MasterBackDao

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()
traj_dao = DestTraj()
master_back_dao = MasterBackDao()

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_relatedness.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_relatedness.txt"


def main(bids: list):
    """
    失效高通量Link处理
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids.tolist())
    for park in tqdm.tqdm(park_list):
        try:
            if park['park_spec'] == 1 or park['show_tag'] in ('门前停车场', '路侧停车场'):
                logger.info(f"{park['bid']} 临街场景不处理")
                continue
            logger.info(f"\n{park['bid']} 开始处理")
            road_relation_json = park['road_relation_childrens']
            if not road_relation_json or len(road_relation_json['link_info']) == 0:
                logger.warning(f"{park['bid']} 没有link_info")
                continue
            # 查询停车场出入口子点
            park_access_list = poi_online_query.get_park_access_by_kv(park_bid=park['bid'], status=1)
            if not park_access_list:
                logger.warning(f"{park['bid']} 没有停车场出入口")
                continue
            # 已聚合的bid
            agg_bids = [x['bid'] for x in road_relation_json['link_info']]
            agg_node_ids = [x['node_id'] for x in road_relation_json['link_info'] if x['type'] == 1]
            agg_link_ids = [x['link_id'] for x in road_relation_json['link_info'] if x['type'] == 2]
            # 保障已聚合的bid都在线
            online_master_bids = master_back_dao.get_park_access_by_kv(bids=agg_bids, status=1)
            online_poi_bids = poi_online_query.get_park_access_by_kv(bids=agg_bids, status=1)
            if len(list(set(agg_bids))) != len(online_poi_bids) != len(online_master_bids):
                logger.warning(f"{park['bid']} BID线上库母库数量不一致")
                continue
            for park_access in park_access_list:
                if park_access['bid'] in agg_bids:
                    continue
                road_relation = park_access['road_relation']
                if not road_relation or 'link_info' not in road_relation or len(road_relation['link_info']) == 0:
                    logger.warning(f"{park['bid']} {park_access['bid']} 没有link_info")
                    continue
                if road_relation['link_info'][0]['type'] == 1 and road_relation['link_info'][0][
                    'node_id'] in agg_node_ids:
                    push(park_access['bid'], [item['bid'] for item in road_relation_json['link_info'] if
                                              'node_id' in item and item['node_id'] == road_relation['link_info'][0][
                                                  'node_id']])
                    continue
                if road_relation['link_info'][0]['type'] == 2 and road_relation['link_info'][0][
                    'link_id'] in agg_link_ids:
                    push(park_access['bid'], [item['bid'] for item in road_relation_json['link_info'] if
                                              item['link_id'] == road_relation['link_info'][0]['link_id']])
                    continue
        except Exception as e:
            traceback.print_exc()
            logger.error(f"{park['bid']} 异常:{e}")


def push(bid, originals):
    """
    更新park_wkt
    Args:
        bid:

    Returns:

    """
    # 写入成果数据
    with open("./achieve/repeat_park_access.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{originals[0]}\n")
        f.flush()
    return
    logger.info(f"{bid} 开始推送下线")
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_REPEAT_ACCESS",
        "source_id": f"STRATEGY_REPEAT_ACCESS_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_REPEAT_ACCESS_0401",
        "status": 2,
    }
    logger.info(f"重复出入口下线:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in tqdm.tqdm(f):
            data_list = line.strip().split("\t")
            if "R6010" in data_list[5]:
                bids.append(data_list[2])

    return bids


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if len(bid_list) == 0:
        bid_list = get_need_deal_bids_by_afs()
    if len(bid_list) == 0:
        exit(f"没有可执行BID")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
