"""
get diff
"""
import sys

infdict = {}
mpoiall = sys.argv[1]
inffile = sys.argv[2]
outfile = sys.argv[3]
online2 = sys.argv[4]
offlinemid = sys.argv[5]
offline2 = sys.argv[6]
mid2bid = {}
bid2mid = {}
bid2mido = {}
mid2bido = {}
midoffset = set()
mid2bidoff = {}
f = open(offlinemid)
for line in f:
	data = line.strip('\n')
	midoffset.add(data)
f.close()

f = open(mpoiall)
for line in f:
	data = line.strip('\n').split('\t')
	mid = data[0]
	bid = data[1]
	name = data[2]
	addr = data[3]
	faceid = data[4]
	poly = data[5]
	type = data[6]
	t1 = data[7]
	t2 = data[8]
	city = data[9]
	infdict[mid] = bid + '\t' + name + '\t' + addr + '\t' + faceid + '\t' + poly + '\t' + poly + '\t' + type + '\t' + city
	if mid in midoffset:
		if not bid == '' and not bid == "0":
			mid2bidoff[mid] = bid
	if not bid == '' and not bid == "0":
		if bid in bid2mido:
			if 'B' in mid and 'A' in bid2mido[bid]:
				mid2bido[mid] = bid
				mid2bido[bid2mido[bid]] = '0'
				bid2mido[bid] = mid
			elif 'B' in mid and 'B' in  bid2mido[bid]:
				print 'new', mid, bid, bid2mido[bid]

		else:
			mid2bido[mid] = bid
			bid2mido[bid] = mid

		mid2bido[mid] = bid
	else:
		mid2bido[mid] = '0'
f.close()

bidset  =set()
f = open(inffile)
ff = open(outfile, 'w')
for line in f:
	data = line.strip('\n').split('\t')
	mid = data[0]
	bid = data[1]
	name = data[2]
	addr = data[3]
	faceid = data[4]
	poly = data[5]
	if '(((' in poly and not 'MULTI' in poly:
		poly = poly.replace('(((', '((').replace(')))', '))')
	type = data[6]
	t1 = data[7]
	t2 = data[8]
	city = data[9]
	infnow = bid + '\t' + name + '\t' + addr + '\t' + faceid + '\t' + poly + '\t' + poly + '\t' + type + '\t' + city
	
	if not mid == "" and not mid == "0":
		if bid == '' or bid == '0':
			bid = '0'
			mid2bid[mid] = bid

		if bid in bid2mid and not bid == '0':
			if 'B' in mid and 'A' in bid2mid[bid]:
				mid2bid[mid] = bid
				mid2bid[bid2mid[bid]] = '0'
				bid2mid[bid] = mid
			elif 'B' in mid and 'B' in bid2mid[bid]:
				print 'new', mid, bid, bid2mid[bid]
		elif not bid == '0':
			mid2bid[mid] = bid
			bid2mid[bid] = mid

	if not mid in infdict:
		if not mid == "" and not mid == "0":	
			ff.write(line)
	else:
		if not infnow == infdict[mid]:
			if not mid == "" and not mid == "0":	
				ff.write(line)

f.close()
ff.close()

f = open(online2, 'w')
for item in mid2bid:
	temp0 = '-1'
	if item in mid2bido:
		if not mid2bido[item] == mid2bid[item]:
			f.write(mid2bid[item] + '\t' + item + '\n')
	if not item in mid2bido:
		if not mid2bid[item] == '0':
			f.write(mid2bid[item] + '\t' + item + '\n')
f.close()

f = open(offline2, 'w')
# 乱码注释 2021-08-05
#�0�7�0�6�0�6�0�5�0�8�0�2mid
for item in mid2bidoff:
	if item == '' or item == "0":
		continue
	f.write(item + '\t' + mid2bidoff[item] + '\n')

# 乱码注释 2021-08-05
#�0�6�0�7mpoi.all�0�0�0�4�0�7�0�3�0�8�0�3 �0�4�0�0mpoi.all�0�0�0�4�0�1�0�3�0�7�0�4�0�8�0�2mid
for item in mid2bido:
	if item == "mid":
		continue
	if not item in mid2bid and not item in mid2bidoff and not mid2bido[item] == '0':
		if item == '' or item == "0":
			continue
		#f.write(mid2bido[item]+'\t'+item+'\n')
		f.write(item + '\t' + mid2bido[item] + '\n')

f.close()



