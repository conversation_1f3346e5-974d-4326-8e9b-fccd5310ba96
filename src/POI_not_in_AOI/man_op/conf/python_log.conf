[loggers]
keys=root

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=fmt

[logger_root]
level=INFO
handlers=fileHandler

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=fmt
args=(sys.stdout,)

[handler_fileHandler]
class=logging.handlers.TimedRotatingFileHandler
level=INFO
formatter=fmt
args=('log/competitor-mining-py.log',)

[formatter_fmt]
format=%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
datefmt=%Y-%m-%d %H:%M:%S
