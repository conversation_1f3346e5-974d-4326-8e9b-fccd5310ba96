# -*- coding: UTF-8 -*-
################################################################################
"""
导出纵向排列的关联多个大门的出入口
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import sys
import shapely.wkt
import shapely.geometry
import uuid
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize

def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../../conf/config.ini')
dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()


def main():
    """
    输出一个出入口关联多个大门的成果数据并下发作业
    """
    sql = "select a.face_id, poi_bid,st_astext(geom) from blu_Face a inner join blu_FacE_poi b on a.face_id = b.face_id \
           where city_name in ('北京市', '广州市', '深圳市')"
    cursor_aoi.execute(sql)
    res = cursor_aoi.fetchall()
    create_plan_bid_list = []
    f_creatplan = open("multinode_access_3city.txt", "w")
    for v in res:
        face_id = v[0]
        bid = v[1]
        aoi_geom = v[2]
        aoi_region = shapely.wkt.loads(aoi_geom)

        sql = "select distinct a.access_id, a.node_id from blu_access a inner join blu_access_gate_rel b on a.access_id = b.access_id \
                where face_id = %s "
        cursor_aoi.execute(sql, [face_id])
        res_access = cursor_aoi.fetchall()
        item = {"bid":bid, "aoi_src_data_extra":[]}
        for access_info in res_access:
            access_id = access_info[0]
            access_node_id = access_info[1]
            sql = "select distinct node_id from blu_access_gate_rel where access_id = %s "
            cursor_aoi.execute(sql, [access_id])
            res_tmp = cursor_aoi.fetchall()
            if res_tmp is None or len(res_tmp) <= 1:
                continue

            #找大门link
            node_id_all_gate = ""
            for gate_info_tmp in res_tmp:
                node_id_all_gate = node_id_all_gate + "','" + gate_info_tmp[0]
            sql = "select distinct link_id, st_astext(geom) from nav_link a inner join nav_gate b \
                    on a.link_id = b.in_linkid or a.link_id = b.out_linkid where node_id = %s "
            cursor_road.execute(sql, [node_id_all_gate.strip("','")])
            res_link = cursor_road.fetchall()
            num = 0
            for res_link_tmp in res_link:
                link_region = shapely.wkt.loads(res_link_tmp[1])
                if link_region.intersects(aoi_region):
                    num = num + 1 
            if num >= len(res_tmp):
                continue
            
            #创建计划
            sql = "select st_astext(geom) from blu_node where node_id = %s"
            cursor_aoi.execute(sql, [access_node_id])
            res_node = cursor_aoi.fetchone()
            if res_node is None:
                continue
            geom = res_node[0]
            item['aoi_src_data_extra'].append({"trans_id": access_node_id + "_" + time.strftime(
                '%Y%m%d', time.localtime(time.time())), "mark_wkt":geom})
        
        if len(item['aoi_src_data_extra']) > 0:
                create_plan_bid_list.append(item)
    
    json.dump(create_plan_bid_list, f_creatplan)
    f_creatplan.close()


if __name__ == '__main__':
    main()
    