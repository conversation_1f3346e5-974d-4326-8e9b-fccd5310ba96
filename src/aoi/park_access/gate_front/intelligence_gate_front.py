# -*- coding: utf-8 -*-
"""
门前停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid

import tqdm
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import unary_union
from loguru import logger
from shapely import wkt

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.gate_front import context
from src.aoi import utils
from src.aoi.park_access import park_utils
from src.aoi.park_access.gate_front import park_access_qc as qc
from src.aoi.park_access.gate_front import geometric
from src.aoi.park_access.gate_front.geometric import METER
import turing_work_ref
from src.aoi.park_access.intelligence_reduce import gate_front as intelligence_reduce

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000

# logger.remove()
log_path = "./log/underground_intelligence.log"
logger.add(log_path, rotation="10 MB")
IS_TEST = True


def cal_storefront_intelligence(ctx: context.Context):
    """
    出入口例行更新流程获取情报
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        get_near_street_poi_by_area,
        get_exp_traj_list,
        area_near_overlap_link,
        agg_exp_traj_end_points,
        end_traj_area_intersects,
        exp_traj_area_intersects,
        parking_traj_area_intersects,
        exp_end_points_relation_gate,
        area_near_gate_and_exp_traj,
        exp_traj_area_intersects_recall,
        area_intersects_link_with_short_edge,
        area_intersects_link_with_short_edge_v2,
        quarter_exp_traj_area_intersects,
        sync_park_access_filter,
        save_park_to_db_by_update,
    )
    pipe_list(ctx)


def input_end_traj(ctx: context.Context, proceed):
    """
    终点轨迹参考资料输出
    Args:
        ctx:
        proceed:

    Returns:

    """
    end_traj_list = turing_work_ref.end_traj_ref(ctx.near_street_poi_list, ctx.area)
    if not end_traj_list:
        logger.info(f"没有终点轨迹")
        return proceed()
    end_traj_union = unary_union(end_traj_list)
    # 先输出文件评估
    area_sp = wkt.loads(ctx.area)
    area_buffer_50m_sp = wkt.loads(ctx.area).buffer(20 / COORDINATE_FACTOR)
    with open("./log/end_traj_list.txt", "w") as f:
        f.write(ctx.bid + "\t" + ctx.area + "\t" + area_buffer_50m_sp.wkt + "\t" + wkt.dumps(end_traj_union) + "\n")
    return proceed()


def get_near_street_poi_list(ctx: context.Context, proceed):
    """
    查询临街POI
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 查询临街POI
    area_buffer_5m_sp = wkt.loads(ctx.area).buffer(5 / COORDINATE_FACTOR)
    near_street_poi_list = ctx.dao.poi_online_query.get_near_street_poi_by_area(area_buffer_5m_sp.wkt)
    if not near_street_poi_list or len(near_street_poi_list) <= 5:
        area_buffer_15m_sp = wkt.loads(ctx.area).buffer(15 / COORDINATE_FACTOR)
        near_street_poi_list = ctx.dao.poi_online_query.get_near_street_poi_by_area(area_buffer_15m_sp.wkt)
        if not near_street_poi_list:
            # 没有临街POI再buffer 5m
            area_buffer_20m_sp = wkt.loads(ctx.area).buffer(15 / COORDINATE_FACTOR)
            near_street_poi_list = ctx.dao.poi_online_query.get_near_street_poi_by_area(area_buffer_20m_sp.wkt)
            if not near_street_poi_list:
                logger.info(f"buffer 10m没有临街POI:")
    logger.info(f"临街POI:{near_street_poi_list}")
    ctx.near_street_poi_list = [item[0] for item in near_street_poi_list] if near_street_poi_list else []
    proceed()


def get_near_street_poi_by_area(ctx: context.Context, proceed):
    """
    通过面投影获取临街POI
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 查询临街POI
    area_buffer_50m_sp = wkt.loads(ctx.area).buffer(50 / COORDINATE_FACTOR)
    near_street_poi_list = ctx.dao.poi_online_query.get_near_street_poi_by_area(area_buffer_50m_sp.wkt)
    if not near_street_poi_list:
        logger.info(f"buffer 30m没有临街POI:")
        return proceed()
    logger.info(f"buff50m临街POI:{len(near_street_poi_list)}")
    poi_list = ctx.dao.poi_online_query.get_poi_by_bids_kv([item[0] for item in near_street_poi_list])
    if not poi_list:
        logger.info(f"buffer 30m没有临街POI:")
        return proceed()
    near_street_poi_list = []
    area_exterior_sp = wkt.loads(ctx.area)
    if area_exterior_sp.geom_type == "MultiPolygon":
        for area in _flat_geom(ctx.area):
            area_exterior_sp = area.buffer(5 / COORDINATE_FACTOR).exterior
            for poi_info in poi_list:
                # 横框高等级道路过滤
                if utils.is_over_high_road_park_access(poi_info['geom'], area.wkt):
                    continue
                # 查询POI在面上投影
                poi_sp = wkt.loads(poi_info['geom'])
                if not get_foot_point(poi_sp, area_exterior_sp):
                    logger.info(f"buffer 临街POI投影无效:{poi_info['bid']}:")
                    continue
                near_street_poi_list.append(poi_info['bid'])
    else:
        area_exterior_sp = wkt.loads(ctx.area).buffer(5 / COORDINATE_FACTOR).exterior
        for poi_info in poi_list:
            # 横框高等级道路过滤
            if utils.is_over_high_road_park_access(poi_info['geom'], ctx.area):
                continue
            # 查询POI在面上投影
            poi_sp = wkt.loads(poi_info['geom'])
            if not get_foot_point(poi_sp, area_exterior_sp):
                logger.info(f"buffer 临街POI投影无效:{poi_info['bid']}:")
                continue
            near_street_poi_list.append(poi_info['bid'])
    ctx.near_street_poi_list = near_street_poi_list
    logger.info(f"临街POI:{near_street_poi_list}")
    return proceed()


def _flat_geom(geom_wkt, geom_type='Polygon'):
    multi_type = f"MultiPolygon"

    def flat(geometry):
        g_type = geometry.geom_type
        if g_type == geom_type:
            yield geometry
        elif g_type == multi_type:
            yield from geometry.geoms
        elif g_type == "GeometryCollection":
            yield from (x for g in geometry.geoms for x in flat(g))

    geom = wkt.loads(geom_wkt) if type(geom_wkt) is str else geom_wkt
    return list(flat(geom))


def get_exp_traj_list(ctx: context.Context, proceed):
    """
    获取经验轨迹列表
    Args:
        ctx:
        proceed:

    Returns:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    exp_traj_list = []
    end_traj_list = []
    area_buffer_20m_sp = wkt.loads(ctx.area).buffer(20 / COORDINATE_FACTOR)
    for poi_bid in tqdm.tqdm(ctx.near_street_poi_list):
        # 经验轨迹
        current_exp_traj_list = utils.get_exp_traj_and_number_track_by_bid(poi_bid, 1, 0.9)
        if not current_exp_traj_list or len(current_exp_traj_list) <= 3:
            current_exp_traj_list = utils.get_exp_traj_and_number_track_by_bid(poi_bid, 0, 1)
        if current_exp_traj_list:
            exp_traj_list += current_exp_traj_list
        # 终点轨迹
        current_end_traj_list = utils.get_traj_list_by_bid_and_geom(poi_bid, area_buffer_20m_sp.wkt)
        if current_end_traj_list:
            end_traj_list += [wkt.loads(item[0]) for item in current_end_traj_list]
        else:
            try:
                current_end_traj_list = utils.get_dest_trajectory(poi_bid)
            except Exception as e:
                print(e)
                current_end_traj_list = [item['traj'] for item in
                                         current_exp_traj_list] if current_end_traj_list else []
            if current_end_traj_list:
                end_traj_list += current_end_traj_list
    ctx.traj.exp_traj_list = exp_traj_list
    ctx.traj.end_traj_list = end_traj_list
    for traj in exp_traj_list:
        print(traj['traj'])
    logger.info(f"经验轨迹量级:{len(ctx.traj.exp_traj_list)}")
    logger.info(f"终点轨迹量级:{len(ctx.traj.end_traj_list)}")
    proceed()


def agg_exp_traj_end_points(ctx: context.Context, proceed):
    """
    经验轨迹终点聚类
    Args:
        ctx:
        proceed:
    """
    if len(ctx.traj.exp_traj_list) == 0:
        return proceed()
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list if item['num_track'] >= 3]
    if len(exp_traj_list) <= 5:
        exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list]
    clusters = park_utils.get_clusters_by_traj(exp_traj_list, 15, 2)
    if not clusters:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    total_points_count = sum(len(value) for _, value in clusters.items())
    convex_hull_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= total_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"经验轨迹终点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        if len([item for item in convex_hull_list if convex_hull.distance(item) < 15 / COORDINATE_FACTOR_NEW]) > 0:
            continue
        convex_hull_list.append(convex_hull)
        if IS_TEST == '0':
            logger.info(f"经验轨迹终点聚类convex_hull: {convex_hull.wkt},points:{len(points)}")
        valid_intelligence = False
        # 获取聚类30m范围类大门
        nav_gates = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(10 / COORDINATE_FACTOR_NEW).wkt)
        if nav_gates:
            open_gates_node_ids = [item[0] for item in nav_gates]
            node_list = ctx.dao.road_dao.get_node_geom(open_gates_node_ids)
            node_info = min(node_list, key=lambda x: area_sp.distance(wkt.loads(x[0])))
            park_access = context.ParkingAccess()
            park_access.node_geom = node_info[0]
            park_access.node_id = node_info[1]
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG
            # 如果大门可入, 置信度高优
            if utils.is_enter_gate(node_info[1]):
                park_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            ctx.parking_access_list.append(park_access)
            valid_intelligence = True
            continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.buffer(10 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item[2] in [7, 8, 9]] if link_list else []
        if intersects_link_list:
            for link_info in intersects_link_list:
                kind = link_info[2]
                link_len = link_info[6]
                link_sp = wkt.loads(link_info[3])
                if not link_sp.buffer(1 / COORDINATE_FACTOR).overlaps(area_sp) \
                        and not link_sp.buffer(1 / COORDINATE_FACTOR).overlaps(area_buffer_5m_sp) \
                        and not link_sp.buffer(1 / COORDINATE_FACTOR).overlaps(area_buffer_10m_sp):
                    continue
                if link_len > 50 or (link_info[2] == 7 and not link_sp.buffer(1 / COORDINATE_FACTOR).overlaps(area_sp)):
                    continue
                parking_access = context.ParkingAccess()
                area_sp = wkt.loads(ctx.area)
                # link_node_id = park_utils.get_relation_node_by_link(link_info[0], area_sp.boundary.wkt)
                link_node_id = park_utils.get_relation_node_by_link(link_info[0],
                                                                    convex_hull.representative_point().wkt)
                node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
                parking_access.type = context.PARKING_ACCESS_TYPE_LINK
                parking_access.node_id = link_node_id
                parking_access.node_geom = node_geom_res[0][0]
                parking_access.link_id = link_info[0]
                parking_access.link_geom = link_info[3]
                parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG_LINK
                # logger.info(f"经验轨迹终点聚类link_id: {link_info[0]}, node_id: {link_node_id}, "
                #             f"center: {convex_hull.centroid.wkt},"
                #             f"convex_hull:{convex_hull.buffer(10 / COORDINATE_FACTOR_NEW).wkt}")
                # Link上不存在紧急大门, kind=8, 置信度高优
                if kind == 8:
                    nav_gates = ctx.dao.road_dao.get_nav_gate_by_geom(link_sp.buffer(3 / COORDINATE_FACTOR_NEW).wkt)
                    close_gate_list = [item[0] for item in nav_gates if utils.is_close_gate(item[0])]
                    if len(close_gate_list) == 0:
                        parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                ctx.parking_access_list.append(parking_access)
                valid_intelligence = True
        if not valid_intelligence:
            # 直接取聚类中心点
            park_access = context.ParkingAccess()
            park_access.node_geom = convex_hull.representative_point().wkt
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG_NODE
            if len(points) > 3:
                park_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            ctx.parking_access_list.append(park_access)
    return proceed()


def end_traj_area_intersects(ctx: context.Context, proceed):
    """
    终点轨迹与面相交点聚类策略扩招
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    filter_traj_list = [item for item in ctx.traj.end_traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        logger.info(f"终点轨迹缺失")
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_10m_sp.wkt, filter_traj_list)
    if not intersect_point_list:
        logger.info(f"终点轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    logger.info(f"\n终点轨迹与AOI交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 7, 3)
    if not clusters:
        logger.info(f"终点轨迹与AOI交割点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    convex_hull_wkt_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR_NEW]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        logger.info(f"终点轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.representative_point()
        # 关联大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item[2] in [8]] if link_list else []
        if intersects_link_list:
            link_res = min(intersects_link_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[3])))
            link_node_id = park_utils.get_relation_node_by_link(link_res[0], convex_center_sp.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_center_sp.wkt
            parking_access.link_id = link_res[0]
            parking_access.link_geom = link_res[3]
            parking_access.strategy = context.GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK
            ctx.parking_access_list.append(parking_access)
            continue

        # 直接取交割点
        park_access = context.ParkingAccess()
        park_access.node_geom = convex_hull.representative_point().wkt
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.GATE_FRONT_END_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(park_access)
    return proceed()


def end_traj_area_intersects_bak(ctx: context.Context, proceed):
    """
    终点轨迹与面相交点聚类策略
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    filter_traj_list = [item for item in ctx.traj.end_traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        logger.info(f"终点轨迹缺失")
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"终点轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    logger.info(f"\n终点轨迹与AOI交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 10, 3)
    if not clusters:
        logger.info(f"终点轨迹与AOI交割点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list]
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            kind = link_info[2]
            app = link_info[7]  # 是否可通行
            link_sp = wkt.loads(link_wkt)
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过停车场面分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(area_buffer_5m_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(area_buffer_5m_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            link_near_gate = park_utils.get_near_nav_gate_by_link(link_id)
            if link_near_gate:
                # logger.info(f"link上存在可通行的道路大门:{link_id}")
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = link_near_gate
            if kind not in [8, 9] or app != 1:
                continue
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)
    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    area_sp = wkt.loads(ctx.area)
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.9:
            continue
        valid_score_count += link_info["link_score"]
        parking_access = context.ParkingAccess()
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            if area_sp.distance(wkt.loads(node_geom_res[0][0])) < 30 / COORDINATE_FACTOR_NEW:
                continue
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        if area_sp.distance(wkt.loads(link_res[0])) < 30 / COORDINATE_FACTOR_NEW:
            continue
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], area_sp.boundary.wkt)
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK
        ctx.parking_access_list.append(parking_access)
    return proceed()


def exp_traj_area_intersects(ctx: context.Context, proceed):
    """
    经验轨迹与面相交点聚类策略
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list] if len(
        ctx.traj.exp_traj_list) > 0 else []
    if len(exp_traj_list) == 0:
        return proceed()
    filter_traj_list = [item for item in exp_traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"经验轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    # logger.info(f"经验轨迹与AOI交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 10, 2)
    if not clusters:
        logger.info(f"经验轨迹与AOI交割点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list]
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"\n\n与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            kind = link_info[2]
            app = link_info[7]  # 是否可通行
            link_sp = wkt.loads(link_wkt)
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过停车场面分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(area_buffer_5m_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(area_buffer_5m_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            link_near_gate = park_utils.get_near_nav_gate_by_link(link_id)
            if link_near_gate:
                # logger.info(f"link上存在可通行的道路大门:{link_id}")
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = link_near_gate
            if kind not in [8, 9] or app != 1:
                continue
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)
    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.9:
            continue
        valid_score_count += link_info["link_score"]
        parking_access = context.ParkingAccess()
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        area_sp = wkt.loads(ctx.area)
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], area_sp.boundary.wkt)
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK
        ctx.parking_access_list.append(parking_access)
    return proceed()


def exp_traj_area_intersects_recall(ctx: context.Context, proceed):
    """
    经验轨迹与面相交点聚类策略扩招
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list] if len(
        ctx.traj.exp_traj_list) > 0 else []
    if len(exp_traj_list) == 0:
        return proceed()
    filter_traj_list = [item for item in exp_traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"经验轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    logger.info(f"\n经验轨迹与AOI交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 10, 2)
    if not clusters:
        logger.info(f"经验轨迹点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    convex_hull_wkt_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 20 / COORDINATE_FACTOR_NEW]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        logger.info(f"经验与边框交割点聚类: {multipoint.wkt}")
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.representative_point()
        # 关联大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE_NEW
                ctx.parking_access_list.append(parking_access)
                continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item[2] in [8]] if link_list else []
        if intersects_link_list:
            link_res = min(intersects_link_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[3])))
            link_node_id = park_utils.get_relation_node_by_link(link_res[0], convex_center_sp.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_center_sp.wkt
            parking_access.link_id = link_res[0]
            parking_access.link_geom = link_res[3]
            parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK_NEW
            ctx.parking_access_list.append(parking_access)
            continue

        # 直接取交割点
        park_access = context.ParkingAccess()
        park_access.node_geom = convex_hull.representative_point().wkt
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(park_access)
    return proceed()


def area_intersects_link_with_short_edge(ctx: context.Context, proceed):
    """
    停车场短边与link相交
    Args:
        ctx:
        proceed:

    Returns:

    """
    area_sp = wkt.loads(ctx.area)
    # 计算外接矩形
    area_rotated_rectangle_sp = area_sp.minimum_rotated_rectangle
    if area_sp.area / area_rotated_rectangle_sp.area < 0.75:
        return proceed()
    # 获取两条短边
    rectangle_points = area_rotated_rectangle_sp.exterior.coords[:-1]
    edge_list = [LineString((rectangle_points[i], rectangle_points[(i + 1) % len(rectangle_points)]))
                 for i in range(len(rectangle_points))]
    sorted_edge_list = sorted(edge_list, key=lambda x: x.length)
    short_edge_list = sorted_edge_list[:2]
    # 计算面短边和link的交点
    for edge in short_edge_list:
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(edge.wkt)
        edge_is_entered = False
        for link_info in link_list:
            link_wkt = wkt.loads(link_info[3])
            if link_info[2] not in [8, 9]:
                continue
            if not area_sp.intersects(link_wkt):
                continue
            intersects_point = edge.intersection(link_wkt)
            if intersects_point.geom_type != "Point":
                continue
            # 取交割点
            edge_is_entered = True
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_geom = intersects_point.wkt
            parking_access.strategy = context.AREA_INTERSECTS_LINK_WITH_SHORT_EDGE
            ctx.parking_access_list.append(parking_access)
        if not edge_is_entered:
            center_point = edge.interpolate(0.5, normalized=True)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_geom = center_point.wkt
            parking_access.strategy = context.AREA_INTERSECTS_LINK_WITH_SHORT_EDGE
            ctx.parking_access_list.append(parking_access)
    return proceed()


def area_intersects_link_with_short_edge_v2(ctx: context.Context, proceed):
    """
    停车场两端点充作情报
    """
    central_line_wkt = ctx.dao.poi_online_query.get_central_line_by_bid(ctx.bid)
    if not central_line_wkt or wkt.loads(central_line_wkt).is_empty:
        logger.warning(f"没有找到中心线：'{ctx.bid}'")
        return proceed()

    geom = wkt.loads(ctx.area)
    central_line = wkt.loads(central_line_wkt)
    central_line = geometric.extend_linestring(central_line, 20 * METER)
    end_pts = geom.exterior.intersection(central_line)
    if end_pts.geom_type != "MultiPoint":
        logger.warning(f"中心线与边框的交点不为MultiPoint：'{ctx.bid}'")
        return proceed()

    end_pts = geometric.flat_point(end_pts)
    if len(end_pts) != 2:
        logger.warning(f"中心线与边框的交点不为2个：'{ctx.bid}'")
        return proceed()

    for point in end_pts:
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_geom = point.wkt
        parking_access.strategy = context.AREA_INTERSECTS_LINK_WITH_SHORT_EDGE_V2
        ctx.parking_access_list.append(parking_access)

    return proceed()


def parking_traj_area_intersects_bak(ctx: context.Context, proceed):
    """
    停车轨迹与面相交点聚类策略
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    # 取停车轨迹
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-30)).strftime("%Y-%m-%d")
    parking_traj_list = ctx.dao.traj_feature_dao.get_parking_points_traj_by_wkt(area_buffer_5m_sp.wkt, traj_time)
    if not parking_traj_list:
        logger.info(f"没有停车轨迹")
        return proceed()
    traj_list = [wkt.loads(item[0]) for item in parking_traj_list]
    filter_traj_list = [item for item in traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        logger.info(f"过滤后没有停车轨迹")
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"停车轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    # logger.info(f"轨迹与AOI交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 10, 3)
    if not clusters:
        logger.info(f"停车轨迹与AOI交割点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    traj_points_agg_achieve_list = []
    filter_node_ids = []
    filter_link_ids = []
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list]
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 取相交的link
        intersects_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.wkt)
        # logger.info(f"聚合点相交的link信息: {intersects_link_list}")
        if not intersects_link_list:
            continue
        agg_achieve_list = []
        for link_info in intersects_link_list:
            # logger.info(f"\n聚合点相交的link_id: {link_info[0]}")
            link_id = link_info[0]
            link_wkt = link_info[3]
            kind = link_info[2]
            app = link_info[7]  # 是否可通行
            link_sp = wkt.loads(link_wkt)
            traj_points_agg_achieve = {"link_id": link_id, "link_wkt": link_wkt, "link_score": 0, "node_id": ""}
            # 穿过停车场面分数 +1
            if link_sp.buffer(0.1 / COORDINATE_FACTOR).overlaps(area_buffer_5m_sp):
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"穿过AOI分数 +1")
            # 在AOI外部长度大于在内部长度 +1
            if link_sp.difference(area_buffer_5m_sp).length > link_sp.length / 2:
                traj_points_agg_achieve["link_score"] += 1
                # logger.info(f"在AOI外部长度大于在内部长度 +1")
            # link上存在可通行的道路大门+3
            link_buffer_5m_sp = link_sp.buffer(5 / COORDINATE_FACTOR)
            link_near_gate = park_utils.get_near_nav_gate_by_link(link_id)
            if link_near_gate:
                # logger.info(f"link上存在可通行的道路大门:{link_id}")
                traj_points_agg_achieve["link_score"] += 3
                traj_points_agg_achieve["node_id"] = link_near_gate
            if kind not in [8, 9] or app != 1:
                continue
            # 经验轨迹通过 +2
            intersects_exp_traj = [x for x in exp_traj_list if link_buffer_5m_sp.intersects(x)]
            if len(intersects_exp_traj) > 0:
                traj_points_agg_achieve["link_score"] += 2
                # logger.info(f"经验轨迹通过 +2")
            agg_achieve_list.append(traj_points_agg_achieve)
        # 获取得分最高的Node或者link
        max_score_result = max(agg_achieve_list, key=lambda item: item["link_score"]) if len(
            agg_achieve_list) > 0 else None
        # logger.info(f"最优聚合成果: {max_score_result}")
        if max_score_result and (
                max_score_result["node_id"] != "" and max_score_result["node_id"] not in filter_node_ids
                or max_score_result["link_id"] not in filter_link_ids):
            traj_points_agg_achieve_list.append(max_score_result)
        filter_link_ids = filter_link_ids + [item["link_id"] for item in agg_achieve_list]
        filter_node_ids = filter_node_ids + [item["node_id"] for item in agg_achieve_list if
                                             item["node_id"] != ""]
    # logger.info(f"聚合成果: {traj_points_agg_achieve_list}")
    if len(traj_points_agg_achieve_list) == 0:
        return proceed()
    # logger.info(f"link_result: {traj_points_agg_achieve_list}")
    traj_points_agg_achieve_list = sorted(traj_points_agg_achieve_list, key=lambda item: item["link_score"],
                                          reverse=True)
    # 取分数占比前80%
    score_count = sum([item["link_score"] for item in traj_points_agg_achieve_list])
    valid_score_count = 0
    for link_info in traj_points_agg_achieve_list:
        if link_info["link_score"] == 0 or valid_score_count / score_count >= 0.9:
            continue
        valid_score_count += link_info["link_score"]
        parking_access = context.ParkingAccess()
        if link_info["node_id"] != "":
            node_geom_res = ctx.dao.road_dao.get_node_geom([link_info["node_id"]])
            parking_access.type = context.PARKING_ACCESS_TYPE_NODE
            parking_access.node_id = link_info["node_id"]
            parking_access.node_geom = node_geom_res[0][0]
            parking_access.strategy = context.GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE
            ctx.parking_access_list.append(parking_access)
            continue
        link_res = ctx.dao.road_dao.get_nav_link_by_link_ids([link_info["link_id"]])[0]
        area_sp = wkt.loads(ctx.area)
        link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], area_sp.boundary.wkt)
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_res[0]
        parking_access.strategy = context.GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK
        ctx.parking_access_list.append(parking_access)
    return proceed()


def parking_traj_area_intersects(ctx: context.Context, proceed):
    """
    经验轨迹与面相交点聚类策略扩招
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    # 取停车轨迹
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-180)).strftime("%Y-%m-%d")
    parking_traj_list = ctx.dao.traj_feature_dao.get_parking_points_traj_by_wkt(area_buffer_5m_sp.wkt, traj_time)
    if not parking_traj_list:
        logger.info(f"没有停车轨迹")
        return proceed()
    traj_list = [wkt.loads(item[0]) for item in parking_traj_list]
    filter_traj_list = [item for item in traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        logger.info(f"过滤后没有停车轨迹")
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"停车轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    logger.info(f"\n停车轨迹与面交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 7, 3)
    if not clusters:
        logger.info(f"停车轨迹与AOI交割点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    convex_hull_wkt_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR_NEW]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        logger.info(f"停车轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.representative_point()
        # 关联大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item[2] in [8]] if link_list else []
        if intersects_link_list:
            link_res = min(intersects_link_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[3])))
            link_node_id = park_utils.get_relation_node_by_link(link_res[0], convex_center_sp.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_center_sp.wkt
            parking_access.link_id = link_res[0]
            parking_access.link_geom = link_res[3]
            parking_access.strategy = context.GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK
            ctx.parking_access_list.append(parking_access)
            continue

        # 直接取交割点
        park_access = context.ParkingAccess()
        park_access.node_geom = convex_hull.representative_point().wkt
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(park_access)
    return proceed()


def exp_end_points_relation_gate(ctx: context.Context, proceed):
    """
    根据经验轨迹终点提召
    Args:
        ctx:l
        proceed:

    Returns:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    # max_num_traj = max(ctx.traj.exp_traj_list, key=lambda x: x['num_track'])
    for traj in ctx.traj.exp_traj_list:
        if traj['num_track'] < 3 and traj['cuid_repetition_rate'] == 0:
            continue
        end_point = Point(traj['traj'].coords[-1])
        gates = ctx.dao.road_dao.get_nav_gate_by_geom(end_point.buffer(15 / COORDINATE_FACTOR).wkt)
        exist_valid_intelligence = False  # 是否有效情报
        if gates:
            open_gates_node_ids = [x[0] for x in gates if utils.is_enter_gate(x[0])]
            if len(open_gates_node_ids) == 0:
                continue
            # 多个大门, 找最近的大门
            node_list = ctx.dao.road_dao.get_node_geom(open_gates_node_ids)
            if not node_list:
                continue
            min_distance_node_res = min(node_list, key=lambda x: area_sp.distance(wkt.loads(x[0])))
            # 如果大门距离Link较远, 则不要
            if area_sp.distance(wkt.loads(min_distance_node_res[0])) > 20 / COORDINATE_FACTOR:
                continue
            park_access = context.ParkingAccess()
            park_access.node_geom = min_distance_node_res[0]
            park_access.node_id = min_distance_node_res[1]
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_GATE
            # 经验轨迹用户重复率大于0 or 大门非紧急则高置性
            if traj['cuid_repetition_rate'] > 0 or utils.is_enter_gate(min_distance_node_res[1]):
                park_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            ctx.parking_access_list.append(park_access)
            exist_valid_intelligence = True
        else:
            area_sp = wkt.loads(ctx.area)
            link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(end_point.buffer(10 / COORDINATE_FACTOR).wkt)
            intersects_link_list = [item for item in link_list if item[2] in [8, 9]] if link_list else []
            if intersects_link_list:
                for link_info in intersects_link_list:
                    kind = link_info[2]
                    link_sp = wkt.loads(link_info[3])
                    if area_sp.overlaps(link_sp.buffer(0.1 / COORDINATE_FACTOR)):
                        parking_access = context.ParkingAccess()
                        area_sp = wkt.loads(ctx.area)
                        link_node_id = park_utils.get_relation_node_by_link(link_info[0], end_point.wkt)
                        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
                        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
                        parking_access.node_id = link_node_id
                        parking_access.node_geom = node_geom_res[0][0]
                        parking_access.link_id = link_info[0]
                        parking_access.link_geom = link_info[3]
                        parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_LINK
                        # Link上不存在紧急大门, kind=8, 置信度高优
                        if kind == 8:
                            nav_gates = ctx.dao.road_dao.get_nav_gate_by_geom(
                                link_sp.buffer(3 / COORDINATE_FACTOR_NEW).wkt)
                            close_gate_list = [item[0] for item in nav_gates if utils.is_close_gate(item[0])]
                            if len(close_gate_list) == 0 and traj['cuid_repetition_rate'] > 0:
                                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                        ctx.parking_access_list.append(parking_access)
                        exist_valid_intelligence = True
        if not exist_valid_intelligence and traj['cuid_repetition_rate'] > 0:
            park_access = context.ParkingAccess()
            park_access.node_geom = end_point.wkt
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_GATE_HIGH_CONFIDENCE
            park_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
            ctx.parking_access_list.append(park_access)
    return proceed()


def area_near_gate_and_exp_traj(ctx: context.Context, proceed):
    """
    根据经验轨迹终点提召, 经验轨迹终点开放大门,大门距离边框10m范围内
    Args:
        ctx:l
        proceed:

    Returns:

    """
    if len(ctx.near_street_poi_list) == 0 or len(ctx.traj.exp_traj_list) == 0:
        return proceed()
    # 获取经验轨迹终点
    exp_end_points = []
    for traj in ctx.traj.exp_traj_list:
        exp_end_points.append(Point(traj['traj'].coords[-1]))

    area_sp = wkt.loads(ctx.area)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR_NEW)
    # 获取边框附近10m范围内开放大门
    gates = ctx.dao.road_dao.get_nav_gate_by_geom(area_buffer_10m_sp.wkt)
    if not gates:
        return proceed()
    open_gates_node_ids = [x[0] for x in gates if utils.is_enter_gate(x[0])]
    if len(open_gates_node_ids) == 0:
        return proceed()
    area_boundary = area_sp.boundary
    for node_id in open_gates_node_ids:
        node_geom_res = ctx.dao.road_dao.get_node_geom([node_id])
        if not node_geom_res:
            continue
        node_sp = wkt.loads(node_geom_res[0][0])
        if node_sp.distance(area_boundary) < 5 / COORDINATE_FACTOR_NEW:
            park_access = context.ParkingAccess()
            park_access.node_geom = node_sp.wkt
            park_access.node_id = node_geom_res[0][1]
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.AREA_NEAR_OPEN_GATE
            ctx.parking_access_list.append(park_access)
            continue
        if node_sp.distance(area_boundary) > 10 / COORDINATE_FACTOR_NEW:
            continue
        # 判断经验轨迹终点距离大门30m范围内
        if len([item for item in exp_end_points if node_sp.distance(item) < 30 / COORDINATE_FACTOR_NEW]) == 0:
            continue
        park_access = context.ParkingAccess()
        park_access.node_geom = node_sp.wkt
        park_access.node_id = node_geom_res[0][1]
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.AREA_NEAR_GATE_AND_EXP_TRAJ
        ctx.parking_access_list.append(park_access)
    return proceed()


def area_near_overlap_link(ctx: context.Context, proceed):
    """
    取通过面附近Link, 8级路可通行
    Args:
        ctx:
        proceed:

    Returns:

    """
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    # 取相交的link
    overlaps_link_list = []
    area_buffer_10m_link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt_new(area_buffer_10m_sp.wkt)
    if area_buffer_10m_link_list:
        overlaps_link_list += area_buffer_10m_link_list
    if len(overlaps_link_list) == 0:
        return proceed()
    all_link_ids = []
    overlaps_link_ids_list = [item['link_id'] for item in overlaps_link_list]
    for link_info in overlaps_link_list:
        if link_info['link_id'] in all_link_ids:
            continue
        all_link_ids.append(link_info['link_id'])
        if link_info['kind'] not in [8, 9] or link_info['app'] != 1:
            continue
        link_sp = wkt.loads(link_info['geom'])
        if not link_sp.buffer(3 / COORDINATE_FACTOR).overlaps(area_sp) \
                and not link_sp.buffer(3 / COORDINATE_FACTOR).overlaps(area_buffer_5m_sp) \
                and not link_sp.buffer(3 / COORDINATE_FACTOR).overlaps(area_buffer_10m_sp):
            continue
        # 8级路必须与高等级道路相连
        s_high_link_list = ctx.dao.road_dao.get_nav_link_by_node_new([link_info['s_nid']], None)
        e_high_link_list = ctx.dao.road_dao.get_nav_link_by_node_new([link_info['e_nid']], None)
        if not s_high_link_list and not e_high_link_list:
            continue
        high_link_list = []
        if s_high_link_list:
            high_link_list += s_high_link_list
        if e_high_link_list:
            high_link_list += e_high_link_list

        # 8级路与低等级(8, 9)道路相连，且低等级道路较短，延伸此8级路
        low_link_list = [item for item in high_link_list if item['kind'] > 7]
        if len(low_link_list) > 0:
            # 超参数：低等级道路较短
            low_link_length = 20
            low_link_ids = [item for item in low_link_list if item['len'] < low_link_length]
            if len(low_link_ids) != 0:
                s_high_link_list_for_low_total = []
                e_high_link_list_for_low_total = []
                for low_link in low_link_ids:
                    # 防止重复计算同一link
                    if low_link['link_id'] in overlaps_link_ids_list:
                        continue
                    # 和原始8级路节点去重
                    if low_link['s_nid'] not in (link_info['s_nid'], link_info['e_nid']):
                        s_high_link_list_for_low = ctx.dao.road_dao.get_nav_link_by_node_new([low_link['s_nid']], None)
                    else:
                        s_high_link_list_for_low = []
                    if low_link['e_nid'] not in (link_info['s_nid'], link_info['e_nid']):
                        e_high_link_list_for_low = ctx.dao.road_dao.get_nav_link_by_node_new([low_link['e_nid']], None)
                    else:
                        e_high_link_list_for_low = []
                    if not s_high_link_list_for_low and not e_high_link_list_for_low:
                        continue
                    s_high_link_list_for_low_total.extend(s_high_link_list_for_low)
                    e_high_link_list_for_low_total.extend(e_high_link_list_for_low)
                if s_high_link_list_for_low_total:
                    s_high_link_list.extend(s_high_link_list_for_low_total)
                if e_high_link_list_for_low_total:
                    e_high_link_list.extend(e_high_link_list_for_low_total)
            # 重新生成相交link列表
            high_link_list = s_high_link_list + e_high_link_list

        high_link_ids = [item for item in high_link_list if item['kind'] <= 7]
        if len(high_link_ids) == 0:
            # 如果是8级路链接的9(辅路), 且经验轨迹通过, 也作为情报点下发
            map_side_road_link_list = [item for item in high_link_list if item['kind'] == 9 and item['form'] == '34']
            if len(map_side_road_link_list) == 0 or link_info['kind'] == 9:
                continue
            logger.info(f"8级路与辅路相连召回:{map_side_road_link_list}")
        s_node_relate_high_link = True if [item for item in s_high_link_list if item['kind'] <= 7] else False
        # link_node_id = park_utils.get_relation_node_by_link(link_info["link_id"], area_sp.boundary.wkt)
        link_node_id = link_info['s_nid'] if s_node_relate_high_link else link_info['e_nid']
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access = context.ParkingAccess()
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info["link_id"]
        parking_access.link_geom = link_info['geom']
        parking_access.strategy = context.GATE_FRONT_AREA_NEAR_LINK
        ctx.parking_access_list.append(parking_access)
    return proceed()


def quarter_exp_traj_area_intersects(ctx: context.Context, proceed):
    """
    经验轨迹与面相交点聚类策略扩招
    Args:
        ctx:
        proceed:

    """
    if len(ctx.near_street_poi_list) == 0:
        return proceed()
    # 获取季度经验轨迹
    quarter_exp_traj_list = []
    for poi_bid in tqdm.tqdm(ctx.near_street_poi_list):
        # 经验轨迹
        current_exp_traj_list = utils.get_quarter_exp_traj_and_number_track_by_bid(poi_bid, 2, 0.9)
        if not current_exp_traj_list or len(current_exp_traj_list) <= 3:
            current_exp_traj_list = utils.get_quarter_exp_traj_and_number_track_by_bid(poi_bid, 0, 1)
        if current_exp_traj_list:
            quarter_exp_traj_list += current_exp_traj_list
    logger.info(f"\n季度经验轨迹: {len(quarter_exp_traj_list)}")
    for exp_traj in quarter_exp_traj_list:
        print(exp_traj['traj'])
    if len(quarter_exp_traj_list) == 0:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    area_buffer_5m_sp = area_sp.buffer(5 / COORDINATE_FACTOR)
    area_buffer_10m_sp = area_sp.buffer(10 / COORDINATE_FACTOR)
    exp_traj_list = [item['traj'] for item in quarter_exp_traj_list]
    if len(exp_traj_list) == 0:
        return proceed()
    filter_traj_list = [item for item in exp_traj_list if item.intersects(area_buffer_10m_sp)]
    if len(filter_traj_list) == 0:
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(area_buffer_5m_sp.wkt, filter_traj_list,
                                                                               False)
    if not intersect_point_list:
        logger.info(f"季度经验轨迹与面没有交点;traj_num:{len(filter_traj_list)}")
        return proceed()
    logger.info(f"\n季度经验轨迹与边框交割点集: {intersect_point_list}")
    clusters = park_utils.clusters_by_point_list(intersect_point_list, 10, 3)
    if not clusters:
        logger.info(f"经验轨迹点聚类失败")
        return proceed()

    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    valid_points_count = sum(len(value) for value in clusters.values())
    convex_hull_wkt_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 20 / COORDINATE_FACTOR_NEW]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        logger.info(f"季度经验与边框交割点聚类: {multipoint.wkt}")
        convex_hull_wkt_list.append(convex_hull)
        convex_center_sp = convex_hull.representative_point()
        # 关联大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[0])))
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_GATE
                ctx.parking_access_list.append(parking_access)
                continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt(convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item[2] in [8]] if link_list else []
        if intersects_link_list:
            link_res = min(intersects_link_list, key=lambda x: convex_center_sp.distance(wkt.loads(x[3])))
            link_node_id = park_utils.get_relation_node_by_link(link_res[0], convex_center_sp.wkt)
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_id = link_node_id
            parking_access.node_geom = convex_center_sp.wkt
            parking_access.link_id = link_res[0]
            parking_access.link_geom = link_res[3]
            parking_access.strategy = context.GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_LINK
            ctx.parking_access_list.append(parking_access)
            continue

        # 直接取交割点
        park_access = context.ParkingAccess()
        park_access.node_geom = convex_hull.representative_point().wkt
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_NODE
        ctx.parking_access_list.append(park_access)
    return proceed()


def get_foot_point(point: Point, line: LineString):
    """
    获取点在线上的投影点
    Args:
        point:
        line:

    Returns:

    """
    # FIXME: 若 dir 不为 1，即有方向，则只允许 POI 在 link 的右侧
    dist = line.project(point)
    foot_point = line.interpolate(dist)
    if 0 < dist < line.length:
        return foot_point

    pts = [(x, y) for x, y in line.coords]
    if abs(dist) < 1e-8:
        p0, p1 = pts[0], pts[1]
    elif abs(dist - line.length) < 1e-8:
        p0, p1 = pts[-1], pts[-2]
    else:
        logger.error(f"foot point not on the line")
        return None

    v_poi = np.array([point.x, point.y]) - np.array([foot_point.x, foot_point.y])
    v_poi = v_poi / np.linalg.norm(v_poi)
    v_line = np.array(p1) - np.array(p0)
    v_line = v_line / np.linalg.norm(v_line)
    if abs(np.dot(v_poi, v_line)) < 1e-8:
        return foot_point
    else:
        return None


def relation_manual_work_achieve(ctx: context.Context, proceed):
    """
    关联人工作业结果
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 获取人工出入口成果数据
    gate_front_achieve_list = ctx.dao.poi_online_query.get_gate_front_achieve()
    short_node_ids = [item['node_id'] for item in gate_front_achieve_list if item['node_id']]
    short_link_ids = [item['link_id'] for item in gate_front_achieve_list if item['link_id']]
    long_node_list = long_link_list = []
    if len(short_node_ids) > 0:
        long_node_res = ctx.dao.trans_dao.get_long_node_by_short_node_ids(short_node_ids)
        long_node_ids = [item[0] for item in long_node_res if long_node_res]
        long_node_list = ctx.dao.road_dao.get_node_geom(long_node_ids) if long_node_ids else []
    if len(short_link_ids) > 0:
        long_link_res = ctx.dao.trans_dao.get_long_link_by_short_link_ids(short_link_ids)
        long_link_ids = [item[0] for item in long_link_res if long_link_res]
        long_link_list = ctx.dao.road_dao.get_nav_link_by_link_ids(long_link_ids) if long_link_ids else []
    area_sp = wkt.loads(ctx.area)
    area_buffer_6m_sp = area_sp.buffer(6 / COORDINATE_FACTOR_NEW)
    # node关联
    for node_info in long_node_list:
        node_sp = wkt.loads(node_info[0])
        if node_sp.intersects(area_buffer_6m_sp) or node_sp.intersects(area_sp):
            park_access = context.ParkingAccess()
            park_access.node_geom = node_info[0]
            park_access.node_id = node_info[1]
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.MANUAL_WORK_ACHIEVE_RELATION_GATE
            ctx.parking_access_list.append(park_access)
    # link关联
    for link_info in long_link_list:
        parking_access = context.ParkingAccess()
        link_node_id = park_utils.get_relation_node_by_link(link_info[3], area_sp.boundary.wkt)
        node_geom_res = ctx.dao.road_dao.get_node_geom([link_node_id])
        parking_access.type = context.PARKING_ACCESS_TYPE_LINK
        parking_access.node_id = link_node_id
        parking_access.node_geom = node_geom_res[0][0]
        parking_access.link_id = link_info[3]
        parking_access.link_geom = link_info[0]
        parking_access.strategy = context.MANUAL_WORK_ACHIEVE_RELATION_LINK
        ctx.parking_access_list.append(parking_access)
    return proceed()


def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter:{len(ctx.parking_access_list)}")
    qc.park_access_qc(ctx)
    return proceed()


def sync_park_access_filter(ctx: context.Context, proceed):
    """
    例行流程出入口情报过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin sync_park_access_filter:{len(ctx.parking_access_list)}")
    qc.sync_auto_park_access_qc(ctx)
    return proceed()


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in sorted(ctx.parking_access_list, key=lambda x: x.is_valid):
            logger.info(f"是否有效:{item.is_valid},关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}")
        return proceed()
    exist_valid_park_access = False
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        exist_valid_park_access = True
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        jdq_batch = 'JQDMQP2024101601'
        data = {
            'bid': ctx.bid,
            'strategy': park_access.strategy,
            'type': 'park_access_gate_front',
            'batch_number': f"park_access_add_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id,
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': jdq_batch,
            'outside_task_id': ctx.task_id,
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
        }
        ctx.dao.poi_online_rw.insert_gate_front_intelligence(data)
    if exist_valid_park_access:
        ctx.dao.poi_online_rw.update_park_storefront_intelligence_status_by_face_id(ctx.park_intelligence_id,
                                                                                    'park_access_pushed')
    else:  # 未匹配到有效出入口,直接推停车场,出入口情报取中心点
        area_sp = wkt.loads(ctx.area)
        center_point = area_sp.centroid
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        data = {
            'bid': ctx.bid,
            'strategy': 'NO_PARK_ACCESS',
            'type': 'park_access_gate_front',
            'batch_number': f"park_access_add_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': center_point.wkt,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id,
            'status': 'INIT',
            'resp': '',
            'node_id': '',
            'link_id': '',
            'jdq_batch': '',
            'road_relation': '',
            'outside_task_id': ctx.task_id,
        }
        ctx.dao.poi_online_rw.insert_gate_front_intelligence(data)
        ctx.dao.poi_online_rw.update_park_storefront_intelligence_status_by_face_id(ctx.park_intelligence_id,
                                                                                    'park_access_pushed')
    proceed()


def save_park_to_db_by_add(ctx: context.Context, proceed):
    """
    停车场面新增流程,出入口情报入库
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in sorted(ctx.parking_access_list, key=lambda x: x.is_valid):
            logger.info(f"TEST是否有效:{item.is_valid},关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, node_geom:\t{item.node_geom}\n")
        return proceed()
    intelligence_ids = []
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        data = {
            'bid': ctx.bid,
            'strategy': park_access.strategy,
            'type': 'park_access_gate_front',
            'batch_number': f"park_access_gate_front_intelligence_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id,
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'outside_task_id': ctx.task_id,  # 约定的批次号, 用于出入口更新
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
        }
        intelligence_ids.append(ctx.dao.poi_online_rw.insert_gate_front_intelligence(data))
    # 未匹配到有效出入口,且存在复用出入口直接复用,直接推停车场,出入口情报取中心点
    if len(intelligence_ids) == 0 and (not ctx.origin_intelligence_ids or len(ctx.origin_intelligence_ids) == 0):
        area_sp = wkt.loads(ctx.area)
        center_point = area_sp.centroid
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        data = {
            'bid': ctx.bid,
            'strategy': 'NO_PARK_ACCESS',
            'type': 'park_access_gate_front',
            'batch_number': f"park_access_gate_front_intelligence_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': center_point.wkt,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id,
            'status': 'INIT',
            'resp': '',
            'node_id': '',
            'link_id': '',
            'jdq_batch': '',
            'road_relation': '',
            'outside_task_id': ctx.task_id,
        }
        intelligence_ids.append(ctx.dao.poi_online_rw.insert_gate_front_intelligence(data))
    # 更新差分任务状态
    status = "GATE_DIFFED_MATCHING_FAILED"
    if ctx.origin_intelligence_ids and len(ctx.origin_intelligence_ids) > 0 and len(intelligence_ids) > 0:
        status = 'GATE_DIFFED_PARTIAL_REUSE'
    elif ctx.origin_intelligence_ids and len(ctx.origin_intelligence_ids) > 0 and len(intelligence_ids) == 0:
        status = 'GATE_DIFFED_REUSE'
    elif (not ctx.origin_intelligence_ids or len(ctx.origin_intelligence_ids) == 0) and len(intelligence_ids) > 0:
        status = 'GATE_DIFFED_MATCHING'
    logger.info(f"更新差分任务状态:{status}, ctx.origin_intelligence_ids:{ctx.origin_intelligence_ids},"
                f"intelligence_ids:{intelligence_ids}")
    if ctx.origin_intelligence_ids and len(ctx.origin_intelligence_ids) > 0:
        intelligence_ids = list(set(intelligence_ids + ctx.origin_intelligence_ids))
    ctx.dao.poi_online_rw.update_park_storefront_strategy_diff_status_by_face_id(face_ids=[ctx.park_intelligence_id],
                                                                                 task_ids=[ctx.task_id],
                                                                                 status=status,
                                                                                 intelligence_ids=json.dumps(
                                                                                     intelligence_ids))
    proceed()


def save_park_to_db_by_update(ctx: context.Context, proceed):
    """
    出入口例行更新入库
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in sorted(ctx.parking_access_list, key=lambda x: x.is_valid):
            logger.info(f"TEST是否有效:{item.is_valid},关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, node_geom\t{item.node_geom}")
        valid_geom_list = [item.node_geom for item in ctx.parking_access_list if item.is_valid]
        invalid_geom_list = [item.node_geom for item in ctx.parking_access_list if not item.is_valid]
        logger.info(f"有效:{valid_geom_list}, 无效:{invalid_geom_list}")
        return proceed()
    exist_valid_park_access = False
    is_exist_intelligences = ctx.dao.poi_online_query.get_pushed_intelligence_by_geom(bids=[ctx.bid])
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        exist_valid_park_access = True
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        ctx.batch = f"park_access_gate_front_intelligence_add_{time.strftime('%Y%m%d', time.localtime())}"
        data = {
            'bid': ctx.bid,
            'strategy': park_access.strategy,
            'type': 'park_access_gate_front',
            'batch_number': ctx.batch,
            'geom': park_access.node_geom,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id if ctx.park_intelligence_id else '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'outside_task_id': ctx.task_id,
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
        }
        ctx.new_intelligence_ids.append(ctx.dao.poi_online_rw.insert_gate_front_intelligence(data))
    # 判断停车场是否存在挖掘出入口情报, 如果不存在，当做无口面直接推送
    exist_park_access_intelligence = ctx.dao.poi_online_query.get_pushed_intelligence_by_geom(bids=[ctx.bid])
    if not exist_valid_park_access and not exist_park_access_intelligence and ctx.park_type == 'new':
        # 未匹配到有效出入口,直接推停车场,出入口情报取中心点
        area_sp = wkt.loads(ctx.area)
        center_point = area_sp.centroid
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        data = {
            'bid': ctx.bid,
            'strategy': 'NO_PARK_ACCESS',
            'type': 'park_access_gate_front',
            'batch_number': ctx.batch,
            'geom': center_point.wkt,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id if ctx.park_intelligence_id else '',
            'status': 'INIT',
            'resp': '',
            'node_id': '',
            'link_id': '',
            'jdq_batch': '',
            'road_relation': '',
            'outside_task_id': ctx.task_id,
        }
        ctx.new_intelligence_ids.append(ctx.dao.poi_online_rw.insert_gate_front_intelligence(data))

    # 停车场状态更新为情报已挖掘
    if exist_valid_park_access:
        rollback_reason = 'expansion_intelligence' if is_exist_intelligences else ''
        ctx.dao.poi_online_rw.update_park_storefront_prod_parking_by_bid('PARK_ACCESS_INTELLIGENCE', ctx.bid,
                                                                         rollback_reason)
    # 如果面更新不存在且不存在情报, 直接更新为read_only
    if not exist_valid_park_access and is_exist_intelligences:
        ctx.dao.poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status('READY', 'READY_ONLINE', [ctx.bid])
    proceed()


def main(bids: list, batch='beijing_20241023_without_beijing_haidian_20241022_20241110'):
    """
    门前停车场情报产出
    Args:
        bids:

    Returns:

    """
    # 获取停车场信息
    bids = bids.tolist() if len(bids) > 0 else None
    status = 'READY'
    city = None
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_park_storefront_result(bids=bids, status=status, city=city)
    # parking_list = poi_online_rw.get_storefront_intelligence(status, face_ids=bids)
    if not parking_list:
        logger.debug("No parking found")
        return
    logger.info(f"开始挖掘门前出入口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        try:
            parking_info = [parking['face_id'], parking['geom'], parking['bid']]
            ctx = context.Context(parking_info, task_id=parking['id'])
            ctx.park_type = parking['type']
            cal_storefront_intelligence(ctx)
            # 情报去无效
            intelligence_reduce.main([parking['bid']])
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[0]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
