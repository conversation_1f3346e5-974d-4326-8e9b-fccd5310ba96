#encoding=gbk
"""
Locate the City by the Shape of the Building
<EMAIL> at 20171012
"""

import os
import sys
import shapely.wkt
import shapely.geometry

def Main():
    """
    Main Function
    """
    infile = open(sys.argv[1])
    city_file = open(sys.argv[2])
    outfile = open(sys.argv[3], "w")
    CityMap = {}

    for line in city_file.readlines():
        try:
            line = line.strip('\n\r')
            name = line.split('\t')[0]
            coor_list = []
            shape = line.split('\t')[3]
            for i in shape.split(';'):
                if i != "" and i is not None:
                    coor_list.append((float(i.split(",")[0]), float(i.split(",")[1])))
            CityMap[name] = shapely.geometry.Polygon(coor_list)
        except Exception as e:
            continue
    city_file.close()

    cnt = 1
    while 1:
        try:
            print cnt
            cnt += 1
            line = infile.readline().strip('\n\r')
            if line is None or line == "":
                break
            center = shapely.wkt.loads(line.split('\t')[5]).centroid
            for city in CityMap.keys():
                if center.intersects(CityMap[city]):
                    print >> outfile, "%s" % (line + "\t" + city)
                    break
        except Exception as e:
            continue    

if __name__ == '__main__':
    Main()
