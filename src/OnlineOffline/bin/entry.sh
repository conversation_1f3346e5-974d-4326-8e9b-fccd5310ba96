#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source $CUR_DIR"/conf.sh"

function SplitCity(){
    rm ${SplitPath}/*;
    local INFILE=$1
    split_line_cnt=` wc -l < ${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}' `
    split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

    for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
      ${PYTHONBIN} ${BinPath}/CityMap.py ${ONE_PART} ${CITYFILE} ${ONE_PART}.out > ${ONE_PART}.log &
    done    
    wait
}

function MidOnline(){
    >ok;
	local INFILE=$1;
	rm ${SplitPath}/*;

    split_line_cnt=` wc -l < ${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}' `
    split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

    for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
            ${PHPPATH} ${BinPath}/MidOnline.php ${ONE_PART} & 
    done    
    wait
}

function MidOffline(){
    >ok;
	local INFILE=$1;
	rm ${SplitPath}/*;

    split_line_cnt=` wc -l < ${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}' `
    split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

    for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
            ${PHPPATH} ${BinPath}/MidOffline.php ${ONE_PART} & 
    done    
    wait	
}

function BidOnline(){
    >ok;
	local INFILE=$1;
	rm ${SplitPath}/*;

    split_line_cnt=` wc -l < ${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}' `
    split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

    for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
            ${PHPPATH} ${BinPath}/bid2midoffline.php ${ONE_PART} &
	#${PHPPATH} ${BinPath}/BidUpdate.php ${ONE_PART} "jichudiwu" "changdi_zhudian" & 
    done    
    wait	
}

function BidOffline(){
    >ok;
	local INFILE=$1;
	rm ${SplitPath}/*;

    split_line_cnt=` wc -l < ${INFILE} | awk '{print sprintf("%.0f", ($1 / 20))}' `
    split -l ${split_line_cnt} ${INFILE} ${SplitPath}/part.input.

    for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
            ${PHPPATH} ${BinPath}/bid2midoffline.php ${ONE_PART} &
	#${PHPPATH} ${BinPath}/BidUpdate.php ${ONE_PART} "jichudiwu" "changdi_zhudian" & 
    done    
    wait	
}

function ClearEnv(){
	rm ${OnlinePath}/*;
	rm ${OfflinePath}/*;
	rm ${FinalPath}/*;
}

function GetData(){
  # data from aoi/bin/entry.sh
	cp ${DATA_PATH}/aoi/online/mid.aoi.online ${OnlinePath};
	cp ${DATA_PATH}/aoi/online/mid.aoi.offline ${OfflinePath};

    cat ${SplitPath}/part.input.a?.out | awk -F "\t" '
    ARGIND==1{
        mid = $1;
        city = "";

        if(NF == 10)
            city = $NF;
        map[mid] = city;
    }
    ARGIND==2{
        mid = $1;
        if(mid in map)
            print $0 "\t" map[mid];
        else
            print $0 "\t" "";
    }' - ${DATA_PATH}/building/online/mid.building.online > ${OnlinePath}/mid.building.online;

	cp ${DATA_PATH}/building/online/bid.building.online ${OnlinePath};
	cp ${DATA_PATH}/building/online/mid.building.offline ${OfflinePath};
	cp ${DATA_PATH}/building/online/bid.building.offline ${OfflinePath};

	awk -F'\t' '
	ARGIND==1{
		if($2 > 0){
		    inf[$2];
		}
		print $0;
	}
	ARGIND==2{
		if($2 in inf){
			next;
		}
		print $0;
	}' ${OnlinePath}/mid.building.online ${OnlinePath}/mid.aoi.online > ${FinalPath}/mid.online.all
#cat ${OnlinePath}/mid.*.online > ${FinalPath}/mid.online.all;
	cat ${OfflinePath}/mid.*.offline | awk -F "\t" '{print $1}' > ${FinalPath}/mid.offline.all;
	cat ${OnlinePath}/bid.*.online | awk -F "\t" '{print $1 "\t" $2}' > ${FinalPath}/bid.online.all;
	cat ${OfflinePath}/bid.*.offline | awk -F "\t" '{print $1 "\t" 0}' > ${FinalPath}/bid.offline.all;
}

function Main(){
	ClearEnv;
	# data from building/bin/GetFinalResult.sh
	SplitCity ${DATA_PATH}/building/online/mid.building.online;
	GetData;

    awk -F'\t' 'ARGIND==1{inf[$1]}ARGIND==2{if($1 in inf && $1 ~ "B" && $2 > 0){print $0}}' ${FinalPath}/mid.offline.all ${DATAPATH}/data.all ${FinalPath}/mid.offline.all > ${FinalPath}/mid.offline.all.zhudian

####上下线，一定要先下后上
	OnlineCnt=`wc -l ${FinalPath}/bid.online.all | awk '{print $1}'`
	OfflineCnt=`wc -l ${FinalPath}/mid.offline.all.zhudian | awk '{print $1}'`
	UpdatelineCnt=`wc -l ${FinalPath}/mid.online.all | awk '{print $1}'`
	ptdate=`date +%Y%m%d`
	ptfilename="tasc_stat_"${ptdate}
	echo -e "地物主点全量更新\tdw_update_count\t"${UpdatelineCnt} > ${FinalPath}/${ptfilename}
	echo -e "地物主点下线\tdw_offline_count\t"${OfflineCnt} >> ${FinalPath}/${ptfilename}
#	iconv -f gbk -t utf-8 -c ${FinalPath}/${ptfilename}.gbk > ${FinalPath}/${ptfilename}
#	rm ${FinalPath}/${ptfilename}.gbk

	${PYTHONBIN} ${BinPath}/getdiff.py ${DATAPATH}/data.all ${FinalPath}/mid.online.all ${FinalPath}/mid.online.all.diff ${FinalPath}/bid.online.all.2 ${FinalPath}/mid.offline.all ${FinalPath}/bid.offline.all.2
	cat ${FinalPath}/bid.online.all.2 >> ${FinalPath}/bid.online.all
	awk -F'\t' '{print $2"\t"0}' ${FinalPath}/bid.offline.all.2 >> ${FinalPath}/bid.offline.all

	cp ${FinalPath}/mid.online.all ${FinalPath}/mid.online.all.bak
	md5sum ${FinalPath}/mid.online.all > ${FinalPath}/mid.online.all.md5
	#mv ${FinalPath}/mid.online.all.diff ${FinalPath}/mid.online.all
	iconv -f gbk -t utf8 -c ${FinalPath}/mid.online.all.diff > ${FinalPath}/mid.online.all.diff.utf8
	iconv -f utf8 -t gbk -c ${FinalPath}/mid.online.all.diff.utf8 > ${FinalPath}/mid.online.all.diff.gbk
	mv ${FinalPath}/mid.online.all.diff ${FinalPath}/mid.online.all.diff.bak
	mv ${FinalPath}/mid.online.all.diff.gbk ${FinalPath}/mid.online.all.diff

	if [ ${OnlineCnt} -gt 100000 -o ${OfflineCnt} -gt 10000 ]; then
		MailWanrning "Object Online More Than 100000 or Offline More Than 10000, Pls Check!" "BasicObjectUpdate"
	else
		BidOffline ${FinalPath}/bid.offline.all
		MidOffline ${FinalPath}/mid.offline.all
		MidOnline ${FinalPath}/mid.online.all.diff
		BidOnline ${FinalPath}/bid.online.all
	fi

}

Main;
