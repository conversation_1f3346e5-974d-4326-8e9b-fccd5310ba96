"""
poi in aoi
"""
import shapely.wkt
import shapely.geometry
import sys
import math


def getFootPoint(point, shape):
    """
    :param point:
    :param shape:
    :return:
    """

    def getNearestLine(point, shape):
        """
        :param point:
        :param shape:
        :return:
        """
        nearestLine, nearestDistance = "", sys.maxint

        points = list(shape.exterior.coords)[:-1]
        for i in range(-1, len(points) - 1):
            x1, y1 = points[i][0], points[i][1]
            x2, y2 = points[i + 1][0], points[i + 1][1]
            line = shapely.geometry.LineString([(x1, y1), (x2, y2)])
            distance = line.distance(point)
            if distance < nearestDistance:
                nearestDistance = distance
                nearestLine = line
        return nearestLine

    line = getNearestLine(point, shape)
    x0, y0 = point.x, point.y
    coords = list(line.coords)
    x1, y1 = coords[0][0], coords[0][1]
    x2, y2 = coords[1][0], coords[1][1]
    if x1 >= x2:
        x1, x2 = x2, x1
        y1, y2 = y2, y1
    k = - ((x1 - x0) * (x2 - x1) + (y1 - y0) * (y2 - y1)) / ((x2 - x1) ** 2 + (y2 - y1) ** 2)
    xn = k * (x2 - x1) + x1
    yn = k * (y2 - y1) + y1
    dist = math.sqrt((x0 - xn) ** 2 + (y0 - yn) ** 2)
    delta = 5
    x = xn + (xn - x0) / dist * delta
    y = yn + (yn - y0) / dist * delta
    return x, y


def main():
    """
    :return: void
    """
    file = open(sys.argv[1])
    outFile1 = open(sys.argv[2], "w")
    outFile2 = open(sys.argv[3], "w")
    for line in file:
        line = line.strip("\n")
        items = line.split("\t")
        bid = items[0]
        shape = items[1]
        poi_x = items[2]
        poi_y = items[3]

        shape = shapely.wkt.loads(shape)
        point = shapely.geometry.Point([float(poi_x), float(poi_y)])
        center = shape.centroid
        x = center.x
        y = center.y

        dist = shape.distance(point)
        if dist > 50:  # send to platform
            outFile1.write(line + "\n")
        elif 0 < dist <= 50:  # auto online
            if center.distance(shape) > 0:
                x, y = getFootPoint(point, shape)
            out = line + "\t" + str(x) + "\t" + str(y) + "\t" + str(shape.distance(point)) + "\t" + str(
                center.distance(point))
            outFile2.write(out + "\n")


main()
