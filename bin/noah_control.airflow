#! /bin/bash

script_dir=$(cd $(dirname $0) && pwd)
mod_dir=$(dirname $script_dir)
py_39_home=/home/<USER>/py39
# !!!适配airflow的worker

# 获取当前脚本的路径，保存到FILE
if [[ -L "$0" ]];then
    FILE=$(readlink -f "$0")
else
    FILE=$0
fi
BASE_DIR=$(cd $(dirname ${FILE}); pwd)/..
echo "base_dir $BASE_DIR"

AIRFLOW_WORK_DIR=/home/<USER>/airflow
AIRFLOW_QUEUE=aoi_strategy_opera

############################## 基础数据包准备 ###############################
# 初始化tools环境
function init_tools(){

    init_python_proxy
    init_afs
    copy_python_39
    init_py39_script
    # 安装依赖
    install_package
    export LC_ALL=en_US.UTF-8
}

function init_python_proxy() {
    mkdir -p /home/<USER>/.pip/
    cat > /home/<USER>/.pip/pip.conf << EOF
[global]
timeout = 60
index = https://pip.baidu-int.com/search/
index-url = https://pip.baidu-int.com/simple/
trusted-host = pip.baidu-int.com
[list]
format = columns
EOF
    echo "set python proxy success"
}

# 初始化afs
function init_afs() {
    if [[ -d "$mod_dir/afs" ]];then
	    echo "afs目录已存在"
	    return
	  else
	    mkdir $mod_dir/afs
	    cd $mod_dir/afs
	    wget -q -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:9864e50c-18bb-44e8-acdf-fb64ea933823" "https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.8.11.2155/files"
	    tar xf output.tar.gz
	  fi
	  if [[ ! -d "$mod_dir/afs/output/bin" ]];then
	    echo 'install afs_tool failed'
	    exit 100
	  fi
}


function copy_python_39() {

  if [[ -d "$py_39_home/bin" ]];then
    echo "python3.9 已经安装"
    return
  fi
  mkdir -p $py_39_home
  afs_cmd="$mod_dir/afs/output/bin/afsshell --username=map-data-streeview --password=map-data-streeview get afs://aries.afs.baidu.com:9902/user/map-data-streeview/aoi-ml/tools/conda_airflow_env.zip $py_39_home/"
  echo "$afs_cmd"
  eval "$afs_cmd"

  cd $py_39_home
  echo "start tar -xf  $(pwd)"
  tar -xf conda_airflow_env.zip
}

function init_py39_script() {
  cat > $py_39_home/py39 << EOF
#!/bin/bash
export PATH=/opt/compiler/gcc-12/bin/:\$PATH
py_39_home=/home/<USER>/py39
/opt/compiler/gcc-12/lib/ld-linux-x86-64.so.2 --library-path \$py_39_home/lib \$py_39_home/bin/python3.9 \$@
EOF
  chmod +x $py_39_home/py39
}

function install_package() {
    $py_39_home/py39 -m pip install opencv-contrib-python==******** opencv-python==******** redis==4.0.2
    $py_39_home/py39 -m pip install apache-airflow==2.8.1 apache-airflow-providers-celery==3.5.1
}

########################## 基础任务包准备 end ###############################

function start_service() {
    # 初始化tools环境
    init_tools
    ### 启动airflow
    mkdir -p $AIRFLOW_WORK_DIR
    # 同步
    rsync -avz "$BASE_DIR/airflow/" $AIRFLOW_WORK_DIR/
    is_running
    if [[ $? -eq 0 ]];then
      info "Process is running!"
      return 0
    fi
    nohup $py_39_home/py39 $py_39_home/bin/airflow celery worker -q $AIRFLOW_QUEUE >>$AIRFLOW_WORK_DIR/log_worker_celery.txt 2>&1 &
    info "starting up $mod_name ..."
    sleep 3
    return 0
}

 # info 日志
function info() {
    echo "INFO: $@"
}

# warning 日志
function warning() {
    echo "WARNING: $@"
}


# 执行进程是否处于运行状态
# 返回 0 进程表示运行中，返回 2 表示运行结束
function is_running() {
    lines=`ps -ef | grep celeryd | grep ForkPoolWorker | grep -v grep|wc -l`
    if [[ lines -gt 0 ]];then
      info "running"
      return 0
    else
      info "not running"
      return 2
    fi
}

# 检查文件和目录是否存在以类型
function check_existence_and_type() {
    local path=$1
    local type=$2

    if ([[ ${type} == "file" ]] && [[ -f ${path} ]]) ||
        ([[ ${type} == "dir" ]] && [[ -d ${path} ]]);then
        info "${path} is a ${type}"
        return 0
    else
        info "${path} is not a ${type}. Unexpected!"
        return 1
    fi
}


# 获取父进程的进程id，有supervise的情况下父进程就是supervise进程
function get_ppid() {
    local ppid=`ps aux | grep supervise | grep "${START_CMD}" | grep -v grep | awk '{print $2}'`
    echo ${ppid}
}

# 获取执行进程的进程id，即supervise监控的进程
function get_pid() {
    local pid=`ps aux | grep "${START_CMD}" | grep -v supervise | grep -v grep | awk '{print $2}'`
    echo ${pid}
}

# 停止
function stop() {
    #is_running
    #if [[ $? -eq 2 ]];then
    #    warning "Process is not running yet!"
    #    return 0
    #fi
    # airflow 一般不需要停止, 可以手动调用
#    $py_39_home/py39 $py_39_home/bin/airflow celery stop
#    sleep 3

   return 0
}


function restart() {
    is_running
    if [[ $? -eq 0 ]];then
      info "Process is running!"
      return 0
    fi
    start_service
    sleep 1
    return 0
}

# 运行状态
# 0 -> running, 1 -> starting, 2 -> stopped
function status() {
    is_running
    if [[ $? -eq 0 ]];then
      info "Process is running!"
    elif [[ $? -eq 2 ]];then
      info 'process stopped!'
      return 2
    fi
    return 0
}

function usage() {
    echo "usage:"
    echo "cd $mod_dir && $0 {start|stop|restart|status}"
}

if [[ "$#" -ne "1" ]]; then
    usage
    exit 1
fi

ACTION=$1
case "${ACTION}" in
    start)
        start_service
    ;;
    stop)
        stop
    ;;
    restart)
        restart
    ;;
    status)
        status
    ;;
    *)
        usage
    ;;
esac