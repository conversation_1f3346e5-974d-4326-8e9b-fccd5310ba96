# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
todo：
    1.人工作业的出入口没有记录和维护，可能会影响对L1 L2级别AOI的判断，推动出入口的履历库建设

AOI的修改场景:
1. 新增AOI并关联了主点bid  是否不存在没有关联主点的场景？
2. 删除并重新画了AOI，faceid变更但是主点不变
3. 删除AOI
4. 新增AOI的关联关系
"""

import collections
import time
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
from access_function import AoiGateMatchGeneralTool
from src.AccessMatch.BatchCtx import BatchContext


class AoiWithInnerLinkGateMatch:
    """
    内内外/外外内大门匹配策略类，可匹配大门扩展两条link后有内部路的大门
    """

    def __init__(self, ctx: BatchContext):
        """
        初始化连接数据库
        :param ctx:
        """
        self.ctx = ctx

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def commit(self):
        """
        数据提交
        """
        self.ctx.pg_tool.commit()

    def find_node_to_process_with_bid(self, bid):
        """
        根据bid寻找附近可能关联的大门信息
        Args:
            bid: poi的bid
        Return:
            face_id: aoi的主键id
            node_id_set: set类型, 大门的nodeid集合
        """
        node_id_set = set()
        sql = '''
            select 
                st_astext(geom),kind,aoi_level, bf.face_id
            from 
                blu_face bf inner join blu_face_poi bfp
            on 
                bf.face_id = bfp.face_id
            where 
                bfp.poi_bid = %s 
                and bf.src != 'SD'
        '''
        self.ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res_aoi = self.ctx.pg_tool.cursor_aoi.fetchone()
        if res_aoi is None or len(res_aoi) == 0:
            # print("没有关联的AOI\t" + bid)
            return "", node_id_set
        if res_aoi[1] == '52' or res_aoi[2] != 2:
            # print("AOI为商圈或非基础院落\t" + bid)
            return "", node_id_set
        aoi_geom = res_aoi[0]
        face_id = res_aoi[3]
        sql = '''
            with tmp as (
                select 
                    link_id, st_intersection(st_geomfromtext(%s, 4326), geom) as geom
                from nav_link
                    where st_crosses(st_geomfromtext(%s, 4326), geom)
            )
            select 
                link_id, st_astext((st_dump(geom)).geom) as geom
            from tmp;
        '''
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_geom, aoi_geom])
        res_point = self.ctx.pg_tool.cursor_road.fetchall()
        if res_point is not None and len(res_point) > 0:
            for point_geom in res_point:
                sql = """
                    select 
                        string_agg(node_id, ''',''') 
                    from nav_node 
                    where 
                        st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)"""
                self.ctx.pg_tool.cursor_road.execute(sql, [point_geom[1]])
                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                if res_node is None or len(res_node) == 0:
                    continue
                node_str = res_node[0][0]
                sql = '''
                    select 
                        node_id, in_linkid, out_linkid 
                    from nav_gate 
                    where 
                        node_id in ('%s')''' % (node_str)
                self.ctx.pg_tool.cursor_road.execute(sql)

                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                for res_node_tmp in res_node:
                    sql = '''
                        select 
                            form 
                        from nav_link 
                        where 
                            link_id = %s 
                            or link_id = %s'''
                    self.ctx.pg_tool.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                    res_form = self.ctx.pg_tool.cursor_road.fetchall()
                    if '52' in res_form[0][0].split(',') and '52' in res_form[1][0].split(','):
                        node_id_set.add(res_node_tmp[0])

        # 内外大门可以不考虑到边框线的距离是否大于40米,内内大门只考虑aoi范围30米内的大门
        aoi_line = aoi_geom.replace("POLYGON((", "LINESTRING(").replace("))", ")")
        sql = """
            select 
                string_agg(node_id, ''',''') 
            from nav_node 
            where 
                st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0003)"""
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_line])
        res_node = self.ctx.pg_tool.cursor_road.fetchall()
        if res_node is not None and len(res_node) > 0:
            node_str = res_node[0][0]
            sql = '''
                select 
                    node_id, in_linkid, out_linkid 
                from nav_gate 
                where 
                    node_id in ('%s')''' % (node_str)
            self.ctx.pg_tool.cursor_road.execute(sql)
            res_node = self.ctx.pg_tool.cursor_road.fetchall()
            for res_node_tmp in res_node:
                sql = '''
                select 
                    form 
                from nav_link 
                where 
                    link_id = %s 
                    or link_id = %s'''
                self.ctx.pg_tool.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                res_form = self.ctx.pg_tool.cursor_road.fetchall()
                if '52' in res_form[0][0].split(',') and '52' in res_form[1][0].split(','):
                    node_id_set.add(res_node_tmp[0])

        return face_id, node_id_set

    def find_aoi_by_link(self, link_geom_dict, node_geom, face_id, node_link_info):
        """
        根据link找大门的候选AOI
        Args:
            link_geom_dict: 字典类型，key是linkid，value是link的几何信息
            node_id: 大门的nodeid
            face_id: aoi的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
        """
        face_id_dict = collections.OrderedDict()
        filter_reason = []
        if len(node_link_info) != 3:
            return
        inner_link = node_link_info[0]
        outter_link = node_link_info[1]
        node_region = shapely.wkt.loads(node_geom)
        for link_id, link_geom in link_geom_dict.items():
            link_region = shapely.wkt.loads(link_geom)
            # 查找相交的AOI
            sql = '''
                select 
                    face_id, st_astext(geom) 
                from blu_face
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom)
                    and kind != '52' 
                    and aoi_level = 2'''
            self.ctx.pg_tool.cursor_aoi.execute(sql, [link_geom])
            res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
            for res_aoi_tmp in res_aoi:
                aoi_region = shapely.wkt.loads(res_aoi_tmp[1])
                link_inter = link_region.intersection(aoi_region)
                point_region = link_region.intersection(aoi_region.boundary)
                if point_region.geom_type == "MultiPoint" or point_region.distance(node_region) > 0.00035:
                    continue
                # 如果和aoi相交的是外部路，此时大门在aoi框的外面，也认为是错误关联
                if link_id in outter_link.split(',') and inner_link != "":
                    if not aoi_region.contains(shapely.wkt.loads(node_geom)):
                        continue
                if link_inter.length / aoi_region.length < 0.005 or link_inter.length < 0.00005:
                    # link与aoi相交的部分长度小于周长的0.5%
                    sql = '''
                        select 
                            node_id, st_astext(a.geom) 
                        from nav_node a inner join nav_link b 
                        on 
                            a.node_id = b.s_nid
                            or a.node_id = b.e_nid 
                        where 
                            b.link_id = %s'''
                    self.ctx.pg_tool.cursor_road.execute(sql, [link_id])
                    res_node = self.ctx.pg_tool.cursor_road.fetchall()
                    for res_node_tmp in res_node:
                        # 找到link两个端点的坐标，判断是否在AOI内
                        if aoi_region.contains(shapely.wkt.loads(res_node_tmp[1])):
                            # 在aoi内，判断是否连接了别的内部路
                            sql = '''
                                select 
                                    count(*)
                                from nav_link 
                                where 
                                    (s_nid = %s 
                                        or e_nid = %s) 
                                    and link_id != %s '''
                            self.ctx.pg_tool.cursor_road.execute(sql, [res_node_tmp[0], res_node_tmp[0], link_id])
                            if self.ctx.pg_tool.cursor_road.fetchone()[0] == 0:
                                # 没有连接其余内部路，不认为与该aoi匹配
                                # if face_id == res_aoi_tmp[0]:
                                #     filter_reason.append("断头路场景下发人工作业")
                                break
                            else:
                                face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]
                else:
                    face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]

        return face_id_dict, filter_reason

    def find_link_by_inin_node(self, node_id, node_geom, aoi_geom):
        """
        根据nodeid查找内部路方向的候选link组
        Args:
            node_id: 大门的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
            link_info: list, 记录大门相连的内部路和外部路
        """
        link_dict = dict()
        inner_link = ""
        outter_link = ""
        sql = '''
            select 
                distinct link_id, s_nid, e_nid, b.node_id, form, st_astext(geom), gate_type
            from nav_link a inner join nav_gate b 
            on 
                a.link_id = b.in_linkid 
                or a.link_id = b.out_linkid
            where 
                b.node_id = %s '''
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id])
        node_id_str = ""
        res_link = self.ctx.pg_tool.cursor_road.fetchall()
        for v_node in res_link:
            link_dict[v_node[0]] = v_node[5]
            node_id_str = node_id_str + "','" + v_node[1] + "','" + v_node[2]
            if '52' in v_node[4].split(','):
                inner_link = inner_link + "," + v_node[0]
            else:
                outter_link = outter_link + "," + v_node[0]
        node_id_str = node_id_str.strip("','")
        sql = '''
            select 
                link_id, st_astext(geom), form, s_nid, e_nid 
            from nav_link
            where 
                (s_nid in ('%s') 
                    or e_nid in ('%s')) 
                and kind > 7 ''' % (node_id_str, node_id_str)
        self.ctx.pg_tool.cursor_road.execute(sql)
        for link_tmp in self.ctx.pg_tool.cursor_road.fetchall():
            link_dict[link_tmp[0]] = link_tmp[1]
            if "52" in link_tmp[2].split(','):
                inner_link = inner_link + "," + link_tmp[0]
            else:
                outter_link = outter_link + "," + link_tmp[0]

        gate_flag = ""
        sql = """
            select 
                string_agg(node_id, ''',''')
            from nav_node
            where 
                st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0006)
                and st_dwithin(st_geomfromtext(%s, 4326), geom, 0.00015)"""
        self.ctx.pg_tool.cursor_road.execute(sql, [node_geom, aoi_geom])
        node_id_str = self.ctx.pg_tool.cursor_road.fetchone()[0]
        sql = '''
            select 
                count(*) 
            from nav_gate 
            where 
                node_id in ('%s') 
                and node_id != '%s' ''' % (node_id_str, node_id)
        self.ctx.pg_tool.cursor_road.execute(sql)
        if self.ctx.pg_tool.cursor_road.fetchone()[0] > 0:
            gate_flag = "multi_gate"

        return link_dict, [inner_link.strip(','), outter_link.strip(','), gate_flag]

    def run(self, num):
        """更新大门通行属性的入口函数
        """
        # print("内内外/外外内大门批处理策略开始：" + 
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))) 

        # 寻找待匹配的大门和AOI的关联关系的bid列表
        aoi_gate_match_general_obj = AoiGateMatchGeneralTool(self.ctx.pg_tool)
        if self.ctx.bid:
            face_bid_dict = {self.ctx.bid: ""}
        else:
            face_bid_dict = aoi_gate_match_general_obj.find_bid_to_match(num)
        # num_all = len(face_bid_dict)
        process_num = 0.0
        for bid, face_id_dst in face_bid_dict.items():
            process_num = process_num + 1
            # print("内内大门匹配策略进度:" + str(process_num / num_all))
            face_id_dst_tmp, node_set = self.find_node_to_process_with_bid(bid)
            if face_id_dst == "":
                face_id_dst = face_id_dst_tmp
            if face_id_dst_tmp == "":
                continue
            if len(node_set) > self.ctx.strategy_node_max:
                aoi_gate_match_general_obj.set_bid_intel_fail(bid, f"需要处理的node过多:{len(node_set)}")
                continue
            if face_id_dst_tmp != face_id_dst:
                aoi_gate_match_general_obj.set_bid_intel_fail(bid, "关联了两个AOI")
                continue

            # 过滤掉一些匹配到的门比较多的AOI
            need_match = True
            # 重要： 这里应用了成果库的匹配结果，后续成果库取消之后需要需要改造
            sql = '''
                select 
                    distinct b.node_id
                from 
                    blu_access a 
                    inner join 
                    blu_access_gate_rel b
                on
                    a.access_id = b.access_id
                where 
                    main_bid  = '%s' 
            ''' % (bid)
            self.ctx.pg_tool.cursor_aoi.execute(sql)
            res_matched = self.ctx.pg_tool.cursor_aoi.fetchall()
            if res_matched and len(res_matched) >= 4:
                need_match = False
            fields = "st_astext(geom), mesh_id"
            res = aoi_gate_match_general_obj.get_aoi_info(face_id_dst, fields)
            if res is None:
                continue
            aoi_geom = res[0]
            for node_id in node_set:
                # 需要重新匹配的场景
                node_geom = aoi_gate_match_general_obj.get_node_geom(node_id)
                if node_geom is None:
                    continue
                link_geom_dict, node_link_info = self.find_link_by_inin_node(node_id, node_geom, aoi_geom)

                # 过滤匹配准确率率比较低的场景
                sql = '''
                    select 
                        count(*) 
                    from poi
                    where 
                        st_dwithin(st_geomfromtext('%s', 4326), geometry, 0.0003)
                        and bid != '%s' and name ~ '幼儿园'
                        and name !~ '门' ''' % (node_geom, bid)
                self.ctx.pg_tool.cursor_poi.execute(sql)
                if self.ctx.pg_tool.cursor_poi.fetchone()[0] > 0:
                    continue
                if not need_match:
                    continue
                if len(node_set) > 4:
                    continue
                if node_link_info[0] == "":
                    continue
                match_way = "batch"
                if node_link_info[2] == "multi_gate":
                    match_way = "manu-check"

                # 基础院落若包含高等级道路 则高等级道路上的大门也应该关联
                if aoi_gate_match_general_obj.find_node_adjacent_high_link(face_id_dst, link_geom_dict, node_geom):
                    aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                     "add",
                                                                     "例行更新流程内外大门批处理策略召回-内部连接高等级道路",
                                                                     "manu-check")

                # 根据link特征匹配AOI
                face_id_dict, filter_reason = self.find_aoi_by_link(link_geom_dict, node_geom,
                                                                    face_id_dst, node_link_info)
                for tmp_reason in filter_reason:
                    aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                     "add",
                                                                     f"例行更新流程内内大门批处理策略召回-{tmp_reason}",
                                                                     "manu-check")
                if len(face_id_dict) == 0:
                    # 这里大门和aoi在两条link之内不相交，可能是数据问题，可以进行轨迹验证,或者下发人工作业
                    if aoi_gate_match_general_obj.aoi_contains_geom(face_id_dst, node_geom):
                        # 下发人工作业？
                        continue
                    continue

                # 挑选最合适的AOI
                face_id_opt = aoi_gate_match_general_obj.find_opt_aoi(face_id_dict)
                if face_id_opt == "":
                    continue
                if face_id_opt != face_id_dst:
                    # 匹配到非清单中的AOI
                    continue

                aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                 "add", "例行更新流程内内大门批处理策略召回", match_way)

            self.commit()
            aoi_gate_match_general_obj.commit()

        # print("内内外/外外内大门批处理策略完成：" + 
        #     time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))) 


if __name__ == '__main__':
    pass
    # obj = AoiWithInnerLinkGateMatch()
    # obj.run()
