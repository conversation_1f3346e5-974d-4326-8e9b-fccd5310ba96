# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""
import datetime
import json
from multiprocessing import Pool
import time
import os
import sys
import traceback

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
# from pg_tool import PgTool
from common import common_tool
import shapely.wkt
import tqdm
from src.common import pgsql

HIGH_CITY = ["北京市", "上海市", "广州市", "深圳市"]


class ManuProduction():
    """
    大门和AOI匹配策略, 策略整合入口
    """

    def __init__(self):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        # PgTool.__init__(self, road_dsn)
        pass

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        # PgTool.__exit__(self, exc_type, exc_val, exc_tb)
        pass

    def commit(self):
        """
        数据提交
        """
        # PgTool.commit(self)
        pass

    def in_nav_list(self, bid):
        """
        判断是否属于导航风险清单
        """
        sql = f"""
            select
                count(*)
            from nav_bid_list
            where
                bid = '{bid}'
        """
        return pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql)[0] > 0
        # self.cursor_poi.execute(sql)
        # if self.cursor_poi.fetchone()[0] > 0:
        #     return True
        # else:
        #     return False

    def is_valid_poi(self, bid):
        """
        判断是否属于能够制作出入口的有效poi
        """
        sql = f"""
            select
                *
            from poi
            where
                bid = '{bid}'
                and std_tag != '交通设施;加油加气站'
                and std_tag != '交通设施;服务区'
                and std_tag != '交通设施;高速公路停车区'
                and std_tag != '交通设施;飞机场'
                and std_tag != '交通设施;火车站'
                 
        """
        ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql)
        if not ret:
            return False
        # 热门城市都下发
        if ret.city in HIGH_CITY:
            return True
        click_pv = ret.click_pv
        if click_pv >= 300:
            return True
        sql = 'select pv from poi_nav_500m_pv where bid = :bid'
        nav_pv = pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql, {"bid": bid})
        if nav_pv and nav_pv.pv >= 30:
            return True
        return False

    def is_matched(self, bid, node_id):
        """
        判断关联关系是否已经存在
        """
        sql = f"""
            select
                count(*)
            from
                blu_access a
                inner join
                blu_access_gate_rel b
            on
                a.access_id = b.access_id
            where
                main_bid = '{bid}'
                and b.node_id = '{node_id}'
        """
        # self.cursor_aoi.execute(sql)
        matched = False
        # num = self.cursor_aoi.fetchone()[0]
        num = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)[0]
        if num > 0:
            matched = True

        sql = f"""
            select
                action
            from gate_bid_change_history
            where
                bid = '{bid}'
                and node_id = '{node_id}'
                and imp_state = 0
            order by
                id desc
            limit 1
        """
        # self.cursor_poi.execute(sql)
        # res = self.cursor_poi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql)
        if matched:
            if res is not None and res[0] == "delete":
                return False
            return True
        else:
            if res is not None and res[0] == "add":
                return True
            return False

    def have_manu_worked(self, bid, node_id):
        """
        判断是否已经下发过人工作业. 检查时间
        """
        year_ago = datetime.datetime.now() - datetime.timedelta(days=365)
        month_3_ago = datetime.datetime.now() - datetime.timedelta(days=90)
        # 给出有效的结论(Y, N),质检时间为1年
        # Y 不设置年限
        sql = f"""
            select
                count(*)
            from gate_aoi_match_check_data
            where
                bid = '{bid}'
                and node_id = '{node_id}'
                and conclusion in ('Y') 
        """
        if pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql)[0] > 0:
            return True
        # N 1年
        sql = f"""
                    select
                        count(*)
                    from gate_aoi_match_check_data
                    where
                        bid = '{bid}'
                        and node_id = '{node_id}'
                        and conclusion in ('N') and create_time >= '{year_ago.strftime("%Y-%m-%d")}'
                """
        if pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql)[0] > 0:
            return True
        # 没有给具体的结论,有效期为三个月
        sql = f"""
        select
                count(*)
            from gate_aoi_match_check_data
            where
                bid = '{bid}'
                and node_id = '{node_id}'
                and conclusion not in ('Y', 'N') and create_time >= '{month_3_ago.strftime("%Y-%m-%d")}'
        """
        if pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql)[0] > 0:
            return True
        return False
        # 为避免重复下发，这里需使用cursor_poi_write
        # self.cursor_poi_write.execute(sql)
        # if self.cursor_poi_write.fetchone()[0] > 0:
        #     return True
        # return False

    def get_geom_type_by_node(self, node_id):
        """
        获取大门的坐标以及开放类型
        """
        sql = f"""
            select
                st_astext(geom), a.type
            from
                nav_gate a
                inner join
                nav_node b
            on
                a.node_id = b.node_id
            where
                a.node_id = '{node_id}'
        """
        return pgsql.query_one(pgsql.ENGINE_ROAD, sql)
        # self.cursor_road.execute(sql)
        # res = self.cursor_road.fetchone()
        # return res

    def is_accurate_AOI(self, bid):
        """
        判断AOI是否精准
        """
        sql = f"""
            select
                aoi_complete
            from blu_face_complete
            where
                main_bid = '{bid}'
        """
        # self.cursor_aoi.execute(sql)
        # res = self.cursor_aoi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)
        if res is not None and res[0] == 3:
            return True

        return False

    def record_manu_work_history(self, bid, node_id, source, ways, node_geom, qb_id=-1):
        """
        保存人工作业的记录
        """
        date = time.strftime('%Y%m%d', time.localtime(time.time()))
        uid = f"{node_id}_{bid}_{date}"
        sql = f"""
            insert into
                gate_aoi_match_check_data (
                    bid,
                    node_id,
                    source,
                    ways,
                    uid
                )
            values(
                '{bid}',
                '{node_id}',
                '{source}',
                '{ways}',
                '{uid}'
            )
        """
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)
        # self.cursor_poi_write.execute(sql)
        if ways == "提召":
            action = 'add'
        else:
            action = 'delete'
        extra = json.dumps({"qb_id": qb_id})
        sql = f"""
                insert into gate_match_sync_integration
                    (uid, main_bid, node_id, is_manual, action, node_geom, extra)
                values
                    ('{uid}', '{bid}', '{node_id}', 1, '{action}', st_geomfromtext('{node_geom}', 4326), '{extra}')
            """
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)
        # self.cursor_poi_write.execute(sql)

    def mark_intel_complete(self, id):
        """
        标记情报已下发完成
        """
        sql = f"""
            update
                gate_aoi_match_check_data_tmp
            set
                status = 2
            where
                id = {id}
        """
        # self.cursor_poi_write.execute(sql)
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)

    def mark_intel_fail(self, id, reason):
        """
        标记情报已下发完成
        """
        sql = f"""
            update
                gate_aoi_match_check_data_tmp
            set
                status = 1,
                reason = '{reason}'
            where
                id = {id}
        """
        # self.cursor_poi_write.execute(sql)
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)

    def is_small_AOI(self, bid):
        """
        judge the bid is small AOI or not
        """
        sql = f"""
            select
                st_area(geom)
            from blu_face a
                 inner join
                 blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
        """
        # self.cursor_aoi.execute(sql)
        # res = self.cursor_aoi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)
        if res is not None and res[0] > 1.8e-07:
            return False
        return True

    def get_geom_by_bid(self, bid):
        """
        return： geom
        """
        sql = f"""
            select
                st_astext(geom)
            from blu_face a
                 inner join
                 blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
                and kind != '52'
                and a.src != 'SD'
        """
        # self.cursor_aoi.execute(sql)
        # return self.cursor_aoi.fetchone()
        return pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)

    def have_open_gate(self, geom):
        """
        判断范围框附近10米有无非紧急门
        """
        sql = f"""
            with tmp as (
                select st_geomfromtext('{geom}', 4326) as geom
            )
            select
                distinct b.node_id
            from
                nav_gate a
                inner join
                nav_node b
            on
                a.node_id = b.node_id,
                tmp
            where
                st_dwithin(b.geom, tmp.geom, 0.0001)
                and type != 0
        """
        return len(pgsql.query_all(pgsql.ENGINE_ROAD, sql)) > 0
        # self.cursor_road.execute(sql)
        # return len(self.cursor_road.fetchall()) > 0

    def is_issued(self, bid, node_id):
        """
        判断是否已经下发过人工作业
        """
        compare_time = datetime.datetime.now() - datetime.timedelta(days=3)
        sql = f"""
            select 1
            from gate_aoi_match_check_data
            where bid = '{bid}' and node_id = '{node_id}' and create_time >= '{compare_time}'
            limit 1
        """
        # self.cursor_poi_write.execute(sql)
        # res = self.cursor_poi_write.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql)
        return res is not None

    def is_agg_jz(self, bid):
        """
        判断是否是聚合院落
        """
        sql = f"""
            select 1
            from blu_face bf
                left join blu_face_poi bfp on bfp.face_id = bf.face_id
            where bf.aoi_level = 1 and bfp.poi_bid = '{bid}'
            limit 1
        """
        # self.cursor_aoi.execute(sql)
        # res = self.cursor_aoi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)
        return res is not None

    def is_not_jz_and_low_pv(self, bid):
        """
        判断是否是低PV的非精准AOI
        """
        sql = f"""
            select 1
            from blu_face_complete
            where aoi_complete < 3 and main_bid = '{bid}'
        """
        # self.cursor_aoi.execute(sql)
        # res = self.cursor_aoi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)

        if res is None:
            # 说明是精准 AOI，返回 false
            return False

        sql = f"""
            select click_pv, std_tag
            from poi
            where bid = '{bid}'
        """
        # self.cursor_poi.execute(sql)
        # pvInfo = self.cursor_poi.fetchone()
        pvInfo = pgsql.query_one(pgsql.ENGINE_POI_ONLINE_SLAVE, sql)
        if pvInfo:
            std_tag = pvInfo[1]
            click_pv = pvInfo[0]
            no_limit_tag = ['购物;购物中心', '医疗;综合医院', '医疗;专科医院', '教育培训;高等院校',
                            '文化传媒;展览馆', '休闲娱乐;剧院', '运动健身;体育场馆',
                            '旅游景点;风景区', '旅游景点;公园', '旅游景点;文物古迹',
                            '旅游景点', '旅游景点;其他', '旅游景点;博物馆',
                            '旅游景点;游乐园', '旅游景点;寺庙',
                            '旅游景点;景点', '旅游景点;海滨浴场',
                            '旅游景点;动物园', '旅游景点;水族馆', '旅游景点;教堂', '旅游景点;植物园'
                            ]
            if std_tag in no_limit_tag:
                return False
            # 存在 pv 信息并且 pv < 210
            # 现在不限制pv
            # if click_pv < 210:
            #     return True
        return False

    def is_no_border(self, bid):
        """
        判断是否是无边框的AOI
        """
        sql = f"""
            select 1
            from blu_face bf 
                inner join blu_face_poi bfp on bf.face_id = bfp.face_id
            where bfp.poi_bid = '{bid}' and kind != '52'
            limit 1
        """
        # self.cursor_aoi.execute(sql)
        # res = self.cursor_aoi.fetchone()
        res = pgsql.query_one(pgsql.ENGINE_MASTERBACK_PROXY, sql)
        return res is None

    def mark_intel_fail_by_bid_node_id(self, node_id, bid, reason):
        """
        通过 bid 和 node_id 标记下发状态失败
        """
        sql = f"""
            update
                gate_aoi_match_check_data_tmp
            set
                status = 1,
                reason = '{reason}'
            where
                bid = '{bid}' and node_id = '{node_id}' and status = 0
        """
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)
        # self.cursor_poi_write.execute(sql)
        # self.conn_poi_write.commit()

    def mark_intel_complete_by_bid_node_id(self, bid, node_id):
        """
        通过 bid 和 node_id 标记下发状态成功
        """
        sql = f"""
            update
                gate_aoi_match_check_data_tmp
            set
                status = 2
            where
                bid = '{bid}' and node_id = '{node_id}' and status = 0
        """
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql)
        # self.cursor_poi_write.execute(sql)
        # self.conn_poi_write.commit()

    def run(self):
        """
        开始下发人工作业
        """
        sql = """
            select
                distinct bid, node_id
            from gate_aoi_match_check_data_tmp
            where
                status = 0
        """
        # self.cursor_poi_write.execute(sql)
        # result = self.cursor_poi_write.fetchall()
        result = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
        idx = 0
        for bid, node_id in tqdm.tqdm(result):
            bid = bid.strip()
            node_id = node_id.strip()
            if idx > 30000:
                break
            if not self.is_valid_poi(bid):
                self.mark_intel_fail_by_bid_node_id(node_id, bid, "无效或者pv不满足要求")
                continue
            # if self.is_matched(bid, node_id):
            #     self.mark_intel_fail_by_bid_node_id(node_id, bid, "关联关系已经存在")
            #     continue
            if self.have_manu_worked(bid, node_id):
                self.mark_intel_fail_by_bid_node_id(node_id, bid, "关联关系已经下发过人工作业")
                continue

            # 判断过去3天有没有下发
            # if self.is_issued(bid, node_id):
            #     self.mark_intel_fail_by_bid_node_id(node_id, bid, "3天内下发过人工作业")
            #     continue

            # 精准AOI的聚合院落aoi_level=1的数据不下发，下发前进行过滤。
            if self.is_agg_jz(bid):
                self.mark_intel_fail_by_bid_node_id(node_id, bid, "聚合院落aoi_level=1的数据不下发")
                continue
            # PV小于210的非精准AOI不建设，下发前进行过滤。
            # if self.is_not_jz_and_low_pv(bid):
            #     self.mark_intel_fail_by_bid_node_id(node_id, bid, "PV小于210的非精准AOI不建设")
            #     continue
            # 无边框的下发前过滤。
            if self.is_no_border(bid):
                self.mark_intel_fail_by_bid_node_id(node_id, bid, "无边框的下发前过滤")
                continue

            res = self.get_geom_type_by_node(node_id)
            if res is None:
                self.mark_intel_fail_by_bid_node_id(node_id, bid, "node_id已经失效")
                continue

            geom = res[0]

            # 找一个 ways 和 qb_id
            sql = f"""
                select ways, qb_id, source
                from gate_aoi_match_check_data_tmp
                where bid = '{bid}' and node_id = '{node_id}' and status = 0
            """
            # self.cursor_poi_write.execute(sql)
            # res = self.cursor_poi_write.fetchall()
            res = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql)
            if not res or len(res) == 0:
                continue

            inputSource = '每日例行匹配流程下发人工作业'
            if len(res) == 1 and res[0][2] == '经验轨迹挖掘':
                inputSource = '经验轨迹挖掘下发人工作业'

            self.record_manu_work_history(bid, node_id, inputSource, res[0][0], geom, res[0][1])
            # self.conn_poi_write.commit()
            self.mark_intel_complete_by_bid_node_id(bid, node_id)


if __name__ == "__main__":
    with ManuProduction() as manu_production:
        try:
            print("start create plan" + time.strftime('%Y%m%d', time.localtime(time.time())))
            manu_production.run()
        except Exception as e:
            # manu_production.conn_poi_write.rollback()
            # 报警
            common_tool.send_hi(f"Access-Create-Plan-daily-Failed-de16-xxl-job-237", ["chenbaojun_cd"])
            traceback.print_exc()
