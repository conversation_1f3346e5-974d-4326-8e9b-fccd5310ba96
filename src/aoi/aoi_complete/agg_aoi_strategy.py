# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚合院落转换策略
"""

import os
import sys
from pathlib import Path

import tqdm

root_dir = Path(os.path.abspath(__file__)).parents[3]
print(root_dir)
sys.path.insert(0, root_dir.as_posix())

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext

# 未匹配到大门的AOI
AOI_COMPLETE_NOT_MATCH = 0
# 普通AOI 有门
AOI_COMPLETE_NORMAL = 1
# 基础AOI 有进出大门
AOI_COMPLETE_BASIC = 2
# 精准AOI
AOI_COMPLETE_ACCURATE = 3
# 精准AOI2.0
AOI_COMPLETE_ACCURATE_V2 = 4

BASIC_AOI_AREA_RATE = 0.7

# AOI层级
AOI_LEVEL_AGG = 1
AOI_LEVEL_BASIC = 2
AOI_LEVEL_INNER = 3


def agg_aoi_statistics(ctx: AoiCompleteContext, bid, poi_info):
    """
    聚合院落AOI精准策略判断：
    1、聚合院落下面的基础院落都为精准
    1、基础院落精准AOI面积占比大于70%
    :return:
    """
    aoi_list = ctx.pg_query_dao.get_agg_aoi(bid)
    if aoi_list is None or len(aoi_list) == 0:
        return AOI_COMPLETE_NOT_MATCH
    if poi_info is None:
        return AOI_COMPLETE_NOT_MATCH

    son_face_id_list = [aoi[3] for aoi in aoi_list if aoi[3] is not None]
    if len(son_face_id_list) == 0:
        return AOI_COMPLETE_NOT_MATCH
    # 查询聚合院落子集信息
    son_aoi_list = ctx.pg_query_dao.get_aoi_complete_by_face_id_list(list(set(son_face_id_list)))
    if son_aoi_list is None or len(son_aoi_list) == 0:
        return AOI_COMPLETE_NOT_MATCH
    basic_aoi_area = 0
    bid_complete_map = {}
    basic_aoi_num = 0
    accurate_basic_aoi_num = 0
    for son_aoi in son_aoi_list:
        face_id = son_aoi[0]
        bid = son_aoi[1]
        aoi_complete = son_aoi[2]
        aoi_level = son_aoi[4]
        if aoi_level == AOI_LEVEL_BASIC:
            basic_aoi_area += son_aoi[3]
            basic_aoi_num += 1
        if bid is not None:
            bid_complete_map[bid] = aoi_complete
        else:
            bid_complete_map[face_id] = aoi_complete
        if aoi_complete is not None and aoi_level == AOI_LEVEL_BASIC and aoi_complete >= AOI_COMPLETE_ACCURATE:
            accurate_basic_aoi_num += 1
    # 判断是否精准
    if float(basic_aoi_area) / aoi_list[0][4] > BASIC_AOI_AREA_RATE and accurate_basic_aoi_num == basic_aoi_num:
        return AOI_COMPLETE_ACCURATE

    return AOI_COMPLETE_NOT_MATCH


def agg_aoi_2(ctx: AoiCompleteContext):
    """
    聚合院落2.0计算
    :return:
    """
    ctx.reconnect()
    agg_aoi_list = ctx.pg_query_dao.get_agg_jz()
    for item in tqdm.tqdm(agg_aoi_list, desc='计算aoi 聚合区域'):
        face_id, bid, complete, is_white, area = item
        is_yy, msg = compute_agg_aoi(ctx, face_id, area, bid)
        if is_yy is False and complete != 4:
            print("not match bid", bid, msg)

        if complete == 4 and is_yy is False and is_white != 1:
            # 需要改成非yuyi
            print("remove bid", bid)
            ctx.pg_rw_dao.delete_yuyi_bid(bid)
        elif is_yy is True and complete != 4:
            print("add bid", bid)
            ctx.pg_rw_dao.add_yuyi_bid(bid)


def compute_agg_aoi(ctx: AoiCompleteContext, face_id, area, bid):
    """
    判断是否符合2.0
    :param face_id:
    :param area:
    :param bid:
    :return: bool
    """
    res = True
    msg_arr = []
    poi_info = ctx.pg_query_dao.get_poi_by_bid(bid)
    if poi_info is None:
        res = False
        return False, "poi is expired"
    main_tag = poi_info[0]
    son_aoi = ctx.pg_query_dao.get_son_aoi(face_id)
    if son_aoi is None or len(son_aoi) < 2:
        res = False
        msg_arr.append("son aoi less than 2")
    son_total_area = 0
    for aoi in son_aoi:
        son_face_id, complete, son_poi_bid, son_area = aoi
        if complete != 4:
            res = False
            msg_arr.append("son aoi is not complete " + bid)
        son_total_area += son_area
    if son_total_area / area < 0.7:
        # 面积小于70%/  最终计算就不考虑压盖了，是质量问题， 直接算面积和了
        res = False
        msg_arr.append("son aoi area not match")
    for aoi in son_aoi:
        son_face_id, complete, son_poi_bid, son_area = aoi
        if son_poi_bid is None:
            msg_arr.append("son aoi bid is empty " + son_face_id)
            continue
        son_poi_info = ctx.pg_query_dao.get_poi_by_bid(son_poi_bid)
        son_poi_name = ""
        son_poi_tag = ""
        if son_poi_info is None:
            msg_arr.append("son aoi bid is expire " + son_poi_bid)
            continue
        son_poi_name = son_poi_info[1]
        son_poi_tag = son_poi_info[0]
        if (main_tag == "房地产;住宅区" and (son_poi_tag != "房地产;住宅区" and son_poi_tag != "房地产")) or (
                main_tag == "教育培训;高等院校" and son_poi_tag != "教育培训;高等院校") or (
                main_tag == "教育培训;中学" and son_poi_tag != "教育培训;中学") or (
                main_tag == "教育培训;小学" and son_poi_tag != "教育培训;小学") or (
                main_tag == "教育培训;幼儿园" and son_poi_tag != "教育培训;幼儿园"):
            res = False
            msg_arr.append("tag not match " + son_poi_bid)
        # 主点是 医疗 或者 医疗;%医院  子点也是， 才可以转换
        if ((main_tag.startswith("医疗;") and main_tag.endswith("医院")) or main_tag == '医疗') and (
                (son_poi_tag.startswith("医疗;") is False or
                 son_poi_tag.endswith("医院") is False) and
                son_poi_tag != '医疗'):
            res = False
            msg_arr.append("tag not match " + son_poi_bid)

        main_cate = main_tag.split(';')
        son_cate = son_poi_tag.split(';')
        if len(main_cate) == 0 or len(son_cate) == 0:
            res = False
            msg_arr.append("tag cate not match " + son_poi_bid)
        if main_cate[0] != son_cate[0]:
            res = False
            msg_arr.append("tag son cate not match " + son_poi_bid)

        # print("{}\t{}\t{}\t{}\t{}\t{}\t{}".format(res, bid, poi_info[1], poi_info[0], son_poi_bid, son_poi_name,
        #                                           son_poi_tag))
    return res, ";".join(msg_arr)


if __name__ == '__main__':
    with AoiCompleteContext() as ctx:
        agg_aoi_2(ctx)
    # agg_aoi_2()