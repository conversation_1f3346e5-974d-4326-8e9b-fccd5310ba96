# !/usr/bin/env python3
"""
geom工具
"""
from shapely import wkt

from mapio.utils import coord
from shapely.geometry import Polygon, MultiPolygon


def transform_polygon(polygon):
    """
    转换 POLYGON 坐标
    :param polygon:
    :return:
    """
    transformed_coords = []
    for x, y in polygon.exterior.coords:
        bd_lon, bd_lat = coord.mercator_to_bd09(x, y)
        gcj_lon, gcj_lat = coord.bd09_to_gcj02(bd_lon, bd_lat)
        transformed_coords.append((gcj_lon, gcj_lat))
    return Polygon(transformed_coords)


def transform_multipolygon(multipolygon):
    """
    转换 MULTIPOLYGON 坐标
    :param multipolygon:
    :return:
    """
    transformed_polygons = [transform_polygon(poly) for poly in multipolygon.geoms]
    return MultiPolygon(transformed_polygons)


def transform_polygon_with_holes(polygon):
    """
    转换 POLYGON 坐标, 包括内环
    :param polygon:
    :return:
    """
    # 转换外环
    transformed_exterior = []
    for x, y in polygon.exterior.coords:
        bd_lon, bd_lat = coord.mercator_to_bd09(x, y)
        gcj_lon, gcj_lat = coord.bd09_to_gcj02(bd_lon, bd_lat)
        transformed_exterior.append((gcj_lon, gcj_lat))

    # 转换内环（洞）
    transformed_holes = []
    for hole in polygon.interiors:
        transformed_hole = []
        for x, y in hole.coords:
            bd_lon, bd_lat = coord.mercator_to_bd09(x, y)
            gcj_lon, gcj_lat = coord.bd09_to_gcj02(bd_lon, bd_lat)
            transformed_hole.append((gcj_lon, gcj_lat))
        transformed_holes.append(transformed_hole)

    return Polygon(transformed_exterior, transformed_holes)


def transform_multipolygon_with_holes(multipolygon):
    """
    转换 MULTIPOLYGON 坐标, 包括内环
    :param multipolygon:
    :return:
    """
    transformed_polygons = [transform_polygon_with_holes(poly) for poly in multipolygon.geoms]
    return MultiPolygon(transformed_polygons)


def trans_mc2gc02(geo):
    """
    百度墨卡托转换为 gcj
    :param geo:
    :return:
    """
    if geo.geom_type == "Polygon":
        return transform_polygon_with_holes(geo)
    elif geo.geom_type == "MultiPolygon":
        return transform_multipolygon_with_holes(geo)
    else:
        raise ValueError(f"Unsupported geometry type: {geo.geom_type}")


def trans_mc2gc02_by_wkt(region_wkt):
    """
    百度墨卡托转换为 gcj
    :param region_wkt:
    :return:
    """
    if not region_wkt:
        return ''
    geo = wkt.loads(region_wkt)
    return trans_mc2gc02(geo)
