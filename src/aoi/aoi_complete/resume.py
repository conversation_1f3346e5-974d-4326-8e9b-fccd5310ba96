# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aoi_complete 履历新增
"""
import os
import sys
import numpy as np

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

AOI_COMPLETE_ACTION_ADD = "ADD"
AOI_COMPLETE_ACTION_UPDATE = "UPDATE"
AOI_COMPLETE_ACTION_DELETE = "DELETE"

from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext


def run(ctx: AoiCompleteContext):
    """
    处理层级变更履历
    :return:
    """
    print(f"开始处理层级变更履历")
    ctx.reconnect()
    resume_list = []
    # 获取精准等级新增/变更信息
    aoi_complete_diff_list = ctx.pg_query_dao.get_diff_aoi_complete()
    if aoi_complete_diff_list is not None:
        for item in aoi_complete_diff_list:
            # 新增
            if item[0] is not None and item[6] is None:
                record = {"complete_info_id": item[0], "main_bid": item[1], "face_id": item[2], "aoi_complete": item[3],
                          "update_time": item[4], "is_white": item[5], "action": AOI_COMPLETE_ACTION_ADD}
                resume_list.append(record)
                if item[0] == 4:
                    ctx.pg_rw_dao.insert_aoi_complete_history(item[1])
                continue
            # 更新
            if item[0] is not None and item[6] is not None:
                record = {"complete_info_id": item[0], "main_bid": item[1], "face_id": item[2], "aoi_complete": item[3],
                          "update_time": item[4], "is_white": item[5], "action": AOI_COMPLETE_ACTION_UPDATE}
                resume_list.append(record)
                if item[0] == 4:
                    ctx.pg_rw_dao.insert_aoi_complete_history(item[1])
    aoi_complete_discount_list = ctx.pg_query_dao.get_discount_aoi_complete()
    if aoi_complete_discount_list is not None:
        for item in aoi_complete_discount_list:
            record = {"complete_info_id": item[0], "main_bid": item[1], "face_id": item[2], "aoi_complete": item[3],
                      "update_time": item[4], "is_white": item[5], "action": AOI_COMPLETE_ACTION_DELETE}
            resume_list.append(record)
    groups = np.array_split(resume_list, len(resume_list) // 1000 + 1)
    for group in groups:
        print(f"开始处理{len(group)}条履历")
        ctx.pg_rw_dao.insert_aoi_complete_history(list(group))
    print(f"处理层级变更履历完成:{len(resume_list)}")


if __name__ == '__main__':
    with AoiCompleteContext() as ctx:
        run(ctx)
