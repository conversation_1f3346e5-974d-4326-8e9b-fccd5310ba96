#!/usr/bin/env bash
DATE=`date +%Y%m%d`
END_NAME=`date +%m%d`
#shell执行路径
WORKPATH=`pwd`

#--------------------正式环境
SCRIPT_PATH="./relyscript"
PSQL="psql -h ************  -p5432 -Umap -d postgres "
PYTHON="/home/<USER>/liming06/aoi-pds-online/python27/bin/python"
PHP="/home/<USER>/odp_fty/php/bin/php"
POI_DATAPATH="/home/<USER>/liming06/ronghe/CJH/non_CJH_online"

#清理进程方法
function killprocess(){
    echo "[INFO] 开始清理${1}进程"
    PROCESS=`ps -ef|grep $1|grep -v grep|grep -v PPID|awk '{print $2}'`
    for i in $PROCESS
    do
        echo "[INFO] 清理${1}进程 [${i}]"
        kill -9 $i
    done
    echo "[INFO] 清理${1}进程结束"
}

#testing  是否具备执行条件    基础环境检查
function testEnvironment(){
    if [ -d "${SCRIPT_PATH}" ]; then
        echo "[ok] SCRIPT_PATH 存在"
    else
        echo "[WARNING] SCRIPT_PATH 不存在"
        exit 1
    fi
    which ${PHP}
    if [ $? -ne 0 ]; then
        echo "[WARNING] PHP 不存在"
        exit 1
    else
        echo "[ok] PHP 存在"
    fi
    which ${PYTHON}
    if [ $? -ne 0 ]; then
        echo "[WARNING] PYTHON 不存在"
        exit 1
    else
        echo "[ok] PYTHON 存在"
    fi
}

#创建文件夹，备份现有AOI数据库，下载新增数据
function setup(){
    local HADOOP=/home/<USER>/liming06/ronghe/CJH/tools/hadoop-client-lbs/hadoop/bin/hadoop
    if [ -d "${WORKPATH}/${DATE}/" ]; then
        echo "[INFO] 日期文件夹已经存在，当日文件可能已经备份过"
    else
        # 生成目录结构
        mkdir ${WORKPATH}/${DATE}
        if [ -d "${WORKPATH}/${DATE}/" ]; then
            echo "[INFO] 创建目录：${WORKPATH}/${DATE}/ 成功。"
        else
            echo "[error] 创建目录：${WORKPATH}/${DATE}/ 失败"
            exit 1
        fi
        cd ${WORKPATH}/${DATE}/
    
    #做全局备份
        ${PSQL} -c "\copy (select * from blu_face_from_se) to '${WORKPATH}/${DATE}/blu_face'"
        if [ $? -ne 0 ]; then
            echo "[WARNING] 备份blu_face_from_se  =>  blu_face失败，请手动回滚"
            exit 1
        fi
        echo "[INFO] 备份blu_face_from_se成功:${WORKPATH}/${DATE}/blu_face"
    
        #下载文件到本地
        #wget -c -q "ftp://cp01-rdqa-webgis-img01.cp01.baidu.com/home/<USER>/chentong/CompetitionAoiProcess/TJ_output/online.final" -O ${WORKPATH}/${DATE}/online.gbk
        #wget -c -q "ftp://cp01-rdqa-webgis-img01.cp01.baidu.com/home/<USER>/chentong/CompetitionAoiProcess/output/online.final" -O ${WORKPATH}/${DATE}/online.gbk
        #wget cq02-map-store-item03.cq02:/home/<USER>/safe_zone_out/CompetitionAoiProcess/output/online.final -O ${WORKPATH}/${DATE}/online.gbk
        #wget ftp://map:<EMAIL>:/home/<USER>/safe_zone_out/CompetitionAoiProcess/output -O ${WORKPATH}/${DATE}/online.gbk
        rm ${WORKPATH}/${DATE}/online.gbk 
        cat k1 > ${WORKPATH}/${DATE}/online.gbk
#        $HADOOP fs -get /app/lbs/lbs-poi/AOI/gaode/online.final ${WORKPATH}/${DATE}/online.gbk
        cat ${WORKPATH}/${DATE}/online.gbk | iconv -f gbk -t utf8 -c > ${WORKPATH}/${DATE}/online.final
    fi
}

# 按bid去重重复数据，查b2b表替换非releasebid
function removeRepeat(){

    if [ -f "${WORKPATH}/${DATE}/online.final" ]; then
        
            mv ${WORKPATH}/${DATE}/online.final ${WORKPATH}/${DATE}/aoi.txt
        

            echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   去重    +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
            # 去重，只加入增量
            # step1 查bid2releasebid 表
            # step2 去重 和现在数据库中的数据对比，只留下bid不在里面的数据
            rm ${POI_DATAPATH}/bid2releasebid
            wget ftp://gzns-map-poi-export.gzns/home/<USER>/poi-data-export/data_inner/bid2releasebid -O ${POI_DATAPATH}/bid2releasebid
            awk -F "\t" 'ARGIND==1{bid=$3; map[bid] } 
                     ARGIND==2{bid=$1; release_bid = $2; if(bid in map){map[bid]=release_bid} } 
                     ARGIND==3{bid=$3; 
                               if(map[bid] != ""){
                                                  temp=$1 "\t" $2 "\t" map[bid]; 
                                                  for(i=4;i<=NF;i++){
                                                                     temp = temp "\t" $i
                                                                    } 
                                                  print temp
                                                }    
                                else{  print $0   }
                             } ' ${WORKPATH}/${DATE}/aoi.txt ${POI_DATAPATH}/bid2releasebid ${WORKPATH}/${DATE}/aoi.txt > ${WORKPATH}/${DATE}/aoi.txt.release_bid


            awk -F "\t" 'ARGIND==1{bid=$3; shape=$2; map[bid]=shape} 
                     ARGIND==2{bid=$3; shape=$2; 
                               if(bid in map){
                                    next
                                } else {
                                    print $0
                                }

                        }' ${WORKPATH}/${DATE}/blu_face ${WORKPATH}/${DATE}/aoi.txt.release_bid > ${WORKPATH}/${DATE}/aoi.txt.to_replace

            rm ${WORKPATH}/${DATE}/aoi.txt;
            mv ${WORKPATH}/${DATE}/aoi.txt.to_replace ${WORKPATH}/${DATE}/aoi.txt;
            cp ${WORKPATH}/${DATE}/aoi.txt ${WORKPATH}/${DATE}/aoi_city
            echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   去重完成   +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
    fi
}


#将新增数据导入aoi_test数据库，删除无效数据
function prepare(){ 
    echo "[INFO] 数据准备工作开始。。"
    ${PSQL} -c "delete from aoi_test;"
    wait
    if [ $? -ne 0 ]; then
        echo "[error] 清空aoi_test数据失败"
        exit 1
    else
        echo "[INFO] 清空aoi_test数据完成"
    fi
    #将aoi_city导入aoi_test
    ${PSQL} -c "\copy aoi_test from '${WORKPATH}/${DATE}/aoi_city'"
    wait
    if [ $? -ne 0 ]; then
        echo "[error] copy aoi_test命令执行失败"
        exit 1
    else
        echo "[INFO] copy命令执行成功"
    fi
    #去掉bid为空和bid重复的数据
    ${PSQL} -c "delete from aoi_test where bid='\"\"';"
    if [ $? -ne 0 ]; then
        echo "[error] 删除aoi_test为空数据失败"
        exit 1
    else
        echo "[INFO] 删除aoi_test为空数据成功"
    fi
    #消除bid重复数据成功
    ${PSQL} -c "\copy (select bid from aoi_test where bid<>'' group by bid having count(*)>1) to '${WORKPATH}/${DATE}/repeat_bid'"
    if [ $? -ne 0 ]; then
        echo "[error] 获取bid重复数据失败"
        exit 1
    fi
    while read LINE
    do
        delete_sql="delete from aoi_test where bid='${LINE}';"
        ${PSQL} -c "${delete_sql}"
        if [ $? -ne 0 ]; then
            echo "[error] 删除bid重复数据失败bid:${LINE}"
            exit 1
        fi
    done < "${WORKPATH}/${DATE}/repeat_bid"
    echo "[INFO] aoi_test数据准备完成"
    echo "[INFO] 数据准备工作结束。。"
}


#将aoi_test数据库备份到aoi_db.txt文件
function update_online_file(){
    echo "[INFO] 复制新增数据库到aoi_db.txt文件"

    #aoi_test导出到aoi_db.txt
    ${PSQL} -c "\copy aoi_test to '${WORKPATH}/${DATE}/aoi_db.txt';"
    wait
    if [ $? -ne 0 ]; then
        echo "[WARNING] 复制新增数据库到aoi_db.txt文件操作错误，请手动回滚"
        exit 1
    fi
    echo "[INFO] 复制新增数据库到aoi_db.txt文件结束"
}

#测压盖生成比率，并输出aoi_rate.txt
function aoirate(){
    #先备份
    if [ -f "${WORKPATH}/${DATE}/aoi_rate.txt" ]; then
        mv "${WORKPATH}/${DATE}/aoi_rate.txt" "${WORKPATH}/${DATE}/aoi_rate_${END_NAME}.txt"
    fi
    if [ -d "${WORKPATH}/data/" ]; then
        echo "[INFO] 数据缓存文件夹存在"
        rm ${WORKPATH}/data/*
        echo "[INFO] 数据缓存清理完成"
    else
        mkdir ${WORKPATH}/data/
    fi
    cd ${WORKPATH}/data/
    #分解
    split -l 2000 ${WORKPATH}/${DATE}/aoi_db.txt -d -a 5 split_
    echo "[INFO] 分割文件完成"
    #kill已存在check_aoi.sh进程
    killprocess check_aoi.sh
    #执行测压盖生成aoi_rate.txt
    echo "[INFO] 生成压盖文件开始 ${WORKPATH}/${DATE}/aoi_rate.txt"
    for file in `ls ${WORKPATH}/data/split_*`
    do
        newfile=${file##*/}
        nohup sh ${WORKPATH}/check_aoi.sh ${WORKPATH}/data/$newfile $WORKPATH $PHP ${WORKPATH}/${DATE}/aoi_rate.txt &
    done
    wait
    echo "[INFO] 生成压盖文件完成"
    mv ${WORKPATH}/data/nohup.out ${WORKPATH}/${DATE}/
    if [ -f "${WORKPATH}/${DATE}/aoi_rate.txt" ]; then
        echo "[INFO] 生成压盖文件：${WORKPATH}/${DATE}/aoi_rate.txt"
    else
        echo "[INFO] 不存在压盖情况"
        touch ${WORKPATH}/${DATE}/aoi_rate.txt
    fi
    echo "[INFO] 生成aoi_rate完成"
    cd ${WORKPATH}/${DATE}/
    #判断压盖逻辑数据没有错误
    if [ -s "${WORKPATH}/data/aoi_rate_error.info" ]; then
        echo "[error] 测试压盖比例数据错误 查看：${WORKPATH}/data/aoi_rate_error.info"
        exit 1
    fi
}

#去重压盖数据
function updatecover(){
    # 删除新增文件中和AOI大库有压盖的数据
    while read LINE
    do
        delete_sql="delete from aoi_test where bid='${LINE}';"
        ${PSQL} -c "${delete_sql}"
        if [ $? -ne 0 ]; then
            echo "[error] 删除bid重复数据失败bid:${LINE}"
            exit 1
        fi
    done < "${WORKPATH}/${DATE}/aoi_rate.txt"
    echo "[INFO] aoi_test删除压盖数据完成"

}

#更新数据库
function updateDataBase_and_sendOnline(){
    echo "[INFO] 开始写入库blu_face_from_se"
    #入库
    ${PSQL} -c "insert into blu_face_from_se select * from aoi_test;"
    wait
    echo "[INFO] 准备上线"
    cd $WORKPATH
    #转换数据格式
    ${PYTHON} ${SCRIPT_PATH}/script/geo_clockwise.py ${WORKPATH}/${DATE}/aoi_db.txt ${WORKPATH}/${DATE}/online
    wait

    #备份上线生成的日志
    if [ -f "${SCRIPT_PATH}/nohup.out" ]; then
        mv ${SCRIPT_PATH}/nohup.out ${SCRIPT_PATH}/CJH_${END_NAME}
    fi
    killprocess aoi_online.sh
    echo "[INFO] 上线数据：${WORKPATH}/${DATE}/online  行数："
    wc -l ${WORKPATH}/${DATE}/online
    sh ${SCRIPT_PATH}/aoi_online.sh ${WORKPATH}/${DATE}/online > ${WORKPATH}/${DATE}/online.log 
    echo "[INFO] 上线完成"
}

function main(){

testEnvironment;
setup;
removeRepeat;
prepare;
update_online_file;
aoirate;
updatecover;
update_online_file;
updateDataBase_and_sendOnline;

    
}

main;
