# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行根据轨迹策略挖掘疑似错误的关联关系
每月1号例行触发一次
Authors: <AUTHORS>
Date:    2023/07/21 09:42:42
"""

import math
import time
from shapely.ops import nearest_points
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
from access_function import AoiGateMatchGeneralTool

class AccessTrajStrategy(PgTool):
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        PgTool.commit(self)

    def get_bid_dig_tra(self):
        """
        查询有轨迹数据的bid信息
        """
        sql = """
            select 
                distinct bid
            from inter_gate_point_cluster
        """
        self.cursor_traj_intel.execute(sql)
        return self.cursor_traj_intel.fetchall()
    
    def get_matched_node_by_bid(self, bid):
        """
        returns: dict()
        """
        node_dict = dict()
        sql = f"""
            select 
                b.node_id, attribute_source
            from 
                blu_access a 
                inner join 
                blu_access_gate_rel b 
            on 
                a.access_id = b.access_id
            where 
                main_bid = '{bid}'
        """
        self.cursor_aoi.execute(sql)
        for node_id, attribute_source, in self.cursor_aoi.fetchall():
            node_dict[node_id] = attribute_source

        sql = '''
            select 
                node_id, 
                action,
                resource
            from gate_bid_change_history
            where 
                bid = %s
                and imp_state = 0 
            order by 
                id 
        '''
        self.cursor_poi.execute(sql, [bid])
        res = self.cursor_poi.fetchall()
        for v in res:
            if v[1] == "add":
                node_dict[v[0]] = v[2]
            if v[1] == "delete":
                if v[0] in node_dict:
                    del node_dict[v[0]]
        return node_dict
    
    def have_traj_point(self, bid, node_geom):
        """
        判断大门附近是否有轨迹挖掘点
        """
        sql = f"""
            select 
                cluster_num
            from inter_gate_point_cluster
            where 
                bid = '{bid}'
                and st_dwithin(geom, st_geomfromtext('{node_geom}', 4326), 0.0003)
        """ 
        self.cursor_traj_intel.execute(sql)
        for num, in self.cursor_traj_intel.fetchall():
            if int(num) > 0:
                return True
        return False
    
    def have_guide_point(self, bid, node_geom):
        """
        判断大门附近是否有引导点
        """
        sql = f"""
            select 
                count(*) 
            from ly_guide_point_split
            where 
                bid = '{bid}'
                and st_dwithin(end_geom, st_geomfromtext('{node_geom}', 4326), 0.0003)
        """
        self.cursor_trajectory.execute(sql)
        return self.cursor_trajectory.fetchone()[0] > 0

    def get_geom_type_by_node(self, node_id):
        """
        获取大门的坐标以及开放类型
        """
        sql = f"""
            select 
                st_astext(geom), a.type
            from 
                nav_gate a 
                inner join 
                nav_node b 
            on 
                a.node_id = b.node_id 
            where 
                a.node_id = '{node_id}'
        """
        self.cursor_road.execute(sql)
        res = self.cursor_road.fetchone()
        return res

    def get_L3_aoi_bid(self):
        """
        返回所有精准AOI的bid
        """
        sql = "select distinct main_bid from blu_face_complete where aoi_complete >= 3"
        self.cursor_aoi.execute(sql)
        return self.cursor_aoi.fetchall()
    
    def get_aoi_geom_by_bid(self, bid):
        """
        返回bid对应框的geom
        """
        sql = f"""
            select 
                st_astext(geom)
            from 
                blu_face a 
                inner join 
                blu_face_poi b 
            on 
                a.face_id = b.face_id
            where 
                poi_bid = '{bid}'
                and aoi_level = 2
        """
        self.cursor_aoi.execute(sql)
        return self.cursor_aoi.fetchone()
    
    def calculate_arc_between_line(self, line_geom1, line_geom2):
        """
        计算两条线段之间的夹角
        """
        coor1 = list(shapely.wkt.loads(line_geom1).coords)
        coor2 = list(shapely.wkt.loads(line_geom2).coords)
        arc1 = math.atan2(coor1[len(coor1) - 1][1] - coor1[0][1], 
                               coor1[len(coor1) - 1][0] - coor1[0][0]) * 180 / math.pi
        arc2 = math.atan2(coor2[len(coor2) - 1][1] - coor2[0][1], 
                               coor2[len(coor2) - 1][0] - coor2[0][0]) * 180 / math.pi
        return arc1 - arc2
    
    def aoi_out_road_gate(self, node_id, aoi_geom):
        """
        获取大门的内部路的连接路
        """
        aoi_region = shapely.wkt.loads(aoi_geom)
        sql = f"""
            select 
                link_id, 
                s_nid, 
                e_nid, 
                st_astext(geom)
            from nav_link 
            where 
                (s_nid = '{node_id}' 
                    or e_nid = '{node_id}'
                ) and form = '52' 
        """
        self.cursor_road.execute(sql)
        res = self.cursor_road.fetchall()
        if res is None or len(res) != 1:
            return False
        inner_link = res[0][0]
        inner_link_geom = res[0][3]
        extension_node = res[0][1]
        if res[0][1] == node_id :
            extension_node = res[0][2]
        
        sql = f"""
            select 
                link_id, 
                st_astext(geom) 
            from nav_link 
            where 
                (s_nid = '{extension_node}'
                    or e_nid  = '{extension_node}'
                ) and link_id != '{inner_link}'
        """
        self.cursor_road.execute(sql)
        res = self.cursor_road.fetchall()
        if res is None or len(res) != 2:
            return False
        inter_link = ""
        apart_link = ""
        link_geom_dict = dict()
        for link_id, link_geom, in res:
            link_geom_dict[link_id] = link_geom
            if shapely.wkt.loads(link_geom).intersects(aoi_region):
                inter_link = link_id
            else:
                apart_link = link_id
        
        if inter_link != "" and apart_link != "":
            arc_int = self.calculate_arc_between_line(link_geom_dict[inter_link], inner_link_geom)
            arc_apa = self.calculate_arc_between_line(link_geom_dict[apart_link], inner_link_geom)
            if abs(arc_int) > 60 and abs(arc_apa) > 60 and abs(arc_int) < 120:
                sql = f"""
                    select 
                        count(*) 
                    from nav_link
                    where 
                        st_intersects(st_geomfromtext('{link_geom_dict[apart_link]}', 4326), geom)
                        and st_intersects(st_geomfromtext('{aoi_geom}', 4326), geom)
                        and link_id != '{inter_link}'
                """
                self.cursor_road.execute(sql)
                if self.cursor_road.fetchone()[0] == 0:
                    return True
        return False
    
    def have_manu_worked(self, bid, node_id):
        """
        判断是否已经下发过人工作业
        """
        sql = f"""
            select 
                count(*) 
            from gate_aoi_match_check_data 
            where 
                bid = '{bid}'
                and node_id = '{node_id}'
        """

        self.cursor_poi.execute(sql)
        if self.cursor_poi.fetchone()[0] > 0:
            return True
        return False
    
    def is_inin_gate(self, node_id):
        """
        判断大门的类型
        """
        sql = f"""
            select 
                count(*) 
            from nav_link 
            where 
                (s_nid = '{node_id}' 
                    or e_nid = '{node_id}'
                ) and not form ~ '52'
        """
        self.cursor_road.execute(sql)
        return self.cursor_road.fetchone()[0] == 0
    
    def have_sub_gate_poi(self, bid, node_geom):
        """
        判断给定的bid所表示的节点是否存在poi门。
        """
        sql = f"""
            select 
                bid
            from poi 
            where 
                st_dwithin(st_geomfromtext('{node_geom}', 4326), geometry, 0.00015) 
                and relation_bid = '{bid}'
                and (name like '%门' 
                    or name like '%门)' 
                    or name like '%门岗' 
                    or name like '%出口'
                    or name like '%入口' 
                    or std_tag = '出入口;门'
                )
        """
        self.cursor_poi.execute(sql)
        return len(self.cursor_poi.fetchall()) > 0
    
    def process_manu_node(self, node_id, bid):
        """
        人工作业的关联关系入库
        """
        sql = """
            insert into 
                gate_aoi_match_check_data_tmp(
                    node_id, 
                    bid, 
                    source, 
                    ways
                )
                values(
                    %s, 
                    %s, 
                    '关联关系例行批处理-轨迹策略例行清查提准', 
                    '清查提准'
                )
        """
        self.cursor_poi_write.execute(sql, [node_id, bid])

    def run(self): 
        """更新大门通行属性的入口函数
        """
        for bid, in self.get_bid_dig_tra():
            aoi_geom = self.get_aoi_geom_by_bid(bid)
            if aoi_geom is None:
                continue
            aoi_geom = aoi_geom[0]
            node_dict = self.get_matched_node_by_bid(bid)
            if len(node_dict) == 1 or len(node_dict) >= 4:
                continue
            for node_id in node_dict:
                if node_dict[node_id] == 1 or node_dict[node_id] == 2:
                    continue
                if self.is_inin_gate(node_id):
                    continue
                res = self.get_geom_type_by_node(node_id)
                if res is None or res[1] == 0:
                    continue
                node_geom = res[0]
                aoi_node_int_geom = list(nearest_points(shapely.wkt.loads(node_geom), 
                                        shapely.wkt.loads(aoi_geom).boundary))[1].wkt
                if (self.have_traj_point(bid, node_geom) or self.have_guide_point(bid, node_geom)
                    or self.have_traj_point(bid, aoi_node_int_geom) 
                    or self.have_guide_point(bid, aoi_node_int_geom)):
                    continue
                if not self.have_manu_worked(bid, node_id) and not self.have_sub_gate_poi(bid, node_geom):
                    #print(bid + "\t" + node_id)
                    self.process_manu_node(node_id, bid)
        
        self.commit()
        
        # for bid, in self.get_L3_aoi_bid():
        #     aoi_geom = self.get_aoi_geom_by_bid(bid)
        #     if aoi_geom is None:
        #         continue
        #     aoi_geom = aoi_geom[0]
        #     node_set = self.get_matched_node_by_bid(bid)
        #     for node_id in node_set:
        #         res = self.get_geom_type_by_node(node_id)
        #         if res is None or res[1] == 0:
        #             continue
        #         node_geom = res[0]
        #         if shapely.wkt.loads(aoi_geom).contains(shapely.wkt.loads(node_geom)):
        #             continue
        #         if self.aoi_out_road_gate(node_id, aoi_geom):
        #             print(bid + "\t" + node_id + "\taoi附近道路上的大门")
        
                
if __name__ == '__main__':
    obj = AccessTrajStrategy()
    obj.run()
