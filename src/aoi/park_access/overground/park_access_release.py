# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""
import datetime
import sys
import os

import requests as requests
import tqdm
import json
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.park_access import park_utils
from src.aoi import utils

COORDINATE_FACTOR = 100000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")


def out_park_access_release():
    """
    出口推送上线
    Returns:

    """
    park_access_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
        type='park_access_overground_out_add_auto', status='INIT')
    if not park_access_list:
        logger.info(f"没有需要推送的停车场情报")
        return
    for park_access in tqdm.tqdm(park_access_list):
        # 查询停车场信息
        park_list = poi_online_query.get_parking_list_by_kv(bids=[park_access['bid']])
        if not park_list:
            logger.error(f"未找到停车场信息:{park_access['bid']}")
            continue
        # 判断情报是否已推送
        if is_pushed(park_access['bid'], wkt.loads(park_access['geom'])):
            poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='INFO_PUSHED')
            logger.info(f"该停车场情报已推送:{park_access['bid']}")
            continue
        # 判断是否已存在精准出口
        if is_exist_accurate_out_park_access(park_access['bid'], park_access['geom']):
            poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']],
                                                                       status='EXIST_ACCURATE_OUT_PARK_ACCESS')
            logger.info(f"附近已存在精准出口:{park_access['bid']}")
            continue
        park_info = park_list[0]
        park_access_name = park_utils.get_park_access_name_by_road_relation(park_info['name'],
                                                                            json.loads(park_access['road_relation']))
        if not park_access_name:
            logger.error(f"停车场出入口名称匹配失败:{park_access['bid']}")
            continue
        # 如果root_pv大于5000且是入口,则不推送,风险太大
        if ('入口' in park_access_name or '出入口' in park_access_name) and park_access['root_pv'] > 5000:
            logger.info(f"pv过高且是出入口跳过:{park_access['bid']},pv:{park_access['root_pv']}")
            continue
        # 计算出入口坐标
        park_access_mc_sp = wkt.loads(utils.gcj_to_mc(park_access['geom']))
        park_access_achieve = {
            "source_id": park_access["source_id"],
            "road_relation": park_access["road_relation"],
            "parent_bid": park_access["bid"],
            "park_access_name": park_access_name,
            "address": park_info["address"],
            "point_x": park_access_mc_sp.x,
            "point_y": park_access_mc_sp.y
        }
        push_park_access(park_access_achieve)
        poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='PUSHED')


def in_park_access_release():
    """
    出入口推送上线
    Returns:

    """
    park_access_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
        type='park_access_overground_add_auto', status='INIT')
    if not park_access_list:
        logger.info(f"没有需要推送的停车场情报")
        return
    for park_access in tqdm.tqdm(park_access_list):
        # 查询停车场信息
        park_list = poi_online_query.get_parking_list_by_kv(bids=[park_access['bid']])
        if not park_list:
            logger.error(f"未找到停车场信息:{park_access['bid']}")
            continue
        # 判断情报是否已推送
        if is_in_park_access_pushed(park_access['bid'], wkt.loads(park_access['geom'])):
            poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='INFO_PUSHED')
            logger.info(f"该停车场情报已推送:{park_access['bid']}")
            continue
        # 判断是否已存在精准出入
        if is_exist_accurate_in_park_access(park_access['bid'], park_access['geom']):
            poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']],
                                                                       status='EXIST_ACCURATE_PARK_ACCESS')
            logger.info(f"附近已存在精准出入口:{park_access['bid']}")
            continue
        park_info = park_list[0]
        park_access_name = park_utils.get_park_access_name_by_road_relation(park_info['name'],
                                                                            json.loads(park_access['road_relation']))
        if not park_access_name:
            logger.error(f"停车场出入口名称匹配失败:{park_access['bid']}")
            continue
        # 如果root_pv大于5000且是入口,则不推送,风险太大
        if park_access['root_pv'] > 5000:
            logger.info(f"pv过高且是出入口跳过:{park_access['bid']},pv:{park_access['root_pv']}")
            continue
        # 计算出入口坐标
        park_access_mc_sp = wkt.loads(utils.gcj_to_mc(park_access['geom']))
        park_access_achieve = {
            "source_id": park_access["source_id"],
            "road_relation": park_access["road_relation"],
            "parent_bid": park_access["bid"],
            "park_access_name": park_access_name,
            "address": park_info["address"],
            "point_x": park_access_mc_sp.x,
            "point_y": park_access_mc_sp.y
        }
        push_park_access(park_access_achieve)
        poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='PUSHED')


def is_pushed(park_bid, park_access_sp):
    """
    判断情报是否已推送
    Returns:

    """
    # 查询已推送的情报
    pushed_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(bid=park_bid, type=[
        'park_access_overground_out_add'], status='PUSHED')
    if not pushed_intelligence_list:
        return False
    pushed_geom_list = [{'id': item['id'], 'geom_sp': wkt.loads(item['geom'])} for item in pushed_intelligence_list]
    distance_value = 20
    is_pushed_intelligence = [item['id'] for item in pushed_geom_list
                              if park_access_sp.distance(item['geom_sp']) <= distance_value / COORDINATE_FACTOR]
    if len(is_pushed_intelligence) > 0:
        return True
    return False


def is_in_park_access_pushed(park_bid, park_access_sp):
    """
    判断情报是否已推送
    Returns:

    """
    # 查询已推送的情报
    pushed_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(bid=park_bid, type=[
        'park_access_overground_add'], status='PUSHED')
    if not pushed_intelligence_list:
        return False
    pushed_geom_list = [{'id': item['id'], 'geom_sp': wkt.loads(item['geom'])} for item in pushed_intelligence_list]
    distance_value = 10
    is_pushed_intelligence = [item['id'] for item in pushed_geom_list
                              if park_access_sp.distance(item['geom_sp']) <= distance_value / COORDINATE_FACTOR]
    if len(is_pushed_intelligence) > 0:
        return True
    return False


def is_exist_accurate_out_park_access(park_bid, park_access_geom):
    """
    判断附近20m是否已存在精准出口
    Args:
        park_bid:
        park_access_geom:

    Returns:

    """
    park_access_sp = wkt.loads(park_access_geom)
    relation_out_park_access_sp_list = park_utils.get_relation_out_park_access(park_bid)
    if not relation_out_park_access_sp_list:
        return False
    if len([relation_sp for relation_sp in relation_out_park_access_sp_list
            if park_access_sp.distance(relation_sp) < 20 / COORDINATE_FACTOR]) > 0:
        return True
    return False


def is_exist_accurate_in_park_access(park_bid, park_access_geom):
    """
    判断附近20m是否已存在精准出入口
    Args:
        park_bid:
        park_access_geom:

    Returns:

    """
    park_access_sp = wkt.loads(park_access_geom)
    relation_out_park_access_sp_list = park_utils.get_relation_out_park_access(park_bid, orientation=1)
    if not relation_out_park_access_sp_list:
        return False
    if len([relation_sp for relation_sp in relation_out_park_access_sp_list
            if park_access_sp.distance(relation_sp) < 20 / COORDINATE_FACTOR]) > 0:
        return True
    return False


def push_park_access(park_access):
    """
    出入口推送上线
    Args:
        park_access:

    Returns:

    """
    # 开始推送
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    show_tag = park_access['park_access_name'].split('-')[-1]
    date = datetime.datetime.now().strftime("%Y%m%d")
    params = {
        "source": "STRATEGY_OUT_PARK_ACCESS",
        "source_id": f"STRATEGY_{park_access['source_id']}",
        "is_add_poi": 1,
        "batch_id": f"STRATEGY_OUT_PARK_ACCESS_{date}",
        "std_tag": "出入口;停车场出入口",
        "show_tag": f"停车场{show_tag}",
        "name": park_access['park_access_name'],
        "address": park_access['address'],
        "point_x": park_access['point_x'],
        "point_y": park_access['point_y'],
        "status": 1,
        "parent_id": park_access['parent_bid'],
        "road_relation": park_access['road_relation'],
    }
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"出入口推送上线:req:{params},resp:{res.text}")


if __name__ == '__main__':
    # 出入口成果自动上线
    release_type = sys.argv[1]
    if release_type == 'out':
        out_park_access_release()
    if release_type == 'in':
        in_park_access_release()
