"""
增加众源采集任务
"""
import datetime

from src.door_naming.strategy_v2 import common
from src.common import pgsql, mysql
import requests
import json
import tqdm


def create_collect(bid):
    """
    创建抓取任务
    :param bid:
    :return:
    """
    aoi_info = common.get_aoi_by_main_bid(bid)
    if not aoi_info:
        print("无边框,无法采集")
        return
    if check_collected(bid):
        print("近期已经下发过")
        return
    push_data = {
        "bid": bid,
        "name": "store_front",
        "wkt": aoi_info.geom,
        "buffer": 1,
    }
    push_ret = push_zhongyuan(push_data)
    print('推送结果', push_ret)


def check_collected(bid):
    """
    检查近期是不是下发过
    暂时按照30天算
    :param bid:
    :return:
    """
    month_ago = datetime.datetime.now() - datetime.timedelta(days=30)
    sql = 'select id from collect_task where bid=:d1 and  created_at >= :d2'
    ret = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RD, sql, {"d1": bid, "d2": month_ago})
    return ret


def push_zhongyuan(payload):
    """
    将指定的POI信息推送到POI接口
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/createCollectTask"

    print(f"payload : {payload}")
    headers = {
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, json=payload)
    # response = requests.post(url, json={
    #     "bid": bid,
    #     "name": name,
    #     "wkt": wkt,
    #     "buffer": 1,
    # })
    print(response.text)
    # time.sleep(1)
    return json.loads(response.text)


def multi_add_collect():
    """
    从情报表里面投放任务
    :return:
    """
    yesterday = datetime.date.today() - datetime.timedelta(days=1)
    sql = 'select bid from apartment_intelligence_task where created_at > :d1'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": yesterday})
    for item in tqdm.tqdm(ret):
        bid = item.bid
        create_collect(bid)


if __name__ == '__main__':
    multi_add_collect()
