# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
todo：
    1.新增的关联关系如果加入进来（或以bid为基准统计改为以faceid为基准统计？没有关联主点的AOI对导航来讲如何应用？）
    2.当前只记录了匹配关系，未考虑是否已经转为出入口
    3.人工作业的出入口没有记录和维护，可能会影响对L1 L2级别AOI的判断，推动出入口的履历库建设

AOI的修改场景:
1. 新增AOI并关联了主点bid  是否不存在没有关联主点的场景？
2. 删除并重新画了AOI，faceid变更但是主点不变
3. 删除AOI
4. 新增AOI的关联关系
"""

import time
import collections
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
from access_function import AoiGateMatchGeneralTool
from src.AccessMatch.BatchCtx import BatchContext


class AoiInOutGateMatch:
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """

    def __init__(self, ctx: BatchContext):
        """
        初始化连接数据库
        """
        self.ctx = ctx

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def commit(self):
        self.ctx.pg_tool.commit()

    def find_node_to_process_with_bid(self, bid):
        """
        根据bid寻找附近可能关联的大门信息
        Args:
            bid: poi的bid
        Return:
            face_id: aoi的主键id
            node_id_set: set类型, 大门的nodeid集合
        """
        node_id_set = set()
        sql = '''
            select 
                st_astext(geom),kind,aoi_level, bf.face_id 
            from blu_face bf inner join blu_face_poi bfp
            on 
                bf.face_id = bfp.face_id 
            where 
                bfp.poi_bid = %s 
                and bf.src != 'SD'
        '''
        self.ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res_aoi = self.ctx.pg_tool.cursor_aoi.fetchone()
        if res_aoi is None or len(res_aoi) == 0:
            # print("没有关联的AOI\t" + bid)
            return "", node_id_set
        if res_aoi[1] == '52' or res_aoi[2] != 2:
            # print("AOI为商圈或非基础院落\t" + bid)
            return "", node_id_set
        aoi_geom = res_aoi[0]
        face_id = res_aoi[3]
        num_gate = 0
        sql = '''
            with tmp as (
                select 
                   link_id, st_intersection(st_geomfromtext(%s, 4326), geom) as geom 
                from nav_link 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom)
            ) 
            select 
                link_id, st_astext((st_dump(geom)).geom) as geom 
            from tmp;'''
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_geom, aoi_geom])
        res_point = self.ctx.pg_tool.cursor_road.fetchall()
        if res_point is not None and len(res_point) > 0:
            for point_geom in res_point:
                sql = """
                    select 
                        string_agg(node_id, ''',''') 
                    from nav_node 
                    where 
                        st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)"""
                self.ctx.pg_tool.cursor_road.execute(sql, [point_geom[1]])
                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                if res_node is None or len(res_node) == 0:
                    continue
                node_str = res_node[0][0]
                sql = '''
                    select 
                        node_id, in_linkid, out_linkid 
                    from nav_gate 
                    where 
                    node_id in ('%s')''' % (node_str)
                self.ctx.pg_tool.cursor_road.execute(sql)

                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                if res_node is not None:
                    num_gate = num_gate + len(res_node)
                for res_node_tmp in res_node:
                    sql = '''
                        select 
                            form 
                        from nav_link 
                        where 
                            link_id = %s 
                            or link_id = %s'''
                    self.ctx.pg_tool.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                    res_form = self.ctx.pg_tool.cursor_road.fetchall()
                    # if ((res_form[0][0] == "52" and res_form[1][0] != "52") or
                    #         (res_form[1][0] == "52" and res_form[0][0] != "52")):
                    if self.ctx.check_is_inner_road(res_form[0][0]) != self.ctx.check_is_inner_road(res_form[1][0]):
                        node_id_set.add(res_node_tmp[0])

        # 找到AOI框内的大门
        sql = """
            select 
                string_agg(node_id, ''',''') 
            from nav_node 
            where 
                st_contains(st_geomfromtext(%s, 4326), geom)"""
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_geom])
        res_node = self.ctx.pg_tool.cursor_road.fetchall()
        if res_node is not None and len(res_node) > 0:
            node_str = res_node[0][0]
            sql = '''
                select 
                    node_id, in_linkid, out_linkid 
                from nav_gate 
                where 
                node_id in ('%s')''' % (node_str)
            self.ctx.pg_tool.cursor_road.execute(sql)
            res_node = self.ctx.pg_tool.cursor_road.fetchall()
            if res_node is not None:
                num_gate = num_gate + len(res_node)
            for res_node_tmp in res_node:
                sql = '''
                    select 
                        form 
                    from nav_link 
                    where 
                        link_id = %s 
                        or link_id = %s'''
                self.ctx.pg_tool.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                res_form = self.ctx.pg_tool.cursor_road.fetchall()
                # if (res_form[0][0] == "52" and res_form[1][0] != "52") or (
                #         res_form[1][0] == "52" and res_form[0][0] != "52" and
                #         "," not in res_form[1][0] and "," not in res_form[0][0]):
                if self.ctx.check_is_inner_road(res_form[0][0]) != self.ctx.check_is_inner_road(res_form[1][0]):
                    node_id_set.add(res_node_tmp[0])

        return face_id, node_id_set

    def find_aoi_by_link(self, link_geom_dict, node_geom, face_id, node_link_info):
        """
        根据link找大门的候选AOI
        Args:
            link_geom_dict: 有序字典类型，key是linkid，value是link的几何信息
            node_id: 大门的nodeid
            face_id: aoi的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
        """
        face_id_dict = collections.OrderedDict()
        filter_reason = []
        node_region = shapely.wkt.loads(node_geom)

        for link_id, link_geom in link_geom_dict.items():
            link_region = shapely.wkt.loads(link_geom)
            # 查找相交的AOI
            sql = '''
                select 
                    face_id, 
                    st_astext(geom) 
                from blu_face 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom) 
                    and kind != '52' 
                    and aoi_level = 2'''
            self.ctx.pg_tool.cursor_aoi.execute(sql, [link_geom])
            res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
            for res_aoi_tmp in res_aoi:
                aoi_region = shapely.wkt.loads(res_aoi_tmp[1])
                aoi_contain_node = aoi_region.contains(node_region)
                if ((link_id == node_link_info[1] and not aoi_contain_node)
                        or (link_id != node_link_info[1] and aoi_contain_node)):
                    continue
                link_inter = link_region.intersection(aoi_region)
                point_region = link_region.intersection(aoi_region.boundary)
                if point_region.geom_type == "MultiPoint":
                    continue
                if point_region.distance(node_region) > 0.0004:
                    if face_id == res_aoi_tmp[0]:
                        filter_reason.append("大门距离link和AOI的交点大于40米")
                    continue
                if link_inter.length / aoi_region.length < 0.005:
                    # link与aoi相交的部分长度小于周长的0.5%
                    sql = '''
                        select 
                            node_id, 
                            st_astext(a.geom) 
                        from nav_node a inner join nav_link b 
                        on 
                            a.node_id = b.s_nid 
                            or a.node_id = b.e_nid 
                        where 
                            b.link_id = %s'''
                    self.ctx.pg_tool.cursor_road.execute(sql, [link_id])
                    res_node = self.ctx.pg_tool.cursor_road.fetchall()
                    for res_node_tmp in res_node:
                        # 找到link两个端点的坐标，判断是否在AOI内
                        if aoi_region.contains(shapely.wkt.loads(res_node_tmp[1])):
                            # 在aoi内，判断是否连接了别的内部路
                            sql = '''
                                select 
                                    count(*) 
                                from nav_link
                                where 
                                    (s_nid = %s 
                                        or e_nid = %s) 
                                    and link_id != %s 
                                    and form like %s
                            '''
                            self.ctx.pg_tool.cursor_road.execute(sql,
                                                                 [res_node_tmp[0], res_node_tmp[0], link_id, '%52%'])
                            if self.ctx.pg_tool.cursor_road.fetchone()[0] == 0:
                                # 没有连接其余内部路，不认为与该aoi匹配
                                # if face_id == res_aoi_tmp[0]:
                                #     此场景目前有效率低，暂不下发人工
                                #     filter_reason.append("断头路场景下发人工作业")
                                break
                            else:
                                face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]
                else:
                    face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]

        return face_id_dict, filter_reason

    def find_aoi_with_outlink(self, node_id):
        """
        根据node查询外部路方向的候选AOI
        """
        face_id_dict = collections.OrderedDict()
        link_geom_dict = dict()
        sql = '''
            select 
                link_id, s_nid, e_nid, b.node_id, form, st_astext(geom)
            from nav_link a inner join nav_gate b
            on 
                a.link_id = b.out_linkid 
                or a.link_id = b.in_linkid
            where 
                b.node_id = %s 
                and a.form not like %s '''
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id, '%52%'])
        link_res = self.ctx.pg_tool.cursor_road.fetchone()
        node_id_extension = link_res[2] if link_res[1] == node_id else link_res[1]

        sql = '''
            select 
                link_id, st_astext(geom) 
            from nav_link
            where 
                (s_nid = %s 
                    or e_nid = %s )
                and link_id != %s 
                and form not like %s '''
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id_extension, node_id_extension, link_res[0], '%52%'])
        link_info = self.ctx.pg_tool.cursor_road.fetchall()
        if link_info is None or len(link_info) == 0:
            return face_id_dict, link_geom_dict
        for link_info_tmp in link_info:
            link_geom_dict[link_info_tmp[0]] = link_info_tmp[1]
            # 查找相交的AOI
            sql = '''
                select 
                    face_id, st_astext(geom) 
                from blu_face 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom) 
                    and st_contains(geom, st_geomfromtext(%s, 4326)) 
                    and kind != '52'
                    and aoi_level = 2'''
            self.ctx.pg_tool.cursor_aoi.execute(sql, [link_info_tmp[1], link_res[5]])
            res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
            if res_aoi is None or len(res_aoi) == 0:
                continue
            for res_aoi_tmp in res_aoi:
                face_id_dict[res_aoi_tmp[0]] = link_res[0], res_aoi_tmp[1]

        return face_id_dict, link_geom_dict

    def run(self, num):
        """更新大门通行属性的入口函数
        """
        # print("内外大门批处理策略开始：" + 
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
        # 寻找待匹配的大门和AOI的关联关系的bid列表
        aoi_gate_match_general_obj = AoiGateMatchGeneralTool(self.ctx.pg_tool)
        if self.ctx.bid:
            face_bid_dict = {self.ctx.bid: ""}
        else:
            face_bid_dict = aoi_gate_match_general_obj.find_bid_to_match(num)
        # num_all = len(face_bid_dict)
        process_num = 0.0
        for bid, face_id_dst in face_bid_dict.items():
            try:
                process_num = process_num + 1
                # print("内外大门匹配策略进度:" + str(process_num / num_all))
                face_id_dst_tmp, node_set = self.find_node_to_process_with_bid(bid)
                if face_id_dst == "":
                    face_id_dst = face_id_dst_tmp
                if face_id_dst_tmp == "":
                    continue
                if len(node_set) > self.ctx.strategy_node_max:
                    if self.ctx.debug:
                        print(f'需要处理的node过多，不处理: {len(node_set)}')
                        exit(100)
                    else:
                        aoi_gate_match_general_obj.set_bid_intel_fail(bid, f"需要处理的node过多:{len(node_set)}")
                    continue
                if face_id_dst_tmp != face_id_dst:
                    if self.ctx.debug:
                        print('关联了两个aoi， 不处理')
                        exit(100)
                    else:
                        aoi_gate_match_general_obj.set_bid_intel_fail(bid, "关联了两个AOI")
                    continue

                for node_id in node_set:
                    # 需要重新匹配的场景
                    node_geom = aoi_gate_match_general_obj.get_node_geom(node_id)
                    if node_geom is None:
                        continue
                    link_geom_dict, node_link_info = aoi_gate_match_general_obj.find_link_by_node(node_id)

                    # 根据link特征匹配AOI
                    face_id_dict, filter_reason = self.find_aoi_by_link(link_geom_dict, node_geom, face_id_dst,
                                                                        node_link_info)
                    for tmp_reason in filter_reason:
                        aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                         "add",
                                                                         f"例行更新流程内外大门批处理策略召回-{tmp_reason}",
                                                                         "manu-check")
                    if len(face_id_dict) == 0:
                        face_id_dict, outlink_geom_dict = self.find_aoi_with_outlink(node_id)
                        # 基础院落若包含高等级道路 则高等级道路上的大门也应该关联
                        if aoi_gate_match_general_obj.find_node_adjacent_high_link(face_id_dst, outlink_geom_dict,
                                                                                   node_geom):
                            aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                             "add",
                                                                             "例行更新流程内外大门批处理策略召回-内部连接高等级道路",
                                                                             "manu-check")
                    if len(face_id_dict) == 0:
                        # 这里大门和aoi在两条link之内不相交，可能是数据问题，可以进行轨迹验证,或者下发人工作业
                        if aoi_gate_match_general_obj.aoi_contains_geom(face_id_dst, node_geom):
                            # 下发人工作业
                            continue
                        continue

                    # 选择最优的AOI
                    face_id_opt = aoi_gate_match_general_obj.find_opt_aoi(face_id_dict)
                    if face_id_opt == "":
                        continue
                    if face_id_opt != face_id_dst:
                        continue

                    aoi_gate_match_general_obj.add_match_achievement(node_id, bid, face_id_dst,
                                                                     "add", "例行更新流程内外大门批处理策略召回",
                                                                     "batch")

                self.commit()
                aoi_gate_match_general_obj.commit()
            except Exception as e:
                print(f"内外大门批处理策略异常:{e}", bid)
                e_msg = str(e)
                if 'connection' in e_msg or 'slots' in e_msg or 'closed' in e_msg:
                    time.sleep(10)
                    self.ctx.pg_reconnect()
                else:
                    raise e
        # print("内外大门批处理策略完成：" +
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


if __name__ == '__main__':
    pass
    # obj = AoiInOutGateMatch()
    # obj.run()
