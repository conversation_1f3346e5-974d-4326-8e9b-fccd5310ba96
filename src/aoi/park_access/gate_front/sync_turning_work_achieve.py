# -*- coding: utf-8 -*-
"""
同步图灵作业成果
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/SWyXWRxQ4s5LK8
"""
import json
import logging
import multiprocessing
import random
import subprocess
import sys
import os
import traceback

import shapely.wkt
import tqdm
import requests
from loguru import logger
from shapely import wkt


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.road import Road
from src.aoi import utils
from src.aoi.dao.trans import Trans

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
road_dao = Road()
traj_dao = DestTraj()
trans_dao = Trans()

# logger.remove()
log_path = "./log/overground_intelligence_push.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True
TURNING_BP_ID = '1210'
TURNING_BP_UPD_ID = '1213'

# 关联类型
RELATE_TYPE_GATE = 1
RELATE_TYPE_LINK = 2
RELATE_TYPE_MIS_ROAD = 3

# 作业结论
CONCLUSION_ACCURATE = 1
CONCLUSION_ADD = 2
CONCLUSION_UPDATE = 3
CONCLUSION_DELETE = 4
CONCLUSION_INVALID = 5


def new_park_access(source_id=None, turing_id=None, data_type=TURNING_BP_ID):
    """
    获取图灵推送成果入库
    Args:
        source_id:

    Returns:

    """
    # 获取待入库的成果数据
    turing_ids = [turing_id] if turing_id else None
    park_access_achieve_list = poi_online_query.get_park_access_turing_result(data_type=data_type,
                                                                              source_id=source_id,
                                                                              turing_ids=turing_ids)
    print(f"data_type: {data_type}; {len(park_access_achieve_list)}")
    if not park_access_achieve_list:
        logger.info("无待入库的成果数据")
        return

    all_callback_bids = []
    for park_access in tqdm.tqdm(park_access_achieve_list):
        try:
            bf_message = json.loads(park_access['bp_message'])
            turing_id = park_access['turing_id']
            # 成果解析入库
            edit_commit = json.loads(bf_message['data'])['edit_commit']
            park_bid = edit_commit['main_poi_bid']
            logger.info(f"\n人工成果入库:{turing_id},{park_bid}")
            # 查询门前停车场信息
            park_list = poi_online_query.get_park_storefront_result(bids=[park_bid])
            if not park_list:
                raise Exception(f'未查询到推送成果对应的门前停车场信息:{turing_id}')
            # 判读是否全部回调
            if data_type == TURNING_BP_ID and not is_all_callback(park_bid, data_type):
                print(f"{turing_id} 相关的作业没有全部回调; bid: {park_bid}")
                continue
            # 回调图灵接收成功
            # callback_by_turing_id(park_access['turing_id'], data_type)

            # 手动作业成果入库
            park_info = park_list[0]
            if data_type == TURNING_BP_ID:
                exist_valid_park_access = save_manual_work_achieve(edit_commit, turing_id, park_info)
            else:
                exist_valid_park_access = save_update_manual_work_achieve(edit_commit, turing_id, park_info)

            # print(exist_valid_park_access)
            # continue

            all_callback_bids.append(park_bid)
            # 更新消息状态
            poi_online_rw.update_parking_turing_result('DONE', turing_id)
            if exist_valid_park_access and park_info['status'] in ['PARK_ACCESS_INTELLIGENCE_PUSHED', 'REPAIRING']:
                poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status(
                    from_status=park_info['status'],
                    to_status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALING',
                    park_bids=[park_bid])
        except Exception as e:
            logger.error(f"推送成果入库失败:{park_access['turing_id']},{e}")
            traceback.print_exc()
    poi_online_rw.update_park_storefront_prod_parking_by_bid_and_status(
        from_status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALING',
        to_status='READY_ONLINE',
        park_bids=all_callback_bids)


def _decide_related_type(sub_park_access: dict) -> int:
    """
    确定 related_type
    """
    if sub_park_access['relation_type'] in [RELATE_TYPE_GATE, RELATE_TYPE_LINK, RELATE_TYPE_MIS_ROAD]:
        return sub_park_access['relation_type']
    if len(sub_park_access.get('gate_info', [])) > 0:
        return RELATE_TYPE_GATE
    if len(sub_park_access.get('link_info', [])) > 0:
        return RELATE_TYPE_LINK
    return sub_park_access['relation_type']


def save_manual_work_achieve(edit_commit: dict, turing_id: str, park_info: dict):
    """
    作业成果入库
    Returns:

    """
    exist_valid_park_access = False
    # 无效停车场,成果不入库
    if edit_commit['parking_conclusion'] != 1:
        logger.info(f"无效停车场\t{turing_id}\t无效:{edit_commit['parking_conclusion']}")
        return exist_valid_park_access
    # 查询park_id
    park_id = -1
    source_ids = [item['unique_id'] for item in edit_commit['sub_info']
                  if "unique_id" in item and item['unique_id'] != '']
    if len(source_ids) > 0:
        park_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(
            source_ids=source_ids)
        park_id = park_intelligence_list[0]['outside_task_id'] if park_intelligence_list else park_id
    park_access_list = []
    for sub_park_access in edit_commit['sub_info']:
        if "unique_id" not in sub_park_access:
            # raise Exception(f'推送成果{turing_id}不存在unique_id')
            print(f'推送成果{turing_id}不存在unique_id')
            sub_park_access['unique_id'] = ''

        # 更新情报结论
        if sub_park_access['unique_id'] != '':
            source_ids.append(sub_park_access['unique_id'])
            poi_online_rw.update_park_access_intelligence_status_by_source_id(
                sub_park_access['unique_id'], sub_park_access['entry_verification_conclusion'])
        if sub_park_access['entry_verification_conclusion'] != CONCLUSION_ADD:
            logger.info(
                f"出入口成果\t{sub_park_access['unique_id']}\t无效:{sub_park_access['entry_verification_conclusion']}")
            continue
        # 关联大门
        manual_work_comment = sub_park_access['sub_content'] if sub_park_access['sub_content'] is not None else ''

        # 不建议开启
        sub_park_access['relation_type'] = _decide_related_type(sub_park_access)
        if sub_park_access['relation_type'] == RELATE_TYPE_GATE:
            gate_info = sub_park_access['gate_info']
            road_relation, park_access_geom = get_road_relation_by_node_id(gate_info[0]['properties']['id'])
            park_access_geom = utils.mc_to_gcj(park_access_geom)
        # 关联LINK
        elif sub_park_access['relation_type'] == RELATE_TYPE_LINK:
            link_info = sub_park_access['link_info']
            # road_relation, park_access_geom = get_road_relation_by_link_id(link_info[0]['properties']['id'])

            point_info = sub_park_access['point_info']
            road_relation, park_access_geom = get_road_relation_by_link_id_v2(
                link_info[0]['properties']['id'], point_info)
            park_access_geom = utils.mc_to_gcj(park_access_geom)
        # 缺路
        elif sub_park_access['relation_type'] == RELATE_TYPE_MIS_ROAD:
            point_info = sub_park_access['point_info']
            road_relation = json.dumps({})
            park_access_geom = utils.bd09_to_gcj(f"point({point_info[0]} {point_info[1]})")
            manual_work_comment = '缺路戳点'
        else:
            raise Exception(f'推送成果{turing_id}关联类型错误:{sub_park_access["relation_type"]}')

        # 保存一下照片，缺路等场景需要用到
        pic_urls = []
        if 'sub_pic' in sub_park_access:
            for sub_pic in sub_park_access['sub_pic']:
                if 'picurl' in sub_pic:
                    pic_urls.append(sub_pic['picurl'])

        # 入成果库
        park_access_list.append({
            "source_id": sub_park_access['unique_id'],
            "face_id": turing_id,
            "parent_bid": park_info['bid'],
            "name": f"{park_info['name'].replace('-门前停车场', '门前停车场')}-入口",
            "address": park_info['address'],
            "road_relation": road_relation,
            "geom": park_access_geom,
            "remark": "turing",
            "status": 'READY_ONLINE',
            "scene": manual_work_comment,
            "suggest_is_open": sub_park_access['suggest_is_open'],
            "park_id": park_id,
            "relation_type": sub_park_access['relation_type'],
            'pic_urls': json.dumps(pic_urls, ensure_ascii=False),
        })
    for data in park_access_list:
        # 判断成果是否重复(不检测LINK,工艺定的,link存在重复属于正常情况,质检兜底拦截再核实):
        if data['relation_type'] != RELATE_TYPE_LINK \
                and is_repeat_park_access(data['parent_bid'], data['road_relation'], data['geom']):
            logger.warning(f"推送成果重复:{data['source_id']}-{data['road_relation']}")
            continue
        logger.info(f"推送成果入库:{data},source_ids:{source_ids}")
        poi_online_rw.insert_park_storefront_prod_access(data)
        exist_valid_park_access = True
    return exist_valid_park_access


def save_update_manual_work_achieve(edit_commit: dict, turing_id: str, park_info: dict):
    """
    更新作业成果入库
    更新的作业结果，可能有新增，更新，删除
    """
    if edit_commit['parking_conclusion'] != 1:
        logger.info(f"更新存在 无效停车场\t{turing_id}\t无效:{edit_commit['parking_conclusion']}")
        return False

    park_id = _fetch_park_id(edit_commit)
    for sub_park_access in edit_commit['sub_info']:
        # 质检作业成果
        _check_update_access_achieve(sub_park_access)

        conclusion = sub_park_access['entry_verification_conclusion']
        is_add = int(sub_park_access['entry_verify_type']) == 1
        if is_add and sub_park_access['unique_id'] != '':
            # 更新新增情报结论
            poi_online_rw.update_park_access_intelligence_status_by_source_id(
                sub_park_access['unique_id'], conclusion)

        if _updated_access_achieve_not_need_save(sub_park_access):
            # 成果不需要保存
            continue
        if conclusion == CONCLUSION_DELETE:
            data = {
                "remark": "turing",
                "status": 'READY_OFFLINE',
                "face_id": turing_id,
            }
        else:
            # 提取作业信息
            road_relation, park_access_geom, manual_work_comment = _extract_work_info(sub_park_access)
            data = {
                "source_id": sub_park_access['unique_id'],
                "face_id": turing_id,
                "parent_bid": park_info['bid'],
                "name": f"{park_info['name'].replace('-门前停车场', '门前停车场')}-入口",
                "address": park_info['address'],
                "road_relation": road_relation,
                "geom": park_access_geom,
                "remark": "turing",
                "status": 'READY_ONLINE',
                "scene": manual_work_comment,
                "suggest_is_open": sub_park_access['suggest_is_open'],
                "pic_urls": json.dumps([], ensure_ascii=False),
            }
            if is_add and park_id == -1:
                park_id = park_info['id']
            if park_id != -1:
                data['park_id'] = park_id

            # 保存一下照片，缺路等场景需要用到
            pic_urls = []
            if 'sub_pic' in sub_park_access:
                for sub_pic in sub_park_access['sub_pic']:
                    if 'picurl' in sub_pic:
                        pic_urls.append(sub_pic['picurl'])
            if len(pic_urls) > 0:
                data['pic_urls'] = json.dumps(pic_urls, ensure_ascii=False)

            # 判断成果是否重复:
            if (is_add
                    and sub_park_access['relation_type'] != RELATE_TYPE_LINK
                    and is_repeat_park_access(data['parent_bid'], data['road_relation'], park_access_geom)):
                logger.warning(f"推送成果重复:{sub_park_access['unique_id']}-{data['road_relation']}")
                continue

        logger.info(f"推送成果入库:{data}; is_add: {is_add}; uq_id:{sub_park_access['unique_id']}")
        if is_add:
            poi_online_rw.insert_park_storefront_prod_access(data)
        else:
            poi_online_rw.update_park_storefront_prod_access(int(sub_park_access['unique_id']), data)
    # 更新修复结果
    poi_online_rw.update_park_storefront_repair_status(turing_id=turing_id, status='DONE')
    return True


def _fetch_park_id(edit_commit: dict) -> int:
    """
    获取 park_id
    """
    park_id = -1
    source_ids = []
    for sub_park_access in edit_commit['sub_info']:
        is_add = int(sub_park_access['entry_verify_type']) == 1
        if is_add and 'unique_id' in sub_park_access and sub_park_access['unique_id'] != '':
            source_ids.append(sub_park_access['unique_id'])

    if len(source_ids) == 0:
        return park_id

    park_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(
        source_ids=source_ids)
    park_id = park_intelligence_list[0]['outside_task_id'] if park_intelligence_list else park_id
    return park_id


def _check_update_access_achieve(sub_park_access: dict) -> bool:
    """
    质检更新的出入口成果，质检失败直接抛出异常
    """
    if "unique_id" not in sub_park_access:
        raise Exception(f'更新推送成果不存在unique_id')
    if "entry_verify_type" not in sub_park_access:
        raise Exception(f'更新推送成果不存在entry_verify_type')

    if int(sub_park_access['entry_verify_type']) not in [1, 2]:
        raise Exception(f"更新推送成果 entry_verify_type 值域异常:{sub_park_access['entry_verify_type']}")

    is_add = int(sub_park_access['entry_verify_type']) == 1
    is_upd = int(sub_park_access['entry_verify_type']) == 2
    conclusion = sub_park_access['entry_verification_conclusion']

    if is_add and conclusion not in [CONCLUSION_ADD, CONCLUSION_INVALID]:
        raise Exception(f"更新推送成果, 新增的核实结果不符合预期：{conclusion}")
    if is_upd and conclusion not in [CONCLUSION_ACCURATE, CONCLUSION_UPDATE, CONCLUSION_DELETE]:
        raise Exception(f"更新推送成果, 更新的核实结果不符合预期：{conclusion}")

    return True


def _updated_access_achieve_not_need_save(sub_park_access: dict) -> bool:
    """
    更新作业成果不需要保存，那么返回 True
    新增时，若结果是无效
    更新时，结论是正确
    上述情况作业结论不需要入库，返回 True
    """
    is_add = int(sub_park_access['entry_verify_type']) == 1
    is_upd = int(sub_park_access['entry_verify_type']) == 2
    conclusion = sub_park_access['entry_verification_conclusion']

    if is_add and conclusion == CONCLUSION_INVALID:
        print(f"新增，但结论是无效")
        return True
    if is_upd and conclusion == CONCLUSION_ACCURATE:
        # 更新，需要判断口的状态，如果口的状态
        accesses = poi_online_query.get_park_storefront_prod_access_list(ids=[int(sub_park_access['unique_id'])])
        if not accesses:
            raise Exception(f"更新，但获取口失败:turing_id: {turing_id}; uq_id: {sub_park_access['unique_id']}")

        access = accesses[0]
        if access['status'] == 'CHECK_FAILED':
            print(f"更新，结论正确，但状态质检失败，需要恢复状态")
            return False

        print(f"更新，但结论是正确")
        return True
    return False


def _extract_work_info(sub_park_access: dict) -> tuple:
    """
    提取作业信息
    """
    manual_work_comment = sub_park_access['sub_content'] if sub_park_access['sub_content'] is not None else ''
    # 不建议开启
    sub_park_access['relation_type'] = _decide_related_type(sub_park_access)
    if sub_park_access['relation_type'] == RELATE_TYPE_GATE:
        gate_info = sub_park_access['gate_info']
        road_relation, park_access_geom = get_road_relation_by_node_id(gate_info[0]['properties']['id'])
        park_access_geom = utils.mc_to_gcj(park_access_geom)
    # 关联LINK
    elif sub_park_access['relation_type'] == RELATE_TYPE_LINK:
        link_info = sub_park_access['link_info']
        # road_relation, park_access_geom = get_road_relation_by_link_id(link_info[0]['properties']['id'])
        # park_access_geom = utils.mc_to_gcj(park_access_geom)

        point_info = sub_park_access['point_info']
        road_relation, park_access_geom = get_road_relation_by_link_id_v2(link_info[0]['properties']['id'], point_info)
        park_access_geom = utils.mc_to_gcj(park_access_geom)
    # 缺路
    elif sub_park_access['relation_type'] == RELATE_TYPE_MIS_ROAD:
        point_info = sub_park_access['point_info']
        road_relation = json.dumps({})
        park_access_geom = utils.bd09_to_gcj(f"point({point_info[0]} {point_info[1]})")
        manual_work_comment = '缺路戳点'
    else:
        raise Exception(f'推送成果{turing_id}关联类型错误:{sub_park_access["relation_type"]}')
    return road_relation, park_access_geom, manual_work_comment


def is_all_callback(park_bid: str, data_type: str):
    """
    判断是否所有成果都已回调
    Returns:

    """
    # 获取推送情报信息
    push_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(
        bid=park_bid, status='PUSHED', remark='turing')
    if not push_intelligence_list:
        raise Exception(f'{park_bid}未查询到推送情报数据')
    outside_ids = [item['outside_id'] for item in push_intelligence_list if item['outside_id']]
    # 查询图灵回调结论
    turing_achieve_list = poi_online_query.get_park_access_turing_result(data_type=data_type,
                                                                         turing_ids=outside_ids, status=None)
    if not turing_achieve_list or len(turing_achieve_list) != len(set(outside_ids)):
        logger.warning(f"{park_bid}成果未全回调:outside_ids:{set(outside_ids)},已回调:{len(turing_achieve_list)}")
        return False
    return True


def callback_by_turing_id(turing_id: str, data_type: str):
    """
    根据turing_id回调函数
    Args:
        turing_id:

    Returns:

    """
    urls = get_callback_urls(data_type)
    url = random.choice(urls)
    print(url)
    # url = f"http://***********:8087/stargaze/feedback/aoi_feedback_ack/{data_type}"
    try:
        params = {
            "id": turing_id,
            "result_code": 1,
            "reason": "",
        }
        logger.info(f"回调req:{params}")
        res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
        logger.info(f"回调res:{res.text}")
    except Exception as e:
        logger.error(f"回调失败:{turing_id},{e}")
        traceback.print_exc()


def get_callback_urls(data_type: str) -> list:
    """
    获取回调 url
    """
    # 测试
    # return [
    #     f"http://***********:8087/stargaze/feedback/aoi_feedback_ack/{data_type}"
    # ]
    callback_urls = []
    bns = 'group.opera-platformIntelligence-plartformIntelligence-all.map-de.all'
    for host in get_host_by_bns(bns):
        ip, port = host
        callback_urls.append(f"http://{ip}:{port}/stargaze/feedback/aoi_feedback_ack/{data_type}")
    return callback_urls


def get_host_by_bns(host_bns: str):
    """
    获取 bns ip
    """
    data = "", 0
    ret = subprocess.run(
        "get_instance_by_service -ips {}".format(host_bns) + " | awk '{print $2,$3,$4}'",
        shell=True,
        stdout=subprocess.PIPE
    )
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            yield data
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue
            yield item_list[0], int(item_list[1])
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(host_bns, code, ret.stdout))
        yield data


def get_road_relation_by_node_id(long_node_id):
    """
    node_id获取road_relation
    """
    road_relation_json = {
        "link_info": []
    }
    # 转短Node
    short_node_list = trans_dao.get_short_node_by_long_node_ids([long_node_id])
    if not short_node_list:
        raise Exception(f'node_id:{long_node_id}未查询到短Node数据')

    short_node_id = short_node_list[0][0]

    # 查询通行性信息
    node_passage_list = traj_dao.get_node_passage_by_long_node_id(long_node_id)
    if not node_passage_list:
        raise Exception(f'node_id:{long_node_id}未查询到通行性数据')

    # todo 获取坐标
    node_geom = node_passage_list[0]['node_geom']
    in_link = node_passage_list[0]["in_link_id"]
    out_link = node_passage_list[0]["out_link_id"]
    passage = node_passage_list[0]["passage"]
    node_geom_mc = utils.gcj_to_mc(node_geom)
    node_sp = shapely.wkt.loads(node_geom_mc)
    coord_str = f"{node_sp.x},{node_sp.y}"

    # passage  in out in_and_out open urgent
    # if passage == "URGENT":
    #     return False, f"nodeid获取通行性为紧急门，{node_id}", ""
    orientation = 1
    if passage == "OUT":
        orientation = 2
    temp_link_info = {
        "link_id": str(in_link),
        "node_id": str(short_node_id),
        "point": coord_str,
        "orientation": orientation,
        "type": 1
    }
    road_relation_json['link_info'].append(temp_link_info)

    if passage in ["IN_AND_OUT", "OPEN"]:
        temp_link_info = {
            "link_id": str(out_link),
            "node_id": str(short_node_id),
            "point": coord_str,
            "orientation": 2,
            "type": 1
        }
        road_relation_json['link_info'].append(temp_link_info)
    road_relation_json_str = json.dumps(road_relation_json)
    return road_relation_json_str, node_geom_mc


def _get_empty_link_info() -> dict:
    """
    获取空的，绑定 link 信息
    """
    return {
        "link_info": [{
            "link_id": '-1',
            "point": '0,0',
            "orientation": 1,
            "type": 2
        }]
    }


def get_road_relation_by_link_id_v2(long_link_id: str, point_info):
    """
    获取 road_relation；更具有 鲁棒性
    """
    try:
        return get_road_relation_by_link_id(long_link_id)
    except Exception as e:
        logging.exception(e)
    empty_link = _get_empty_link_info()
    json_out = json.dumps(empty_link)
    point_gcj = f"point({point_info[0]} {point_info[1]})"
    point_mct = utils.gcj_to_mc(point_gcj)
    return json_out, point_mct


def get_road_relation_by_link_id(long_link_id):
    """
    link_id获取road_relation
    """
    # 转短link
    short_link_list = trans_dao.get_short_link_by_long_link_ids([long_link_id])
    if not short_link_list:
        raise Exception(f'node_id:{long_link_id}未查询到短link数据')

    short_link_id = short_link_list[0][0]
    link_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
    if not link_list:
        raise Exception(f"link_id\t{long_link_id}\t未查询到link数据")

    link_res = link_list[0]
    link_geom_mc = utils.gcj_to_mc(link_res['geom'])
    link_sp = shapely.wkt.loads(link_geom_mc)
    link_center_sp = link_sp.interpolate(0.5, normalized=True)
    coord_str = f"{link_center_sp.x},{link_center_sp.y}"
    dict_out = {
        "link_info": [{
            "link_id": short_link_id,
            "point": coord_str,
            "orientation": 1,
            "type": 2
        }]
    }
    json_out = json.dumps(dict_out)
    return json_out, link_center_sp.wkt


def is_repeat_park_access(park_bid: str, park_access_road_relation: json, park_access_geom: str) -> bool:
    """
    判断是否重复出入口
    Args:
        park_bid:
        park_access_road_relation:
        park_access_geom:

    Returns:

    """
    park_access_sp = shapely.wkt.loads(park_access_geom)
    if not park_access_road_relation or not park_bid:
        raise Exception(f"关联信息为空:park_access_road_relation:{park_access_road_relation},park_bid:{park_bid}")
    # 获取成果库出入口
    related_node_ids = []
    related_link_ids = []
    park_access_list = poi_online_query.get_park_storefront_prod_access_list(parent_bids=[park_bid],
                                                                             status=['READY_ONLINE', 'ALREADY_ONLINE'])

    if park_access_list:
        # 根据出入口位置判断是否重复
        repeat_park_access = [item for item in park_access_list
                              if park_access_sp.distance(shapely.wkt.loads(item['geom'])) < 1 / COORDINATE_FACTOR]
        if len(repeat_park_access) > 0:
            logger.info(f"出入口重复:{repeat_park_access}")
            return True
        for related_park_access in park_access_list:
            road_relation = related_park_access['road_relation']
            if road_relation is not None and "link_info" in road_relation:
                related_node_ids += [x["node_id"] for x in road_relation["link_info"] if
                                     "node_id" in x and x['node_id']]
                related_link_ids += [x["link_id"] for x in road_relation["link_info"] if
                                     "link_id" in x and x['link_id']]
    # 查询线上已关联成果库出入口
    park_access_list = poi_online_query.get_sub_pois(park_bid=park_bid)
    if park_access_list:
        for related_park_access in park_access_list:
            road_relation = related_park_access[3]
            if road_relation is not None and "link_info" in road_relation:
                related_node_ids += [x["node_id"] for x in road_relation["link_info"] if
                                     "node_id" in x and x['node_id']]
                related_link_ids += [x["link_id"] for x in road_relation["link_info"] if
                                     "link_id" in x and x['link_id']]
    if len(related_node_ids) == len(related_link_ids) == 0:
        return False
    # 获取待关联的Node or Link
    node_id = None
    link_id = None
    road_relation_json = json.loads(park_access_road_relation)
    if "link_info" in road_relation_json and len(road_relation_json["link_info"]) > 0:
        node_id = road_relation_json["link_info"][0]['node_id'] if "node_id" in road_relation_json["link_info"][
            0] else None
        link_id = road_relation_json["link_info"][0]['link_id'] if "link_id" in road_relation_json["link_info"][
            0] else None
    if (node_id and node_id in related_node_ids) or (link_id and link_id in related_link_ids):
        logger.info(f"出入口重复:node_id:{node_id},related_node_ids:{related_node_ids},link_id:{link_id},"
                    f"related_link_ids:{related_link_ids}")
        return True
    return False


if __name__ == '__main__':
    source_id = sys.argv[1] if sys.argv[1] != '0' else None
    turing_id = sys.argv[2] if sys.argv[2] != '0' else None
    new_park_access(source_id, turing_id, TURNING_BP_ID)
    new_park_access(source_id, turing_id, TURNING_BP_UPD_ID)

