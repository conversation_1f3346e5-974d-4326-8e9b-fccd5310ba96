#! /bin/bash

script_dir=$(cd $(dirname $0) && pwd)
mod_dir=$(dirname $script_dir)
# !!!此处修改为bin文件名，跟Makefile中bin文件名保持一致
mod_name=job_with_consumer

# 初始化tools环境
function init_tools(){
    if [[ -d "$mod_dir/output/tools" ]];then
        echo "tools already exists"
        cd "$mod_dir/output/tools"
        bash init.sh
        echo "init tools env"
        source  /home/<USER>/.bash_profile
        return
    else
        cd $mod_dir
        rm -rf output*
        rm /home/<USER>/.bash_profile
        wget -q -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:b9eb2d63-10d7-48ef-ac6f-e43b816a9cae" "https://irepo.baidu-int.com/rest/prod/v3/baidu/newdms/nutstools/releases/*******/files"
        tar xf output.tar.gz
        cd output
        tar xf nutstools.tar.gz
        cd tools
        bash init.sh
        echo "init tools env"
        source  /home/<USER>/.bash_profile
    fi
}

# 初始化afs
function init_afs() {
    if [[ -d "$mod_dir/afs" ]];then
	    echo "afs目录已存在"
	    return
	  else
	    mkdir $mod_dir/afs
	    cd $mod_dir/afs
	    wget -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:9864e50c-18bb-44e8-acdf-fb64ea933823" "https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.8.11.2155/files"
	    tar xf output.tar.gz
	  fi
}


function start_service() {
    # 初始化tools环境
    init_tools

    echo -ne "starting up $mod_name ..."
    return 0
    
}

function stop_service() {

    echo -ne "shuting down $mod_name ..."

    return 0
}

function restart_service() {
  return 0
}

# 运行状态
# 0 -> running, 1 -> starting, 2 -> stopped
function status() {
  return 0
}

function usage() {
    echo "usage:"
    echo "cd $mod_dir && $0 {start|stop|restart|status}"
}

if [[ "$#" -ne "1" ]]; then
    usage
    exit 1
fi

ACTION=$1
case "${ACTION}" in
    start)
        start_service
    ;;
    stop)
        stop_service
    ;;
    restart)
        restart_service
    ;;
    status)
        status
    ;;
    *)
        usage
    ;;
esac