#!/bin/bash

####总控文件 包括：
##1. 获取上游最新发布数据
##2. 更新AOI的形状
##3. 更新建筑物的形状
##4. 更新形状库 更新挂接关系
##5. 产出相关的mapinfo文件 方便可视化

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../conf/globalConf.sh
CURR_DATA=$DATA_PATH"/jichudiwu"
CURR_LOG=$LOG_PATH"/jichudiwu"

####备份历史数据 获取最新形状库数据
##1. 备份七天之前的形状库 最多备份70天
##2. 获取最新的形状库数据 根据type字段产出建筑物部分和AOI部分
function PrepareData(){
  if [ ! -d "${CURR_DATA}" ]; then
    mkdir -p "${CURR_DATA}";
  fi

  if [ ! -d "${CURR_LOG}" ]; then
    mkdir -p "${CURR_LOG}";
  fi

  if [ ! -f "${PYTHONBIN}" ]; then
    echo "python does not exists";
    exit;
  fi

  DATE=$(date -d "7 days ago" +"%Y%m%d");
  mv "${CURR_DATA}/data.all" "${CURR_DATA}/data.all.${DATE}";
  local bak_data_cnt=`ls ${BAKPATH}/data.all.* | wc -l`
  local need_del_cnt=`echo "${bak_data_cnt}-10" | bc`
  if [ ${need_del_cnt} -gt 1 ]; then
    for item in `ls ${BAKPATH}/data.all.* | sort | head -${need_del_cnt}`; do
      rm ${item}
    done
  fi

  wget -q ${BUILDING_PATH} -O ${CURR_DATA}/data.all;
  cat ${CURR_DATA}/data.all | awk -F "\t" '{
    type = $7;
    if(type == 0)
      print $0 > "'${CURR_DATA}/data.building'";
    if(type == 1)
      print $0 > "'${CURR_DATA}/data.aoi'";
  }'
}

####获取最新果壳发布数据
### 更换数据源为图像
function GetNewData(){
  cd ${SRC_PATH}/GetNewData;
  sh -x entry.sh city.all 2 > ${CURR_LOG}/log.GetNewData;
  cd -;
}

####计算AOI部分
function AoiUpdate(){
  cd ${SRC_PATH}/aoi;
  sh -x bin/entry.sh 2>${CURR_LOG}/AoiUpdate;
  cd -;
}

####计算建筑物部分
function BuildingUpdate(){
  cd ${SRC_PATH}/building;
  sh -x bin/entry.sh 2>${CURR_LOG}/BuildingUpdate;
  cd -;
}

####更新形状库 更新挂接关系
function OnlineOffline(){
  cd ${SRC_PATH}/OnlineOffline;
  sh -x bin/entry.sh 2>${CURR_LOG}/OnlineOffline;
  cd -;
}

function GenDiff(){
  cd ${SRC_PATH}/GenDiff;
  sh -x bin/entry.sh 2>${CURR_LOG}/GenDiff;
  cd -;
}

####产出mapinfo文件
function GenMidMif(){
  cd ${SRC_PATH}/Midmif;
  sh -x bin/entry.sh 2>${CURR_LOG}/GenMidMif;
  cd -;
}

####主函数
function Main(){
  PrepareData;
  GetNewData;
  AoiUpdate;
  BuildingUpdate;
  OnlineOffline;
  GenDiff;
  GenMidMif;
}

Main;
