# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""

import json
from multiprocessing import Pool
import time
import os
import sys
import traceback
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from pg_tool import PgTool
from common import common_tool


class IntelImp(PgTool):
    """
    大门和AOI匹配策略, 策略整合入口
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        数据提交
        """
        PgTool.commit(self)

    def run(self):
        """
        主函数
        """
        sql = """
            select
                distinct main_bid
            from blu_face_complete
            where
                aoi_complete = 2
        """
        self.cursor_aoi.execute(sql)
        for bid, in self.cursor_aoi.fetchall():
            sql = f"""
                insert into
                gate_match_intel_src(
                    bid,
                    src
                )values(
                    '{bid}',
                    'NotAccurateAoi'
                )
            """
            self.cursor_poi_write.execute(sql)
        self.commit()


if __name__ == "__main__":
    with IntelImp() as obj:
        try:
            print("start import L1 intel" + time.strftime('%Y%m%d', time.localtime(time.time())))
            obj.run()
        except Exception as e:
            obj.conn_poi_write.rollback()
            #报警
            common_tool.send_hi("Intel-import-daily-Failed", ["lifan14"])
            traceback.print_exc()