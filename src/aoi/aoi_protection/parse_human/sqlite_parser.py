"""
从sqlite 获取数据
"""
import datetime
import os
import requests
import pandas as pd
import sqlite3
import shutil
import json

from typing import List
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from pathlib import Path

from src.aoi.aoi_protection import context, cfg
from src.door_naming.DaoBeeflow import DaoBee<PERSON>
from src.door_naming.util import exec_shell_cmd

save_path = Path(os.path.abspath(__file__)).parents[1] / 'sqlite_download'


def load_from_sqlite(task_id, wpf_id) -> List[context.HumanHandleData]:
    """
    从sqlite获取人工作业结果
    :param task_id:
    :param wpf_id:
    :return:
    """
    human_list: List[context.HumanHandleData] = []
    with DaoBeeflow() as dao:
        load_sqlite_work_data(dao, task_id, wpf_id, human_list)
        print(f"解析的结果: {human_list}")
    return human_list


def load_sqlite_work_data(dao: DaoBeeflow, task_id, wpf_id, human_list: List[context.HumanHandleData]):
    """
    加载文件
    :param dao:
    :param task_id:
    :param wpf_id:
    :param human_list
    :return:
    """

    sql = 'select wpfd_content from' \
          ' work_package_flow_data where wpf_id=%s and wpfd_key=%s' \
          ' order by wpfd_id desc limit 1'
    dao.cursor_beeflow.execute(sql, [wpf_id, 'result_data'])
    ret = dao.cursor_beeflow.fetchone()
    if not ret:
        print(f"{wpf_id} 无法获取下载数据")
        return
    # 下载数据
    result_url = ret[0]
    local_path = save_path / datetime.date.today().strftime("%Y-%m-%d") / str(task_id) / "poi.zip"
    local_path.parent.mkdir(exist_ok=True, parents=True)
    # 下载文件
    download_file(result_url, local_path)
    # 处理文件
    handle_file(local_path, task_id, human_list)
    # 删除数据
    print(f'清理文件:{local_path.parent}')
    shutil.rmtree(local_path.parent)


def download_file(url, local_path):
    """
    下载文件
    :param url:
    :param local_path:
    :return:
    """
    retry_strategy = Retry(
        total=3,  # 总共重试次数
        status_forcelist=[429, 500, 502, 503, 504],  # 针对这些状态码进行重试
        allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]  # 仅在这些方法中重试
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    http = requests.Session()
    http.mount("http://", adapter)
    http.mount("https://", adapter)
    with http.get(url, stream=True) as r:
        r.raise_for_status()
        with open(local_path, 'wb') as f:
            for chunk in r.iter_content(chunk_size=4 * 1024 * 1024):
                f.write(chunk)


def handle_file(zip_path, task_id, human_list: List[context.HumanHandleData]):
    """
    处理文件
    :param zip_path:
    :param task_id:
    :param human_list:
    :return:
    """
    parent_path = zip_path.parent.as_posix()
    exec_shell_cmd(f"cd {parent_path}; unzip -o poi.zip")
    ttfb_files = Path(parent_path).glob("aoi.work")

    sql = 'select work_resource, work_result from work '
    for item in ttfb_files:
        with sqlite3.connect(item) as conn:
            df = pd.read_sql_query(sql, conn)
            for _, row in df.iterrows():
                work_resource_str = row['work_resource']
                work_result_str = row['work_result']
                work_resource = json.loads(work_resource_str)
                work_result = json.loads(work_result_str)
                bid = work_resource['bd_poi']['bid']
                work_conclusion = work_result['work_conclusion']
                if work_conclusion and work_conclusion == 1:
                    human_ret = cfg.CAN_OFFLINE
                else:
                    human_ret = cfg.CANNOT_OFFLINE
                d = context.HumanHandleData(wpf_task_id=task_id, bid=bid, wpf_human_status=human_ret)
                human_list.append(d)
