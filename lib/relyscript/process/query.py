# encoding=gbk
import struct
import socket
import mcpack

nshead_item_key = ["id", "version", "log_id", "provider", "magic_num", 
    "reserved", "body_len"]
header_len = struct.calcsize("2HI16s3I")


def make_nshead(nshead):
    header = [nshead[key] for key in nshead_item_key]
    packed_header = struct.pack("2HI16s3I", *header)
    return packed_header


def parse_nshead(packed_header):
    return struct.unpack("2HI16s3I", packed_header)


def connect(ip, port):
    ss = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        ss.connect((ip, port))
    except:
        print "can't connect server!"
        return None
    return ss


def send(ss, data):
    data_len = len(data)
    total_sent = 0
    while total_sent < data_len:
        sent = ss.send(data[total_sent:])
        if sent == 0:
            raise RuntimeError("socket connection broken")
        total_sent += sent


def recv(ss, data_len):
    total_recv = 0
    data = []
    while total_recv < data_len:
        recv_data = ss.recv(data_len - total_recv)
        if recv_data is '':
            raise RuntimeError("socket connection broken")
        data.append(recv_data)
        total_recv += len(recv_data)
    return "".join(data)


def query(conn, mcpack_data):
    nshead = {"id": 0, 
            "version": 1, 
            "log_id": 0, 
            "provider": "poi-region",    ####
            "magic_num": 0xfb709394,
            "reserved": 0, 
            "body_len": len(mcpack_data)}
    packed_header = make_nshead(nshead)
    try:
        send(conn, packed_header)
        send(conn, mcpack_data)
        rcv_header = recv(conn, header_len)
        header = parse_nshead(rcv_header)
        data_len = header[6]
        mcpack_data = recv(conn, data_len)
        json_result = mcpack.loads(mcpack_data)
    except socket.error as e1:
        print e1
        conn.close()
        return None
    except RuntimeError as e2:
        print e2
        conn.close()
        return None
    return json_result
