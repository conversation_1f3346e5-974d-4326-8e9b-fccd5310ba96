# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
aoi_resume库数据表
"""
import time
from dataclasses import dataclass


@dataclass
class BluFaceCompleteHistory:
    """
    blu_face_complete_history表,字段包括:
    complete_info_id,main_bid,face_id,aoi_complete,update_time,is_white,version
    """
    complete_info_id: str
    main_bid: str
    face_id: str
    aoi_complete: int
    update_time: str
    is_white: int
    action: str


@dataclass
class PoiNaviIntelligence:
    """
    poi_navi_intelligence表,字段包括:
    id,qb_id,bid,gid,end_geom_str,date1,date2,src,end_geom,mesh_id,relation_bid,name,created_at
    """
    id: int
    qb_id: str
    bid: str
    gid: str
    end_geom_str: str
    date1: str
    date2: str
    src: str
    end_geom: str
    mesh_id: str
    relation_bid: str
    name: str
    created_at: str

