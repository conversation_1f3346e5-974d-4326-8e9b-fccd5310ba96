# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，只跑单个任务
临时脚本
"""
import json
import time
import uuid
import os
import sys
import traceback
import datetime
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[2]
sys.path.insert(0, root_path.as_posix())

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from src.common import common_tool
from src.AccessMatch.batch_inin_access import AoiWithInnerLinkGateMatch
from src.AccessMatch.batch_inout_access import AoiInOutGateMatch
from src.AccessMatch.batch_virture_access import AoiVirtureGateMatch
from src.AccessMatch.batch_outout_access import AoiOutOutGateMatch
from batch_ininout_access import AoiInInOutGateMatch
from access_improve_accurate import ImproveAccurateStrategy
import aoi_v1_protected
from src.AccessMatch.BatchCtx import BatchContext


def auto_strategy_match(num, bid):
    """
    可以多进程跑策略, 只跑数据库中id对进程数量取余等于num的记录
    由于此函数含有数据库的游标,无法放在类中传给多进程调用
    """
    # 关联内外大门
    print(f"start auto_strategy_match {num} \n")
    with BatchContext(bid=bid) as ctx:
        with AoiInOutGateMatch(ctx) as obj1:
            obj1.run(num)

        # 关联内内大门
        with AoiWithInnerLinkGateMatch(ctx) as obj2:
            obj2.run(num)

        # 关联内内外大门
        with AoiInInOutGateMatch(ctx) as obj3:
            obj3.run(num)

        # 关联虚拟门
        with AoiVirtureGateMatch(ctx) as obj4:
            obj4.run(num)

        # 关联外外大门
        with AoiOutOutGateMatch(ctx) as obj5:
            obj5.run(num)

        # 策略提准
        with ImproveAccurateStrategy(ctx) as obj6:
            obj6.run(num)


class AoiGateMatch:
    """
    大门和AOI匹配策略, 策略整合入口
    """

    def __init__(self, ctx: BatchContext):
        """
        初始化连接数据库
        :param ctx: 字典:道路库的连接方式
        """
        self.batch_ctx = ctx

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def commit(self):
        """
        数据提交
        """
        self.batch_ctx.pg_tool.commit()

    def get_batch_node_by_bid(self, bid):
        """
        根据bid获取原有批处理产生的出入口成果
        """
        sql = """
        select
            distinct b.node_id
        from blu_access a
            inner join blu_access_gate_rel b
                on a.access_id = b.access_id
        where
            main_bid = %s
            and relation_source = 6"""
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        node_set = set()
        for v in res:
            node_set.add(v[0])

        sql = '''
        select
            node_id,
            action,
            resource
        from gate_bid_change_history
        where
            bid = %s
            and imp_state = 0
        order by
            id '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add" and v[2] == 6:
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def get_matched_batch_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的自动化上线成果
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'batch'
            and action = 'add'
            and status = 0 """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_matched_manu_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的需要人工核实的成果
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'manu-check'
            and action = 'add'
            and status = 0"""
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_matched_check_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的疑似不应该关联的大门
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'check-necessary'
            and status = 0"""
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_manu_node_by_bid(self, bid):
        """
        根据bid获取原有人工作业产生的出入口成果
        """
        sql = """
        select
            distinct b.node_id
        from blu_access a
            inner join blu_access_gate_rel b
                on a.access_id = b.access_id
        where
            main_bid = %s
            and relation_source != 6"""
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        node_set = set()
        for v in res:
            node_set.add(v[0])

        sql = '''
        select
            node_id,
            action,
            resource
        from gate_bid_change_history
        where
            bid = %s
            and imp_state = 0
        order by
            id '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add" and v[2] != 6:
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def get_manu_achievement_by_bid(self, bid):
        """
        获取历史人工作业的成果
        """
        sql = '''
        select
            node_id, conclusion, create_time
        from gate_aoi_match_check_data
        where
            bid = %s
            and conclusion != '' 
        order by
            id
        '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        node_res_dict = dict()
        for v in res:
            create_time = v[2]
            conclusion = v[1]
            if conclusion == 'N':
                if create_time > datetime.datetime.now() - datetime.timedelta(days=360):
                    node_res_dict[v[0]] = v[1]
            else:
                node_res_dict[v[0]] = v[1]
        black_list = set()
        write_list = set()
        for node_id, conclusion in node_res_dict.items():
            if conclusion == 'N' or conclusion == 'NOT MATCHED':
                black_list.add(node_id)
                write_list.discard(node_id)
            if conclusion == 'Y' or conclusion == 'MATCHED':
                write_list.add(node_id)
                black_list.discard(node_id)
        return black_list, write_list

    def auto_intel_imp(self, bid, node_id, action, src='', qb_id=-1):
        """
        自动化生成的情报入库
        """
        uid = str(uuid.uuid4()).replace('-', '')
        extra = json.dumps({"qb_id": qb_id})
        if action != 'delete' or '失效' in src:
            sql = """
                insert into
                    gate_bid_change_history
                        (node_id, bid, action, flow_state, src, resource, uid, extra)
                    values
                        (%s, %s, %s, '待入库', %s, 6, %s, %s)
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, action, src, uid, extra])

        sql = f"""
            insert into gate_match_sync_integration
                (uid, main_bid, node_id, is_manual, action, extra)
            values
                ('{uid}', '{bid}', '{node_id}', 0, 'add', '{extra}')
        """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def process_add_node(self, add_node_set, bid, qb_id):
        """
        新增的关联关系入库
        """
        for node_id in add_node_set:
            self.auto_intel_imp(bid, node_id, 'add', '关联关系例行批处理', qb_id)

    def process_delete_node(self, delete_node_set, bid, qb_id):
        """
        需要删除的关联关系入库
        """
        sql = f"""
            select
                count(*)
            from
                blu_face a
                inner join
                blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
                and aoi_level != 3
                and kind != '52'
        """
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql)
        num_aoi = self.batch_ctx.pg_tool.cursor_aoi.fetchone()[0]
        for node_id in delete_node_set:
            # 先删除数据，后下发人工
            self.auto_intel_imp(bid, node_id, 'delete', '关联关系例行批处理', qb_id)
            # 如果AOI不存在或者是内部院落，直接删除无须人工作业
            if num_aoi > 0:
                sql = """
                    insert into
                        gate_aoi_match_check_data_tmp(
                            node_id,
                            bid,
                            source,
                            ways,
                            qb_id
                        )
                        values(
                            %s,
                            %s,
                            '关联关系例行批处理-删除后下发人工',
                            '提召',
                            %s
                        )
                """
                self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def process_manu_node(self, node_manu, bid, qb_id=-1):
        """
        人工作业的关联关系入库
        """
        for node_id in node_manu:
            # 此部分数据暂时不下发，所以先临时入gate_aoi_match_check_data_tmp
            # 新流程验收完成可以例行下发时再做处理
            sql = """
                insert into
                    gate_aoi_match_check_data_tmp(
                        node_id,
                        bid,
                        source,
                        ways,
                        qb_id
                    )
                    values(
                        %s,
                        %s,
                        '关联关系例行批处理',
                        '提召',
                        %s
                    )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def improve_accurate_node(self, node_set, bid, qb_id=-1):
        """
        疑似错误的关联关系入库
        """
        for node_id in node_set:
            # 此部分数据暂时不下发，所以先临时入gate_aoi_match_check_data_tmp
            # 新流程验收完成可以例行下发时再做处理
            sql = """
                insert into
                    gate_aoi_match_check_data_tmp(
                        node_id,
                        bid,
                        source,
                        ways,
                        qb_id
                    )
                    values(
                        %s,
                        %s,
                        '关联关系例行批处理',
                        '清查提准',
                        %s
                    )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def deleted_node_imp(self, delete_node, bid, face_id):
        """
        自动化删除的大门，需要下发人工作业
        """
        for node_id in delete_node:
            sql = """
            insert into
                gate_bid_match_tmp_res(
                    bid,
                    face_id,
                    node_id,
                    action,
                    src,
                    process_type
                )
                values(
                    %s,
                    %s,
                    %s,
                    'delete',
                    '例行更新流程删除数据下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid, face_id, node_id])

    def state_rollback(self):
        """
        回滚
        :return:
        """
        sql = """
            update
                gate_match_intel_src
            set
                status = 0
            where
                status = 1
        """

        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        self.batch_ctx.pg_tool.conn_poi_write.commit()

    def post_process(self):
        """
        人工作业的成果的处理：
            如果情报是因为AOI的范围框变动导致重新匹配，不直接删除，但需要重新下发作业
            如果是其他情报引起的，人工作业成果直接继承；
        批处理成果的处理：
            需要新增的部分过滤掉黑名单，直接上线
            需要删除的部分，过滤掉白名单后直接上线，并下发人工核实（是否要过滤白名单待评估，后续优化）；
            注意白名单只能放在减号的右侧
        """

        protected_main_bid = aoi_v1_protected.load_protected_main_bids(self.batch_ctx.pg_tool)

        # # 从gate_match_intel_src表中查询本次处理了哪些bid
        # sql = f"""
        #     select
        #         face_id,
        #         bid,
        #         src,
        #         qb_id
        #     from gate_match_intel_src
        #     where
        #         bid != '{self.batch_ctx.target_bid}'
        #         and status = 1
        #         and fail_reason = ''
        #         order by id
        # """
        # self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        # res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        # self.batch_ctx.pg_tool.conn_poi_write.commit()

        bid = self.batch_ctx.bid

        if bid in protected_main_bid:
            print("在保护集合中。。。")
            return
        face_id = self.get_face_id(bid)
        src = '手动触发。。。'
        qb_id = -1
        # 现有库里自动关联的出入口
        node_batch_origin = self.get_batch_node_by_bid(bid)
        print("node_batch_origin:" + str(node_batch_origin))

        # 现有库里人工作业的出入口
        node_manu_origin = self.get_manu_node_by_bid(bid)
        print("node_manu_origin:" + str(node_manu_origin))

        # 策略自动批处理产出的出入口数据
        node_batch_current = self.get_matched_batch_node_by_bid(bid)
        print("node_batch_current:" + str(node_batch_current))

        # 策略产出需要人工核实的出入口
        node_manu_current = self.get_matched_manu_node_by_bid(bid)

        # 策略产出疑似不应关联的大门
        node_check_current = self.get_matched_check_node_by_bid(bid)
        print("node_check_current:" + str(node_check_current))

        # 关联关系的黑名单、白名单（暂不考虑人工作业的成果是否会失效，可优化，
        # 但需考虑同一个关联关系下发多次结论不同时的处理）
        node_blacklist, node_whitelist = self.get_manu_achievement_by_bid(bid)
        print("node_blacklist:" + str(node_blacklist))
        print("node_whitelist:" + str(node_whitelist))

        # 策略产出的下发人工作业的出入口，需要去掉批处理关联的部分
        node_manu_current = node_manu_current - node_batch_current
        print("node_manu_current:" + str(node_manu_current))

        # 之前批处理能关联，现在不能关联的部分直接删除，需要去掉白名单
        delete_node = node_batch_origin - node_batch_current - node_whitelist
        print("delete_node:" + str(delete_node))

        # 如果AOI的框变了，库里原有的人工作业的关联关系需要重新下发人工确认，否则只下发策略召回的部分
        if src == "AOI的范围框变更":
            # add_node是自动批处理的大门，可以直接入库，需要去掉库里已经存在的批处理生成的出入口，以及黑名单
            # 这里由于库里人工作业成果会被删除，所以不会减去node_manu_origin
            add_node = node_batch_current - node_batch_origin - node_blacklist

            # 注意node_manu集合取交集或并集的顺序不能变
            # 由于库里可能存在人工作业产生但被标记为批处理产生的数据，所以只要删除的数据都要再下发人工作业
            node_manu = ((node_manu_current | node_manu_origin) - node_batch_current) | delete_node
        elif src == "aoi-bid-del":
            add_node = node_batch_current
            delete_node = node_batch_origin | node_manu_origin - add_node
            node_manu = node_manu_current
        else:
            # 人工作业的成果保留，所以无需再增加
            add_node = node_batch_current - node_batch_origin - node_manu_origin - node_blacklist
            # node_manu = (node_manu_current - node_manu_origin) | delete_node
            node_manu = node_manu_current - node_manu_origin

        # 一定能关联上的node列表，用来判断缺失哪些高通量大门，这里为了保证能转化成精准AOI，
        # 将人工作业的大门都当做了不需要关联的部分，增加了一部分人工作业的成本，
        # 后续优化后可以取消，将node_manu改为delete_node即可
        current_achievement = (node_batch_origin | node_manu_origin | add_node) - node_manu - delete_node
        node_check_current = ((node_batch_origin | node_manu_origin | add_node) - delete_node
                              - node_blacklist - node_whitelist).intersection(node_check_current)

        # 获取AOI缺门的信息，目前没有返回值来区分是否能转化成精准AOI，待后续优化
        # 这个计算太复杂了,应该在aoi层级计算输出,需要移动走
        # _, high_pv_gate_lack = accurate_aoi_info.get_aoi_level_by_bid_and_node(
        #     bid, current_achievement - node_check_current)

        # 精准AOI的判断逻辑产生的输出，也会下发人工作业
        # node_manu = node_manu | set(high_pv_gate_lack)
        # delete_node = delete_node - node_manu

        self.process_add_node(add_node, bid, qb_id)
        print("add_node:" + str(add_node))

        # 直接批处理删除,之后下发人工核实
        self.process_delete_node(delete_node, bid, qb_id)

        self.process_manu_node(node_manu, bid, qb_id)
        print("node_manu:" + str(node_manu))
        self.deleted_node_imp(delete_node, bid, face_id)
        # self.gate_lacked_imp(high_pv_gate_lack, bid, face_id)
        self.improve_accurate_node(node_check_current, bid, qb_id)
        self.commit()

    def get_face_id(self, bid):
        """
        get_face_id
        :param bid:
        :return:
        """
        sql = 'select a.src from blu_face a inner join blu_face_poi b on a.face_id=b.face_id where b.poi_bid=%s'
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        ret = self.batch_ctx.pg_tool.cursor_aoi.fetchone()
        if ret:
            return ret[0]
        return ''

    def pre_process(self):
        """
        处理失败会导致gate_bid_match_tmp_res中有status中为0的部分，需要删除以免影响本次匹配
        """
        return

    def imp_poi_strategy_result(self):
        """
        poi策略产出结果导入
        """

        # poi表名
        table_name = self.batch_ctx.pg_tool.env + "_poi_strategy_relate_result"

        sql = f"""
            select
                node_id,
                face_id,
                bid
            from {table_name}
            where
                is_target = 1
            """
        self.batch_ctx.pg_tool.cursor_trajectory.execute(sql)
        res = self.batch_ctx.pg_tool.cursor_trajectory.fetchall()
        for v in res:
            node_id = v[0]
            face_id = v[1]
            bid = v[2]

            sql = f"""
                insert into
                    gate_bid_match_tmp_res (
                        node_id,
                        bid,
                        face_id,
                        action,
                        status,
                        src,
                        process_type
                    )
                values (
                    '{node_id}',
                    '{bid}',
                    '{face_id}',
                    'add',
                    0,
                    '例行更新流程poi大门策略下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def get_matched_bid_by_node(self, node_id):
        """
        获取该大门关联的所有bid，包括已入库和未入库的部分
        """

        sql = """
            select
                distinct main_bid
            from
                blu_access a
                inner join
                blu_access_gate_rel b
            on
                a.access_id = b.access_id
            where
                b.node_id = %s
        """

        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [node_id])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        self.batch_ctx.pg_tool.conn_aoi.commit()
        bid_set = set()
        for v in res:
            bid_set.add(v[0])

        sql = '''
            select
                bid,
                action
            from gate_bid_change_history
            where
                node_id = %s
                and imp_state = 0
            order by
                id
        '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [node_id])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add":
                bid_set.add(v[0])
            if v[1] == "delete":
                bid_set.discard(v[0])
        return bid_set

    def run(self):
        """
        策略入口
        """
        print('开始处理：bid：', self.batch_ctx.bid)
        auto_strategy_match(0, self.batch_ctx.bid)
        # 后处理
        self.post_process()
        # print("match completed")
        # 数据结果查询


def run_by_bid(bid):
    """
    根据bid执行任务
    :param bid:
    :return:
    """
    with BatchContext(bid=bid) as batch_ctx:
        with AoiGateMatch(batch_ctx) as aoi_gate_match:
            try:
                print(time.strftime('%Y-%m-%d', time.localtime(time.time())))
                aoi_gate_match.run()
            except Exception as e:
                print(e)
                # 报警
                # common_tool.send_hi("Access-Batch-Daily-Failed-de16-xxl-job-167", ['chenbaojun_cd'])
                # aoi_gate_match.state_rollback()
                traceback.print_exc()


if __name__ == "__main__":
    """
    程序入口, 这里支持单个bid进行策略匹配
    """
    print("出入口例行匹配开始:" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    target_bid = ['6371909852800361981']
    for bid in target_bid:
        run_by_bid(bid)
