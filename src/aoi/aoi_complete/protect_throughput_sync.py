"""
高通量保护数据同步脚本
同步重点垂类 pv top 1000 数据, 其他垂类 pv 超过 1000 数据
"""
import csv
import glob
import os
import sys
import datetime
from tqdm import tqdm

from src.aoi.aoi_complete import AoiCompleteContext

ImportantTag = [
    '购物;购物中心',
    '教育培训;高等院校',
    '医疗;专科医院',
    '医疗;综合医院',
    '运动健身;体育场馆',
    '文化传媒;展览馆',
    '休闲娱乐;剧院',
]
JingDianTag = '旅游景点'
JiaoTongTag = '交通设施'
PvBigFile = '/home/<USER>/mnt'
TagBigFile = '/home/<USER>/aoi-strategy/current_release/src/poi_data_sync/data/poi_utf8'
TodayYmd = datetime.datetime.today().strftime("%Y%m%d")


def _get_data_by_iter(file_path: str, delimiter='\t', fieldnames=None, encode='utf-8'):
    """ 使用迭代器获取文件内容 """

    csv.field_size_limit(sys.maxsize)
    with open(file_path, 'r', newline='', encoding=encode) as file:
        reader = csv.DictReader(file, delimiter=delimiter, fieldnames=fieldnames)
        for row in reader:
            yield row


def _get_pv_files(date):
    """ 获取 pv 文件列表 """

    pv_path = os.path.join(PvBigFile, date)
    files = glob.glob(f'{pv_path}/part-00*')
    return files


def top_1000(datas):
    """取前 1000 个"""

    res = sorted(datas, key=lambda x: x['pv'], reverse=True)[:1000]
    return res


def sync_important_tag_data(ctx: AoiCompleteContext):
    """重点垂类保护"""

    tag_path = os.path.join(TagBigFile)
    wait_check_data = {}

    for data in tqdm(_get_data_by_iter(tag_path, delimiter="\t"), desc=f"tag 处理中", mininterval=5):
        if data.get('std_tag') and (
                data['std_tag'] in ImportantTag or JiaoTongTag in data['std_tag'] or JingDianTag in data['std_tag']):
            wait_check_data[data['bid']] = {
                'bid': data['bid'], 'std_tag': data['std_tag'], 'pv': 0}

    set_values = set(wait_check_data.keys())
    print("初次待处理数据 {} 条".format(len(set_values)))

    pv_files = _get_pv_files(date=TodayYmd)
    for idx, file in enumerate(pv_files):
        for data in tqdm(_get_data_by_iter(file, delimiter="\t", fieldnames=['bid', 'pv', 'uv']),
                         desc=f"{idx + 1}/{len(pv_files)} 处理中", mininterval=5):
            if data['bid'] in set_values:
                wait_check_data[data['bid']]['pv'] = int(data.get('pv', '0'))

    sort_list = {
        JingDianTag: [],
        JiaoTongTag: []
    }
    for vv in ImportantTag:
        sort_list[vv] = []

    for vv in tqdm(wait_check_data.values(), desc='待排序数组处理', mininterval=1):
        if 'pv' in vv:
            if JingDianTag in vv['std_tag']:
                sort_list[JingDianTag].append(vv)
            elif JiaoTongTag in vv['std_tag']:
                sort_list[JiaoTongTag].append(vv)
            else:
                sort_list[vv['std_tag']].append(vv)

    # 写入数据库
    tags = ImportantTag + [JiaoTongTag, JingDianTag]
    for vv in tqdm(tags, desc='数据写入'):
        wait_data = top_1000(sort_list[vv])
        ctx.pg_rw_dao.batch_insert_protect_high_throughput_data(wait_data)
        print("{} 需入库数据 {} 条".format(vv, len(wait_data)))


def sync_other_tag_data(ctx: AoiCompleteContext):
    """其他垂类保护"""

    # 获取全部超过 1000 pv 的数据
    pv_files = _get_pv_files(date=TodayYmd)
    wait_check_data = {}
    for idx, file in enumerate(pv_files):
        for data in tqdm(_get_data_by_iter(file, delimiter="\t", fieldnames=['bid', 'pv', 'uv']),
                         desc=f"{idx + 1}/{len(pv_files)} 处理中", mininterval=5):
            if data['pv'] != '' and int(data['pv']) > 1000:
                wait_check_data[data['bid']] = data
    print("初次待处理数据 {} 条".format(len(wait_check_data.items())))

    pois = ctx.pg_query_dao.get_poi_list_by_bid_list(list(wait_check_data.keys()))

    # 通过读取数据库填充 std_tag
    pois_dict = {item[0]: item for item in pois}
    for kk, vv in wait_check_data.items():
        try:
            vv['std_tag'] = pois_dict[kk][1]
        except KeyError:
            vv['std_tag'] = ''

    # 非重点垂类保护入库
    last_insert_data = []
    for key, value in wait_check_data.items():
        if value['std_tag'] == '':
            continue
        last_insert_data.append(value)

    ctx.pg_rw_dao.batch_insert_protect_high_throughput_data(last_insert_data)
    print("需入库数据 {} 条".format(len(last_insert_data)))


if __name__ == '__main__':
    # 高通量保护数据写入
    with AoiCompleteContext() as _ctx:
        sync_important_tag_data(_ctx)
        sync_other_tag_data(_ctx)
