"""
主点恢复失效例行流程
注意：每天需要在关联关系批处理流程运行之前完成此流程
"""
import time
import uuid
import os
import sys
import traceback
import json

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from pg_tool import PgTool
from common import common_tool
from datetime import datetime


class MainBidRecover(PgTool):
    """
    主点失效恢复流程处理
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        :return:
        """
        return

    def commit(self):
        """
        数据提交
        """
        PgTool.commit(self)

    def run(self):
        """
        主点失效恢复流程
        1、主点失效：存储母库出入口快照信息
        2、主点恢复：如数据校验通过后，不进行关联匹配策略，生成恢复入库情报；如数据校验不通过，走原关联匹配流程
        """
        # 主点失效
        self.process_main_bid_invalid()
        print("process_main_bid_invalid done")

        # 主点恢复
        self.process_main_bid_recover()
        print("process_main_bid_recover done")

        self.commit()

        curr_time = time.strftime('%Y-%m-%d', time.localtime(time.time()))
        print(f"------{curr_time} 主点恢复失效完成：all done------")

    def process_main_bid_invalid(self):
        """
        处理主点失效
        """
        sql = '''
            select
                bid, face_id, id
            from
                gate_match_intel_src
            where
                status = 0
                and bid != ''
                and src = 'aoi-bid-del'
        '''
        self.cursor_poi.execute(sql)
        res = self.cursor_poi.fetchall()

        for item in res:
            bid, face_id, id = item
            bool_invalid = self.bool_bid_invalid(face_id, bid)
            if not bool_invalid:
                print("[invalid - not bool_invalid]", "bid：", bid, "face_id", face_id, "intel_id", id)
                continue  # 非失效bid不处理

            bool_exist = self.exist_poi_recover_record_by_intel_src_id(id)
            if bool_exist:
                print("[invalid - bool_exist]", "bid：", bid, "face_id", face_id, "intel_id", id)
                continue  # 已失效处理

            # 启动失效流程：存储出入口快照
            self.record_invalid_bid_access(face_id, bid, id)
            print("invalid record success:", id, bid, face_id)

    def bool_bid_invalid(self, face_id, bid):
        """
        判断是否是主点失效
        """
        sql = f"""
            select status
            from invalid_aoi_history
            where face_id = '{face_id}'
            and original_bid = '{bid}' and status = 0 and restore_time_v0 is null and created_at >= '2024-04-02'
        """
        self.cursor_poi.execute(sql)
        res = self.cursor_poi.fetchone()
        if res is None:
            return False
        else:
            return True

    def record_invalid_bid_access(self, face_id, bid, intel_src_id):
        """
        记录出入口快照
        """
        get_access_sql = f'''
            select
                access_id, face_id, node_id, mesh_id, status, kind, name, name_source,
                access_poi_bid, access_poi_mid, update_time, memo, extra_field1, extra_field2, geom,
                main_bid, face_relate_type, form, relation_source, attribute_source, geom_navi
            from
                blu_access
            where
                face_id = '{face_id}' and main_bid = '{bid}'
        '''
        self.cursor_aoi.execute(get_access_sql)
        res = self.cursor_aoi.fetchall()
        if len(res) == 0:
            print("[invalid - get_access_sql none]", "bid：", bid, "face_id", face_id, "intel_id", intel_src_id)
            return False

        uid = str(uuid.uuid4()).replace("-", "")
        for item in res:
            record_access_sql = f'''
                insert into blu_access_poi_recover(
                    access_id, face_id, node_id, mesh_id, status, kind, name, name_source,
                    access_poi_bid, access_poi_mid, update_time, memo, extra_field1, extra_field2, geom,
                    main_bid, face_relate_type, form, relation_source, attribute_source, geom_navi, uuid
                ) values (
                    %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            '''
            self.cursor_aoi_resume.execute(record_access_sql, [
                item[0], item[1], item[2], item[3], item[4], item[5], item[6], item[7],
                item[8], item[9], item[10], item[11], item[12], item[13], item[14],
                item[15], item[16], item[17], item[18], item[19], item[20], uid
            ])

            access_id = item[0]
            self.record_access_gate_rel(access_id, uid)
            self.record_access_link_rel(access_id, uid)
            self.record_access_label(access_id, uid)
            self.record_access_block_rel(access_id, uid)
        self.add_poi_recover_record(face_id, bid, uid, intel_src_id)

    def record_access_gate_rel(self, access_id, uid):
        """
        记录blu_access_gate_rel信息
        """
        select_sql = f'''
            select
                access_gate_rel_id, access_id, mesh_id, node_id, gate_id, update_time, source
            from
                blu_access_gate_rel
            where
                access_id = '{access_id}'
        '''
        self.cursor_aoi.execute(select_sql)
        res = self.cursor_aoi.fetchall()
        for item in res:
            insert_sql = f'''
                insert into blu_access_gate_rel_poi_recover(
                    access_gate_rel_id, access_id, mesh_id, node_id, gate_id, update_time, source, uuid
                ) values (
                    %s, %s, %s, %s, %s, %s, %s, %s
                )
            '''
            self.cursor_aoi_resume.execute(insert_sql, [
                item[0], item[1], item[2], item[3], item[4], item[5], item[6], uid
            ])

    def record_access_link_rel(self, access_id, uid):
        """
        记录blu_access_link_rel信息
        """
        select_sql = f'''
            select
                access_link_rel_id, access_id, mesh_id, link_id, link_type, update_time, source, node_id
            from
                blu_access_link_rel
            where
                access_id = '{access_id}'
        '''
        self.cursor_aoi.execute(select_sql)
        res = self.cursor_aoi.fetchall()
        for item in res:
            insert_sql = f'''
                insert into blu_access_link_rel_poi_recover(
                    access_link_rel_id, access_id, mesh_id, link_id, link_type, update_time, source, node_id, uuid
                ) values (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            '''
            self.cursor_aoi_resume.execute(insert_sql, [
                item[0], item[1], item[2], item[3], item[4], item[5], item[6], item[7], uid
            ])

    def record_access_label(self, access_id, uid):
        """
        记录blu_access_label信息
        """
        select_sql = f'''
              select
                  label_id, access_id, type, info, memo, mesh_id, create_time, update_time
              from
                  blu_access_label
              where
                  access_id = '{access_id}'
          '''
        self.cursor_aoi.execute(select_sql)
        res = self.cursor_aoi.fetchall()
        for item in res:
            insert_sql = f'''
              insert into blu_access_label_poi_recover(
                  label_id, access_id, type, info, memo, mesh_id, create_time, update_time, uuid
              ) values (
                  %s, %s, %s, %s, %s, %s, %s, %s, %s
              )
            '''
            self.cursor_aoi_resume.execute(insert_sql, [
                item[0], item[1], item[2], item[3], item[4], item[5], item[6], item[7], uid
            ])

    def record_access_block_rel(self, access_id, uid):
        """
        记录blu_access_block_rel信息
        """
        select_sql = f'''
             select
                 access_block_rel_id, access_id, mesh_id, node_id, update_time, source
             from
                 blu_access_block_rel
             where
                 access_id = '{access_id}'
        '''
        self.cursor_aoi.execute(select_sql)
        res = self.cursor_aoi.fetchall()
        for item in res:
            insert_sql = f'''
                 insert into blu_access_block_rel_poi_recover(
                     access_block_rel_id, access_id, mesh_id, node_id, update_time, source, uuid
                 ) values (
                     %s, %s, %s, %s, %s, %s, %s
                 )
            '''
            self.cursor_aoi_resume.execute(insert_sql, [
                item[0], item[1], item[2], item[3], item[4], item[5], uid
            ])

    def add_poi_recover_record(self, face_id, bid, uid, intel_src_id):
        """
        快照记录
        """
        sql = '''
            insert into
                poi_recover_record(uuid, face_id, bid, status, intel_src_id, create_time)
            values (%s, %s, %s, %s, %s, %s)
        '''
        create_time = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        self.cursor_aoi_resume.execute(sql, [uid, face_id, bid, 0, intel_src_id, create_time])

    def exist_poi_recover_record_by_intel_src_id(self, intel_src_id):
        """
        判断intel_src_id的情报是否已经失效处理，避免重复失效处理
        """
        sql = f"select * from poi_recover_record where intel_src_id = '{intel_src_id}'"
        self.cursor_aoi_resume.execute(sql)
        res = self.cursor_aoi_resume.fetchone()
        if res is not None:
            return True
        else:
            return False

    def process_main_bid_recover(self):
        """
        处理主点恢复流程
        """
        sql = '''
             select
                 id, bid, face_id, node_id
             from
                 gate_match_intel_src
             where
                 status = 0
                 and bid != ''
                 and src = 'aoi-bid-add'
         '''
        self.cursor_poi.execute(sql)
        res = self.cursor_poi.fetchall()

        for item in res:
            id, bid, face_id, node_id = item
            bool_recover = self.bool_bid_recover(face_id, bid)
            if not bool_recover:
                print("[recover -  not bool_recover]", "bid：", bid, "face_id", face_id, "intel_id", id)
                continue  # 非恢复bid，不处理

            bool_changed = self.bool_bid_face_changed(face_id, bid)
            if bool_changed:
                print("[recover -  bool_changed]", "bid：", bid, "face_id", face_id, "intel_id", id)
                continue  # bid关联的框有变更不处理

            # 启动恢复流程
            self.bid_recover_executor(id, face_id, bid, node_id)

    def bid_recover_executor(self, id, face_id, bid, node_id):
        """
        执行bid关联的出入口信息恢复
        1、取出待恢复的出入口信息批次uuid
        2、基于uuid生成恢复情报
        3、标记当前关联情报完成
        4、标记bid失效情报记录状态完成
        """
        record = self.get_bid_not_recovered_record(bid, face_id)
        if len(record) == 0 or len(record) > 1:
            print("[recover -  bid_recover_executor]", "bid：", bid, "face_id", face_id, "intel_id", id)
            return False

        uid = record[0][0]
        self.create_bid_recover_information(uid, bid, face_id, node_id)
        self.update_intel_completed_by_id(id)
        self.update_recover_completed_by_uuid(uid)
        print("recover success:", uid)

    def create_bid_recover_information(self, uid, bid, face_id, node_id):
        """
        基于uuid生成恢复情报
        """
        get_mesh_id_sql = f"select mesh_id from blu_access_poi_recover where uuid = '{uid}'"
        self.cursor_aoi_resume.execute(get_mesh_id_sql)
        mesh_res = self.cursor_aoi_resume.fetchone()
        if mesh_res is None:
            print("[recover -  get_mesh_id_sql]", "bid：", bid, "face_id", face_id, "uid", uid)
            return False

        insert_sql = '''
             insert into gate_bid_change_history (
                 node_id, bid, face_id, action, extra, imp_state, src, flow_state, resource, mesh_id, pri
             ) values (
                 %s, %s, %s, 'poi_recover', %s, 0, 'POI失效后恢复-自动恢复', '待入库', 6, %s, 20000
             )
         '''
        extra_data = {"recover_id": uid}
        extra = json.dumps(extra_data)
        self.cursor_poi_write.execute(insert_sql, [node_id, bid, face_id, extra, mesh_res[0]])

    def update_recover_completed_by_uuid(self, uid):
        """
        根据id标记失效poi恢复完成
        """
        update_time = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        sql = f'''
            update
                poi_recover_record
            set
                status = 1, update_time = '{update_time}'
            where
                uuid = '{uid}' and status = 0
         '''
        self.cursor_aoi_resume.execute(sql)

    def get_bid_not_recovered_record(self, bid, face_id):
        """
        根据bid, face_id找未恢复的失效记录
        """
        sql = f'''
             select
                 uuid
             from
                 poi_recover_record
             where
                 status = 0
                 and face_id = '{face_id}'
                 and bid = '{bid}'
         '''
        self.cursor_aoi_resume.execute(sql)
        res = self.cursor_aoi_resume.fetchall()
        return res

    def update_intel_completed_by_id(self, id):
        """
        根据id标记情报完成
        100-恢复失效专属完成标识
        """
        sql = f'''
            update
                gate_match_intel_src
            set
                status = 100
            where id = '{id}' and status = 0
        '''
        self.cursor_poi_write.execute(sql)

    def bool_bid_recover(self, face_id, bid):
        """
        判断是否是主点恢复
        """
        sql = f"""
              select status
              from invalid_aoi_history
              where face_id = '{face_id}'
              and original_bid = '{bid}' and status = 1 and restore_time_v0 is not null and created_at >= '2024-04-02'
          """
        self.cursor_poi.execute(sql)
        res = self.cursor_poi.fetchone()
        if res is None:
            return False
        else:
            return True

    def bool_bid_face_changed(self, face_id, bid):
        """
        校验bid对应框是否在失效期间有变更（影响关联策略的变更src、geom、kind、aoi_level）
        """
        blu_sql = f"select src, kind, st_astext(geom), aoi_level from blu_face where face_id = '{face_id}'"
        self.cursor_aoi.execute(blu_sql)
        blu_res = self.cursor_aoi.fetchone()
        if blu_res is None:
            return True

        invalid_history_sql = f"""
            select
                src, kind, st_astext(geom), aoi_level
            from
                invalid_aoi_history
            where
                original_bid = '{bid}'
            and face_id = '{face_id}' and status = 1 and restore_time_v0 is not null and created_at >= '2024-04-02'
            order by restore_time_v0 desc limit 1
        """
        self.cursor_poi.execute(invalid_history_sql)
        history_res = self.cursor_poi.fetchone()
        if history_res is None:
            return True

        if (blu_res[0] != history_res[0] or
                blu_res[1] != history_res[1] or
                blu_res[2] != history_res[2] or
                blu_res[3] != history_res[3]):
            return True
        return False


if __name__ == "__main__":
    """
    main
    """
    print("主点失效恢复开始:" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    with MainBidRecover() as main_bid_recover:
        try:
            main_bid_recover.run()
        except Exception as e:
            common_tool.send_hi("main_bid_recover failed", ['chenbaojun_cd'])
            traceback.print_exc()
