# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
poi_pv例行更新
"""
import datetime
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("..")
from aoi import PgUpdate
from pathlib import Path

today = datetime.date.today().strftime("%Y%m%d")
POI_PV_TABLE_NAME = 'poi_pv'
NEW_POI_PV_TABLE_NAME = 'poi_pv_new'
AFS_POI_PV_DIR = f"/home/<USER>/pv_data"

pg_rw_dao = PgUpdate.PgUpdate()


def run(new_poi_pv_dir: str):
    """
    POI pv 更新
    :return:
    """
    pg_rw_dao.create_poi_pv_table(new_poi_pv_dir)


if __name__ == '__main__':
    # 获取更新文件目录
    done = False
    for i in range(0, 10):
        if done:
            exit(0)
        last_date = datetime.date.today() - datetime.timedelta(days=i)
        last_date_str = last_date.strftime("%Y%m%d")
        done_file = Path(f'{AFS_POI_PV_DIR}/{last_date_str}/_done')
        if not done_file.exists():
            print(f"{done_file} 不存在,重新找目录")
            continue
        print(f"{done_file}存在")
        poi_pv_file = f"{AFS_POI_PV_DIR}/{last_date_str}/part-00000"
        print(f"更新文件：{poi_pv_file}")
        if not os.path.exists(poi_pv_file):
            raise Exception(f"更新文件不存在：{poi_pv_file}")
        run(poi_pv_file)
        done = True
        print("更新完成")
        break
