# -*- coding: utf-8 -*-
"""
门前停车场情报去无效
"""
import datetime
import multiprocessing
import sys
import os
import tqdm
import json
import numpy as np
import traceback
from shapely import wkt
from loguru import logger
from dataclasses import dataclass, field
from datetime import datetime
from datetime import timedelta
from shapely.geometry import Point, Polygon, MultiPoint, LineString
from sklearn.cluster import DBSCAN

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.traj_db import TrajDB
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.road import Road
from src.common import pipeline
from src.aoi import utils
from src.aoi.park_access import park_utils
from shapely.ops import nearest_points

COORDINATE_FACTOR = 100000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
traj_db = TrajDB()
dest_traj_dao = DestTraj()
road_dao = Road()
# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")
CATEGRORY = ['房地产;住宅区', '房地产;写字楼']
IS_TEST = 1


@dataclass
class ParkAccessIntelligence:
    """
    停车场情报
    """
    id: int = field(default=0, init=False)
    geom: str = field(default="", init=False)
    is_invalid: bool = field(default=False, init=False)
    strategy_value: str = field(default="", init=False)
    remark: str = field(default="", init=False)


class Context:
    """
    情报去无效处理上下文e
    """

    def __init__(self, intelligence_list, parking):
        """
        初始化
        Args:
            intelligence_list:
            parking:
        """
        self.origin_intelligence_list = intelligence_list
        self.park_area = parking['geom']
        self.center_line = parking['central_line']
        self.extend_center_line = extend_linestring(wkt.loads(parking['central_line']), 30 / COORDINATE_FACTOR)
        self.intelligence_list = list()
        self.init_intelligence_list()

    def init_intelligence_list(self):
        """
        初始化情报列表
        Returns:

        """
        for intelligence in self.origin_intelligence_list:
            park_access = ParkAccessIntelligence()
            park_access.id = intelligence['id']
            park_access.geom = wkt.loads(intelligence['geom'])
            self.intelligence_list.append(park_access)


def extend_linestring(line: LineString, dist: float) -> LineString:
    """
    延长 LineString 的两端，根据指定的距离
    :param line: 待延长的 LineString
    :param dist: 需要延长的距离
    :return: 返回延长后的 LineString
    """

    def extend_point(p0: Point, p1: Point):
        vec = np.array([p1.x - p0.x, p1.y - p0.y])
        vec = vec / np.linalg.norm(vec)
        vec = vec * dist
        return Point(p1.x + vec[0], p1.y + vec[1])

    points = [Point(x, y) for x, y in line.coords]
    points = [extend_point(points[1], points[0]), *points[1:-1], extend_point(points[-2], points[-1])]
    return LineString(points)


def create_intelligence(ctx: Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        filter_by_cross_high_road,
        filter_in_area,
        filter_by_near_central_line,
        save_park_access_intelligence_to_db,
    )
    pipe_list(ctx)


def filter_by_near_central_line(ctx: Context, proceed):
    """
    过滤情报点在中心线投影距离较近的情报点, 10m
    Args:
        ctx:

    Returns:

    """
    logger.info(f"filter_by_near_central_line")
    invalid_intelligence_ids = []
    park_area_sp = wkt.loads(ctx.park_area)
    logger.info(f"延长中心线:{ctx.extend_center_line}")
    for intelligence in ctx.intelligence_list:
        if intelligence.id in invalid_intelligence_ids or intelligence.is_invalid:
            continue
        foot_point_sp = utils.get_foot_point(intelligence.geom, ctx.extend_center_line)
        if not foot_point_sp:
            logger.info(f"投影点计算失败")
            continue
        for sub_intelligence in ctx.intelligence_list:
            if sub_intelligence.id == intelligence.id or sub_intelligence.id in invalid_intelligence_ids \
                    or sub_intelligence.is_invalid:
                continue
            sub_foot_point_sp = utils.get_foot_point(sub_intelligence.geom, ctx.extend_center_line)
            # 如果两者在中心线投影距离小于10m, 则过滤一个点
            if foot_point_sp.distance(sub_foot_point_sp) < 10 / COORDINATE_FACTOR:
                # logger.info(f"过滤掉一个点:{intelligence.geom}-{sub_intelligence.geom}-")
                # 判断是是否在内部区域(非临街方向)
                intelligence_is_in_area, intelligence_high_level_link_sp = is_in_area(intelligence.geom,
                                                                                      ctx.center_line, park_area_sp)
                sub_intelligence_is_in_area, sub_intelligence_high_level_link_sp = is_in_area(sub_intelligence.geom,
                                                                                              ctx.center_line,
                                                                                              park_area_sp)
                # 判断附近是否存在大门
                intelligence_near_exist_gate = road_dao.get_nav_gate_by_geom(
                    intelligence.geom.buffer(3 / COORDINATE_FACTOR).wkt)
                sub_intelligence_near_exist_gate = road_dao.get_nav_gate_by_geom(
                    sub_intelligence.geom.buffer(3 / COORDINATE_FACTOR).wkt)
                # 高优判断非临街方向, 存在大门,优先保留大门
                if not intelligence_is_in_area and not sub_intelligence_is_in_area and (
                        not intelligence_high_level_link_sp or not intelligence_high_level_link_sp.intersects(
                    ctx.extend_center_line)) and intelligence_near_exist_gate and not sub_intelligence_near_exist_gate:
                    sub_intelligence.is_invalid = True
                    sub_intelligence.strategy_value = "filter_by_near_central_line_gate"
                    sub_intelligence.remark = f"有效\t{intelligence.geom},无效\t{sub_intelligence.geom}"
                    invalid_intelligence_ids.append(sub_intelligence.id)
                    break
                if not intelligence_is_in_area and not sub_intelligence_is_in_area and (
                        not sub_intelligence_high_level_link_sp or not sub_intelligence_high_level_link_sp.intersects(
                    ctx.extend_center_line)) and not intelligence_near_exist_gate and sub_intelligence_near_exist_gate:
                    intelligence.is_invalid = True
                    intelligence.strategy_value = "filter_by_near_central_line_gate"
                    intelligence.remark = f"有效\t{sub_intelligence.geom},无效\t{intelligence.geom}"
                    invalid_intelligence_ids.append(intelligence.id)
                    break
                # 如果两个点一个在内部,一个在外部,则取外部点
                if intelligence_is_in_area and not sub_intelligence_is_in_area:
                    intelligence.is_invalid = True
                    intelligence.strategy_value = "filter_by_near_central_line_in_and_out"
                    intelligence.remark = f"有效\t{sub_intelligence.geom},无效\t{intelligence.geom}"
                    invalid_intelligence_ids.append(intelligence.id)
                    break
                if not intelligence_is_in_area and sub_intelligence_is_in_area:
                    sub_intelligence.is_invalid = True
                    sub_intelligence.strategy_value = "filter_by_near_central_line_in_and_out"
                    sub_intelligence.remark = f"有效\t{intelligence.geom},无效\t{sub_intelligence.geom}"
                    invalid_intelligence_ids.append(sub_intelligence.id)
                    break
                # 取距离停车场面最近的点
                if park_area_sp.distance(sub_intelligence.geom) < park_area_sp.distance(intelligence.geom):
                    intelligence.is_invalid = True
                    intelligence.strategy_value = "filter_by_near_central_line_nearst"
                    intelligence.remark = f"有效\t{sub_intelligence.geom},无效\t{intelligence.geom}"
                    invalid_intelligence_ids.append(intelligence.id)
                    break
                else:
                    sub_intelligence.is_invalid = True
                    sub_intelligence.strategy_value = "filter_by_near_central_line_nearst"
                    sub_intelligence.remark = f"有效\t{intelligence.geom},无效\t{sub_intelligence.geom}"
                    invalid_intelligence_ids.append(sub_intelligence.id)
                    break
    logger.info(f"过滤掉的点:{invalid_intelligence_ids}")
    return proceed()


def filter_in_area(ctx: Context, proceed):
    """
    过滤内部区域点位, 点位距离面超过8m
    Args:
        ctx:

    Returns:

    """
    logger.info(f"filter_in_area")
    park_sp = wkt.loads(ctx.park_area)
    for intelligence in ctx.intelligence_list:
        if intelligence.is_invalid or intelligence.geom.distance(park_sp) < 8 / COORDINATE_FACTOR:
            logger.info(f"8m范围内,不过滤:{intelligence.geom.distance(park_sp) * COORDINATE_FACTOR}")
            continue
        # 如果距离面在15m以内,且附近30m没有情报点,则不过滤
        if park_sp.distance(intelligence.geom) < 15 / COORDINATE_FACTOR:
            exist_near_points = [item.geom for item in ctx.intelligence_list if
                                 item.geom != intelligence.geom and item.geom.distance(
                                     intelligence.geom) < 30 / COORDINATE_FACTOR]
            if len(exist_near_points) == 0:
                logger.info(f"15m范围内且30m没有其他点,不过滤:{intelligence.geom.distance(park_sp) * COORDINATE_FACTOR}")
                continue
        # 如果情报点在中心线没有投影,则不走这个过滤,存在有效case被过滤
        # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/2b9e1eeb622e48
        foot_point_sp = utils.get_foot_point(intelligence.geom, wkt.loads(ctx.center_line))
        if not foot_point_sp:
            continue
        park_access_sp = intelligence.geom
        park_access_buffer_100m_sp = park_access_sp.buffer(200 / COORDINATE_FACTOR)
        # 获取情报点距离最近的高等级道路
        high_level_link_list = road_dao.is_over_high_road_link(park_access_buffer_100m_sp.wkt)
        if not high_level_link_list:
            logger.info(f"情报点附近不存在高等级道路:{park_access_sp.wkt}")
            continue
        nearest_high_level_link = min(high_level_link_list,
                                      key=lambda item: park_access_sp.distance(wkt.loads(item['geom'])))
        nearest_high_link_sp = wkt.loads(nearest_high_level_link['geom'])
        # 获取情报点在高等级LINK的投影点
        foot_point = park_utils.get_foot_point(park_access_sp, nearest_high_link_sp)
        if not foot_point:
            logger.info(f"情报点在同等级道路投影失败:{park_access_sp.wkt},{nearest_high_level_link['link_id']}")
            continue
        # 投影点与情报点的连线如果穿过停车场面,则认为情报点在停车场面内部
        park_access_foot_point_sp = LineString([park_access_sp.coords[0], foot_point])
        intersects_poly = park_sp.intersection(park_access_foot_point_sp)
        # logger.info(f"情报点连线:{park_access_foot_point_sp.wkt},intersects_poly:{intersects_poly}")
        if not intersects_poly:
            continue
        if intersects_poly.length > 9 / COORDINATE_FACTOR and park_access_sp.distance(park_sp) > 5 / COORDINATE_FACTOR:
            intelligence.is_invalid = True
            intelligence.strategy_value = "filter_in_area"
            intelligence.remark = f"无效\t{intelligence.geom}"
    return proceed()


def filter_by_cross_high_road(ctx: Context, proceed):
    """
    过滤跨高等级道路(7及及7级路以上)的点位
    Args:
        ctx:

    Returns:

    """
    logger.info(f"filter_by_cross_high_road")
    park_sp = wkt.loads(ctx.park_area)
    for intelligence in ctx.intelligence_list:
        if intelligence.is_invalid:
            # 已经无效不考虑
            continue

        park_access_sp = intelligence.geom
        p1, p2 = nearest_points(park_sp, park_access_sp)
        if p1.distance(p2) < 1e-8:
            # 线距离过短不考虑
            continue

        line_wkt = LineString([p1, p2]).wkt
        line_wkt_extend = extend_linestring(wkt.loads(line_wkt), -2 / COORDINATE_FACTOR).wkt
        cross_links = road_dao.cross_level_links(line_wkt_extend, 7)
        cross_link_ids = [x[0] for x in cross_links]
        links_infos = road_dao.get_nav_link_by_link_ids(cross_link_ids)
        if len(cross_link_ids) >= 2:
            # 情报点距离link很近(<=2m) >=2根，说明是路口多link这种，属于有效情报点，否则无效
            near_to_links = []
            for link_info in links_infos:
                link_sp = wkt.loads(link_info[0])
                if link_sp.distance(park_access_sp) <= 2 / COORDINATE_FACTOR:
                    near_to_links.append(link_info)

            if len(near_to_links) < 2:
                intelligence.is_invalid = True
                intelligence.strategy_value = "filter_by_cross_high_road"
                intelligence.remark = f"无效\t{intelligence.geom}, 跨>=2根高等级link"
                logger.info(f"{intelligence.remark}")
                continue
        elif len(cross_link_ids) == 1:
            for link_info in links_infos:
                link_wkt, _, _, _, _, _, dir = link_info
                if dir == 1:
                    # 双方向
                    dist_to_link = wkt.loads(link_wkt).distance(park_access_sp)
                    if dist_to_link > 6 / COORDINATE_FACTOR:
                        intelligence.is_invalid = True
                        intelligence.strategy_value = "filter_by_cross_high_road"
                        intelligence.remark = f"无效\t{intelligence.geom},跨1根方向是双向的link，并且情报点到压盖的路网距离>6m"
                        logger.info(f"{intelligence.remark}")
                        continue
        logger.info(f"情报点和面最近点连线不跨高等级道路(>=7)：{line_wkt_extend}")
    return proceed()


def save_park_access_intelligence_to_db(ctx: Context, proceed):
    """
    保存智能结果到数据库
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin save_park_access_intelligence_to_db:{len(ctx.intelligence_list)}")
    for intelligence in ctx.intelligence_list:
        if not intelligence.is_invalid:
            continue
        if IS_TEST == '0':
            logger.info(f"存在无效情报:{intelligence.id},{intelligence.geom}\n"
                        f"strategy_value:{intelligence.strategy_value},\nremark:{intelligence.remark}")
            continue
        poi_online_rw.update_park_access_intelligence_strategy_value_by_id([intelligence.id],
                                                                           intelligence.strategy_value)
        poi_online_rw.update_park_access_intelligence_status_by_id([intelligence.id], 'INVALID')
    return proceed()


def is_in_area(intel_point_sp, area, park_area_sp):
    """
    判断情报点是否在区域内, 非临街方向
    Args:
        intel_point_sp:
        area:
        park_area_sp:

    Returns:

    """
    if park_area_sp.contains(intel_point_sp):
        return False, False
    intel_point_buffer_100m_sp = intel_point_sp.buffer(100 / COORDINATE_FACTOR)
    area_sp = wkt.loads(area)
    # 获取情报点距离最近的高等级道路
    high_level_link_list = road_dao.is_over_high_road_link(intel_point_buffer_100m_sp.wkt)
    if not high_level_link_list:
        logger.info(f"情报点附近不存在高等级道路:{area_sp.wkt}")
        return False, False
    nearest_high_level_link = min(high_level_link_list,
                                  key=lambda item: intel_point_sp.distance(wkt.loads(item['geom'])))
    nearest_high_link_sp = wkt.loads(nearest_high_level_link['geom'])
    # 获取情报点在高等级LINK的投影点
    foot_point = park_utils.get_foot_point(intel_point_sp, nearest_high_link_sp)
    if not foot_point:
        logger.info(f"情报点在同等级道路投影失败:{intel_point_sp.wkt},{nearest_high_level_link['link_id']}")
        return False, False
    # 投影点与情报点的连线如果穿过停车场面,则认为情报点在停车场面内部
    park_access_foot_point_sp = LineString([intel_point_sp.coords[0], foot_point])
    intersects_poly = area_sp.intersection(park_access_foot_point_sp)
    logger.info(f"情报点连线:{park_access_foot_point_sp.wkt},intersects_poly:{intersects_poly}")
    if not intersects_poly:
        return False, park_access_foot_point_sp
    return True, park_access_foot_point_sp


def main(bid_list: list):
    """
    地上停车场情报去无效
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_park_storefront_result(bids=bid_list)
    if not parking_list:
        logger.debug("No intelligent data found")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"\n门前停车场情报去无效:{parking['bid']}")
            if not parking['central_line']:
                logger.info(f"没有中心线")
                continue
            # 查询情报信息
            type = 'park_access_gate_front'
            intelligence_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(type=type,
                                                                                               bids=[parking['bid']],
                                                                                               status='INIT')
            if not intelligence_list:
                logger.info(f"未查询到待推送的情报信息")
                continue
            ctx = Context(intelligence_list, parking)
            create_intelligence(ctx)
            continue
        except Exception as e:
            print(f"情报去无效失败:{parking['bid']}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
