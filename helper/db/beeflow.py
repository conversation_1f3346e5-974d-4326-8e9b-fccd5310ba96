# !/usr/bin/env python3

from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session

Base = declarative_base()


class WorkPackageFlow(Base):
    # 工单状态值域wpf_status
    TASK_STATUS_DATA_WAITTING = 1
    TASK_STATUS_UNCLAIM = 2
    TASK_STATUS_WORKING = 3
    TASK_STATUS_FLOWING = 4
    TASK_STATUS_PASS = 5
    TASK_STATUS_FAILED = 6
    TASK_STATUS_FLOW_FAILED = 7
    TASK_STATUS_REPAIR = 8
    TASK_STATUS_REPAIR_WORKING = 9
    TASK_STATUS_REWORK = 10
    TASK_STATUS_REWORK_WORKING = 11

    __tablename__ = 'work_package_flow'

    wpf_id = Column(BigInteger, nullable=False, primary_key=True)  # 要素情报id
    wpf_prev_id = Column(BigInteger)
    wpf_task_id = Column(BigInteger, default=0)
    wpf_section = Column(Integer, default=0)
    wpf_status = Column(Integer, default=0)
    wpf_delete_flag = Column(Integer, default=0)
    wpf_end_time = Column(BigInteger, default=0)
    wpf_claim_time = Column(BigInteger, default=0)

    def __str__(self):
        return str(self.wpf_id)


class WorkPackageFlowData(Base):
    WpfWorkPackage = "work_data"
    WpfResultPackage = "result_data"

    __tablename__ = 'work_package_flow_data'
    wpfd_id = Column(BigInteger, nullable=False, primary_key=True)
    wpf_id = Column(BigInteger, nullable=False, default=0)
    wpfd_key = Column(String, nullable=False)
    wpfd_type = Column(Integer, nullable=False)
    wpfd_content = Column(Text)

    def __repr__(self):
        return str(self.wpfd_id)


class TaskRecord(Base):
    __tablename__ = 'task_record'

    task_id = Column(BigInteger, nullable=False, primary_key=True)  # 要素情报id
    task_project_id = Column(Integer, default=0)
    plan_id = Column(Integer, default=0)
    task_status = Column(Integer, default=0)

    def __str__(self):
        return str(self.task_id)


class TaskPlan(Base):
    __tablename__ = 'task_plan'

    plan_id = Column(BigInteger, nullable=False, primary_key=True)  # 要素情报id
    plan_type = Column(Integer, default=0)
    plan_project_id = Column(Integer, default=0)
    plan_status = Column(Integer, default=0)
    plan_delete_flag = Column(Integer, default=0)

    def __str__(self):
        return str(self.plan_id)


class NewTrafficReleaseRecord(Base):
    STATUS_CREATED = 0
    STATUS_FINISHED = 1

    __tablename__ = 'new_traffic_release_record'
    id = Column(Integer, primary_key=True)
    wpf_id = Column(BigInteger, nullable=False, default=0)
    status = Column(SmallInteger, nullable=False, default=0)
    create_time = Column(DateTime, nullable=False, default=func.now())
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return str(self.id)


class ScenicReleaseRecord(Base):
    STATUS_CREATED = 0
    STATUS_FINISHED = 1
    TransStatusInit = 0
    TransStatusSuccess = 1
    TransStatusFail = -1

    __tablename__ = 'scenic_release_record'
    id = Column(Integer, primary_key=True)
    wpf_id = Column(BigInteger, nullable=False, default=0)
    work_id = Column(String, nullable=False, default='')
    bid = Column(String(100), nullable=False, default='')
    mid = Column(String(100), nullable=False, default='')
    name = Column(String(100), nullable=False, default='')
    conclusion = Column(String(256), nullable=False, default='')
    trans_status = Column(SmallInteger, nullable=False, default=0)
    create_time = Column(DateTime, nullable=False, default=func.now())
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return str(self.id)
