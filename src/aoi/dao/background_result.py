# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
背景成果 数据库
"""
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class BackgroundResultDao(PgDao):
    """
    背景成果 数据库
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "identify_res" in conf:
            background_result_conf = conf['identify_res']
            super().__init__(background_result_conf)
        else:
            raise Exception("identify_res 配置不存在")

    def get_bud_identity_by_batch_and_step(self, batch: str, step: str):
        """
        获取建筑物矢量化范围框
        :param batch: 批次
        :param step: 当前流转步骤
        """
        sql = f"""
            select st_astext(geom), block, id from bud_identify_res where batchno=%s and step=%s
        """
        return self.queryall(sql, [batch, step])

    def insert_bud_after_progress_record(self, record: dict):
        """
        建筑物后处理记录入库
        :param record:
        :return:
        """
        sql = f"""
            insert into bud_identify_res(
                guid,batchno,step,geom
            ) values(%s, %s, %s, st_geomfromtext(%s,4326))
        """
        return self.execute(sql, [record['guid'], record['batchno'], record['step'], record['geom']])
