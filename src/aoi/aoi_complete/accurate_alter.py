# -*- coding: utf-8 -*-
"""
精准AOI报警统计
"""
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
import PgQuery
import PgUpdate
import accurate_aoi_info

dao = PgQuery.PgQuery()


# 失效 层级变化  未关联大门
def accurate_alter_run():
    """
    精准AOI数据减少量级统计
    :return:
    """
    accurate_aoi_diff_list = dao.get_diff_accurate_aoi()
    if accurate_aoi_diff_list is None or len(accurate_aoi_diff_list) == 0:
        return
    for accurate_aoi in accurate_aoi_diff_list:
        # 判断是否存在漏关联大门
        bid = accurate_aoi[1]
        deletion_node_aoi_res = accurate_aoi_info.accurate_aoi_level_cal([bid])
        # if deletion_node_aoi_res is not None and len(deletion_node_aoi_res) != 0 and bid in deletion_node_aoi_res:
            