"""
投放风雷任务
"""
import os
import sys
from pathlib import Path

import shapely.wkt

root_path = Path(os.path.abspath(__file__)).parents[3]
print(root_path)
sys.path.insert(0, root_path.as_posix())
from src.common import pgsql
from src.common import common_tool
from src.door_naming.strategy_v2 import common
import uuid
import pandas as pd
import tqdm

# 重复情报距离检测
CHECK_DIS = 15 * 1.1 * 1e-5
# 重复情报时间检测
CHECK_DATE = 90


def check_manual_added(bid, node_geom, check_days=CHECK_DATE):
    """
    检查是否手动添加过风雷情报,添加过就不再添加
    :param bid:
    :param node_geom:
    :param check_days
    :return:
    """
    geom_buffer = shapely.wkt.loads(node_geom).buffer(CHECK_DIS).simplify(1e-5).wkt
    sql = f"select item_id, mark_geom  from qb_gate_prepush_preprocess where item_id = '{bid}' " \
          f' and st_intersects(ST_GeomFromText(mark_geom, 4326),' \
          f" ST_GeomFromText('{geom_buffer}', 4326)) " \
          f" and create_time > now() - interval '{check_days} days' "
    ret = pgsql.query_one(pgsql.ENGINE_DEST_TRAJ_TO_AOI, sql)
    return ret


def add_task(bid, node_geom, check_days=CHECK_DATE):
    """
    添加任务
    :param bid: 情报主点
    :param node_geom: 需要添加大门的位置
    :param check_days
    :return:
    """

    if check_manual_added(bid, node_geom, check_days):
        print(f"已经投放过情报:{bid} - {node_geom}")
        return
    mesh_id = common_tool.get_mesh_id(node_geom)
    poi_info = common.get_poi_info_by_bid(bid)
    properties = ''
    if poi_info and mesh_id:
        properties = f"{poi_info.name}|{poi_info.std_tag}".replace("'", '')
    qb_id = uuid.uuid4().hex
    sql = """
            insert into qb_gate_prepush_preprocess(qb_id, item_id, intel_type, mark_geom, lead_geom, 
            mesh_id, cityadcode, mark_status, param_1, param_2, param_3, strategy, qb_src, push_platform, properties)
            values(:qb_id, :bid, 0, :node_geom, :node_geom2, :mesh_id,
             '', 1, '2', '0|0||0|0|0|4', '3|0|-1|0', 'MIS_MINING_NEW', 2, 1, :properties)
        """
    pgsql.try_execute(
        pgsql.ENGINE_DEST_TRAJ_TO_AOI,
        sql,
        {
            "qb_id": qb_id,
            "bid": bid,
            "node_geom": node_geom,
            "node_geom2": node_geom,
            "mesh_id": mesh_id,
            "properties": properties
        }, try_times=100,
    )


def run(path, check_days=CHECK_DATE):
    """
    根据文件下发任务
    :param path:
    :param check_days
    :return:
    """
    df = pd.read_csv(path, converters={"main_bid": str, "near_10_door_node_id": str}, sep='\t')
    df = df[df['near_10_door_node_id'] == '']
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df)):
        main_bid = row['main_bid']
        geom = row['geom']
        try:
            print(f"add {main_bid} {geom}")
            add_task(main_bid, geom, check_days)
        except Exception as e:
            print(e.args)
            continue


def get_buffered_aoi(poi_geom, buffer_size):
    """
    获取附近的aoi的主点,用于下发任务
    :param poi_geom
    :param buffer_size:
    :return:
    """
    buffer_size = buffer_size * 1.1 * 1e-5
    buffer_geom = shapely.wkt.loads(poi_geom).buffer(buffer_size).wkt
    sql = 'with tmp as (select st_geomfromtext(:geom, 4326) as geom_buff) ' \
          'select b.poi_bid, st_astext(a.geom) as geom ' \
          ' from blu_face a inner join blu_face_poi b on a.face_id=b.face_id, tmp ' \
          'where st_intersects(a.geom, tmp.geom_buff) and a.src !=:d1 and b.poi_bid !=:d2'
    buffered_ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"geom": buffer_geom, "d1": 'SD', "d2": ''})
    nearest_bid = None
    nearest_dis = buffer_size
    if buffered_ret:
        for item in buffered_ret:
            poi_bid = item.poi_bid
            geom = shapely.wkt.loads(item.geom)
            if common.get_poi_info_by_bid(poi_bid):
                dis = geom.distance(shapely.wkt.loads(poi_geom))
                if dis < nearest_dis:
                    nearest_bid = poi_bid
                    nearest_dis = dis
    return nearest_bid


def mark_match_aoi_and_manual(geom, check_days=CHECK_DATE):
    """
    只提供mark点,不提供主点的任务下发人工
    :param check_days
    :return:
    """
    # 找到bid对应的aoi, buffer100米即可
    match_bid = get_buffered_aoi(geom, 200)
    if match_bid:
        print("匹配到AOI, 下发任务")
        add_task(match_bid, geom, check_days)
    else:
        print(f"没有匹配到aoi,无法下发人工:{geom}")


def add_manual_cross(check_days=CHECK_DATE):
    """
    轨迹交割点情报投放
    :return:
    """
    df = pd.read_csv('疑似缺门场景_link交割点.tsv', converters={"bid": str}, sep='\t')
    for idx, row in tqdm.tqdm(df.iterrows(), total=len(df), desc='下发风雷任务'):
        main_bid = row['bid']
        geom = row['sub_door_link_moved_geom']
        try:
            add_task(main_bid, geom, check_days)
        except Exception as e:
            print('add_task_fail', e.args)
            continue


if __name__ == '__main__':
    pass
    # add_manual()
    # tmp_online_data()
