#!/bin/bash
file=$1
BASEDIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
function update_monitor() {
# self monitor while update online data, retry when error
# $1 --> script
# $2 --> update_file
    local remain_tmp_file="${2}.remain"
    local done_tmp_file="${2}.done"
    local tmp_file="${2}.tmp"

    # init
    cp $2 $remain_tmp_file
    rm -f $done_tmp_file
    touch $done_tmp_file

    local retry_count=-1
    local update_data_count=0
    while [ -s $remain_tmp_file ]; do
    # keep update until all done
        retry_count=$(($retry_count+1))
        #echo "retry count: $retry_count" >> "/home/<USER>/liming06/ronghe/CJH/aoi_auto_refresh/log/aoi-to-pds.log"

        python $1 $remain_tmp_file $done_tmp_file
        local done_count=`wc -l $done_tmp_file | cut -d " " -f 1`
        update_data_count=$(($done_count+$update_data_count))

        # 此处依赖数据中的bid是唯一的，不然有可能在retry的时候漏掉后面重复bid的数据
        # 该依赖是合理的，bid的唯一性应该由数据提供方保证
        awk -F '\t' '
            ARGIND==1{a[$1]}
            ARGIND==2{
                if(!($1 in a)) {
                    print $0;
                }
            }
        ' $done_tmp_file $remain_tmp_file > $tmp_file

        mv $tmp_file $remain_tmp_file
        rm -f $done_tmp_file
        touch $done_tmp_file
    done

    echo "aoi-to-pds done [$2], [$update_data_count] rows, retry count: $retry_count" | mail -s "AOI-TO-PDS" "<EMAIL>"
}

update_monitor "$BASEDIR/process/process_recall.py" "${file}"

