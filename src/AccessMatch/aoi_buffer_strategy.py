# -*- coding: UTF-8 -*-
################################################################################
"""
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
每月触发一次
重要清单buffer40米,下发所有未关联大门
"""
import collections
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
import sys
from pypika import Query, Table, Field

std_tag_list = ["", "", "", ""]


class AoiBufferStrategy(PgTool):
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """

    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        提交事务。

        Args:
            无参数。

        Returns:
            无返回值。

        """
        PgTool.commit(self)

    def is_matched(self, bid, node_id):
        """
        判断关联关系是否已经存在
        """
        sql = """
            select 
                count(*) 
            from 
                blu_access a 
                inner join 
                blu_access_gate_rel b 
            on 
                a.access_id = b.access_id
            where 
                main_bid = '%s'
                and b.node_id = '%s' 
        """ % (
            bid,
            node_id,
        )
        self.cursor_aoi.execute(sql)
        matched = False
        num = self.cursor_aoi.fetchone()[0]
        if num > 0:
            matched = True

        sql = """
            select 
                action
            from gate_bid_change_history
            where 
                bid = '%s'
                and node_id = '%s'
                and imp_state = 0 
            order by 
                id desc
            limit 1
        """ % (
            bid,
            node_id,
        )
        self.cursor_poi.execute(sql)
        res = self.cursor_poi.fetchone()
        if matched:
            if res is not None and res[0] == "delete":
                return False
            return True
        else:
            if res is not None and res[0] == "add":
                return True
            return False

    def find_link_by_node(self, node_id):
        """
        根据nodeid查找候选link组
        Args:
            node_id: 大门的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
        """
        link_dict = collections.OrderedDict()
        sql = """
            select 
                distinct link_id, s_nid, e_nid, b.node_id, form, st_astext(geom)
            from nav_link a inner join nav_gate b 
            on 
                a.link_id = b.in_linkid 
                or a.link_id = b.out_linkid 
            where 
                b.node_id = %s """
        self.cursor_road.execute(sql, [node_id])
        node_id_str = ""
        res_link = self.cursor_road.fetchall()
        for v_node in res_link:
            link_dict[v_node[0]] = v_node[5]
            if v_node[4] == "52":
                node_id_str = node_id_str + "','" + v_node[1] + "','" + v_node[2]
        node_id_str = node_id_str.strip("','")
        sql = """
            select 
                string_agg(adjoin_nid, ''',''') 
            from nav_node 
            where 
                node_id in ('%s') 
                and adjoin_nid != '' """ % (
            node_id_str
        )
        self.cursor_road.execute(sql)
        adjoin_nid = self.cursor_road.fetchone()[0]
        if adjoin_nid is not None:
            node_id_str = node_id_str + "','" + adjoin_nid
        sql = """
            select 
                link_id, st_astext(geom) 
            from nav_link 
            where 
                (s_nid in ('%s') or 
                    e_nid in ('%s')
                ) 
            """ % (
            node_id_str,
            node_id_str,
        )
        self.cursor_road.execute(sql)
        for link_tmp in self.cursor_road.fetchall():
            link_dict[link_tmp[0]] = link_tmp[1]
        link_list = list(link_dict.keys())
        for link_id in link_list:
            sql = """
                select
                    link_id, st_astext(geom) 
                from nav_link 
                where 
                    st_intersects(geom, st_geomfromtext('%s', 4326))
                    and link_id != '%s'
                    and kind > 7
            """ % (
                link_dict[link_id],
                link_id,
            )
            self.cursor_road.execute(sql)
            for link_tmp in self.cursor_road.fetchall():
                link_dict[link_tmp[0]] = link_tmp[1]
        return link_dict

    def node_matched(self, node_id, level):
        """
        判断大门是否已经关联
        """
        sql = """
            select 
                main_bid
            from 
                blu_access a 
                inner join 
                blu_access_gate_rel b
            on 
                a.access_id = b.access_id
            where 
                b.node_id = '%s' 
        """ % (
            node_id
        )
        self.cursor_aoi.execute(sql)
        matched = False
        for (bid,) in self.cursor_aoi.fetchall():
            sql = """
                select 
                    aoi_level 
                from 
                    blu_face a inner join blu_face_poi b 
                on 
                    a.face_id = b.face_id 
                where 
                    poi_bid = '%s'
            """ % (
                bid
            )
            self.cursor_aoi.execute(sql)
            res_level = self.cursor_aoi.fetchone()
            if res_level is None or res_level[0] >= 3:
                continue
            if level == 0:
                sql = """
                    select 
                        count(*)
                    from gate_aoi_match_check_data
                    where 
                        bid = '%s'
                        and node_id = '%s'
                        and conclusion = 'Y'
                """ % (
                    bid,
                    node_id,
                )
                self.cursor_poi.execute(sql)
                if self.cursor_poi.fetchone()[0] == 0:
                    return matched

            sql = """
                select 
                    count(*)
                from blu_face_complete
                where 
                    main_bid = '%s'
            """ % (
                bid
            )
            self.cursor_aoi.execute(sql)
            if self.cursor_aoi.fetchone()[0] > 0:
                matched = True
                break
        return matched

    def get_bid_list(self):
        """
        返回要处理的bid集合
        """
        sql = """
            select 
                bid 
            from bid_gate_todo_lists 
            where 
                important = 1 
                and aoi_level = 2
        """
        self.cursor_poi.execute(sql)
        bid_set = set()
        for (bid,) in self.cursor_poi.fetchall():
            bid_set.add(bid)
        sql = """
            select 
                poi_bid 
            from 
                blu_face a, blu_face_poi b, blu_aoi_rel c
            where 
                a.face_id = b.face_id 
                and a.face_id = c.face_id2
                and aoi_level = 2 
                and a.kind != '52' 
                and poi_bid != ''
                and src != 'SD'
        """
        self.cursor_aoi.execute(sql)
        for (bid,) in self.cursor_aoi.fetchall():
            bid_set.add(bid)

        sql = "select distinct main_bid from blu_face_complete where aoi_complete < 3"
        self.cursor_aoi.execute(sql)
        for (bid,) in self.cursor_aoi.fetchall():
            sql = "select count(*) from poi_pv where bid = '%s' and pv >= 210 " % bid
            self.cursor_poi.execute(sql)
            if self.cursor_poi.fetchone()[0] > 0:
                bid_set.add(bid)

        return bid_set

    def process_manu_node(self, node_manu, bid, src="", type=""):
        """
        人工作业的关联关系入库
        """
        for node_id in node_manu:
            sql = """
                insert into 
                    gate_aoi_match_check_data_tmp(
                        node_id, 
                        bid, 
                        source, 
                        ways
                    )
                    values(
                        %s, 
                        %s, 
                        %s, 
                        %s
                    )
            """
            self.cursor_poi_write.execute(sql, [node_id, bid, src, type])

    def get_aoi_geom_with_bid(self, bid):
        """
        根据bid获取aoi的geom
        """
        sql = """
            select 
                st_astext(geom)
            from 
                blu_face a
                inner join
                blu_face_poi b 
            on 
                a.face_id = b.face_id
            where 
                poi_bid = %s
                and aoi_level = 2 
                and kind != '52'
        """
        self.cursor_aoi.execute(sql, [bid])
        geom = self.cursor_aoi.fetchone()
        return geom

    def get_aoi_near_gate(self, bid, level):
        """
        返回aoi buffer 40米疑似需要关联的大门
        """
        node_set = set()
        geom = self.get_aoi_geom_with_bid(bid)
        if geom is None:
            return node_set
        else:
            geom = geom[0]

        # 查询所有的大门
        sql = """
            select 
                node_id,
                st_astext(geom),
                st_AsText(st_shortestline(geom, st_geomfromtext(%s, 4326))),
                st_distance(geom, st_geomfromtext(%s, 4326))
            from nav_node
            where 
                st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)
        """
        self.cursor_road.execute(sql, [geom, geom, geom])
        for (
            node_id,
            geom_node,
            line_geom,
            dis,
        ) in self.cursor_road.fetchall():
            sql = """
                select 
                    distinct link_id, 
                    s_nid, 
                    e_nid, 
                    form, 
                    st_astext(geom)
                from 
                    nav_link a 
                    inner join 
                    nav_gate b 
                on 
                    a.link_id = b.in_linkid 
                    or a.link_id = b.out_linkid 
                where 
                    b.node_id = %s 
            """
            self.cursor_road.execute(sql, [node_id])
            res_gate = self.cursor_road.fetchall()
            aoi_contains_gate = shapely.wkt.loads(geom).contains(
                shapely.wkt.loads(geom_node)
            )
            if len(res_gate) != 2:
                continue
            # 如果没有外部路
            if "52" in res_gate[0][3] and "52" in res_gate[1][3]:
                node_list = []
                node_list.append(res_gate[0][1])
                node_list.append(res_gate[0][2])
                node_list.append(res_gate[1][1])
                node_list.append(res_gate[1][2])
                sql = """
                    select 
                        adjoin_nid
                    from nav_node 
                    where 
                        node_id in ('%s') 
                        and adjoin_nid != '' 
                """ % (
                    "','".join(node_list)
                )
                self.cursor_road.execute(sql)
                for (node_tmp,) in self.cursor_road.fetchall():
                    node_list.append(node_tmp)
                sql = """
                    select 
                        count(*) 
                    from nav_link 
                    where 
                        (s_nid in ('%s') or 
                            e_nid in ('%s')
                        ) 
                        and not form ~ '52' 
                """ % (
                    "','".join(node_list),
                    "','".join(node_list),
                )
                self.cursor_road.execute(sql)
                if self.cursor_road.fetchone()[0] == 0:
                    continue

            if not aoi_contains_gate:
                sql = """
                    select 
                        count(*)
                    from nav_link 
                    where 
                        st_intersects(st_geomfromtext('%s', 4326), geom) 
                        and kind <= 7
                        and link_id != '%s'
                        and link_id != '%s'
                """ % (
                    line_geom,
                    res_gate[0][0],
                    res_gate[1][0],
                )
                self.cursor_road.execute(sql)
                if self.cursor_road.fetchone()[0] > 0:
                    continue
            if self.is_matched(bid, node_id):
                continue
            if self.node_matched(node_id, level):
                if dis <= 0.0002:
                    link_dict = self.find_link_by_node(node_id)
                    flag = False
                    for link_id in link_dict:
                        link_geom = link_dict[link_id]
                        if shapely.wkt.loads(link_geom).intersects(
                            shapely.wkt.loads(geom)
                        ):
                            print(bid + "\t" + node_id)
                            flag = True
                            break
                    if flag:
                        continue
                continue
            node_set.add(node_id)

        return node_set

    def run(self):
        """
        运行函数，对重要的aoi清单进行buffer 40 米的策略。
        该策略有效率相对较低，但召回率高
        Args:
            无
        Returns:
            无
        """
        # 跑buffer40米的策略
        for bid in self.get_bid_list():
            sql = "select aoi_complete from blu_face_complete where main_bid = '%s'" % (
                bid
            )
            self.cursor_aoi.execute(sql)
            level = self.cursor_aoi.fetchone()
            if level is None:
                continue
            else:
                level = level[0]
            node_set = self.get_aoi_near_gate(bid, level)
            # for node_id in node_set:
            #     print(bid + "\t" + node_id)
            self.process_manu_node(node_set, bid, "关联关系例行批处理-重要清单buffer40米", "提召")

        self.commit()


if __name__ == "__main__":
    obj = AoiBufferStrategy()
    obj.run()
