"""
地上新增出入口 step 5
"""
from distutils.log import info
from re import L
import sys
import uuid
import os
import time
import hashlib
import tqdm
import requests
import subprocess
import math
import json
import copy 
import shutil
import tarfile
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import nearest_points
from shapely.affinity import translate
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi.park_access.light_check import config
from src.common import pipeline
from src.aoi import utils
from src.aoi.park_access import park_utils
from collections import defaultdict, deque
import logging as log 
log.basicConfig(level=log.ERROR, format='%(asctime)s %(filename)s[line:%(lineno)d] %(message)s')

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
road_dao = Road()
trans_dao = Trans()
dest_traj = DestTraj()

light_status = config.light_status
light_check_status = config.light_check_status


def process_one_task(task):
    """
    处理一个任务
    """
    source = 'STRATEGY_LIGHT_CHECK_ACCESS_ADD_AUTO'
    park_bid = task['park_bid']
    res = {
        "status": 0,
        "source": source,
        "source_id": 'light_check_access_add_auto' + "_" + str(uuid.uuid4()).replace("-", ""),
        "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "park_bid": park_bid,
        "node_id": "",
        "link_id": "",
        "long_node_id": "",
        "long_link_id": "",
        "road_relation_type": ""
    }
    long_node_id = task['long_node_id']
    long_link_id = task['long_link_id']
    node_info = road_dao.get_node_list([long_node_id])
    logger.info(node_info)
    if node_info:
        logger.info(node_info)
        res['gcj_geom'] = node_info[0]['geom']
        short_node_list = trans_dao.get_short_node_by_long_node_ids([long_node_id])
        if short_node_list:
            res['node_id'] = short_node_list[0][0]
            res['long_node_id'] = long_node_id
            res['road_relation_type'] = 'node'
    else:
        # 失效时根据3m内找个最近的node
        logger.info(task)
        node_wkt = wkt.loads(task['node_geom']).buffer(3 / 110000).wkt
        logger.info(node_wkt)
        node_near = road_dao.get_nav_node_by_geom(node_wkt)
        logger.info(node_near)
        if node_near and len(node_near) > 0:
            long_node_id = node_near[0][0]
            node_info = road_dao.get_node_list([long_node_id])
            if node_info:
                res['gcj_geom'] = node_info[0]['geom']
                short_node_list = trans_dao.get_short_node_by_long_node_ids([long_node_id])
                if short_node_list:
                    res['node_id'] = short_node_list[0][0]
                    res['long_node_id'] = long_node_id
                    res['road_relation_type'] = 'node'
    logger.info(res)
    
    if res['road_relation_type'] == 'node':
        semantic_res = dest_traj.get_nav_gate_traffic([long_node_id])
        if not semantic_res or len(semantic_res) == 0:
            res['long_node_id'] = ""
            res['road_relation_type'] = ''

    
    if res['road_relation_type'] == "":
        link_info = road_dao.get_nav_link(link_ids=[long_link_id])
        logger.info(link_info)
        if link_info:
            res['gcj_geom'] = Point(wkt.loads(link_info[0]['geom']).coords[0]).wkt
            short_link_list = trans_dao.get_short_link_by_long_link_ids([long_link_id])
            if short_link_list:
                res['link_id'] = short_link_list[0][0]
                res['long_link_id'] = long_link_id
                res['road_relation_type'] = 'link'
    park_info  = poi_online_query.get_park_info(park_bid)
    if not park_info:
        res['status'] = "park_not_exist"
        return res
    park_name = park_info[0]
    res['access_address'] = park_info[9]
    passage = get_passage(res['long_node_id'], res['long_link_id'])
    if passage not in ['入口', '出入口']:
        res['status'] = "passage_not_in"
        return res
    res['access_show_tag'] = "停车场" + passage
    res['access_name'] = park_name.replace("-", "") + "-" + passage
    node_geom_bd = utils.gcj_to_mc(res['gcj_geom'])
    res['bdmc_geom'] = node_geom_bd
    res['point_x'] = wkt.loads(node_geom_bd).x
    res['point_y'] = wkt.loads(node_geom_bd).y
    res['status'] =  "pushed"
    return res


def get_passage(long_node_id, long_link_id):
	"""
	获取通行性
	"""
	if long_node_id != "":
		passage = utils.get_nav_gate_semantic(long_node_id)
	elif long_link_id != "":
		passage = '入口'
	return passage


def push_task(res):
	"""
	推送出入口
	"""
	request = {
		"source": res['source'],
		"source_id": res['source_id'],
		"parent_id": res['park_bid'],
		"show_tag": res['access_show_tag'],
		"name": res['access_name'],
		"address": res['access_address'],
		"point_x": res['point_x'],
		"point_y": res['point_y'],
		"status": 1,
		"is_add_poi": 1,
		"force_update": 1
	}
	if res['road_relation_type'].lower() == "node":
		request["node_id"] = res['node_id']
	elif res['road_relation_type'].lower() == "link":
		request["link_id"] = res['link_id']
	else:
		return False
	logger.info(request)
	return utils.data_update_request(request, is_debug=False)
    

def access_add_push(light_check):
    """
    获取掘金结果
    """
    status = light_check['status']
    if status  != light_status['access_need_add']:
        return None
    res = process_one_task(light_check)
    logger.info(res)
    update_dict = {
        'msg': res['status'],
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }
    if res['status'] ==  "pushed":
        update_dict['source_id'] = res['source_id']
        push_res = push_task(res)
        logger.info(f"push_res:{push_res}")
        if push_res:
            update_dict['status'] = light_status['access_add_pushed_success']
            update_dict['msg'] = 'push_success'
            update_dict['road_relation_type'] = res['road_relation_type']
            update_dict['push_poi_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            update_dict['status'] = light_status['access_add_pushed_fail']
            update_dict['msg'] = 'push_fail'
    else:
        update_dict['status'] = light_status['access_add_pushed_fail']
    return update_dict


def main():
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    limit = 10000
    light_check_list = poi_online_query.get_light_check_list(bids=None, 
        status=[light_status['access_need_add']],
        limit=limit,
        # intelligence_id=[2054356],
        intelligence_type=['park_access_overground_add'],
        update_time_sort=False)
    if not light_check_list:
        logger.error("No light_check_list found")
        return
    success_num = 0
    wait_num = 0
    for light_check in tqdm.tqdm(light_check_list):
        logger.info(light_check)
        try:
            id = light_check['id']
            # 获取待推送列表
            update_dict = access_add_push(light_check)
            # 同步掘金结果
            condition = {
                "id": id,
                "status": light_status["access_need_add"],
            }
            logger.info(update_dict)
            logger.info(condition)
            poi_online_rw.update_park_access_light_check(update_dict, condition)
            if update_dict['status'] == light_status['access_add_pushed_success']:
                success_num += 1
                intelligence_update_dict = {
                    'status': 'PUSHED',
                    'remark': 'light_check'
                }
                intelligence_condition = {
                    'id': light_check['intelligence_id'],
                }
                poi_online_rw.update_park_access_intelligence(intelligence_update_dict, intelligence_condition)
            else:
                wait_num += 1
        except Exception as e:
            print(e)
            continue
    logger.info(f"总数量：{len(light_check_list)}, 推送成功 {success_num}, 推送失败{wait_num}")


if __name__ == '__main__':
	main()
