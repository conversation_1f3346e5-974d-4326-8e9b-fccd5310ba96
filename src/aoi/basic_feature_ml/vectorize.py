# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矢量化
"""
import os

import cv2
from osgeo import ogr, gdal, gdalconst
from shapely import wkt


def binary_img_switch_geojson(image_filepath, origin_wkt, vertorize_result_dir):
    """
    二值图矢量化
    :param img_path:  图片地址
    :param wkt:  坐标
    :param batch:  批次
    :return: 矢量化成果文件
    """
    geom = wkt.loads(origin_wkt)
    minx, miny, maxx, maxy = geom.bounds
    gdal.AllRegister()
    ds = gdal.Open(image_filepath, gdalconst.GA_ReadOnly)

    ds.SetGeoTransform((minx,
                        0.000002680394224609375000,
                        0,
                        maxy,
                        0,
                        -0.000002682494975000000000))
    # 波段，从1开始
    src_band = ds.GetRasterBand(1)
    # osr.SpatialReference()

    image_name = os.path.basename(image_filepath)
    image_hash = image_name.split(".")[0]

    drv = ogr.GetDriverByName("GeoJSON")
    geojson_file = "{}/{}.json".format(vertorize_result_dir, image_hash)
    dst_ds = drv.CreateDataSource(geojson_file)
    fld = ogr.FieldDefn("DN", ogr.OFTInteger)
    dst_layer = dst_ds.CreateLayer(vertorize_result_dir, srs=None)
    dst_layer.CreateField(fld)
    dst_field = dst_layer.GetLayerDefn().GetFieldIndex("DN")
    options = ["8CONNECTED=8"]
    # 矢量化
    result = gdal.Polygonize(src_band, None, dst_layer, dst_field, options, callback=None)
    if result is None:
        raise ValueError(f'矢量化失败: {image_filepath}')
    return True