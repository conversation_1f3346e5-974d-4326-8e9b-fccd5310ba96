# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
轨迹库2
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class TrajFeatureDao(PgDao):
    """
    轨迹库1
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "traj_feature_db" in conf:
            poi_online_r_conf = conf['traj_feature_db']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("traj_feature_db 配置不存在")

    def get_parking_points_traj_by_wkt(self, parking_area_wkt: str, traj_time: str, limit: int = 5000):
        """
        获取停车点熄火点轨迹
        Returns:

        """
        sql = f"""
            select st_astext(parking_traj_geom) from parking_points 
            where ST_Intersects(geom, st_geomfromtext(%s, 4326))  
                and parking_type='end_car'
                and traj_time > %s
                and parking_traj_geom is not null
                and parking_traj_geom != ''
            limit %s
        """
        return self.queryall(sql, [parking_area_wkt, traj_time, limit])

    def get_broken_points_by_geom(self, geom: str):
        """
        获取断头轨迹点
        Args:
            geom:

        Returns:

        """
        sql = f"""
            select st_astext(geom) as geom from broken_points where st_intersects(geom, st_geomfromtext(%s, 4326))
        """
        return self.queryall_by_kv_cursor(sql, [geom])
