#!/usr/bin/env bash
BASEDIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
# 固定php脚本位置 不使用这个参数了
WORKPATH=$2
PHP=$3
file=$1
out_file=$4

function update_aoi(){
    echo "[INFO] 更新AOI库中的名称";
    while read LINE
    do
        bid=`echo -e "$LINE"|awk -F"\t" '{print $3}'`
        city=`echo -e "$LINE"|awk -F"\t" '{print $11}'`
        ${PHP} "${BASEDIR}/select_db.php" ${bid} ${city} ${out_file}
    done < "$file"
}

function main(){
    update_aoi
}

main
