# -*- coding: utf-8 -*-
"""
出入口新增情报过滤
"""
from asyncio import tasks
from ctypes import util
import datetime
from fileinput import filename
import multiprocessing
from operator import is_
import sys
import os
import uuid
import math
import json
from tkinter import W

import tqdm
import requests
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.affinity import translate
from shapely import wkt
from datetime import datetime
from datetime import timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi import utils

from shapely import wkt
from loguru import logger

COORDINATE_FACTOR = 110000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
master_back_dao = MasterBackDao()
road_dao = Road()
trans_dao = Trans()

# logger.remove()
log_path = "./log/access_del.log"
logger.add(log_path, rotation="100 MB")


def dataUpdateRequest(bid, status):
	"""
	发送更新请求
	"""
	# 构建请求参数映射
	reqMap = {
		"bid": bid,
		"status": status,
		"source": 'STRATEGY_ACCESS_DEL_BY_RONGYU_AUTO'
	}
	# 打印更新请求
	print("更新请求:", reqMap)
	# 请求的URL
	url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
	# 将请求参数映射转换为JSON字符串
	payload = json.dumps(reqMap)
	# 打印请求载荷
	print(payload)
	# exit(1)
	# 设置请求头
	headers = {
		'Content-Type': 'application/json'
	}
	# 发送POST请求
	response = requests.request("POST", url, headers=headers, data=payload)
	# 检查响应状态码
	if response.status_code != 200:
		# 发送消息通知请求更新失败
		print("发送消息通知请求更新失败")


def main():
	"""
	主函数
	"""
	# 读取文件 
	with open('./input/rongyu_0514.txt', 'r') as f:
		status = 2
		for line in f:
			arr = line.strip().split('\t')
			if len(arr) != 4:
				continue
			rongyu_type = arr[0]
			bid1 = arr[1]
			bid2 = arr[2]
			mian_pv = arr[3]
			if int(mian_pv) >= 10000:
				continue
			del_bid = bid1
			if rongyu_type == 'node_same':
				poi_info1 = poi_online_query.get_poi_info_by_bid(bid1)
				access_info1 = poi_online_query.get_park_access_by_kv(status=1, bids=[bid1])
				if not access_info1:
					continue
				click_pv1 = 0
				if poi_info1:
					click_pv1 = poi_info1[5]
				poi_info2 = poi_online_query.get_poi_info_by_bid(bid2)
				access_info2 = poi_online_query.get_park_access_by_kv(status=1, bids=[bid2])
				click_pv2 = 0
				if poi_info2:
					click_pv2 = poi_info2[5]
				if not access_info2:
					continue
				is_has_node1 = 0
				is_has_node2 = 0
				if "link_info" in access_info1[0][3] and len(access_info1[0][3]['link_info']) > 0 \
					and 'node_id' in access_info1[0][3]['link_info'][0]:
					is_has_node1 = 1
				if "link_info" in access_info2[0][3] and len(access_info2[0][3]['link_info']) > 0 \
					and 'node_id' in access_info2[0][3]['link_info'][0]:
					is_has_node2 = 1
				if is_has_node1 == 1 and is_has_node2 == 0:
					del_bid = bid2
					print(f"{bid1}有node和{bid2}无node,删除后面的{del_bid}")
				else:
					if click_pv1 > click_pv2:
						del_bid = bid2
						print(f"{bid1}pv{click_pv1}大于{bid2}pv{click_pv2},删除后面的{del_bid}")
			print(f"删除{del_bid}:{line}")
			dataUpdateRequest(del_bid, status)


if __name__ == '__main__':
	main()
	
# cat run_rongyu_auto.log | grep 出入口冗余待删除  
# | awk -F"\t" '{print $3"\t"$4"\t"$5"\t"$6}' > ../access_add/input/rongyu_0514.txt
# aq_py3 push_access_del.py 