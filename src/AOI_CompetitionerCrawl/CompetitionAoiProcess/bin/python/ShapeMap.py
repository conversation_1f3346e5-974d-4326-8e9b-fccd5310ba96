#encoding=gbk
"""
Building Match if they are Connected
z<PERSON><PERSON><PERSON><PERSON>@baidu.com at 20171012
"""

import os
import sys
import shapely.wkt
import shapely.geometry

hash = 1600
GeomMap = {}
IntersectMap = {}

def GetSymbol(a, b):
    """
    GetSymbol
    """
    if(a % b >= b / 2):
        return 1
    else:
        return -1


def GetHashList(x, y, raw_x, raw_y, hash):
    """
    Find a Shape's Hash List
    """
    HashList = [(x, y), (x, y + GetSymbol(raw_y, hash)), (x + GetSymbol(raw_x, hash), y), \
        (x + GetSymbol(raw_x, hash), y + GetSymbol(raw_y, hash))]
    return HashList


def FindIntersect(key, shape, mid):
    """
    Find the Intersection
    """
    if not key in GeomMap.keys():
        return
    AllMidList = GeomMap[key]
    result = []
    for couple in AllMidList:
        old_mid = couple[0]
        old_mid_shape = couple[1]
        if old_mid_shape.distance(shape) <= 0.0:
            try:
                intersection_area = old_mid_shape.intersection(shape).area
                old_area = old_mid_shape.area
                new_area = shape.area
		if intersection_area / old_area > 0.2 or intersection_area / new_area > 0.2:
		      result.append((old_mid, intersection_area / old_area, intersection_area / new_area))
            except Exception as e:
                result.append((old_mid, 1, 1))
    return result


def InsertShape(key, shape, mid):
    """
    Insert a Shape Match Result
    """
    GeomMap[key].append([mid, shape])


def Main():
    """
    Main Function
    """
    OldMidFile = open(sys.argv[1], "r")
    NewMidFile = open(sys.argv[2], "r")
    OutFile = open(sys.argv[3], "w")
    cnt = 0
    while 1:
        lines = OldMidFile.readlines(10000)
        if not lines:
            break
        for line in lines:
            cnt += 1
            print cnt
            line = line.strip('\n\r')
            mid = line.split('\t')[0]
            try:
                shape = shapely.wkt.loads(line.split('\t')[1])
                center = shape.centroid
                key = str(int(center.x / hash)) + "|" + str(int(center.y / hash))
                if not key in GeomMap.keys():
                    GeomMap[key] = []
                InsertShape(key, shape, mid)
            except Exception as e:
                continue
    OldMidFile.close()

    while 1:
        lines = NewMidFile.readlines(10000)
        if not lines:
            break
        for line in lines:
            line = line.strip('\n\r')
            try:
                mid = line.split('\t')[0]
                shape = shapely.wkt.loads(line.split('\t')[1])
                center = shape.centroid
                x = int(center.x / hash)
                y = int(center.y / hash)
                key_list = GetHashList(x, y, center.x, center.y, hash)
                result_list = []
                for i in key_list:
                    key = str(i[0]) + "|" + str(i[1])
                    result = FindIntersect(key, shape, mid)
                    if result is None:
                        continue
                    result_list.extend(result)
                info = mid
                for i in result_list:
                    info += ("\t" + str(i[0]) + "|" + str(i[1]) + "|" + str(i[2]))
                print >> OutFile, "%s" % (info)
            except Exception as e:
                continue
    NewMidFile.close()

if __name__ == '__main__':
    Main()
