"""
掘金结果同步 step 3
"""
from distutils.log import info
import sys
import os
import time
import hashlib
import tqdm
import requests
import subprocess
import math
import json
import copy 
import shutil
import tarfile
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely.ops import nearest_points
from shapely.affinity import translate
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.park_access.light_check import config
from src.common import pipeline
from src.aoi.park_access.light_check import push_juejin_context
from src.aoi import utils
from src.aoi.park_access import park_utils
from collections import defaultdict, deque
import logging as log 
log.basicConfig(level=log.ERROR, format='%(asctime)s %(filename)s[line:%(lineno)d] %(message)s')


light_status = config.light_status
light_check_status = config.light_check_status


def get_juejin_result(light_check, poi_online_query):
    """
    获取掘金结果
    """
    image_name = f"{light_check['intelligence_id']}_{light_check['img_num']}_{light_check['intelligence_id']}_{light_check['id']}_0"
    access_verify = poi_online_query.get_light_check_verify_by_img_id(image_name)
    if not access_verify:
        return False
    if access_verify['conclusion']:
        access_verify['conclusion'] = int(access_verify['conclusion'])
    if access_verify['conclusion'] == '' and access_verify['conclusion_txt'] in light_check_status:
        access_verify['conclusion'] = light_check_status[access_verify['conclusion_txt']]
    return {
        'status': light_status['light_check_synced'],
        'conclusion_txt': access_verify['conclusion_txt'],
        'conclusion': access_verify['conclusion'],
    }

def main():
    """
    出入口情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    bids_arr = None
    limit = 100000
    light_check_list = poi_online_query.get_light_check_list(bids=bids_arr, 
        status=[light_status['light_check_pushed']],
        limit=limit,
        update_time_sort=True)
    if not light_check_list:
        logger.error("No light_check_list found")
        return
    logger.info(f"待同步掘金结果{len(light_check_list)}")
    success_num = 0
    wait_num = 0
    for light_check in tqdm.tqdm(light_check_list):
        id = light_check['id']
        # 获取掘金结果
        update_dict = get_juejin_result(light_check, poi_online_query)
        # 同步掘金结果
        if not update_dict:
            wait_num += 1
            continue
        condition = {
            "id": id,
            "status": light_status["light_check_pushed"],
        }
        logger.info(update_dict)
        logger.info(condition)
        poi_online_rw.update_park_access_light_check(update_dict, condition)
        success_num += 1
    logger.info(f"总数量：{len(light_check_list)}, 同步成功：{success_num}, 等待同步：{wait_num}")


if __name__ == '__main__':
	main()
