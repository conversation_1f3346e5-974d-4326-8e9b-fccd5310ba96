# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
各个层级AOI量级统计
"""

import os
import sys
import datetime

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi import PgQuery
from aoi import PgUpdate

pg_query_dao = PgQuery.PgQuery()
pg_update_dao = PgUpdate.PgUpdate()

# 商单数据
BUSINESS_DATA_SRC = "SD"
# 统计分类
STATISTICS_CATEGORY_AOI_TOTAL = "AOI_TOTAL"


def aoi_level_statistics():
    """
    边框量级统计
    :return:
    """
    date_day = datetime.datetime.now().strftime("%Y-%m-%d")
    business_aoi_statistics = {"aoi_valid_number": 0, "aoi_invalid_number": 0, "aoi_no_bid_number": 0,
                               "date_day": date_day, "category": STATISTICS_CATEGORY_AOI_TOTAL, "aoi_src": "BUSINESS"}
    customer_aoi_statistics = {"aoi_valid_number": 0, "aoi_invalid_number": 0, "aoi_no_bid_number": 0,
                               "date_day": date_day, "category": STATISTICS_CATEGORY_AOI_TOTAL, "aoi_src": "CUSTOMER"}
    face_id = ''
    number = 0
    while 1:
        aoi_list = pg_query_dao.get_aoi_by_face_id(face_id)
        if aoi_list is None or len(aoi_list) == 0:
            break
        # POI 信息查询
        poi_bid_list = [aoi[1] if aoi[1] is not None else '' for aoi in aoi_list]
        poi_info_map = {}
        poi_info_list = pg_query_dao.get_poi_list_by_bid_list(poi_bid_list)
        if poi_info_list is not None and len(poi_info_list) > 0:
            poi_info_map = {poi_info[0]: poi_info for poi_info in poi_info_list}
        number += len(aoi_list)
        print(f"当前进度：{number}")
        # 边框信息统计
        for aoi in aoi_list:
            face_id = aoi[0]
            poi_bid = aoi[1]
            # 商单数据
            if aoi[2] == BUSINESS_DATA_SRC:
                if poi_bid == "" or poi_bid is None:
                    business_aoi_statistics["aoi_no_bid_number"] += 1
                elif poi_bid in poi_info_map:
                    business_aoi_statistics["aoi_valid_number"] += 1
                else:
                    business_aoi_statistics["aoi_invalid_number"] += 1
            else:
                if poi_bid == "" or poi_bid is None:
                    customer_aoi_statistics["aoi_no_bid_number"] += 1
                    continue
                elif poi_bid in poi_info_map:
                    customer_aoi_statistics["aoi_valid_number"] += 1
                else:
                    customer_aoi_statistics["aoi_invalid_number"] += 1
    # 查询精准AOI量级
    accurate_aoi = pg_query_dao.get_accurate_aoi_count()
    business_aoi_statistics['accurate_aoi_number'] = accurate_aoi[0]
    customer_aoi_statistics['accurate_aoi_number'] = 0
    # 成果入库
    pg_update_dao.insert_aoi_statistics(business_aoi_statistics)
    pg_update_dao.insert_aoi_statistics(customer_aoi_statistics)
    print(f"统计完成")


if __name__ == '__main__':
    aoi_level_statistics()
