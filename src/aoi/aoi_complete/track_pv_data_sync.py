"""
轨迹pv数据同步
"""
import os
import sys
from pathlib import Path
from src.door_naming.DaoDestTraj import DaoDestTraj

root_path = Path(os.path.abspath(__file__)).parents[4]
print(root_path)
current_path = Path(os.path.abspath(__file__)).parent

work_dir = current_path / 'pv_sync_data'
work_dir.mkdir(parents=True, exist_ok=True)

pv_file = work_dir / 'track_pv_table.txt'
import datetime
from src.door_naming.DaoTrack import DaoTrack
from src.common import common_tool


def get_valid_gate_pv_table(dao_track: DaoTrack):
    """
    查询 PV table
    :return:
    """
    sql = "SELECT tablename FROM pg_tables WHERE tablename like 'ft_nav_gate_202%' and length(tablename) = 20" \
          " order by tablename desc offset 1 limit 30"
    dao_track.cursor_track_ga.execute(sql)
    ret = dao_track.cursor_track_ga.fetchall()
    for item in ret:
        sql = 'SELECT reltuples::bigint FROM pg_class WHERE relname = %s'
        dao_track.cursor_track_ga.execute(sql, [item[0]])
        count_ret = dao_track.cursor_track_ga.fetchone()
        # 这个表正常有 700w数据,如果小于某个值就放弃
        if count_ret[0] < 7000000:
            continue
        return item[0]
    raise Exception("无法获取高置信表:gates_track_confidence_bmf_202*")


def sync_ft_nav_gate():
    """
    保存本地数据
    :return:
    """
    with DaoTrack() as dao_track, open(pv_file, 'w') as f:
        pv_table = get_valid_gate_pv_table(dao_track)
        print("开始导出数据, 导出的表名称:", pv_table)
        dao_track.cursor_track_ga.copy_to(f, table=pv_table, columns=['gate_id', 'node_id', 'all_pv_30d'], sep='\t',
                                          null='')


def sync_to_db():
    """
    同步到数据库
    :return:
    """

    today = datetime.date.today().strftime('%Y%m%d')
    table_name = f'ft_nav_gate_pv_{today}'
    print(f"开始创建数据表:{table_name}")
    with DaoDestTraj() as dao_dest_traj:
        sql = f'drop table if exists {table_name}'
        dao_dest_traj.cursor.execute(sql)
        dao_dest_traj.conn.commit()
        # 创建数据表
        sql = f'create table {table_name}' \
              f' (gate_id varchar(50) primary key , node_id varchar(50), ' \
              f'all_pv_30d int default 0)'
        dao_dest_traj.cursor.execute(sql)
        dao_dest_traj.conn.commit()

        with open(pv_file) as f:
            dao_dest_traj.cursor.copy_from(f, table_name, sep='\t', columns=['gate_id', 'node_id', 'all_pv_30d'])
        dao_dest_traj.conn.commit()

        sql = f'create index if not exists idx_{table_name}_node_id on {table_name} (node_id)'
        dao_dest_traj.cursor.execute(sql)
        dao_dest_traj.conn.commit()

        # 切换view
        view_name = 'ft_nav_gate_pv'
        sql = f'create or replace view {view_name} as select * from {table_name} '
        dao_dest_traj.cursor.execute(sql)
        dao_dest_traj.conn.commit()


def clean_old():
    """
    删除旧数据
    :return:
    """
    with DaoDestTraj() as dao_dest_traj:
        for i in range(2, 10):
            old_data = datetime.date.today() - datetime.timedelta(days=i)
            table_name = f'ft_nav_gate_pv_{old_data.strftime("%Y%m%d")}'
            print("开始清理旧的数据", table_name)
            sql = f'drop table if exists {table_name}'
            dao_dest_traj.cursor.execute(sql)
            dao_dest_traj.conn.commit()


def run():
    """
    主任务
    :return:
    """
    sync_ft_nav_gate()
    sync_to_db()
    clean_old()


if __name__ == '__main__':
    try:
        run()
    except Exception as e:
        common_tool.send_hi(f"同步道路的node pv失败:{e.args}, 请及时恢复,代码位置在de16:{current_path}",
                            atuserids=['chenbaojun_cd'])
