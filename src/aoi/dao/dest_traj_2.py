# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
轨迹库2
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class DestTrajTwo(PgDao):
    """
    轨迹库1
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "dest_traj_2" in conf:
            poi_online_r_conf = conf['dest_traj_2']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("dest_traj_2 配置不存在")

    def get_traj_list_by_bid(self, bid: list, hash_index: int, in_geom: None, traj_time=None, limit=5000):
        """
        获取bid对应的终点轨迹列表
        Args:
            bid:
            hash_index:
            in_geom:
            traj_time:
            limit:

        Returns:

        """
        sql = f"""
           select st_astext(geom) from  dest_traj where bid=%s and hash_index=%s
        """
        args = [bid, hash_index]
        if in_geom:
            sql += " and st_intersects(geom,st_geomfromtext(%s,4326))"
            args.append(in_geom)
        if traj_time:
            sql += " and traj_time>=%s"
            args.append(traj_time)
        if limit:
            sql += f" limit %s"
            args.append(limit)
        return self.queryall(sql, args)
