# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下线BID
"""
import time

import requests
import pymysql

expimpMysqlConf = {"databases": "bee_flow", "user": "bee_flow_r", "pwd": "bee_flow_r_2021",
                   "host": "*************", "port": 5730}

# 连接MySQL
expimpDbConn = pymysql.connect(host=expimpMysqlConf['host'],
                               port=expimpMysqlConf['port'],
                               user=expimpMysqlConf['user'],
                               password=expimpMysqlConf['pwd'],
                               database=expimpMysqlConf['databases'],
                               charset='utf8')
aoiDao = expimpDbConn.cursor()
def run(bid_file, result_file):
    """
    作业有效率统计
    :return:
    """
    number = 0
    data = [{"bid":"bid","source_name":"分类","aoi_level":"层级:0未调查、1聚合、3内部", "is_send_work":"是否下发过作业:0未下发;1已下发", "work_info":"作业计划类型及状态"}]
    valid_bid = set()
    with open(bid_file, 'r') as file_data:
        for line_data in file_data:
            number += 1
            line_data = line_data.strip("\n")
            line_data = line_data.strip("\r")
            items = line_data.split("\t")
            bid = items[0]
            print(f"当前进度：{number}, 成果量：{len(valid_bid)}")
            if bid == "":
                continue
            # tmp_data = {"bid": bid, "source_name": items[2], "aoi_level": items[1]}
            sql = f"select bid,work_status,task_id,strategy_type from strategy_feature_list where bid='{bid}' and strategy_type in(31,33,34,35,36) and work_status=3"
            # sql = f"-- select strategy_type,work_status from strategy_feature_list where bid='{bid}'"
            aoiDao.execute(sql)
            res_list = aoiDao.fetchall()
            if res_list is None or len(res_list) == 0:
                continue
            for res in res_list:
                if res[3] == 31:
                    sql = f"select wpf_id from unify_work_history where task_id={res[2]} and work_resource like '%{bid}%' and (work_result like '%\"work_conclusion\":2%' or work_result like '%\"work_conclusion\":4%')"
                if res[3] == 33:
                    sql = f"select wpf_id from unify_work_history where task_id={res[2]} and work_resource like '%{bid}%' and (work_result like '%\"work_conclusion\":1%' or work_result like '%\"work_conclusion\":2%')"
                if res[3] == 34:
                    sql = f"select wpf_id from unify_work_history where task_id={res[2]} and work_resource like '%{bid}%' and (work_result like '%\"work_conclusion\":1%' or work_result like '%\"work_conclusion\":2%')"
                if res[3] == 35:
                    sql = f"select wpf_id from unify_work_history where task_id={res[2]} and work_resource like '%{bid}%' and work_result like '%\"work_conclusion\":1%'"
                if res[3] == 36:
                    sql = f"select wpf_id from unify_work_history where task_id={res[2]} and work_resource like '%{bid}%' and work_result like '%\"work_conclusion\":1%'"
                aoiDao.execute(sql)
                result = aoiDao.fetchone()
                if result is not None or len(result) > 0:
                    valid_bid.add(res[0])
                    break
    # 成果写入
    file = open(result_file, 'w')
    for statistics_data in data:
        values = statistics_data.values()
        strs = '\t'.join(values) + "\n"
        file.write(strs)
    file.close()






if __name__ == '__main__':
    run("./bid.txt", "./result.txt")







