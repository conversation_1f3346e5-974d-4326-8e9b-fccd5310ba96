"""
format convert
"""
import sys
import util


def main():
    """
    :return:  void
	"""
    file = open(sys.argv[1])

    cnt = 0
    for line in file:
        if cnt == 0:
            # print line.strip("\n")
            cnt += 1
            continue
        items = line.strip("\n").split(",")
        temp = ""
        inPoly = False
        last_index = 0
        for i in range(1, len(items)):
            if "POLYGON" in items[i]:
                inPoly = True
            if inPoly:
                temp += items[i] + "_"
            if "))" in items[i]:
                inPoly = False
                last_index = i
        temp = temp.strip("\"").strip("POLYGON((").strip("_\"").strip("))")
        shape = temp.replace(" ", ",")
        faceid = items[0]
        name = items[last_index + 2]
        city = items[last_index + 9]
        update_date = items[last_index + 11]
        if name == "" or len(name) == 0:
            continue

        print str(cnt) + "\t" + name + "\t" + city + "\t" + city + "\t" + "0" + "\t" + "0" + "\t" + shape + "\t" + "1"
        cnt += 1


main()
