"""
被拦截的aoi给到poi, 这个脚本只能在de16上跑,或者把aoi-ml挂载到数据库
"""
import os
import sys
import datetime
import tqdm
import numpy as np
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
sys.path.insert(0, root_path.as_posix())

from src.common import common_tool, mysql
import pandas as pd
from src.door_naming.strategy_v2 import common

AFS_DIR = Path('/home/<USER>/mnt/aoi-ml/intercept_main_bid')

if not AFS_DIR.is_dir():
    common_tool.send_hi(f'afs目录de16:{AFS_DIR}不存在,请检查afs挂载情况')


def run():
    """
    任务导出
    :return:
    """
    today = datetime.date.today().strftime('%Y%m%d')
    if (AFS_DIR / f"{today}.tsv").exists():
        return
    sql = 'select distinct(bid) as bid from aoi_protect_information where can_offline=2 '
    cannot_bids = mysql.query_all(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, as_dict=True)
    cannot_bids = [bid['bid'] for bid in cannot_bids]
    bid_groups = np.array_split(cannot_bids, len(cannot_bids) // 1000 + 1)
    lines_all = []
    for bid_group in tqdm.tqdm(bid_groups):
        poi_dict = common.multi_get_poi_info_by_bid(list(bid_group))
        aoi_dict = common.multi_get_aoi_by_main_bid(list(bid_group))
        for bid, poi_info in poi_dict.items():
            if bid not in aoi_dict:
                continue
            if aoi_dict[bid].src == 'SD':
                continue
            lines_all.append({
                "bid": bid,
                'name': poi_info.name,
                'std_tag': poi_info.std_tag,
                'address': poi_info.address,
                'geom(gcj02)': poi_info.geom,
                "city": poi_info.city,
                'status': poi_info.status,
            })
    pd.DataFrame(lines_all).to_csv(AFS_DIR / f"{today}.tsv", index=False, sep='\t')


if __name__ == '__main__':
    run()
