from flask import Flask, request, jsonify
import urllib.parse
import webbrowser
import json

app = Flask(__name__)

@app.route('/display')
def display_images():
    """
    显示图片的Flask视图函数。
    """
    # 获取并解码JSON参数
    json_str = request.args.get('json', '')
    if not json_str:
        return jsonify({"error": "Missing json parameter"}), 400
    
    try:
        # URL解码并解析JSON
        decoded_json = urllib.parse.unquote(json_str)
        data = json.loads(decoded_json)
        
        # 构建HTML响应
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>BOS Key 地图展示</title>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ margin-bottom: 30px; }}
                .gallery {{
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                    gap: 20px;
                }}
                .card {{
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .map-img {{
                    width: 100%;
                    height: 250px;
                    object-fit: cover;
                    border: 1px solid #eee;
                }}
                .info {{ margin-top: 10px; }}
                .timestamp {{ color: #666; font-size: 0.9em; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>轨迹点地图展示</h1>
                <p>共 {len(data)} 个点位</p>
            </div>
            <div class="gallery">
        """
        
        # 为每个数据项生成卡片
        base_url = "http://m.map.baidu.com:8011/"
        for item in data:
            bos_key = item.get('bos_key', '')
            if not bos_key:
                continue
                
            map_url = base_url + bos_key
            html += f"""
            <div class="card">
                <h3>{bos_key}</h3>
                <img class="map-img" src="{map_url}" 
                     onerror="this.src='https://via.placeholder.com/300x200?text=图片加载失败'" 
                     alt="地图截图">
                <div class="info">
                    <p class="timestamp">时间: {item.get('time', 'N/A')}</p>
                    <p>速度: {item.get('speed', 'N/A')} km/h</p>
                    <p>坐标: {item.get('x', '')}, {item.get('y', '')}</p>
                    <p><a href="{map_url}" target="_blank">查看大图</a></p>
                </div>
            </div>
            """
        
        html += """
            </div>
        </body>
        </html>
        """
        return html
        
    except json.JSONDecodeError as e:
        return jsonify({"error": f"Invalid JSON format: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"error": f"Server error: {str(e)}"}), 500

if __name__ == '__main__':
    # 示例调用URL (自动打开浏览器)
    sample_data = [
        {
            "bos_key": "P_yika20250405001011_1743819537650",
            "cdid": "20250405543159ra",
            "time": "2025-04-05 10:18:57",
            "speed": 4.83,
            "x": 116.55758392,
            "y": 27.88094276
        },
        {
            "bos_key": "P_yika20250405001011_1743819530982",
            "cdid": "20250405543159ra",
            "time": "2025-04-05 10:18:50",
            "speed": 1.5,
            "x": 116.55738657,
            "y": 27.88090553
        }
    ]
    encoded_json = urllib.parse.quote(json.dumps(sample_data))
    webbrowser.open(f'http://gznj-sys-rpm01-a7c9a.gznj.baidu.com:8011/display?json={encoded_json}')
    
    # 启动服务，指定端口8011
    app.run(host='0.0.0.0', port=8011, debug=True)