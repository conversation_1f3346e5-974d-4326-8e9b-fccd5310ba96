#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../conf/globalConf.sh

RAW_DATA="$DATA_PATH/aoi/$(date +%Y-%m-%d).csv"
mkdir -p "$DATA_PATH/aoi"
MD5_FILE="$DATA_PATH/aoi/$(date +%Y-%m-%d).md5"
#商圈类(kind 52)的不需要产出
$PG_MASTER_BACK_CONN -d master_back -c "\copy  (select
  a.face_id,
  st_astext(gcj2mc(a.geom)),
  b.poi_bid,
  a.name_ch,
  a.kind,
  area,
  scene_id,
  display_flag,
  proj_flag,
  a.mesh_id,
  city_name,
  src,
  update_time,
  a.memo,
  a.aoi_level,
  a.aoi_level_source,
  a.access_complete
from blu_face a
left join blu_face_poi b on a.face_id = b.face_id
where
  a.kind != '52'
  and b.poi_bid != ''
) to '$RAW_DATA'"
aoiRows=`wc -l "$RAW_DATA" | awk '{print$1}'`
if [ $aoiRows -lt 100000 ]; then
  MailWarning "背景库导出aoi数量低于阈值, 当前数量$aoiRows" "全量aoi导出数量低于阈值"
fi
md5sum "$RAW_DATA" | awk '{print $1}' > "$MD5_FILE"
#python $LIB_PATH/geo_clockwise.py "$RAW_DATA" "$DATA_PATH/poi_region.txt"
#md5sum "$DATA_PATH/poi_region.txt" > "$DATA_PATH/poi_region.txt.md5"

if [ ! -z "$1" ] && [ $1 == "release" ]; then
  cp "$RAW_DATA" "$MD5_FILE" "$FTP_PATH/release/aoi/"
  find "$FTP_PATH/release/aoi/" -mtime +5 -exec rm {} \;
fi

