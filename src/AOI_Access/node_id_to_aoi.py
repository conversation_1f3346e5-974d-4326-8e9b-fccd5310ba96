#-*- coding: utf-8 -*-
"""
nodeid 找aoi和aoi级别
"""
import time
import psycopg2
import requests
import json


curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}
 
# poiDevConf = {"db": "poi_online",
#             "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
#             "host": "**************", "port": 9432}

poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}


#连接poi测试库
# poiDevDbConn = psycopg2.connect(database=poiDevConf["db"],
#                               user=poiDevConf["user"],
#                               password=poiDevConf["pwd"],
#                               host=poiDevConf["host"],
#                               port=poiDevConf["port"])
# poiDevDao = poiDevDbConn.cursor()

#连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()


def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    # 初始化数据
    with open("/home/<USER>/chengqiong/aoi-strategy/aoi_access/node_id_to_aoi_res.txt", "w") as fw: 
        fw.write("node_id\tlevel\tcity_name\tname\n")
        for node_id in open("node_id_list.txt"):
            node_id = node_id.strip()
            node_id = node_id.strip(',')
            fw.write(node_id + "\t")
            sql = "select b.face_id from blu_access_gate_rel a \
                            inner join blu_access b on a.access_id = b.access_id \
                        where a.node_id = '{}'".format(node_id)
            backgroundDao.execute(sql)
            face_data = backgroundDao.fetchone()  
            face_id = ''
            if face_data:
                face_id = face_data[0]
            else:
                sql = "select face_id from aoi_gate_match_batch_result \
                     where success_node_id like '%{}%' order by id desc".format(node_id)
                poiDao.execute(sql)
                res = poiDao.fetchone()
                if res:
                    level = res[0]
            if face_id:
                sql = "select level,bid from aoi_gate_match_batch_result \
                     where face_id = '{}' order by id desc".format(face_id)
                poiDao.execute(sql)
                aoi_gate_list = poiDao.fetchone()
                if aoi_gate_list:
                    level = aoi_gate_list[0]
                    fw.write(level + "\t")
                else:
                    fw.write('null' + "\t")
                
                sql = "select city_name,name_ch from blu_face where face_id = '{}'".format(face_id)
                backgroundDao.execute(sql)
                res = backgroundDao.fetchone()
                city_name = 'null'
                name_ch = 'null'
                if res:
                    city_name = res[0]
                    name_ch = res[1]
                fw.write(city_name + "\t" + name_ch + "\t")
            fw.write("\n")
        print("end")
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

if __name__ == "__main__":
    process()