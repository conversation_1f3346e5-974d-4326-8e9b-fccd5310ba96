"""
关联关系情报自动化闭环
"""
import time
import os
import sys
import traceback
import requests
import uuid
import tqdm

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")

from datetime import datetime
from pg_tool import PgTool
from common import common_tool
import aoi_v1_protected

class IntelAutoClose(PgTool):
    """
    关联关系情报自动化闭环流程
    """

    def __init__(self, road_dsn={}):
        """
        初始化数据库
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        :return:
        """
        return

    def commit(self):
        """
        数据提交
        """
        PgTool.commit(self)

    def run(self):
        """
        关联关系情报自动化闭环流程
        """
        start_time = time.strftime('%Y-%m-%d', time.localtime(time.time()))
        print(f"------{start_time} 关联关系情报自动化闭环流程 start------")

        # 情报上传
        print("push_intelligence start")
        self.push_intelligence()
        print("push_intelligence done")

        # 情报拉取
        print("retrieve_intelligence start")
        self.fetch_intelligence()
        print("retrieve_intelligence done")

        end_time = time.strftime('%Y-%m-%d', time.localtime(time.time()))
        print(f"------{end_time} 关联关系情报自动化闭环流程 all done------")

    def push_intelligence(self):
        """
        情报上传：将关联情报库上传给一体化情报库
        """
        intel_list = self.fetch_intel_list()
        for intel in tqdm.tqdm(intel_list):
            id, bid = intel
            uid = self.get_uuid()
            res = self.sync_qb({
                "from_src": "关联关系-履历库变更DIFF",
                "src": 2,
                "ref_qb_id": uid,
                "main_poi_bid": bid,
                "qb_type": 2
            })
            if res["code"] != 0:
                print("sync_qb_failed_intel_id:", id)
                continue

            qb_id = res["data"]["qb_id"]
            sql = f"""
                update gate_match_intel_src 
                set qb_id = {qb_id}, ref_qb_id = '{uid}'
                where id = {id}
            """
            self.cursor_poi.execute(sql)
            self.preempt_qb(qb_id)

    def fetch_intel_list(self):
        """
        获取需要同步的情报列表
        """
        sql = """
            select id, bid 
            from gate_match_intel_src 
            where status = 0 and qb_id = -1
        """
        self.cursor_poi.execute(sql)
        intel_list = self.cursor_poi.fetchall()
        return intel_list

    def fetch_intelligence(self):
        """
        情报拉取：将一体化情报库关联关系情报拉取到关联情报库
        """
        feature_list = self.get_feature_list()
        protected_main_bids = aoi_v1_protected.load_protected_main_bids(self)
        for feature in tqdm.tqdm(feature_list):
            qb_id, bid, ref_qb_id, from_src = feature
            curr_time = datetime.today().strftime("%Y-%m-%d %H:%M:%S")
            if bid in protected_main_bids:
                continue
            sql = """
                insert into gate_match_intel_src (
                    bid, src, status, qb_id, ref_qb_id, start_time, update_time
                ) values (
                   %s, %s, 0, %s, %s, %s, %s
                )
            """
            self.cursor_poi.execute(sql, (bid, from_src, qb_id, ref_qb_id, curr_time, curr_time))
            self.preempt_qb(qb_id)

    def preempt_qb(self, qb_id):
        """
        抢占情报
        """
        resp = self.preempt_qb_by_id(qb_id)
        if resp['code'] == 0:
            self.commit()
        else:
            print("preempt_qb_failed_qb_id:", qb_id)
            self.conn_poi.rollback()

    @staticmethod
    def preempt_qb_by_id(qb_id):
        """
         根据ID抢占QB
         :param qb_id: 情报ID
         :return: 响应体
         """
        req = requests.post(
            url="https://mapde-poi.baidu-int.com/prod/integration/qbPreempt",
            json={"qb_id": qb_id},
            timeout=10,
        )
        return req.json()

    @staticmethod
    def sync_qb(payload):
        """
        同步QB
        :param payload: 请求体
        :return: 响应体
        """
        req = requests.post(
            url="https://mapde-poi.baidu-int.com/prod/integration/qbSyncV2",
            json=payload,
            timeout=10,
        )
        return req.json()

    @staticmethod
    def get_uuid():
        """
        获取uuid
        """
        return str(uuid.uuid4()).replace("-", "")

    def get_feature_list(self):
        """
        获取关联关系要素情报列表
        """
        sql = f"""
            select id, main_poi_bid, ref_qb_id, from_src
            from integration_qb
            where src = 2 and flow_status = 1 and qb_type = 2 
        """
        self.cursor_poi.execute(sql)
        feature_list = self.cursor_poi.fetchall()
        return feature_list


if __name__ == "__main__":
    """
    main
    """
    print("关联关系情报自动化闭环开始:" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    with IntelAutoClose() as auto_close:
        try:
            auto_close.run()
        except Exception as e:
            common_tool.send_hi("关联关系情报自动化闭环：失败-de16-xxl-job-226", ['chenbaojun_cd'])
            traceback.print_exc()
