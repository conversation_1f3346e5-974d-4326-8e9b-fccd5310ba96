CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CUR_DIR"/../../../conf/globalConf.sh

WorkPath="${DATA_PATH}/OnlineOffline"
BinPath="${CUR_DIR}"
OnlinePath="${WorkPath}/online"
OfflinePath="${WorkPath}/offline"
FinalPath="${WorkPath}/final"
SplitPath="${WorkPath}/split"
CITYFILE="${BinPath}/city.shape"

mkdir -p "$WorkPath" "$OnlinePath" "$OfflinePath" "$FinalPath" "$SplitPath" "$OnlinePath"
