# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
【深圳克而瑞房产地铁项目】商业化数据输出
"""
import os
import tqdm
import psycopg2
import csv
import tempfile
import traceback
import argparse
from shapely import wkt
from typing import Dict, List, Optional

# 母库
pdd_sh_conf = {"db": "master_back", "user": "master_back", "pwd": "master_back",
               "host": "*************",
               "port": 8033}

# poi
poi_online_conf = {"db": "poi_online", "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
                   "host": "*************",
                   "port": 8032}

# business_order
buConf = {"db": "business_order", "user": "business_order_se_rw", "pwd": "kpigzear",
          "host": "**************",
          "port": 7432}

# 连接母库
pdd_sd_conn = psycopg2.connect(database=pdd_sh_conf["db"],
                               user=pdd_sh_conf["user"],
                               password=pdd_sh_conf["pwd"],
                               host=pdd_sh_conf["host"],
                               port=pdd_sh_conf["port"])
pdd_sd_dao = pdd_sd_conn.cursor()

# 连接business_order
buDbConn = psycopg2.connect(database=buConf["db"],
                            user=buConf["user"],
                            password=buConf["pwd"],
                            host=buConf["host"],
                            port=buConf["port"])
bus_cursor = buDbConn.cursor()

# 连接POI
poi_conn = psycopg2.connect(
    database=poi_online_conf["db"],
    user=poi_online_conf["user"],
    password=poi_online_conf["pwd"],
    host=poi_online_conf["host"],
    port=poi_online_conf["port"]
)
poi_cursor = poi_conn.cursor()

not_relate_keywords = ['电子眼', '公交线路', '标注', '出入口', '自然地物', '门址', '道路', '铁路', '行政界线', '其他线要素', '行政区划', '水系', '绿地', '停车场']
suffix_keyworkds_1 = ['栋', '号楼', '号', '幢', '舍', '楼', '座', '大厦']
suffix_keyworkds_2 = ['馆', '宫', '殿', '门', '阁', '斋', '堂', '亭', '台', '居']
suffix_keyworkds_3 = ['公司', '集团', '厂']

root_dir = "/home/<USER>/xianghonghui/business_order/shenzhen"
bud_path = root_dir + "/background/00-aoi"
blu_path = root_dir + "/background/01-landutil"
blc_path = root_dir + "/background/02-landcover"
brw_path = root_dir + "/background/03-railway"
bad_path = root_dir + "/background/04-divison"
bad_county_path = root_dir + "/background/04-divison/county"
bad_town_path = root_dir + "/background/04-divison/town"
province_list = {
    'st_astext': "POLYGON((107.066028790918 25.471650056,107.066034189102 25.471649413255,107.066039285447 25.471647520959,107.*********** 25.4716444849199,107.*********** 25.4716404748976,107.066050091977 25.4716357151126,107.066051527325 25.4716304717083,107.066051691367 25.4716250378697,107.*********** 25.4716197174297,107.066048240441 25.4716148078808,107.066048240441 25.4716148078808,107.066040438356 25.4716024285145,107.066038094557 25.4715979711039,107.066036474378 25.4715932027787,107.066035617445 25.4715882401612,107.066035544718 25.4715832046258,107.066036257974 25.4715782193303,107.06603773977 25.4715734062038,107.066039953864 25.4715688829644,107.066042846104 25.4715647602402,107.066046345752 25.4715611388638,107.066050367215 25.4715581074059,107.066050367215 25.4715581074058,107.066054583005 25.4715554190761,107.066059294683 25.4715528503533,107.066064313141 25.4715509494786,107.066069544254 25.4715497521047,107.066074889907 25.4715492806894,107.066080249839 25.4715495440743,107.06608552352 25.4715505373196,107.066090612037 25.471552241796,107.066095419951 25.4715546255346,107.066099857085 25.4715576438265,107.066103840217 25.471561240061,107.066103840217 25.471561240061,107.066154865758 25.4716140864254,107.066159025972 25.4716173895047,107.066163903438 25.4716194937966,107.066169160828 25.4716202537682,107.066174434542 25.4716196168597,107.066179359849 25.4716176271197,107.066183596114 25.4716144221589,107.066186850357 25.4716102236328,107.066188897512 25.4716053219119,107.066189596 25.471600056,107.066189596 25.471600056,107.066189596 25.471300056,107.066189322095 25.4712948295768,107.06618850338 25.4712896604155,107.066187148826 25.4712846051503,107.066185273273 25.4712797191678,107.06618289727 25.471275056,107.06618004685 25.4712706667374,107.066176753241 25.4712665994697,107.06617305253 25.4712628987587,107.066168985263 25.4712596051503,107.066164596 25.4712567547298,107.066159932832 25.4712543787271,107.06615504685 25.4712525031742,107.066149991585 25.47125114862,107.066144822423 25.4712503299052,107.066139596 25.471250056,107.066139596 25.471250056,107.065839596 25.471250056,107.065834369577 25.4712503299052,107.065829200415 25.47125114862,107.06582414515 25.4712525031742,107.065819259168 25.4712543787271,107.065814596 25.4712567547298,107.065810206737 25.4712596051503,107.06580613947 25.4712628987587,107.065802438759 25.4712665994697,107.06579914515 25.4712706667374,107.06579629473 25.471275056,107.065793918727 25.4712797191678,107.065792043174 25.4712846051503,107.06579068862 25.4712896604155,107.065789869905 25.4712948295768,107.065789596 25.471300056,107.065789596 25.471300056,107.065789596 25.471600056,107.065789869905 25.4716052824232,107.06579068862 25.4716104515845,107.065792043174 25.4716155068497,107.065793918727 25.4716203928322,107.06579629473 25.471625056,107.06579914515 25.4716294452626,107.065802438759 25.4716335125303,107.06580613947 25.4716372132413,107.065810206737 25.4716405068497,107.065814596 25.4716433572702,107.065819259168 25.4716457332729,107.06582414515 25.4716476088258,107.065829200415 25.47164896338,107.065834369577 25.4716497820948,107.065839596 25.471650056,107.065839596 25.471650056,107.066028790918 25.471650056))"
}

def run(blue_face_csv_path=None, bud_face_csv_path=None):
    """
    【深圳克而瑞房产地铁项目】商业化数据输出

    Args:
        blue_face_csv_path: blu_export输出的CSV文件路径
        bud_face_csv_path: bud_export输出的CSV文件路径
    """
    print(f"开始blu导出")
    blu_export(blue_face_csv_path)
    print(f"开始bud导出")
    bud_export(blue_face_csv_path, bud_face_csv_path)

def get_std_tags_from_external_db(poi_ids: List[str]) -> Dict[str, Optional[str]]:
    """
    从外部数据库获取std_tag信息

    Args:
        poi_ids: poi_id列表

    Returns:
        Dict[poi_id, std_tag]: poi_id到std_tag的映射，如果没有找到对应记录则为None
    """
    if not poi_ids:
        return {}

    std_tags = {}

    try:
        # 构建查询条件，使用参数化查询防止SQL注入
        # 注意：这里需要根据实际的表名和字段名进行调整
        placeholders = ','.join(['%s'] * len(poi_ids))
        query = f"""
            SELECT bid, std_tag
            FROM poi
            WHERE bid IN ({placeholders})
        """

        poi_cursor.execute(query, poi_ids)
        results = poi_cursor.fetchall()

        # 构建结果字典
        for poi_bid, std_tag in results:
            std_tags[str(poi_bid)] = std_tag

        # 为没有找到的poi_id设置None
        for poi_id in poi_ids:
            if str(poi_id) not in std_tags:
                std_tags[str(poi_id)] = None

    except psycopg2.Error as e:
        print(f"外部数据库查询错误: {e}")
        # 如果查询失败，为所有poi_id设置None
        for poi_id in poi_ids:
            std_tags[str(poi_id)] = None

    return std_tags


def blu_export(output_path=None):
    """
    AOI数据输出，包含从外部数据库获取的std_tag信息

    Args:
        output_path: 输出CSV文件的路径，如果为None则使用默认路径
    """
    # 设置默认输出路径
    if output_path is None:
        output_path = "/Users/<USER>/Downloads/blue_face.csv"
    # 获取AOI所需数据
    sql = """
        SELECT
            bf.face_id, bfp.poi_bid, st_area(bf.geom::geography) as area, ST_Perimeter(bf.geom::geography) as perimeter, bf.name_ch,
            ST_AsText(bf.geom) AS geom
        FROM blu_face bf
        INNER JOIN blu_face_poi bfp
        ON bf.face_id = bfp.face_id
        where bfp.poi_bid in ('9613357449661244188', '15805401385673958468', '16428497351266975910', '14461531089649178845');
    """

    # 生成临时文件路径
    temp_csv_path = tempfile.mktemp(suffix='.csv')

    try:
        # 执行原始查询并导出到临时CSV文件
        cmd = f"""
            ogr2ogr -overwrite -f 'CSV' {temp_csv_path} PG:"host='*************' dbname='master_back' port='8033' user='master_back' password='master_back'" -sql "{sql}" -lco ENCODING=UTF-8
        """
        result = os.system(cmd)

        if result != 0:
            print(f"原始数据查询失败，命令返回码: {result}")
            return

        # 读取CSV文件并提取poi_bid值
        poi_ids = []
        rows_data = []

        with open(temp_csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                poi_bid = row.get('poi_bid')
                if poi_bid:
                    poi_ids.append(poi_bid)
                rows_data.append(row)

        print(f"从原始查询中提取到 {len(poi_ids)} 个poi_bid值")

        # 从外部数据库获取std_tag信息
        std_tags = get_std_tags_from_external_db(poi_ids)

        # if not os.path.exists(bud_path):
        #     os.makedirs(output_path)

        # 合并数据并写入最终的CSV文件（使用参数化的输出路径）
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            if rows_data:
                # 定义新的字段名，将poi_bid改为poi_id，name_ch改为name
                fieldnames = ['face_id', 'poi_id', 'area', 'perimeter', 'name', 'geom', 'std_tag']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for row in rows_data:
                    poi_bid = row.get('poi_bid', '')
                    # 创建新的行数据，重命名字段
                    new_row = {
                        'face_id': row.get('face_id', ''),
                        'poi_id': poi_bid,  # poi_bid 改为 poi_id
                        'area': row.get('area', ''),
                        'perimeter': row.get('perimeter', ''),
                        'name': row.get('name_ch', ''),  # name_ch 改为 name
                        'geom': row.get('geom', ''),
                        'std_tag': std_tags.get(str(poi_bid), '')
                    }
                    writer.writerow(new_row)

        print(f"数据已成功导出到: {output_path}")
        print(f"共处理 {len(rows_data)} 条记录，其中 {len([v for v in std_tags.values() if v is not None])} 条记录找到了对应的std_tag")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")

    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_csv_path):
                os.unlink(temp_csv_path)
        except:
            pass

def bud_export(csv_input_path=None, output_path=None):
    """
    建筑物数据输出

    Args:
        csv_input_path: blu_export生成的CSV文件路径，如果为None则使用默认路径
        output_path: 输出CSV文件的路径，如果为None则使用默认路径
    """
    # 设置默认输入路径
    if csv_input_path is None:
        csv_input_path = "/Users/<USER>/Downloads/blue_face.csv"

    # 设置默认输出路径
    if output_path is None:
        output_path = "/Users/<USER>/Downloads/bud_face.csv"

    try:
        # 检查CSV文件是否存在
        if not os.path.exists(csv_input_path):
            print(f"错误：找不到输入文件 {csv_input_path}")
            print("请先运行 blu_export() 函数生成该文件")
            return

        # 从CSV文件读取face_id和geom数据（移除std_tag读取，改为从POI绑定获取）
        aoi_data = []
        with open(csv_input_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                geom = row.get('geom')
                face_id = row.get('face_id')  # 获取face_id字段
                if geom and face_id:  # 确保face_id和geom都存在
                    aoi_data.append({
                        'face_id': face_id,  # 保存face_id
                        'geom': geom
                        # 注意：移除std_tag读取，改为从POI绑定过程中获取
                    })

        print(f"从CSV文件读取到 {len(aoi_data)} 个AOI geom数据（包含face_id）")

        # 执行st_contains查询，每个AOI geom与bud_face表中所有记录进行比较
        results = []

        for i, aoi_item in enumerate(aoi_data):
            aoi_geom = aoi_item['geom']
            aoi_face_id = aoi_item['face_id']  # 获取AOI的face_id
            print(f"处理第 {i+1} 个AOI geom (face_id: {aoi_face_id})")

            # 修改查询逻辑：移除aoi_id字段，改为使用face_id进行查询
            contains_sql = """
                SELECT face_id, struct_id, st_area(geom::geography) as area, ST_Perimeter(geom::geography) as perimeter, st_astext(geom), height
                FROM bud_face
                WHERE ST_Contains(ST_GeomFromText(%s, 4326), geom);
            """

            pdd_sd_dao.execute(contains_sql, (aoi_geom,))
            contains_results = pdd_sd_dao.fetchall()

            print(f"  与 {len(contains_results)} 个bud_face记录进行比较")

            # 处理每个比较结果（调整索引，因为移除了aoi_id字段）
            for result_row in contains_results:
                face_id = result_row[0]
                struct_id = result_row[1]
                area = result_row[2]  # 索引调整：原来是[3]，现在是[2]
                perimeter = result_row[3]  # 索引调整：原来是[4]，现在是[3]
                geom = result_row[4]  # 索引调整：原来是[5]，现在是[4]
                height = result_row[5]  # 索引调整：原来是[6]，现在是[5]

                # 根据struct_id查询对应的name_ch，处理"虚拟POI"情况
                name_ch_sql = """
                    SELECT CASE WHEN name_ch = '虚拟POI' THEN '' ELSE name_ch END as name_ch
                    FROM bud_struct WHERE struct_id = %s
                """
                pdd_sd_dao.execute(name_ch_sql, (struct_id,))
                name_ch_result = pdd_sd_dao.fetchone()
                name_ch = name_ch_result[0] if name_ch_result else None

                results.append({
                    'std_tag': '',  # 初始化为空，将在POI绑定过程中更新
                    'face_id': face_id,
                    'struct_id': struct_id,
                    'aoi_id': aoi_face_id,  # 修改：CSV列名显示为aoi_id，数据内容仍为face_id值
                    'area': area,
                    'perimeter': perimeter,
                    'geom': geom,
                    'hegiht': height,
                    'name_ch': name_ch
                })

                print(f"  AOI geom {i+1} (face_id: {aoi_face_id}) contains bud_face {face_id} (name_ch: {name_ch})")

        # 输出结果到CSV文件（使用参数化的输出路径）
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            # 修改字段名：CSV列标题显示为aoi_id，但数据内容仍使用face_id值
            fieldnames = ['face_id', 'struct_id', 'name_ch', 'aoi_id', 'hegiht', 'area', 'perimeter', 'geom', 'std_tag']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                writer.writerow(result)

        print(f"st_contains查询结果已导出到: {output_path}")
        print(f"共处理 {len(results)} 个空间包含关系比较结果")

        # 调用bind_poi_name函数进行名称更新（直接传递CSV文件路径）
        print("开始进行POI名称绑定...")
        try:
            poi_bindings = bind_poi_name(output_path)

            # 如果有POI绑定信息，重新写入CSV文件
            if poi_bindings:
                print(f"获得 {len(poi_bindings)} 个POI名称和标签更新，正在更新CSV文件...")

                # 更新results中的name_ch和std_tag字段
                # 使用(struct_id, geom)组合精确匹配每个建筑物记录
                updated_count = 0
                for result in results:
                    struct_id = result.get('struct_id')
                    geom = result.get('geom')

                    if struct_id and geom:
                        # 创建与bind_poi_name中相同的唯一键
                        struct_geom_key = f"{struct_id}_{hash(geom)}"

                        # 查找对应的POI绑定信息
                        if struct_geom_key in poi_bindings:
                            poi_info = poi_bindings[struct_geom_key]
                            result['name_ch'] = poi_info['name']  # 更新POI名称
                            result['std_tag'] = poi_info['std_tag']  # 更新POI标签
                            updated_count += 1

                print(f"成功更新了 {updated_count} 个建筑物记录的POI信息")

                # 重新写入CSV文件
                with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['face_id', 'struct_id', 'name_ch', 'aoi_id', 'hegiht', 'area', 'perimeter', 'geom', 'std_tag']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for result in results:
                        writer.writerow(result)

                print(f"CSV文件已更新，成功绑定 {len(poi_bindings)} 个POI名称和标签")
            else:
                print("没有找到可绑定的POI名称")

        except Exception as e:
            print(f"POI名称绑定过程中发生错误: {e}")
            print("继续使用原始名称...")

    except psycopg2.Error as e:
        print(f"数据库查询错误: {e}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

def bind_poi_name(bud_face_csv_path=None):
    """
    为建筑物绑定POI名称和标签

    Args:
        bud_face_csv_path: bud_face.csv文件路径，如果为None则使用默认路径

    Returns:
        Dict[struct_id_geom_key, Dict]: 返回以(struct_id, geom)为键的POI绑定信息字典
        格式: {struct_id_geom_key: {'name': poi_name, 'std_tag': poi_std_tag, 'bid': poi_bid}}
    """

    poi_bindings = {}  # 存储POI绑定信息 (struct_id_geom_key -> poi_info)

    # 设置默认CSV文件路径
    if bud_face_csv_path is None:
        bud_face_csv_path = "/Users/<USER>/Downloads/bud_face.csv"

    try:
        # 检查CSV文件是否存在
        if not os.path.exists(bud_face_csv_path):
            print(f"错误：找不到bud_face.csv文件 {bud_face_csv_path}")
            return {}

        # 从CSV文件读取所有建筑物数据，替代数据库查询
        bud_structs = []

        print(f"开始从CSV文件读取建筑物数据: {bud_face_csv_path}")

        with open(bud_face_csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                struct_id = row.get('struct_id')
                geom = row.get('geom')

                # 读取所有有效的建筑物数据 - 保持与bud_export相同的过滤逻辑
                if struct_id and geom:
                    bud_structs.append((struct_id, geom))

        print(f"从CSV文件中找到 {len(bud_structs)} 个建筑物进行POI名称绑定")

        near_no_poi = 0
        near_no_poi_by_no_keyword = 0
        near_no_poi_by_filter_poi_bid = 0
        bind_max_pv = 0
        match_num = 0
        error_count = 0  # 新增：统计处理异常的建筑物数量
        invalid_data_count = 0  # 新增：统计无效数据（struct_id或geom为空）的建筑物数量
        for bud_struct in tqdm.tqdm(bud_structs):
            try:
                struct_id, geom = bud_struct[0], bud_struct[1]
                if not struct_id or not geom:
                    invalid_data_count += 1  # 统计无效数据
                    continue
                geom_sp = wkt.loads(geom)
                geom_buffer_5m_sp = geom_sp.buffer(0.00010)
                # 查询建筑物附近POI
                sql = f"""
                    select bid,name,std_tag,address,click_pv from poi where st_contains(st_geomfromtext('{geom_buffer_5m_sp.wkt}',4326),geometry)
                """
                poi_cursor.execute(sql)
                pois = poi_cursor.fetchall()
                if len(pois) == 0:
                    near_no_poi += 1
                    continue
                # 排除特殊关联字,不可匹配
                valid_pois = []
                for poi in pois:
                    bid, name, address = poi[0], poi[1], poi[3]  # 只解包需要的字段
                    is_valid = True
                    for keywords in not_relate_keywords:
                        if keywords in name or keywords in address:
                            is_valid = False
                            break
                    if is_valid:
                        valid_pois.append(poi)

                # 修正统计逻辑：如果所有POI都被关键字过滤掉，则对该建筑物统计一次关键字过滤
                if len(valid_pois) == 0:
                    near_no_poi_by_no_keyword += 1
                    continue
                # 去掉AOI主点的POI
                bids = [item[0] for item in valid_pois]
                bid_str = "','".join(bids)
                sql = f"""
                    select poi_bid from blu_face_poi where poi_bid in('{bid_str}')
                """
                pdd_sd_dao.execute(sql)
                main_poi_list = pdd_sd_dao.fetchall()
                if main_poi_list:
                    main_poi_bids = [item[0] for item in main_poi_list]
                    valid_pois = [item for item in valid_pois if item[0] not in main_poi_bids]
                if len(valid_pois) == 0:
                    near_no_poi_by_filter_poi_bid += 1
                    continue
                # 按照关键字后缀关联匹配
                keywords_1_pois = []
                keywords_2_pois = []
                keywords_3_pois = []
                for poi in valid_pois:
                    name = poi[1]  # 只获取name字段用于匹配
                    if any([poi for keywords in suffix_keyworkds_1 if name.endswith(keywords)]):
                        keywords_1_pois.append(poi)
                        continue
                    if any([poi for keywords in suffix_keyworkds_2 if name.endswith(keywords)]):
                        keywords_2_pois.append(poi)
                        continue
                    if any([poi for keywords in suffix_keyworkds_3 if name.endswith(keywords)]):
                        keywords_3_pois.append(poi)
                        continue
                # 挑选pv最高的绑定
                bind_poi = None
                if keywords_1_pois:
                    bind_poi = max(keywords_1_pois, key=lambda x: x[-1])
                if not bind_poi and keywords_2_pois:
                    bind_poi = max(keywords_2_pois, key=lambda x: x[-1])
                if not bind_poi and keywords_3_pois:
                    bind_poi = max(keywords_3_pois, key=lambda x: x[-1])
                if not bind_poi:
                    bind_poi = max(valid_pois, key=lambda x: x[-1])
                    bind_max_pv += 1
                # 开始绑定建筑物信息
                bid, name, std_tag = bind_poi[0], bind_poi[1], bind_poi[2]  # 获取bid、name和std_tag字段
                print(f"建筑物匹配POI - struct_id:{struct_id}, poi_bid:{bid}, poi_name:{name}, std_tag:{std_tag}")

                # 使用(struct_id, geom)组合作为唯一键，确保每个建筑物记录都有唯一的POI绑定
                struct_geom_key = f"{struct_id}_{hash(geom)}"  # 创建唯一键
                poi_bindings[struct_geom_key] = {
                    'name': name,
                    'std_tag': std_tag,
                    'bid': bid,
                    'struct_id': struct_id
                }
                match_num += 1

            except Exception as e:
                print(f"处理struct_id {struct_id} 时发生错误: {e}")
                print(traceback.format_exc())
                error_count += 1  # 统计异常情况
                continue

        # 输出统计信息并验证数据一致性
        total_processed = len(bud_structs)
        total_categorized = match_num + near_no_poi + near_no_poi_by_no_keyword + near_no_poi_by_filter_poi_bid + error_count + invalid_data_count

        print(f"POI名称绑定完成:")
        print(f"  处理建筑物数量: {total_processed}")
        print(f"  成功绑定数量: {match_num}")
        print(f"  附近无POI: {near_no_poi}")
        print(f"  关键字过滤: {near_no_poi_by_no_keyword}")
        print(f"  AOI主点过滤: {near_no_poi_by_filter_poi_bid}")
        print(f"  无效数据: {invalid_data_count}")
        print(f"  处理异常: {error_count}")
        print(f"  使用最高PV: {bind_max_pv}")
        print(f"  ----")
        print(f"  统计验证: 分类总数 = {total_categorized}, 处理总数 = {total_processed}")

        # 验证统计数据一致性
        if total_categorized == total_processed:
            print(f"  ✅ 统计数据一致性验证通过")
        else:
            print(f"  ❌ 统计数据不一致！差值: {total_categorized - total_processed}")
            print(f"     可能存在重复计数或遗漏统计的情况")

        return poi_bindings

    except Exception as e:
        print(f"bind_poi_name函数执行错误: {e}")
        print(traceback.format_exc())
        return {}

def main():
    """
    主函数，处理命令行参数并执行相应的导出功能
    """
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='深圳克而瑞房产地铁项目商业化数据输出工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python shenzhen.py
  python shenzhen.py --blue-face-csv /path/to/blue_face.csv
  python shenzhen.py --bud-face-csv /path/to/bud_face.csv
  python shenzhen.py --blue-face-csv /path/to/blue_face.csv --bud-face-csv /path/to/bud_face.csv
        """
    )

    # 添加命令行参数
    parser.add_argument(
        '--blue-face-csv',
        type=str,
        default='/Users/<USER>/Downloads/blue_face.csv',
        help='blu_export函数输出的CSV文件路径 (默认: /Users/<USER>/Downloads/blue_face.csv)'
    )

    parser.add_argument(
        '--bud-face-csv',
        type=str,
        default='/Users/<USER>/Downloads/bud_face.csv',
        help='bud_export函数输出的CSV文件路径 (默认: /Users/<USER>/Downloads/bud_face.csv)'
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 显示配置信息
    print("=" * 60)
    print("深圳克而瑞房产地铁项目商业化数据输出")
    print("=" * 60)
    print(f"blu_export输出路径: {args.blue_face_csv}")
    print(f"bud_export输出路径: {args.bud_face_csv}")
    print("=" * 60)

    # 执行数据导出，传递命令行参数
    try:
        run(args.blue_face_csv, args.bud_face_csv)
        print("\n✅ 数据导出完成！")
    except Exception as e:
        print(f"\n❌ 数据导出失败: {e}")
        print(traceback.format_exc())
        return 1
    finally:
        if poi_cursor:
            poi_cursor.close()
        if poi_conn:
            poi_conn.close()
    return 0

if __name__ == '__main__':
    exit(main())