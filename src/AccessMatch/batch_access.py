# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""
import json
from multiprocessing import Pool
import time
import uuid
import os
import sys
import traceback

from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[2]
sys.path.insert(0, root_path.as_posix())

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from src.AccessMatch.pg_tool import PgTool
from src.common import common_tool
from batch_inin_access import AoiWithInnerLinkGateMatch
from batch_inout_access import AoiInOutGateMatch
from batch_virture_access import AoiVirtureGateMatch
from batch_outout_access import AoiOutOutGateMatch
from batch_ininout_access import AoiInInOutGateMatch
from access_improve_accurate import ImproveAccurateStrategy
import aoi_v1_protected
from src.AccessMatch.BatchCtx import BatchContext


def auto_strategy_match(num):
    """
    可以多进程跑策略, 只跑数据库中id对进程数量取余等于num的记录
    由于此函数含有数据库的游标,无法放在类中传给多进程调用
    """
    # 关联内外大门
    print(f"start auto_strategy_match {num} \n")
    with BatchContext() as ctx:
        with AoiInOutGateMatch(ctx) as obj1:
            obj1.run(num)

        # 关联内内大门
        with AoiWithInnerLinkGateMatch(ctx) as obj2:
            obj2.run(num)

        # 关联内内外大门
        with AoiInInOutGateMatch(ctx) as obj3:
            obj3.run(num)

        # 关联虚拟门
        with AoiVirtureGateMatch(ctx) as obj4:
            obj4.run(num)

        # 关联外外大门
        with AoiOutOutGateMatch(ctx) as obj5:
            obj5.run(num)

        # 策略提准
        with ImproveAccurateStrategy(ctx) as obj6:
            obj6.run(num)


class AoiGateMatch:
    """
    大门和AOI匹配策略, 策略整合入口
    """

    def __init__(self, ctx: BatchContext):
        """
        初始化连接数据库
        :param ctx: 字典:道路库的连接方式
        """
        self.batch_ctx = ctx

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def commit(self):
        """
        数据提交
        """
        self.batch_ctx.pg_tool.commit()

    def get_batch_node_by_bid(self, bid):
        """
        根据bid获取原有批处理产生的出入口成果
        """
        sql = """
        select
            distinct b.node_id
        from blu_access a
            inner join blu_access_gate_rel b
                on a.access_id = b.access_id
        where
            main_bid = %s
            and relation_source = 6"""
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        node_set = set()
        for v in res:
            node_set.add(v[0])

        sql = '''
        select
            node_id,
            action,
            resource
        from gate_bid_change_history
        where
            bid = %s
            and imp_state = 0
        order by
            id '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add" and v[2] == 6:
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def get_matched_batch_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的自动化上线成果
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'batch'
            and action = 'add'
            and status = 0 """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_matched_manu_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的需要人工核实的成果
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'manu-check'
            and action = 'add'
            and status = 0"""
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_matched_check_node_by_bid(self, bid):
        """
        根据bid获取本次批处理流程产出的疑似不应该关联的大门
        """
        sql = """
        select
            distinct node_id
        from gate_bid_match_tmp_res
        where
            bid = %s
            and process_type = 'check-necessary'
            and status = 0"""
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        node_set = set()
        for v in res:
            node_set.add(v[0])
        return node_set

    def get_manu_node_by_bid(self, bid):
        """
        根据bid获取原有人工作业产生的出入口成果
        """
        sql = """
        select
            distinct b.node_id
        from blu_access a
            inner join blu_access_gate_rel b
                on a.access_id = b.access_id
        where
            main_bid = %s
            and relation_source != 6"""
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        node_set = set()
        for v in res:
            node_set.add(v[0])

        sql = '''
        select
            node_id,
            action,
            resource
        from gate_bid_change_history
        where
            bid = %s
            and imp_state = 0
        order by
            id '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add" and v[2] != 6:
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def get_manu_achievement_by_bid(self, bid):
        """
        获取历史人工作业的成果
        """
        sql = '''
        select
            node_id, conclusion, create_time
        from gate_aoi_match_check_data
        where
            bid = %s
            and conclusion != ''
        order by
            id
        '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        node_res_dict = dict()
        for v in res:
            create_time = v[2]
            conclusion = v[1]
            # 对于N的结论,保留时间为360天
            if conclusion == 'N':
                if create_time > datetime.datetime.now() - datetime.timedelta(days=360):
                    node_res_dict[v[0]] = v[1]
            else:
                node_res_dict[v[0]] = v[1]
        black_list = set()
        write_list = set()
        for node_id, conclusion in node_res_dict.items():
            if conclusion == 'N' or conclusion == 'NOT MATCHED':
                black_list.add(node_id)
                write_list.discard(node_id)
            if conclusion == 'Y' or conclusion == 'MATCHED':
                write_list.add(node_id)
                black_list.discard(node_id)
        return black_list, write_list

    def auto_intel_imp(self, bid, node_id, action, src='', qb_id=-1):
        """
        自动化生成的情报入库
        """
        uid = str(uuid.uuid4()).replace('-', '')
        extra = json.dumps({"qb_id": qb_id})
        # 策略删除的暂时不自动删除,在人工核实流程删, 一些失效或者范围变更的可以删
        if action != 'delete' or '失效' in src or 'AOI的范围框变更' in src or 'del' in src:
            sql = """
                insert into
                    gate_bid_change_history
                        (node_id, bid, action, flow_state, src, resource, uid, extra)
                    values
                        (%s, %s, %s, '待入库', %s, 6, %s, %s)
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, action, src, uid, extra])

        sql = f"""
            insert into gate_match_sync_integration
                (uid, main_bid, node_id, is_manual, action, extra)
            values
                ('{uid}', '{bid}', '{node_id}', 0, 'add', '{extra}')
        """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def process_add_node(self, add_node_set, bid, qb_id):
        """
        新增的关联关系入库
        """
        for node_id in add_node_set:
            self.auto_intel_imp(bid, node_id, 'add', '关联关系例行批处理', qb_id)

    def process_delete_node(self, delete_node_set, bid, qb_id, src):
        """
        需要删除的关联关系入库
        """
        sql = f"""
            select
                count(*)
            from
                blu_face a
                inner join
                blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
                and aoi_level != 3
                and kind != '52'
        """
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql)
        num_aoi = self.batch_ctx.pg_tool.cursor_aoi.fetchone()[0]
        for node_id in delete_node_set:
            # 先删除数据，后下发人工
            self.auto_intel_imp(bid, node_id, 'delete', f'关联关系例行批处理-{src}', qb_id)
            # 如果AOI不存在或者是内部院落，直接删除无须人工作业
            if num_aoi > 0:
                sql = """
                    insert into
                        gate_aoi_match_check_data_tmp(
                            node_id,
                            bid,
                            source,
                            ways,
                            qb_id
                        )
                        values(
                            %s,
                            %s,
                            '关联关系例行批处理-删除后下发人工',
                            '提召',
                            %s
                        )
                """
                self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def process_manu_node(self, node_manu, bid, qb_id=-1):
        """
        人工作业的关联关系入库
        """
        for node_id in node_manu:
            # 此部分数据暂时不下发，所以先临时入gate_aoi_match_check_data_tmp
            # 新流程验收完成可以例行下发时再做处理
            sql = """
                insert into
                    gate_aoi_match_check_data_tmp(
                        node_id,
                        bid,
                        source,
                        ways,
                        qb_id
                    )
                    values(
                        %s,
                        %s,
                        '关联关系例行批处理',
                        '提召',
                        %s
                    )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def improve_accurate_node(self, node_set, bid, qb_id=-1):
        """
        疑似错误的关联关系入库
        """
        for node_id in node_set:
            # 此部分数据暂时不下发，所以先临时入gate_aoi_match_check_data_tmp
            # 新流程验收完成可以例行下发时再做处理
            sql = """
                insert into
                    gate_aoi_match_check_data_tmp(
                        node_id,
                        bid,
                        source,
                        ways,
                        qb_id
                    )
                    values(
                        %s,
                        %s,
                        '关联关系例行批处理',
                        '清查提准',
                        %s
                    )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, qb_id])

    def update_intel_completed(self):
        """
        当前情报标记完成
        """
        sql = '''
            update
                gate_match_intel_src
            set
                status = 2
            where
                bid != ''
                and status = 1
                and fail_reason = ''
        '''
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def update_intel_processing(self, id):
        """
        当前情报标记完成
        """
        sql = '''
            update
                gate_match_intel_src
            set
                status = 1
            where
                id = %s'''
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [id])

    def update_manu_achievement_state(self):
        """
        当前bid已完成,标记产出结果已处理
        """
        sql = """
        update
            gate_bid_match_tmp_res
        set
            status = 1
        where
            status = 0 """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def deleted_node_imp(self, delete_node, bid, face_id):
        """
        自动化删除的大门，需要下发人工作业
        """
        for node_id in delete_node:
            sql = """
            insert into
                gate_bid_match_tmp_res(
                    bid,
                    face_id,
                    node_id,
                    action,
                    src,
                    process_type
                )
                values(
                    %s,
                    %s,
                    %s,
                    'delete',
                    '例行更新流程删除数据下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid, face_id, node_id])

    def gate_lacked_imp(self, high_pv_gate_lack, bid, face_id):
        """
        自动化删除的大门，需要下发人工作业
        """
        for node_id in high_pv_gate_lack:
            sql = """
            insert into
                gate_bid_match_tmp_res (
                    bid,
                    face_id,
                    node_id,
                    action,
                    src,
                    process_type
                ) values(
                    %s,
                    %s,
                    %s,
                    'add',
                    '例行更新流程精准AOI判断策略下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, [bid, face_id, node_id])

    def state_rollback(self):
        sql = """
            update
                gate_match_intel_src
            set
                status = 0
            where
                status = 1
        """

        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        self.batch_ctx.pg_tool.conn_poi_write.commit()

    def bid_exist_aoi(self, bid):
        """
        judge bid exists aoi or not
        """
        sql = f"""
            select
                count(*)
            from
                blu_face a
                inner join
                blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
                and aoi_level = 2
        """
        self.batch_ctx.pg_tool.cursor_aoi.execute(sql)
        return self.batch_ctx.pg_tool.cursor_aoi.fetchone()[0] > 0

    def post_process(self):
        """
        人工作业的成果的处理：
            如果情报是因为AOI的范围框变动导致重新匹配，不直接删除，但需要重新下发作业
            如果是其他情报引起的，人工作业成果直接继承；
        批处理成果的处理：
            需要新增的部分过滤掉黑名单，直接上线
            需要删除的部分，过滤掉白名单后直接上线，并下发人工核实（是否要过滤白名单待评估，后续优化）；
            注意白名单只能放在减号的右侧
        """

        protected_main_bid = aoi_v1_protected.load_protected_main_bids(self.batch_ctx.pg_tool)

        # 从gate_match_intel_src表中查询本次处理了哪些bid
        sql = """
            select
                face_id,
                bid,
                src,
                qb_id
            from gate_match_intel_src
            where
                bid != ''
                and status = 1
                and fail_reason = ''
                order by id
        """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        res = self.batch_ctx.pg_tool.cursor_poi_write.fetchall()
        self.batch_ctx.pg_tool.conn_poi_write.commit()
        for v in res:
            face_id = v[0]
            bid = v[1]
            if bid in protected_main_bid:
                continue
            src = v[2]
            qb_id = v[3]
            # 现有库里自动关联的出入口
            node_batch_origin = self.get_batch_node_by_bid(bid)
            print("node_batch_origin:" + str(node_batch_origin))

            # 现有库里人工作业的出入口
            node_manu_origin = self.get_manu_node_by_bid(bid)
            print("node_manu_origin:" + str(node_manu_origin))

            # 策略自动批处理产出的出入口数据
            node_batch_current = self.get_matched_batch_node_by_bid(bid)
            print("node_batch_current:" + str(node_batch_current))

            # 策略产出需要人工核实的出入口
            node_manu_current = self.get_matched_manu_node_by_bid(bid)

            # 策略产出疑似不应关联的大门
            node_check_current = self.get_matched_check_node_by_bid(bid)
            print("node_check_current:" + str(node_check_current))

            # 关联关系的黑名单、白名单（暂不考虑人工作业的成果是否会失效，可优化，
            # 但需考虑同一个关联关系下发多次结论不同时的处理）
            node_blacklist, node_whitelist = self.get_manu_achievement_by_bid(bid)
            print("node_blacklist:" + str(node_blacklist))
            print("node_whitelist:" + str(node_whitelist))

            # 策略产出的下发人工作业的出入口，需要去掉批处理关联的部分
            node_manu_current = node_manu_current - node_batch_current
            print("node_manu_current:" + str(node_manu_current))

            # 之前批处理能关联，现在不能关联的部分直接删除，需要去掉白名单
            delete_node = node_batch_origin - node_batch_current - node_whitelist
            print("delete_node:" + str(delete_node))

            # 如果AOI的框变了，库里原有的人工作业的关联关系需要重新下发人工确认，否则只下发策略召回的部分
            if src == "AOI的范围框变更":
                # add_node是自动批处理的大门，可以直接入库，需要去掉库里已经存在的批处理生成的出入口，以及黑名单
                # 这里由于库里人工作业成果会被删除，所以不会减去node_manu_origin
                add_node = node_batch_current - node_batch_origin - node_blacklist

                # 注意node_manu集合取交集或并集的顺序不能变
                # 由于库里可能存在人工作业产生但被标记为批处理产生的数据，所以只要删除的数据都要再下发人工作业
                node_manu = ((node_manu_current | node_manu_origin) - node_batch_current) | delete_node
            elif src == "aoi-bid-del":
                add_node = node_batch_current
                delete_node = node_batch_origin | node_manu_origin - add_node
                node_manu = node_manu_current
            else:
                # 人工作业的成果保留，所以无需再增加
                add_node = node_batch_current - node_batch_origin - node_manu_origin - node_blacklist
                # node_manu = (node_manu_current - node_manu_origin) | delete_node
                node_manu = node_manu_current - node_manu_origin

            # 一定能关联上的node列表，用来判断缺失哪些高通量大门，这里为了保证能转化成精准AOI，
            # 将人工作业的大门都当做了不需要关联的部分，增加了一部分人工作业的成本，
            # 后续优化后可以取消，将node_manu改为delete_node即可
            current_achievement = (node_batch_origin | node_manu_origin | add_node) - node_manu - delete_node
            node_check_current = ((node_batch_origin | node_manu_origin | add_node) - delete_node
                                  - node_blacklist - node_whitelist).intersection(node_check_current)

            # 获取AOI缺门的信息，目前没有返回值来区分是否能转化成精准AOI，待后续优化
            # 这个计算太复杂了,应该在aoi层级计算输出,需要移动走
            # _, high_pv_gate_lack = accurate_aoi_info.get_aoi_level_by_bid_and_node(
            #     bid, current_achievement - node_check_current)

            # 精准AOI的判断逻辑产生的输出，也会下发人工作业
            # node_manu = node_manu | set(high_pv_gate_lack)
            # delete_node = delete_node - node_manu

            self.process_add_node(add_node, bid, qb_id)
            print("add_node:" + str(add_node))

            # 直接批处理删除,之后下发人工核实
            self.process_delete_node(delete_node, bid, qb_id, src)

            self.process_manu_node(node_manu, bid, qb_id)
            print("node_manu:" + str(node_manu))
            self.deleted_node_imp(delete_node, bid, face_id)
            # self.gate_lacked_imp(high_pv_gate_lack, bid, face_id)
            self.improve_accurate_node(node_check_current, bid, qb_id)
            self.commit()

        self.update_intel_completed()
        self.update_manu_achievement_state()
        self.commit()

    def pre_process(self):
        """
        处理失败会导致gate_bid_match_tmp_res中有status中为0的部分，需要删除以免影响本次匹配
        """
        sql = '''
            delete
            from
                gate_bid_match_tmp_res
            where
                status = 0'''
        batch_size = 20
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

        # 本次重跑，先确定是否是上次没跑完的，没跑完的继续跑，跑完了再执行下一批
        sql = '''
            select count(*) from gate_match_intel_src where status = 1 and bid != '' 
        '''
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        if self.batch_ctx.pg_tool.cursor_poi_write.fetchone()[0] < batch_size:
            # 下面的代码重度依赖 status = 1 这个值，并且必须一批全跑完，数据量太大很容易跑挂，这里限制下status = 1 的数量
            # 暂时一批设置为5000， 能保证快速跑完，一天可以多跑几批 （暂时定4小时一批，可以基本保证跑完 ）
            # pgsql 没有 update .. limit 查询方式，先通过子查询查询查询出前5000条的最大的id， 再根据max_id 进行更新再进行更新
            sql = f'''
            with tmp as (
                  select id from gate_match_intel_src
                   where bid != '' and status in (0, 10) order by id asc limit {batch_size})
            select max(tmp.id) from tmp
            '''
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
            max_id_data = self.batch_ctx.pg_tool.cursor_poi_write.fetchone()[0]
            if max_id_data and int(max_id_data) > 0:
                print(f"更新任务，max_id: {max_id_data}")
                sql = '''
                    update gate_match_intel_src
                     set status = 1
                    where bid != '' and status in (0, 10) and id <= %s
                '''
                self.batch_ctx.pg_tool.cursor_poi_write.execute(sql, (int(max_id_data),))
            else:
                print("没有需要执行的任务")
        self.batch_ctx.pg_tool.conn_poi_write.commit()

    def imp_poi_strategy_result(self):
        """
        poi策略产出结果导入
        """

        # poi表名
        table_name = self.batch_ctx.pg_tool.env + "_poi_strategy_relate_result"

        sql = f"""
            select
                node_id,
                face_id,
                bid
            from {table_name}
            where
                is_target = 1
            """
        self.batch_ctx.pg_tool.cursor_trajectory.execute(sql)
        res = self.batch_ctx.pg_tool.cursor_trajectory.fetchall()
        for v in res:
            node_id = v[0]
            face_id = v[1]
            bid = v[2]

            sql = f"""
                insert into
                    gate_bid_match_tmp_res (
                        node_id,
                        bid,
                        face_id,
                        action,
                        status,
                        src,
                        process_type
                    )
                values (
                    '{node_id}',
                    '{bid}',
                    '{face_id}',
                    'add',
                    0,
                    '例行更新流程poi大门策略下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def imp_ydd_strategy_result(self):
        """
        poi策略产出结果导入
        """

        # poi表名
        table_name = self.batch_ctx.pg_tool.env + "_yd_strategy_relate_result"

        sql = f"""
            select
                node_id,
                face_id,
                bid
            from {table_name}
            where
                is_target = 1
            """
        self.batch_ctx.pg_tool.cursor_trajectory.execute(sql)
        res = self.batch_ctx.pg_tool.cursor_trajectory.fetchall()
        for v in res:
            node_id = v[0]
            face_id = v[1]
            bid = v[2]

            sql = f"""
                insert into
                    gate_bid_match_tmp_res (
                        node_id,
                        bid,
                        face_id,
                        action,
                        status,
                        src,
                        process_type
                    )
                values (
                    '{node_id}',
                    '{bid}',
                    '{face_id}',
                    'add',
                    0,
                    '例行更新流程引导点策略下发人工核实',
                    'manu-check'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

    def exec_poi_ydd_strategy(self):
        """
        执行引导点和poi策略
        """

        # 执行策略
        cmds = """
            source /home/<USER>/.bashrc;
            cd %s/../trajectory_relate/;
            sh run_strategy.sh %s
            """ % (os.path.dirname(os.path.abspath(__file__)),
                   self.batch_ctx.pg_tool.env)

        code, out_list = common_tool.exec_shell_cmd(cmds)
        if code != 0:
            raise Exception('poi和引导点策略处理失败, 返回信息{}'.format(str(out_list)))
        print(code)
        print(out_list)
        # 获取poi大门策略产出结果
        self.imp_poi_strategy_result()

        # 获取引导点策略产出结果
        self.imp_ydd_strategy_result()

    def copy_change_info(self):
        date = time.strftime('%Y%m%d', time.localtime(time.time()))
        gate_table_name = f"gate_trigger_{date}_{self.batch_ctx.pg_tool.env}"
        aoi_table_name = f"aoi_trigger_{date}_{self.batch_ctx.pg_tool.env}"
        gate_sql = f"""
            select
                to_regclass('{gate_table_name}')
        """
        aoi_sql = f"""
            select
                to_regclass('{aoi_table_name}')
        """
        retry = 0
        while (retry < 50):
            self.batch_ctx.pg_tool.cursor_trajectory.execute(gate_sql)
            res_gate = self.batch_ctx.pg_tool.cursor_trajectory.fetchone()
            self.batch_ctx.pg_tool.cursor_trajectory.execute(aoi_sql)
            res_aoi = self.batch_ctx.pg_tool.cursor_trajectory.fetchone()
            if res_gate[0] == "" or res_aoi[0] == "":
                retry = retry + 1
                time.sleep(300)
                continue
            break

        if retry >= 50:
            raise Exception('get bid change info failed')

        sql = f"""
            select
                bid,
                node_id,
                face_id,
                type
            from
                {gate_table_name}
            where
                bid != ''
        """
        self.batch_ctx.pg_tool.cursor_trajectory.execute(sql)
        data = self.batch_ctx.pg_tool.cursor_trajectory.fetchall()
        for v in data:
            bid = v[0]
            node_id = v[1]
            face_id = v[2]
            type = v[3]
            sql = f"""
                insert into
                    gate_match_intel_src(
                        bid,
                        face_id,
                        node_id,
                        src
                    )
                values(
                    '{bid}',
                    '{face_id}',
                    '{node_id}',
                    'node-{type}'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)

        sql = f"""
            select
                bid,
                face_id,
                type
            from
                {aoi_table_name}
            where
                bid != ''
        """
        self.batch_ctx.pg_tool.cursor_trajectory.execute(sql)
        data = self.batch_ctx.pg_tool.cursor_trajectory.fetchall()
        for v in data:
            bid = v[0]
            face_id = v[1]
            type = v[2]
            sql = f"""
                insert into
                    gate_match_intel_src(
                        bid,
                        face_id,
                        src
                    )
                values(
                    '{bid}',
                    '{face_id}',
                    'aoi-{type}'
                )
            """
            self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        self.batch_ctx.pg_tool.conn_poi_write.commit()

    def get_matched_bid_by_node(self, node_id):
        """
        获取该大门关联的所有bid，包括已入库和未入库的部分
        """

        sql = """
            select
                distinct main_bid
            from
                blu_access a
                inner join
                blu_access_gate_rel b
            on
                a.access_id = b.access_id
            where
                b.node_id = %s
        """

        self.batch_ctx.pg_tool.cursor_aoi.execute(sql, [node_id])
        res = self.batch_ctx.pg_tool.cursor_aoi.fetchall()
        self.batch_ctx.pg_tool.conn_aoi.commit()
        bid_set = set()
        for v in res:
            bid_set.add(v[0])

        sql = '''
            select
                bid,
                action
            from gate_bid_change_history
            where
                node_id = %s
                and imp_state = 0
            order by
                id
        '''
        self.batch_ctx.pg_tool.cursor_poi.execute(sql, [node_id])
        res = self.batch_ctx.pg_tool.cursor_poi.fetchall()
        self.batch_ctx.pg_tool.conn_poi.commit()
        for v in res:
            if v[1] == "add":
                bid_set.add(v[0])
            if v[1] == "delete":
                bid_set.discard(v[0])
        return bid_set

    def process_invalid_gate(self):
        """
        处理失效的大门数据，删除对应的关联关系
        """
        sql = """
            select
                distinct node_id, qb_id
            from gate_match_intel_src
            where
                status = 1
                and src = 'node-del'
        """
        self.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
        for node_id, qb_id in self.batch_ctx.pg_tool.cursor_poi_write.fetchall():
            bid_set = self.get_matched_bid_by_node(node_id)
            for bid in bid_set:
                self.auto_intel_imp(bid, node_id, 'delete', '关联关系例行批处理-大门失效', qb_id)

        self.batch_ctx.pg_tool.conn_poi_write.commit()

    def run(self):
        """
        策略入口
        """

        # 获取道路和AOI的变更信息
        # self.copy_change_info()

        # 预处理
        self.pre_process()

        # 首先处理失效的大门
        self.process_invalid_gate()

        # 多进程跑策略匹配
        with Pool(5) as p:
            p.map(auto_strategy_match, [i for i in range(10)])

        # poi和引导点策略匹配
        # self.exec_poi_ydd_strategy()

        # 后处理
        # self.post_process()
        # print("match completed")


def run():
    """
        程序入口
        """
    print("出入口例行匹配开始:" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    success = False
    with BatchContext() as batch_ctx:
        with AoiGateMatch(batch_ctx) as aoi_gate_match:
            try:
                print(time.strftime('%Y-%m-%d', time.localtime(time.time())))
                aoi_gate_match.run()
                success = True
            except Exception as e:
                print(e)
                # 报警
                # common_tool.send_hi("Access-Batch-Daily-Failed-de16-xxl-job-167", ['chenbaojun_cd'])
                # aoi_gate_match.state_rollback()
                traceback.print_exc()

        # 这里数据库需要重连,重新处理
        if success:
            # 后处理操作
            with AoiGateMatch(batch_ctx) as aoi_gate_match:
                aoi_gate_match.batch_ctx.pg_reconnect()
                aoi_gate_match.post_process()
                print("match completed")
                sql = '''
                                update
                                    gate_match_intel_src
                                set
                                    status = 4
                                where
                                    bid != ''
                                    and status = 1
                            '''
                aoi_gate_match.batch_ctx.pg_tool.cursor_poi_write.execute(sql)
                aoi_gate_match.batch_ctx.pg_tool.conn_poi_write.commit()
        else:
            print("match failed")
            with AoiGateMatch(batch_ctx) as aoi_gate_match:
                aoi_gate_match.batch_ctx.pg_reconnect()
                aoi_gate_match.state_rollback()
                print("match rollbacked")


if __name__ == "__main__":
    import datetime

    _now = datetime.datetime.now()
    while datetime.datetime.now() - _now < datetime.timedelta(minutes=5):
        run()
        time.sleep(3)
