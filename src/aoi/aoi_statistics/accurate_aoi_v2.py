# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准AOI2.0统计监控
"""
import copy

import datetime
import json
import os
import sys
from pathlib import Path

root_dir = Path(os.path.abspath(__file__)).parents[4]
sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.insert(0, root_dir.as_posix())
sys.path.append("../..")
sys.path.append("..")
from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext
from src.common import common_tool

# 精准AOI
AOI_COMPLETE_ACCURATE = 3
# 精准AOI2.0
AOI_COMPLETE_ACCURATE_V2 = 4
# 语义化人工作业来源
BLU_ACCESS_SRC_MANUAL = 2
BLU_ACCESS_SRC_STRATEGY = 7

SEND_MEG_TOKEN = "d0394428b72d096f84ccb56fc1269345c"
GROUP_ID = 7749482


# SEND_MEG_TOKEN = "d0fc9b10e8f2744f789e8408d0ee67be0"
# GROUP_ID = 8131246


def accurate_aoi_v2(ctx: AoiCompleteContext):
    """
    精准AOI2.0统计监控
    :return:
    """
    ctx.reconnect()
    current_day_time = datetime.datetime.now().strftime("%Y-%m-%d")
    accurate_aoi_data = {"v1_add_num": 0, "v2_add_num": 0, "v1_to_v2_rate": 0, "v2_to_normal_num": 0,
                         "v2_add_manual_num": 0, "v2_add_strategy_num": 0, "v2_discount_num": 0, "expire_poi_num": 0,
                         "date_day": current_day_time, "category": "ACCURATE_AOI_V2"}
    # 新增精准AOI统计
    accurate_aoi_diff = ctx.pg_query_dao.get_diff_accurate_aoi_v2()
    print(f"""新增：{len(accurate_aoi_diff["diff_add"])}, 减少：{len(accurate_aoi_diff["diff_discount"])}""")
    if accurate_aoi_diff["diff_add"] is not None and len(accurate_aoi_diff["diff_add"]) > 0:
        for accurate_aoi in accurate_aoi_diff["diff_add"]:
            if accurate_aoi[1] == AOI_COMPLETE_ACCURATE:
                accurate_aoi_data["v1_add_num"] += 1
            else:
                accurate_aoi_data["v2_add_num"] += 1
                # 查询精准AOI2.0新增人工、策略数量
                blu_access_list = ctx.pg_query_dao.get_blu_access_by_bids([accurate_aoi[0]])
                if blu_access_list is None or len(blu_access_list) == 0:
                    continue
                manual_work_num = len(
                    [blu_access[0] for blu_access in blu_access_list if blu_access[1] == BLU_ACCESS_SRC_MANUAL])
                if manual_work_num > 0:
                    accurate_aoi_data["v2_add_manual_num"] += 1
        accurate_aoi_data["v2_add_strategy_num"] = accurate_aoi_data["v2_add_num"] - accurate_aoi_data[
            "v2_add_manual_num"]
    # 下线量级统计
    v2_discount_bids = []
    if accurate_aoi_diff["diff_discount"] is not None and len(accurate_aoi_diff["diff_discount"]) > 0:
        v2_discount_bids = [item[0] for item in accurate_aoi_diff["diff_discount"] if
                            item[1] == AOI_COMPLETE_ACCURATE_V2]
        accurate_aoi_data["v2_discount_num"] = len(v2_discount_bids)

    # 查询每日失效量级
    expire_poi_list = ctx.pg_query_dao.get_expire_poi_by_date(current_day_time)
    if expire_poi_list is not None and len(expire_poi_list) > 0:
        expire_bids = [item[0] for item in expire_poi_list]
        accurate_aoi_data["expire_poi_num"] = len(set(v2_discount_bids).intersection(set(expire_bids)))

    accurate_aoi_data['v2_to_normal_num'] = accurate_aoi_data["v2_discount_num"] - accurate_aoi_data["expire_poi_num"]
    ctx.pg_rw_dao.insert_accurate_aoi_v2_statistics(accurate_aoi_data)
    # 查询历史变更为非精准总量及
    history_accurate_aoi = ctx.pg_query_dao.get_accurate_v2_offline_total()

    # 发送msg
    msg = f"精准2.0统计：{current_day_time}\n" \
          f"新增量级：{accurate_aoi_data['v2_add_num']}\n" \
          f"人工作业新增量级：{accurate_aoi_data['v2_add_manual_num']}\n" \
          f"策略产出新增量级：{accurate_aoi_data['v2_add_strategy_num']}\n" \
          f"下线总量级：{accurate_aoi_data['v2_discount_num']}\n" \
          f"失效量级：{accurate_aoi_data['expire_poi_num']}\n" \
          f"变更为非精准量级(下线总量级-失效量级)：{accurate_aoi_data['v2_to_normal_num']}\n" \
          f"变化量级：{accurate_aoi_data['v2_add_num'] - accurate_aoi_data['v2_discount_num']}\n" \
          f"历史变更为非精准总量及：{history_accurate_aoi[0] + accurate_aoi_data['v2_to_normal_num']}"
    common_tool.send_hi(msg, [], SEND_MEG_TOKEN, GROUP_ID)


if __name__ == '__main__':
    with AoiCompleteContext() as ctx:
        accurate_aoi_v2(ctx)
