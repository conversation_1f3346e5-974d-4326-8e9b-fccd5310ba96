# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P安环业务数据导出商单数据输出
"""
import os
import sys

import psycopg2
import tqdm as tqdm
from shapely import wkt
import traceback
import numpy as np

backConf = {"db": "master_back",
            "user": "master_back", "pwd": "master_back",
            "host": "*************", "port": 8033}

# poi正式
poiConf = {"db": "poi_online",
           "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
           "host": "*************", "port": 8032}

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()

# 连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                             user=poiConf["user"],
                             password=poiConf["pwd"],
                             host=poiConf["host"],
                             port=poiConf["port"])
poiDao = poiDbConn.cursor()

buConf = {"db": "business_order", "user": "business_order_se_rw", "pwd": "kpigzear",
          "host": "**************",
          "port": 7432}
buDbConn = psycopg2.connect(database=buConf["db"],
                            user=buConf["user"],
                            password=buConf["pwd"],
                            host=buConf["host"],
                            port=buConf["port"])
pdd_sd_dao = buDbConn.cursor()

not_relate_keywords = ['电子眼', '公交线路', '标注', '出入口', '自然地物', '门址', '道路', '铁路', '行政界线', '其他线要素', '行政区划', '水系', '绿地', '停车场']
suffix_keyworkds_1 = ['栋', '号楼', '号', '幢', '舍', '楼', '座', '大厦']
suffix_keyworkds_2 = ['馆', '宫', '殿', '门', '阁', '斋', '堂', '亭', '台', '居']
suffix_keyworkds_3 = ['公司', '集团', '厂']


def run(struct_ids):
    # 获取所有未匹配上POI的建筑物
    struct_str = "','".join(struct_ids.tolist())
    sql = f"""
        select struct_id,st_astext(geom) from sd_bud_struct where match_poi_bid='' and struct_id in('{struct_str}') order by random()
    """
    pdd_sd_dao.execute(sql)
    bud_structs = pdd_sd_dao.fetchall()
    near_no_poi = 0
    near_no_poi_by_no_keyword = 0
    near_no_poi_by_filter_poi_bid = 0
    bind_max_pv = 0
    match_num = 0
    for bud_struct in tqdm.tqdm(bud_structs):
        try:
            struct_id, geom = bud_struct[0], bud_struct[1]
            if not struct_id or not geom:
                continue
            geom_sp = wkt.loads(geom)
            geom_buffer_5m_sp = geom_sp.buffer(0.00010)
            # 查询建筑物附近POI
            sql = f"""
                select bid,name,std_tag,address,click_pv from poi where st_contains(st_geomfromtext('{geom_buffer_5m_sp.wkt}',4326),geometry)
            """
            poiDao.execute(sql)
            pois = poiDao.fetchall()
            if len(pois) == 0:
                near_no_poi += 1
                continue
            # 排除特殊关联字,不可匹配
            valid_pois = []
            for poi in pois:
                bid, name, std_tag, address, click_pv = poi[0], poi[1], poi[2], poi[3], poi[4]
                for keywords in not_relate_keywords:
                    if keywords in name or keywords in address:
                        near_no_poi_by_no_keyword += 1
                        continue
                    valid_pois.append(poi)
            if len(valid_pois) == 0:
                continue
            # 去掉AOI主点的POI
            bids = [item[0] for item in valid_pois]
            bid_str = "','".join(bids)
            sql = f"""
                select poi_bid from blu_face_poi where poi_bid in('{bid_str}')
            """
            backgroundDao.execute(sql)
            main_poi_list = backgroundDao.fetchall()
            if main_poi_list:
                main_poi_bids = [item[0] for item in main_poi_list]
                valid_pois = [item for item in valid_pois if item[0] not in main_poi_bids]
            if len(valid_pois) == 0:
                near_no_poi_by_filter_poi_bid += 1
                continue
            # 按照关键字后缀关联匹配
            keywords_1_pois = []
            keywords_2_pois = []
            keywords_3_pois = []
            for poi in valid_pois:
                bid, name, std_tag, address, click_pv = poi[0], poi[1], poi[2], poi[3], poi[4]
                if any([poi for keywords in suffix_keyworkds_1 if name.endswith(keywords)]):
                    keywords_1_pois.append(poi)
                    continue
                if any([poi for keywords in suffix_keyworkds_2 if name.endswith(keywords)]):
                    keywords_2_pois.append(poi)
                    continue
                if any([poi for keywords in suffix_keyworkds_3 if name.endswith(keywords)]):
                    keywords_3_pois.append(poi)
                    continue
            # 挑选pv最高的绑定
            bind_poi = None
            if keywords_1_pois:
                bind_poi = max(keywords_1_pois, key=lambda x: x[-1])
            if not bind_poi and keywords_2_pois:
                bind_poi = max(keywords_2_pois, key=lambda x: x[-1])
            if not bind_poi and keywords_3_pois:
                bind_poi = max(keywords_3_pois, key=lambda x: x[-1])
            if not bind_poi:
                bind_poi = max(valid_pois, key=lambda x: x[-1])
                bind_max_pv += 1
            # 开始绑定建筑物信息
            bid, name, std_tag, address, click_pv = bind_poi[0], bind_poi[1], bind_poi[2], bind_poi[3], bind_poi[4]
            print(f"建筑物匹配POIbid:{struct_id}:{bid}")
            name = name.replace("'", "\'")
            address = address.replace("'", "\'")
            match_num += 1
        except Exception as e:
            # 连接背景母库
            print(traceback.format_exc())
    print(
        f"附近没有POI数量:{near_no_poi},关键词过滤:{near_no_poi_by_no_keyword},AOI主点过滤:{near_no_poi_by_filter_poi_bid},绑定最高pv:{bind_max_pv},绑定成功:{match_num} ")





def match_bud_address(struct_ids):
    """
    过滤跟AOI穿过的建筑物
    Returns:

    """
    struct_str = "','".join(struct_ids.tolist())
    sql = f"""
        select struct_id,st_astext(geom) from bud_face where struct_id in('{struct_str}')
    """
    backgroundDao.execute(sql)
    bud_structs = backgroundDao.fetchall()
    num = 0
    for bud_struct in tqdm.tqdm(bud_structs):
        struct_id, geom = bud_struct[0], bud_struct[1]
        # 判读落在的五级行政区划
        sql = f"""
            select pro_name,city_name,county_name,town_name,village_name from bad_admin_vil_gcj where st_intersects(st_geomfromtext('{geom}',4326),geom)
        """
        pdd_sd_dao.execute(sql)
        res = pdd_sd_dao.fetchone()
        address = res[0] + res[1] + res[2] + res[3] + res[4]
        sql = f"""
            update sd_bud_tj set address='{address}' where struct_id='{struct_id}'
        """
        pdd_sd_dao.execute(sql)
        buDbConn.commit()
        # print(f"建筑物匹配的地址:struct_id:{struct_id},address:{address},geom:{geom}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    run(np.array(bid_list))
    # match_bud_address(np.array(bid_list))