# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid

import requests as requests
import tqdm
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from sklearn.cluster import DBSCAN
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.common import pipeline
from src.aoi.park_access import context
from src.aoi.park_access import park_utils
from src.aoi import utils
from src.aoi.park_access import park_access_qc as qc

COORDINATE_FACTOR = 100000
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

BUILD_GATE_CATEGORY = ['医疗;综合医院', '医疗;专科医院', '教育培训;高等院校', '房地产;住宅区', '房地产;其他', '房地产', '公司企业;公司', '公司企业;园区', '公司企业',
                       '公司企业;厂矿', '公司企业;农林园艺', '教育培训;中学', '教育培训;小学', '教育培训;幼儿园', '教育培训;培训机构', '教育培训;其他', '教育培训;科研机构',
                       '教育培训;科技馆', '教育培训;成人教育', '教育培训;特殊教育学校', '教育培训', '政府机构;各级政府', '政府机构;行政单位', '政府机构;公检法机构',
                       '政府机构;政治教育机构', '政府机构;中央机构', '政府机构;涉外机构', '汽车服务;汽车销售', '汽车服务;汽车配件', '汽车服务;汽车维修', '汽车服务;汽车检测场',
                       '文化传媒;广播电视', '绿地;高尔夫球场', '医疗;疗养院', '医疗;其他', '医疗', '生活服务;物流公司']
NOT_BUILD_CATEGORY = ['交通设施;加油加气站', '交通设施;飞机场', '交通设施;火车站', '购物;购物中心']
AREA_FIRST_CATEGORY = ['交通设施;飞机场', '交通设施;火车站', '旅游景点;风景区', '旅游景点;公园', '旅游景点;人文景观', '购物;购物中心', '医疗;专科医院', '医疗;综合医院']
IS_TEST = True


def create_intelligence(ctx: context.Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        cal_end_traj_feature,
        park_accept_traj_feature,
        navigate_traj_feature,
        competitor_feature,
        end_traj_and_competitor_and_aoi_access,
        park_accept_traj_and_competitor_and_aoi_access,
        navigate_traj_and_competitor_and_aoi_access,
        competitor_and_top_traj,
        aoi_access_and_top_traj,
        park_access_filter,
        save_intelligence_to_db,
    )
    pipe_list(ctx)


def cal_end_traj_feature(ctx: context.Context, proceed):
    """
    轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info("begin traj area intersects")
    # 取轨迹与AOI边框交割点
    ints_p_wkt_arr = park_utils.calcul_intersect_points_by_out_traj(ctx.aoi_info.wkt,
                                                                    ctx.traj.filter_aoi_dest_traj_list)
    if not ints_p_wkt_arr:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in ints_p_wkt_arr]
    if IS_TEST == '0':
        logger.info(f"终点与边框交割点: {ints_p_wkt_arr}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, db.labels_):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(ints_p_wkt_arr, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # logger.info(f"交割点聚类成果: {sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)}")
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"终点与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 15 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.end_traj_features = cluster_points_wkt_list
    proceed()


def park_accept_traj_feature(ctx: context.Context, proceed):
    """
    采纳轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin park_accept_traj_area_intersects")
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    traj_time = (datetime.datetime.now() + datetime.timedelta(days=-30)).strftime("%Y-%m-%d")
    park_accept_traj_list = ctx.dao.traj_db.get_park_accept_traj(geom=aoi_sp.wkt, accept_time=traj_time)
    if not park_accept_traj_list:
        logger.info(f"没有采纳轨迹")
        return proceed()
    traj_list = [wkt.loads(item['arrived_traj']) for item in park_accept_traj_list]
    traj_list = park_utils.traj_intercept(traj_list, ctx.aoi_info.wkt)
    traj_list = park_utils.filter_noise_trace_by_angle_and_intersects(traj_list)
    if not traj_list:
        return proceed()
    intersect_point_list = park_utils.calcul_intersect_points_by_out_traj(aoi_sp.buffer(-5 / COORDINATE_FACTOR).wkt,
                                                                          traj_list, area_buffer_distance=-10)
    if not intersect_point_list:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    if IS_TEST == '0':
        logger.info(f"采纳轨迹与边框交割点: {intersect_point_list}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"采纳轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.park_accept_features = cluster_points_wkt_list
    proceed()


def navigate_traj_feature(ctx: context.Context, proceed):
    """
    导航轨迹与AOI边界相交点聚类策略
    Args:
        ctx:
        proceed:
    """
    logger.info(f"begin park_accept_traj_area_intersects")
    aoi_sp = wkt.loads(ctx.aoi_info.wkt)
    traj_list = ctx.dao.poi_online_query.get_navigate_traj(bids=[ctx.parking_info.bid])
    if not traj_list:
        logger.info(f"没有导航轨迹")
        return proceed()
    traj_list = [wkt.loads(item['geom']) for item in traj_list]
    intersect_point_list = park_utils.calcul_intersect_points_by_out_traj(aoi_sp.buffer(-5 / COORDINATE_FACTOR).wkt,
                                                                          traj_list, area_buffer_distance=-10)
    if not intersect_point_list:
        return proceed()
    # 计算交割点集质心
    points = [wkt.loads(geom) for geom in intersect_point_list]
    if IS_TEST == '0':
        logger.info(f"导航轨迹与边框交割点: {intersect_point_list}")
    # 根据轨迹点聚类
    point_coords = np.array([(point.x, point.y) for point in points])
    db = DBSCAN(eps=8 / COORDINATE_FACTOR, min_samples=5).fit(point_coords)
    labels = db.labels_
    clusters = {}
    valid_points_count = 0
    for point, label in zip(intersect_point_list, labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        valid_points_count += 1
        clusters[label].append(point)
    # 根据前80%轨迹聚类点获取最优的Node或者link
    already_cal_points_count = 0
    convex_hull_wkt_list = []
    cluster_points_wkt_list = []
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= valid_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in clusters[label]]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        if IS_TEST == '0':
            logger.info(f"导航轨迹与边框交割点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(5 / COORDINATE_FACTOR)
        # 如果当前聚类点距离之前聚类点小于10m则跳过该聚类点
        if len([item for item in convex_hull_wkt_list if item.distance(convex_hull) < 10 / COORDINATE_FACTOR]) > 0:
            convex_hull_wkt_list.append(convex_hull)
            continue
        convex_hull_wkt_list.append(convex_hull)
        cluster_points_wkt_list.append(multipoint)
    ctx.feature.navigate_traj_features = cluster_points_wkt_list
    proceed()


def competitor_feature(ctx: context.Context, proceed):
    """
    竞品情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin competitor_feature")
    park_access_navi_competitor_list = park_utils.get_competitor_out_park_access(ctx.parking_info.bid)
    logger.info(f"竞品数据抓取:{len(park_access_navi_competitor_list)}")
    if not park_access_navi_competitor_list:
        return proceed()
    ctx.feature.competitor_features = [wkt.loads(item['geom']) for item in park_access_navi_competitor_list]
    competitor_features_str = ",".join(str(feature) for feature in ctx.feature.competitor_features)
    logger.info(f"竞品数据:{competitor_features_str})")
    return proceed()


def end_traj_and_competitor_and_aoi_access(ctx: context.Context, proceed):
    """
    top 终点轨迹 + 叠加竞品 + 关联关系 自动化关联出口
    Args:
        ctx:
        proceed:
    """
    logger.info("begin end_traj_and_competitor_and_aoi_access")
    if len(ctx.feature.end_traj_features) == 0:
        return proceed()
    for item in ctx.aoi_info.blu_access_list:
        if not utils.is_out_gate(item.node_id):
            continue
        node_sp = wkt.loads(item.node_geom)
        # 附近20m范围内存在竞品
        near_competitors = [item for item in ctx.feature.competitor_features if
                            item.buffer(20 / COORDINATE_FACTOR).contains(node_sp)]
        if not near_competitors:
            continue
        # 取top3轨迹
        top_num = 0
        for traj_points in ctx.feature.end_traj_features[0:3]:
            top_num += 1
            traj_convex_sp = traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            if exterior_line_sp.length > 200 / COORDINATE_FACTOR:
                logger.info(f"{traj_points} 终点轨迹聚类不合理")
                continue
            traj_convex_buffer_10m_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if traj_convex_buffer_10m_sp.contains(node_sp):
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = item.node_id
                parking_access.node_geom = item.node_geom
                parking_access.strategy = context.OVERGROUND_END_TRAJ_AND_COMPETITOR_AND_AOI_ACCESS
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                if top_num == 2:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_MEDIUM
                if top_num == 3:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_LOW
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                ctx.parking_access_list.append(parking_access)
    proceed()


def park_accept_traj_and_competitor_and_aoi_access(ctx: context.Context, proceed):
    """
    top 采纳轨迹 + 叠加竞品 + 关联关系 自动化关联出口
    Args:
        ctx:
        proceed:
    """
    logger.info("begin end_traj_and_competitor_and_aoi_access")
    if len(ctx.feature.park_accept_features) == 0:
        return proceed()
    for item in ctx.aoi_info.blu_access_list:
        if not utils.is_out_gate(item.node_id):
            continue
        node_sp = wkt.loads(item.node_geom)
        # 附近20m范围内存在竞品
        near_competitors = [item for item in ctx.feature.competitor_features if
                            item.buffer(20 / COORDINATE_FACTOR).contains(node_sp)]
        if not near_competitors:
            continue
        # 取top3轨迹
        top_num = 0
        for traj_points in ctx.feature.park_accept_features[0:3]:
            top_num += 1
            traj_convex_sp = traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            if exterior_line_sp.length > 200 / COORDINATE_FACTOR:
                logger.info(f"{traj_points} 采纳轨迹聚类不合理")
                continue
            traj_convex_buffer_10m_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if traj_convex_buffer_10m_sp.contains(node_sp):
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = item.node_id
                parking_access.node_geom = item.node_geom
                parking_access.strategy = context.OVERGROUND_PARK_ACCEPT_TRAJ_AND_COMPETITOR_AND_AOI_ACCESS
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                if top_num == 2:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_MEDIUM
                if top_num == 3:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_LOW
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                ctx.parking_access_list.append(parking_access)
    proceed()


def navigate_traj_and_competitor_and_aoi_access(ctx: context.Context, proceed):
    """
    top 导航轨迹 + 叠加竞品 + 关联关系 自动化关联出口
    Args:
        ctx:
        proceed:
    """
    logger.info("begin navigate_traj_and_competitor_and_aoi_access")
    if len(ctx.feature.navigate_traj_features) == 0:
        return proceed()
    for item in ctx.aoi_info.blu_access_list:
        if not utils.is_out_gate(item.node_id):
            continue
        node_sp = wkt.loads(item.node_geom)
        # 附近20m范围内存在竞品
        near_competitors = [item for item in ctx.feature.competitor_features if
                            item.buffer(20 / COORDINATE_FACTOR).contains(node_sp)]
        if not near_competitors:
            continue
        # 取top3轨迹
        top_num = 0
        for traj_points in ctx.feature.navigate_traj_features[0:3]:
            top_num += 1
            traj_convex_sp = traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            if exterior_line_sp.length > 200 / COORDINATE_FACTOR:
                logger.info(f"{traj_points} 采纳轨迹聚类不合理")
                continue
            traj_convex_buffer_10m_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if traj_convex_buffer_10m_sp.contains(node_sp):
                parking_access = context.ParkingAccess()
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = item.node_id
                parking_access.node_geom = item.node_geom
                parking_access.strategy = context.OVERGROUND_NAVIGATE_TRAJ_AND_COMPETITOR_AND_AOI_ACCESS
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                if top_num == 2:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_MEDIUM
                if top_num == 3:
                    parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_LOW
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                ctx.parking_access_list.append(parking_access)
    proceed()


def competitor_and_top_traj(ctx: context.Context, proceed):
    """
    竞品 + top1轨迹
    Args:
        ctx:
        proceed:
    """
    logger.info("begin competitor_and_top_traj")
    if not ctx.feature.competitor_features:
        return proceed()
    for competitor_sp in ctx.feature.competitor_features:
        parking_access = context.ParkingAccess()
        traj_sp = None
        # top1终点轨迹
        end_traj_points = ctx.feature.end_traj_features[0] if ctx.feature.end_traj_features else None
        if end_traj_points:
            traj_convex_sp = end_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(competitor_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "end_traj"
        # top1 采纳轨迹
        park_accept_traj_points = ctx.feature.park_accept_features[0] if ctx.feature.park_accept_features else None
        if park_accept_traj_points and not traj_sp:
            traj_convex_sp = park_accept_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(competitor_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "park_accept_traj"
        # top1 导航轨迹
        navigate_traj_points = ctx.feature.navigate_traj_features[0] if ctx.feature.navigate_traj_features else None
        if navigate_traj_points and not traj_sp:
            traj_convex_sp = navigate_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(competitor_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "navigate_traj"
        if not traj_sp:
            continue
        # 附近存在AOI出入口直接关联
        for item in ctx.aoi_info.blu_access_list:
            if not utils.is_out_gate(item.node_id):
                continue
            if traj_sp.contains(wkt.loads(item.node_geom)):
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = item.node_id
                parking_access.node_geom = item.node_geom
                parking_access.confidence = context.INTELLIGENCE_CONFIDENCE_HIGH
                parking_access.strategy = context.OVERGROUND_TOP_TRAJ_AND_COMPETITOR
                ctx.parking_access_list.append(parking_access)
                return proceed()
        # 找附近出口大门
        node_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(traj_sp.wkt)
        if node_gate_list:
            node_ids = [x[0] for x in node_gate_list if utils.is_out_gate(x[0])]
            if not node_ids:
                node_ids = [x[0] for x in node_gate_list]
            node_list = ctx.dao.road_dao.get_node_geom(node_ids)
            # 取距离子点最近的节点
            if node_list:
                node_res = min(node_list, key=lambda x: competitor_sp.distance(wkt.loads(x[0])))
                parking_access.type = context.PARKING_ACCESS_TYPE_NODE
                parking_access.node_id = node_res[1]
                parking_access.node_geom = node_res[0]
                parking_access.strategy = context.OVERGROUND_TOP_TRAJ_AND_COMPETITOR
                ctx.parking_access_list.append(parking_access)
    proceed()


def aoi_access_and_top_traj(ctx: context.Context, proceed):
    """
    竞品 + top1轨迹
    Args:
        ctx:
        proceed:
    """
    logger.info("begin aoi_access_and_top_traj")
    if not ctx.aoi_info.blu_access_list or ctx.aoi_info.std_tag not in BUILD_GATE_CATEGORY:
        return proceed()
    for item in ctx.aoi_info.blu_access_list:
        if not utils.is_out_gate(item.node_id):
            logger.info(f"{item} 不是出入口")
            continue
        parking_access = context.ParkingAccess()
        node_sp = wkt.loads(item.node_geom)
        traj_sp = None
        # top1终点轨迹
        end_traj_points = ctx.feature.end_traj_features[0] if ctx.feature.end_traj_features else None
        if end_traj_points:
            traj_convex_sp = end_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(node_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "end_traj"
        # top1 采纳轨迹
        park_accept_traj_points = ctx.feature.park_accept_features[0] if ctx.feature.park_accept_features else None
        if park_accept_traj_points and not traj_sp:
            traj_convex_sp = park_accept_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(node_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "park_accept_traj"
        # top1 导航轨迹
        navigate_traj_points = ctx.feature.navigate_traj_features[0] if ctx.feature.navigate_traj_features else None
        if navigate_traj_points and not traj_sp:
            traj_convex_sp = navigate_traj_points.convex_hull.buffer(1 / COORDINATE_FACTOR)
            exterior_line_sp = traj_convex_sp.exterior
            traj_convex_buffer_sp = traj_convex_sp.buffer(10 / COORDINATE_FACTOR)
            if exterior_line_sp.length < 200 / COORDINATE_FACTOR and traj_convex_buffer_sp.contains(node_sp):
                traj_sp = traj_convex_buffer_sp
                parking_access.remark = "navigate_traj"
        logger.info(f"aoi_access_top轨迹:{traj_sp}")
        if not traj_sp:
            continue
        # 找附近出口大门
        parking_access.type = context.PARKING_ACCESS_TYPE_NODE
        parking_access.node_id = item.node_id
        parking_access.node_geom = item.node_geom
        parking_access.strategy = context.OVERGROUND_TOP_TRAJ_AND_AOI_ACCESS
        ctx.parking_access_list.append(parking_access)
    proceed()


def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.out_park_access_auto_qc(ctx)
    return proceed()


def save_intelligence_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_access_intelligence_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type}, 是否有效:{item.is_valid}, node_id位置\t{item.node_id},"
                        f"node_geom位置\t{item.node_geom}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, strategy_values:{item.strategy_value}")
        valid_geom_list = [item.node_geom for item in ctx.parking_access_list if item.is_valid]
        invalid_geom_list = [item.node_geom for item in ctx.parking_access_list if not item.is_valid]
        logger.info(f"有效:{valid_geom_list}, 无效:{invalid_geom_list}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, 是否有效:{park_access.is_valid},node_id位置\t{park_access.node_id}, "
                        f"策略:{park_access.strategy}, strategy_value:{park_access.strategy_value}, "
                        f"link_id\t{park_access.link_id}, 过滤原因:{park_access.filter_reason}")
            continue
        guid = str(uuid.uuid4()).replace("-", "")
        road_relation, park_access_mc_geom = park_utils.get_road_relation_by_node_id(park_access.node_id)
        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': ctx.intelligence_type,
            'batch_number': f"park_access_overground_out_auto_add_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': guid,
            'source_id': f"park_access_overground_out_auto_add_{guid}",
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'road_relation': json.dumps(road_relation),
            'park_access_bid': '',
            'confidence': park_access.confidence,
            'remark': park_access.remark,

        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)

    proceed()


def release():
    """
    出口推送上线
    Returns:

    """
    park_access_list = poi_online_query.get_gate_front_park_access_intelligence_by_kv(
        type='park_access_overground_out_add_auto', status='INIT')
    if not park_access_list:
        logger.info(f"没有需要推送的停车场情报")
        return
    for park_access in tqdm.tqdm(park_access_list):
        # 查询停车场信息
        park_list = poi_online_query.get_parking_list_by_kv(bids=[park_access['bid']])
        if not park_list:
            logger.error(f"未找到停车场信息:{park_access['bid']}")
            continue
        # 判断情报是否已推送
        if is_pushed(park_access['bid'], wkt.loads(park_access['geom'])):
            poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='INFO_PUSHED')
            logger.info(f"该停车场情报已推送:{park_access['bid']}")
            continue
        park_info = park_list[0]
        park_access_name = park_utils.get_park_access_name_by_road_relation(park_info['name'],
                                                                            json.loads(park_access['road_relation']))
        if not park_access_name:
            logger.error(f"停车场出入口名称匹配失败:{park_access['bid']}")
            continue
        # 计算出入口坐标
        park_access_mc_sp = wkt.loads(utils.gcj_to_mc(park_access['geom']))
        park_access_achieve = {
            "source_id": park_access["source_id"],
            "road_relation": park_access["road_relation"],
            "parent_bid": park_access["bid"],
            "park_access_name": park_access_name,
            "address": park_info["address"],
            "point_x": park_access_mc_sp.x,
            "point_y": park_access_mc_sp.y
        }
        push_park_access(park_access_achieve)
        poi_online_rw.update_park_access_intelligence_status_by_id([park_access['id']], status='PUSHED')


def is_pushed(park_bid, park_access_sp):
    """
    判断情报是否已推送
    Returns:

    """
    # 查询已推送的情报
    pushed_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(bid=park_bid, type=[
        'park_access_overground_out_add'], status='PUSHED')
    if not pushed_intelligence_list:
        return False
    pushed_geom_list = [{'id': item['id'], 'geom_sp': wkt.loads(item['geom'])} for item in pushed_intelligence_list]
    distance_value = 20
    is_pushed_intelligence = [item['id'] for item in pushed_geom_list
                              if park_access_sp.distance(item['geom_sp']) <= distance_value / COORDINATE_FACTOR]
    if len(is_pushed_intelligence) > 0:
        return True
    return False


def push_park_access(park_access):
    """
    出入口推送上线
    Args:
        park_access:

    Returns:

    """
    # 开始推送
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    show_tag = park_access['park_access_name'].split('-')[-1]
    date = datetime.datetime.now().strftime("%Y%m%d")
    params = {
        "source": "STRATEGY_OUT_PARK_ACCESS",
        "source_id": f"STRATEGY_{park_access['source_id']}",
        "is_add_poi": 1,
        "batch_id": f"STRATEGY_OUT_PARK_ACCESS_{date}",
        "std_tag": "出入口;停车场出入口",
        "show_tag": f"停车场{show_tag}",
        "name": park_access['park_access_name'],
        "address": park_access['address'],
        "point_x": park_access['point_x'],
        "point_y": park_access['point_y'],
        "status": 1,
        "parent_id": park_access['parent_bid'],
        "road_relation": park_access['road_relation'],
    }
    exit(params)
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"出入口推送上线:req:{params},resp:{res.text}")


def main(bid_list: list):
    """
    地上停车场情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"\n地上停车场出口自动化:{parking[7]}")
            root_bid = parking[14]
            root_poi_list = poi_online_query.get_poi_by_bids_kv([root_bid])
            if not root_poi_list:
                logger.info(f"根节点POI不存在:{root_bid}")
                continue
            root_poi_info = root_poi_list[0]
            if not root_poi_info or root_poi_info['std_tag'] in AREA_FIRST_CATEGORY:
                logger.info(f"特定垂类不走自动化:{root_poi_info['std_tag']}")
                continue
            if root_poi_info['click_pv'] > 10000 or (
                    root_poi_info['click_pv'] > 5000 and root_poi_info['std_tag'] not in BUILD_GATE_CATEGORY):
                logger.info(f"pv太高不走自动化:pv:{root_poi_info['click_pv']}")
                continue
            ctx = context.Context(parking)
            ctx.intelligence_type = "park_access_overground_out_add_auto"
            ctx.init_context()
            ctx.get_park_sub_pois()
            # 主点AOI不存在/停车场面在AOI外部不走自动化
            park_sp = wkt.loads(ctx.parking_info.park_wkt)
            if ctx.aoi_info.bid == '' or ctx.aoi_info.wkt == '' or not wkt.loads(ctx.aoi_info.wkt).contains(park_sp):
                logger.info(f"主点AOI不存在/停车场面在AOI外部不走自动化")
                continue
            if ctx.aoi_info.std_tag in AREA_FIRST_CATEGORY or '航站楼' in ctx.parking_info.name:
                logger.info(f"特定垂类不走自动化")
                continue
            # AOI 场景
            if ctx.aoi_info.bid:
                # 获取出入口信息
                ctx.get_aoi_blu_access_list()
                # 轨迹过滤AOI范围外的
                ctx.filter_traj_by_aoi()
                # 计算AOI出入口轨迹通量
                ctx.cal_dest_traj_pass_blu_access()
            ctx.traj.filter_aoi_dest_traj_list = ctx.traj.dest_traj_list
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[7]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if not bid_list:
        park_import_list = poi_online_query.get_park_import_list()
        bid_list = [item['bid'] for item in park_import_list]
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
    # release()
