#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用bid作为键的修复方案
"""

import os
import csv

def analyze_struct_id_duplicates(csv_path="/Users/<USER>/Downloads/bud_face.csv"):
    """分析CSV文件中struct_id的重复情况"""
    if not os.path.exists(csv_path):
        print(f"❌ 文件不存在: {csv_path}")
        return
    
    print("=== 分析struct_id重复情况 ===")
    print(f"文件路径: {csv_path}")
    print()
    
    struct_id_count = {}
    struct_id_details = {}
    total_rows = 0
    
    with open(csv_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            total_rows += 1
            struct_id = row.get('struct_id', '').strip()
            
            if struct_id:
                # 统计struct_id出现次数
                struct_id_count[struct_id] = struct_id_count.get(struct_id, 0) + 1
                
                # 记录struct_id的详细信息
                if struct_id not in struct_id_details:
                    struct_id_details[struct_id] = []
                
                struct_id_details[struct_id].append({
                    'face_id': row.get('face_id', ''),
                    'name_ch': row.get('name_ch', ''),
                    'std_tag': row.get('std_tag', ''),
                    'geom': row.get('geom', '')[:50] + '...' if len(row.get('geom', '')) > 50 else row.get('geom', '')
                })
    
    # 分析结果
    unique_struct_ids = len(struct_id_count)
    duplicate_struct_ids = [sid for sid, count in struct_id_count.items() if count > 1]
    
    print(f"📊 统计结果:")
    print(f"  总行数: {total_rows}")
    print(f"  唯一struct_id数量: {unique_struct_ids}")
    print(f"  重复的struct_id数量: {len(duplicate_struct_ids)}")
    print()
    
    if duplicate_struct_ids:
        print("⚠️  发现重复的struct_id:")
        for i, struct_id in enumerate(duplicate_struct_ids[:5]):  # 只显示前5个
            count = struct_id_count[struct_id]
            print(f"  {i+1}. struct_id: {struct_id} (出现{count}次)")
            
            # 显示该struct_id的详细信息
            for j, detail in enumerate(struct_id_details[struct_id]):
                print(f"     记录{j+1}: face_id={detail['face_id'][:20]}..., name_ch={detail['name_ch']}, std_tag={detail['std_tag']}")
            print()
        
        if len(duplicate_struct_ids) > 5:
            print(f"  ... 还有 {len(duplicate_struct_ids) - 5} 个重复的struct_id")
        
        print("🎯 这证实了使用struct_id作为键会导致数据覆盖的问题！")
    else:
        print("✅ 没有发现重复的struct_id")
    
    return len(duplicate_struct_ids) > 0

def verify_bid_key_solution():
    """验证使用bid作为键的解决方案"""
    print("\n=== 验证bid键解决方案 ===")
    
    # 检查修改后的函数签名
    file_path = "src/aoi/business_order/shenzhen.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        {
            'name': '函数返回值文档更新',
            'pattern': 'Tuple\\[Dict\\[bid, poi_name\\], Dict\\[bid, poi_std_tag\\], Dict\\[bid, struct_id\\]\\]',
            'found': 'Tuple[Dict[bid, poi_name], Dict[bid, poi_std_tag], Dict[bid, struct_id]]' in content
        },
        {
            'name': 'bid_to_struct_mapping变量定义',
            'pattern': 'bid_to_struct_mapping = {}',
            'found': 'bid_to_struct_mapping = {}' in content
        },
        {
            'name': '使用bid作为键存储POI名称',
            'pattern': 'updated_names[bid] = name',
            'found': 'updated_names[bid] = name' in content
        },
        {
            'name': '使用bid作为键存储POI标签',
            'pattern': 'updated_std_tags[bid] = std_tag',
            'found': 'updated_std_tags[bid] = std_tag' in content
        },
        {
            'name': '建立bid到struct_id的映射',
            'pattern': 'bid_to_struct_mapping[bid] = struct_id',
            'found': 'bid_to_struct_mapping[bid] = struct_id' in content
        },
        {
            'name': '返回三个字典',
            'pattern': 'return updated_names, updated_std_tags, bid_to_struct_mapping',
            'found': 'return updated_names, updated_std_tags, bid_to_struct_mapping' in content
        },
        {
            'name': '接收三个返回值',
            'pattern': 'updated_names, updated_std_tags, bid_to_struct_mapping = bind_poi_name',
            'found': 'updated_names, updated_std_tags, bid_to_struct_mapping = bind_poi_name' in content
        },
        {
            'name': '使用映射关系更新数据',
            'pattern': 'for bid, mapped_struct_id in bid_to_struct_mapping.items():',
            'found': 'for bid, mapped_struct_id in bid_to_struct_mapping.items():' in content
        }
    ]
    
    print("🔍 检查修改内容:")
    all_passed = True
    
    for check in checks:
        status = "✅ 通过" if check['found'] else "❌ 失败"
        print(f"  {status} {check['name']}")
        if not check['found']:
            all_passed = False
    
    return all_passed

def explain_solution_benefits():
    """解释解决方案的优势"""
    print("\n=== 解决方案优势分析 ===")
    print()
    print("🎯 **使用bid作为键的优势**:")
    print()
    print("1. **唯一性保证**:")
    print("   - bid是POI的业务唯一标识符")
    print("   - 避免了struct_id重复导致的数据覆盖")
    print("   - 确保每个POI绑定结果都有唯一的存储位置")
    print()
    print("2. **数据完整性**:")
    print("   - 不会因为相同struct_id而丢失POI信息")
    print("   - 保持了所有成功绑定的POI数据")
    print("   - 支持一个struct_id对应多个不同POI的情况")
    print()
    print("3. **逻辑清晰**:")
    print("   - bid -> POI信息的直接映射")
    print("   - bid -> struct_id的反向映射用于数据更新")
    print("   - 分离了POI数据存储和建筑物数据更新的逻辑")
    print()
    print("4. **扩展性**:")
    print("   - 便于后续添加更多POI相关字段")
    print("   - 支持复杂的POI-建筑物关系处理")
    print("   - 为未来的数据分析和处理提供了更好的基础")

def main():
    """主函数"""
    print("bid键解决方案测试和验证")
    print("=" * 50)
    
    # 分析struct_id重复情况
    has_duplicates = analyze_struct_id_duplicates()
    
    # 验证解决方案
    solution_correct = verify_bid_key_solution()
    
    # 解释解决方案优势
    explain_solution_benefits()
    
    print("\n=== 总结 ===")
    if has_duplicates:
        print("✅ 确认存在struct_id重复问题，使用bid作为键是必要的")
    else:
        print("ℹ️  当前数据中未发现struct_id重复，但使用bid作为键仍然是更安全的做法")
    
    if solution_correct:
        print("✅ bid键解决方案实现正确")
        print("🎉 修改完成！现在POI绑定使用bid作为唯一键，避免了数据覆盖问题")
    else:
        print("❌ bid键解决方案实现有问题，请检查代码修改")
    
    print("\n📋 **下一步建议**:")
    print("1. 运行修改后的代码测试功能")
    print("2. 比较修改前后的数据行数和内容")
    print("3. 验证POI绑定的准确性和完整性")
    print("4. 确认std_tag字段的数据来源正确")

if __name__ == '__main__':
    main()
