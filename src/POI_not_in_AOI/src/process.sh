#!/bin/bash
BASEDIR=$(readlink -f "$(dirname "${BASH_SOURCE[0]}")")
source "$BASEDIR/../../../conf/globalConf.sh"

temp="$TMP_PATH/poi_not_in_aoi/temp"
data="$TMP_PATH/poi_not_in_aoi/data"
mkdir -p "$temp" "$data"
today=$(date +%Y-%m-%d)
# 这个是脚本执行周期， 如果上一周期已经发送过的，这个周期就不在处理了
yesterday=$(date +%Y-%m-%d --date="-7 day")

function check_AOI_info(){
  cp "$DATA_PATH/aoi/$today.csv" "$data/aoi.data"

  awk -F "\t" '
  ARGIND==1{
          bid = $3;
          shape = $2;
          map[bid]=shape
  }

  ARGIND==2{
          bid = $1;
          x = $20;
          y = $21;
          cp_di = $25;
          if(bid in map){
                  print bid "\t" map[bid] "\t" x "\t" y "\t" cp_di;
          }

  }' "$data/aoi.data" "$DATA_PATH/poi/poi_res.utf8" > "$temp/aoi.info.temp"
#  这个脚本处理的是cp_di 这个字段，  观察后面脚本，不需要考虑来源
#  $PYTHONBIN "$BASEDIR/json2txt.py" "$temp/aoi.info.temp" > "$temp/aoi.info"

}

function check_POI_in_AOI(){
    $PYTHONBIN "$BASEDIR/poiInAoi.py" "$temp/aoi.info" "$data/poi_not_in_aoi.$today" "$data/poi_not_in_aoi.auto.$today"
    current=$(date "+%Y-%m-%d %H:%M:%S")
    timeStamp=$(date -d "$current" +%s)
    awk -v ts="$timeStamp" -f "$BASEDIR/format.awk" "$data/poi_not_in_aoi.auto.$today" > "$data/poi_not_in_aoi.auto_online_format.$today"
    cd "$BASEDIR/../man_op/" || exit
    $PYTHONBIN update.py "$data/poi_not_in_aoi.auto_online_format.$today"
    cd - || exit;
}

function checkDiff(){

        awk -F "\t" '
        ARGIND==1{
                bid=$1;
                map[bid]
        }
        ARGIND==2{
                bid=$1;
                if(bid in map){
                        next
                } else {
                        print $0
                }

        }' "$data/poi_not_in_aoi.$yesterday" "$data/poi_not_in_aoi.$today" > "$data/toPlatform.$today"

}

function sendMail(){

        sh "$BASEDIR/sendData.sh";
}

function main(){

    check_AOI_info;
    check_POI_in_AOI;
    checkDiff;
    sendMail;
}

main;
