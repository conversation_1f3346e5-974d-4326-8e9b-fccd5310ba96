# -*- coding:utf-8 -*-
"""
pg库查询方法类
"""
import os
import sys 
import time
import json
import logging
import conf
import psycopg2
from nutspg.nutsdbapi import NutsDBApi

reload(sys)
sys.setdefaultencoding('utf8')

class PgBase(object):
    """
    数据库工具类
    """
    def __init__(self, db_info):
        try:
            self.db_info = db_info
        except Exception as e:
            raise e

    def connect(self):
        """
        连接数据库函数
        Returns:
            返回pg库连接游标
        """
        try:
            pg_conn = psycopg2.connect(host=self.db_info["host"], user=self.db_info["user"],
                password=self.db_info["passwd"], port=self.db_info["port"], database=self.db_info["db"])
            return pg_conn
        except Exception as e:
            logging.error("pg失败[%s][%s]" % (str(self.db_info), e)) 
            raise e

    def select(self, sql):
        """
        查询sql语句
        Args:
            sql: 字符串类型，需要查询的语句
        Returns:
            list类型，数据库的查询结果
        """
        try:
            pg_conn = self.connect()
            cur = pg_conn.cursor()
            cur.execute(sql)
            ret = cur.fetchall()
            cur.close()
            self.disconnect(pg_conn)
            return ret
        except Exception as e:
            self.disconnect(pg_conn)
            logging.error("select 失败[%s][%s]" % (sql, e))
            raise e

    def disconnect(self, pg_conn):
        """
        与数据库断开连接
        """
        try:
            pg_conn.close()
        except Exception as se:
            pass

    def execute(self, sql):
        """
        执行sql语句
        Args:
            sql: 字符串类型，需要执行的sql语句
        """
        try:
            pg_conn = self.connect()
            cur = pg_conn.cursor()
            if type(sql) is list:
                for item_sql in sql:
                    cur.execute(item_sql)
            else:
                cur.execute(sql)
            pg_conn.commit()
            cur.close()
            self.disconnect(pg_conn)
        except Exception as e:
            self.disconnect(pg_conn)
            logging.error("sql失败[%s][%s]" % (str(sql), e))
            raise e
