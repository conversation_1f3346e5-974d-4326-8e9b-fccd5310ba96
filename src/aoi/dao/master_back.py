# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
背景成果 数据库
"""
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class MasterBackDao(PgDao):
    """
    背景成果 数据库
    """

    def __init__(self, shared_pool=True, pool=None):
        conf = CommTool.get_config("pg").get("master_back")
        if not conf:
            raise Exception("master_back 配置不存在")
        self.db_conf = conf

        # 传递配置和连接池给父类，由父类统一管理连接池
        super().__init__(pg_conf=conf, pool=pool, use_shared_pool=shared_pool)

    def get_intersects_aoi(self, geom: str):
        """
        获取与AOI相交的建筑物
        :param geom: AOI几何体
        """
        sql = f"""
            select bfp.poi_bid from blu_face bf 
                inner join blu_face_poi bfp on bf.face_id=bfp.face_id 
            where st_intersects(bf.geom, st_geomfromtext(%s, 4326)) 
                and src != 'SD' and kind !~ '52'
        """
        return self.queryall(sql, [geom])

    def get_aoi_info_by_bid(self, bid: str):
        """
        获取AOI信息通过BID
        :param bid:
        :return:
        """
        sql = f"""
            select st_area(bf.geom::geography),st_astext(geom),poi_bid,bf.aoi_level,bfc.aoi_complete from blu_face bf 
                inner join blu_face_poi bfp on bf.face_id=bfp.face_id 
                left join blu_face_complete bfc on bfp.poi_bid=bfc.main_bid
            where poi_bid = %s;
        """
        return self.queryone(sql, [bid])

    def get_blu_access_list_by_main_bid(self, main_bid):
        """
        查询AOI大门Node
        :param main_bid:
        :return:
        """
        sql = f"""
            select distinct bagr.node_id, st_astext(ba.geom) from blu_access ba 
                inner join blu_access_gate_rel bagr on ba.access_id=bagr.access_id 
            where ba.main_bid = %s
        """
        return self.queryall(sql, [main_bid])

    def get_blu_access_list_by_node_id(self, node_id: str):
        """
        获取语义化化成果
        Args:
            node_id:

        Returns:

        """
        sql = f"""
            select distinct main_bid,ba.name from blu_access ba 
                inner join blu_access_gate_rel bagr on ba.access_id=bagr.access_id 
            where bagr.node_id=%s
        """
        res = self.queryall(sql, [node_id])
        if not res:
            return []
        blu_access_list = [{"main_bid": item[0], "name": item[1]} for item in res]
        return blu_access_list

    def get_parking_by_bids(self, bids: list, status=None):
        """
        获取停车场信息
        Args:
            bids:
            status:

        Returns:

        """
        sql = f"""
            select bid, road_relation,open_limit_new from parking where bid=ANY(%s)
        """
        args = [bids]
        if status:
            sql += " and status=%s"
            args.append(status)
        return self.queryall(sql, args)

    def get_main_bid_by_node(self, node_id):
        """
        根据节点获取主BID
        Args:
            node_id:
        Returns:
        """
        sql = f"""
            select b.main_bid, st_astext(c.geom)
            from blu_access_gate_rel a 
                inner join blu_access b on a.access_id = b.access_id  
                inner join blu_face c on b.face_id = c.face_id
            where  a.node_id = %s
        """
        return self.queryall(sql, [node_id])
    def get_aoi_main_bid_by_node_id(self, long_node_id: str):
        """
        获取语义化化成果
        Args:
            pg:
            long_node_id:

        Returns:

        """
        sql = f"""
            select distinct main_bid,ba.name from blu_access ba 
                inner join blu_access_gate_rel bagr on ba.access_id=bagr.access_id 
            where bagr.node_id=%s
        """
        return self.queryall_by_kv_cursor(sql, [long_node_id])

    def get_intersects_aoi_by_geom(self, geom: str):
        """
        获取AOI主点
        Args:
            geom:

        Returns:

        """
        sql = """
           select b.poi_bid, st_astext(a.geom) as geom from blu_face a
           inner join blu_face_poi b on a.face_id = b.face_id
           where st_intersects(a.geom, st_geomfromtext(%s, 4326))
               and a.aoi_level = 2
               and a.src != 'SD'
       """
        return self.queryall_by_kv_cursor(sql, [geom])

    def get_park_access_list(self, park_access_bids):
        """
        获取停车场大门节点
        Args:
            park_access_bids:

        Returns:

        """
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom), road_relation,status from parking
            where bid = ANY(%s)
            and std_tag = '出入口;停车场出入口'
        """
        return self.queryall_by_kv_cursor(sql, [park_access_bids])

    def get_bad_admin_by_geom(self, geom):
        """
        通过范围获取行政区划
        Args:
            geom:

        Returns:

        """
        sql = f"""
            select province_ch, cityname from mesh_conf_wkt
            where st_intersects(wkt, st_geomfromtext(%s, 4326))
        """
        return self.queryone_by_kv_cursor(sql, [geom])

    def get_mesh_conf_wkt_by_city(self, cities):
        """
        通过范围获取行政区划
        Args:
            cities:

        Returns:

        """
        sql = f"""
            select cityname,st_astext(wkt) as geom from mesh_conf_wkt where cityname=ANY(%s)
        """
        return self.queryall(sql, [cities])

    def get_park_access_by_kv(self, park_bid=None, wkt=None, status=None, show_tags=None, bids=None):
        """
        获取停车场出入口
        Args:
            park_bid:
            wkt:
            status:
            show_tags:

        Returns:

        """
        args = []
        sql = f"""
            select bid, parent_id, st_astext(gcj_geom) as geom, road_relation,status,name,show_tag from parking
            where std_tag = '出入口;停车场出入口' and status != 2
        """
        if park_bid:
            sql += f" and parent_id = %s"
            args.append(park_bid)
        if wkt:
            sql += f" and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)"
            args.append(wkt)
        if status:
            sql += f" and status = %s"
            args.append(status)
        if show_tags:
            sql += f" and show_tag = ANY(%s)"
            args.append(show_tags)
        if bids:
            sql += f" and bid = ANY(%s)"
            args.append(bids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_blc_face(self, kinds=None, geom=None):
        """
        获取停车场出入口
        Args:
            park_bid:
            wkt:
            status:
            show_tags:

        Returns:

        """
        args = []
        sql = f"""
            select face_id from blc_face where 1=1
        """
        if kinds:
            sql += f" and kind = ANY(%s)"
            args.append(kinds)
        if geom:
            sql += f" and st_intersects(st_geomfromtext(%s, 4326), geom)"
            args.append(geom)
        return self.queryall_by_kv_cursor(sql, args)

    def get_mesh_conf_by_geom(self, geom):
        """
        通过范围获取行政区划
        Args:
            geom:

        Returns:

        """
        sql = f"""
            select mesh_id from mesh_conf_wkt where st_intersects(wkt, st_geomfromtext(%s, 4326))
        """
        return self.queryall_by_kv_cursor(sql, [geom])

    def is_group_aoi(self, bid):
        """
        判断是否是聚合院落
        """
        sql = f"""
            select 1
            from blu_face bf
                left join blu_face_poi bfp on bfp.face_id = bf.face_id
            where bf.aoi_level = 1 and bfp.poi_bid = '{bid}'
            limit 1
        """
        return self.queryall_by_kv_cursor(sql, [])