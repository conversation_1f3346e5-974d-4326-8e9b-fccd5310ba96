#!/bin/bash

CUR_DIR=$(readlink -f $(dirname $BASH_SOURCE[0]))
source "$CUR_DIR"/../conf/globalConf.sh

#日志保留天数
LOG_SAVE_DAYS=7
#数据保留天数
DATA_SAVE_DAYS=30
#垃圾站保留天数
TRASH_SAVE_DAYS=3

TRASH_PATH=$(readlink -f "$ROOT/../trash")
mkdir -p "$TRASH_PATH"

function clear_log() {
  find "$LOG_PATH/" -type f -mtime +$LOG_SAVE_DAYS -exec mv {} "$TRASH_PATH/" \;
}

function clear_data() {
  find "$TMP_PATH/" -mtime +$DATA_SAVE_DAYS -exec mv {} "$TRASH_PATH" \;
}

function clear_trash() {
  # 先删文件，再删目录
  find "$TRASH_PATH/" -type f -mtime +$TRASH_SAVE_DAYS -exec rm -rf {} \;
  find "$TRASH_PATH/" -mtime +$TRASH_SAVE_DAYS -exec rm -rf {} \;
}

clear_log
clear_data
clear_trash


