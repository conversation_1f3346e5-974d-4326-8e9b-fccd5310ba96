"""
intersection filter temp
"""
import sys
import shapely.wkt
import shapely.geometry

bound = 0.5
geoMap = dict()


def insertKey(bid, shape):
    """
    :param bid:
    :param shape:
    :return: void
    """
    if bid not in geoMap.keys():
        geoMap[bid] = shape


def checkIntersection(gid, gaode_shape):
    """
    :param gid:
    :param gaode_shape:
    :return: array
    """
    result = []
    for bid in geoMap:
        baidu_shape = geoMap[bid]
        if gaode_shape.distance(baidu_shape) <= 0.0:
            intersection_area = gaode_shape.intersection(baidu_shape).area

            if intersection_area / gaode_shape.area > bound and intersection_area / baidu_shape.area > bound:
                result.append(bid + ":" + str(intersection_area / gaode_shape.area) + ":" + str(
                    intersection_area / baidu_shape.area))
    return result


def main():
    """
    :return: void
    """
    # read data
    baidu_aoi_file = open(sys.argv[1])
    gaode_aoi_file = open(sys.argv[2])
    # write result
    no_intersection = open(sys.argv[3], "w")
    intersection = open(sys.argv[4], "w")

    while True:  # load baidu aoi data into hash_map
        line = baidu_aoi_file.readline()
        if not line:
            break
        line = line.strip("\r\n")
        items = line.split("\t")
        bid = items[0]
        raw_shape = items[1]
        try:
            shape = shapely.wkt.loads(raw_shape)
            insertKey(bid, shape)
        except:
            print line

    baidu_aoi_file.close()

    while True:
        line = gaode_aoi_file.readline()
        if not line:
            break
        line = line.strip("\r\n")
        items = line.split("\t")
        gid = items[0]  # gl bid
        raw_shape = items[1]
        try:
            shape = shapely.wkt.loads(raw_shape)
        except:
            print line

        result = checkIntersection(gid, shape)

        if len(result) == 0:
            no_intersection.write(gid + "\n")
        else:
            out = gid
            for temp in result:
                out = out + "\t" + temp
            intersection.write(out + "\n")

    gaode_aoi_file.close()
    no_intersection.close()
    intersection.close()


main()
