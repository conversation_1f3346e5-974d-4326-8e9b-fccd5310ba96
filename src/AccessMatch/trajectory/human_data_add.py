"""
添加1.0 的人工作业任务

# 更新match_history
# 添加到
"""
import dataclasses
from typing import List
from src.AccessMatch.pg_tool import PgTool
import datetime
from src.common import csv_tools
import tqdm

today = datetime.date.today().strftime("%Y%m%d")
bids_strategy_file = 'all_bids/exp_traj_bids_strategy.tsv'
bids_export_file = "all_bids/exp_traj_manual.csv"


@dataclasses.dataclass
class Record:
    """
    入库集合
    """
    bid: str
    node_id: str
    geom: str
    uid: int = ''
    ways: str = '提招'
    source: str = '经验轨迹挖掘'
    batch_id: str = 'exp_traj_search_' + today
    batch_name: str = '经验轨迹挖掘_人工作业_' + today


@dataclasses.dataclass
class Ctx:
    """
    导出数据
    """
    records: List[Record] = dataclasses.field(default_factory=list)


def add_manual(ctx: Ctx):
    """
    添加人工记录（去重）
    :param ctx:
    :return:
    """
    idx = 0
    with PgTool() as pgsql:
        for item in tqdm.tqdm(ctx.records):
            sql = 'select * from gate_aoi_match_check_data where bid=%s and node_id=%s'
            pgsql.cursor_poi.execute(sql, [item.bid, item.node_id])
            ret = pgsql.cursor_poi.fetchone()
            if ret:
                print(f"已存在，跳过:{item.uid},{item.node_id}")
                continue
            else:
                # sql = """
                #         insert into gate_aoi_match_check_data (bid, node_id, source, ways, uid)
                #         values(%s, %s, %s, %s, %s)
                #     """
                # pgsql.cursor_poi.execute(sql, [item.bid, item.node_id, item.source, "提召", item.uid])

                # 一体化关联关系作业情报库
                sql = """
                       insert into gate_aoi_match_check_data_tmp(bid, node_id, source, ways, status)
                       values(%s, %s, %s, %s, %s)
                    """
                pgsql.cursor_poi.execute(sql, [item.bid, item.node_id, item.source, '提招', 0])
                pgsql.commit()
                idx += 1


def load_data(ctx: Ctx):
    """
    加载数据
    :param ctx:
    :return:
    """
    df = csv_tools.read_csv_as_dataframe(bids_strategy_file,
                                         converters={"aoi_bid": str, 'node_id': str, 'msg': str})
    for idx, item in df.iterrows():
        msg = item['msg']
        if msg:
            continue
        record = Record(item['aoi_bid'], item['node_id'], item['node_wkt'])
        record.uid = f"{record.node_id}_{record.bid}_{today}"
        ctx.records.append(record)


def export(ctx: Ctx):
    """
    导出入库的备份数据
    :param ctx:
    :return:
    """
    csv_tools.list_map_to_csv([dataclasses.asdict(x) for x in ctx.records], bids_export_file)


def run(ctx: Ctx):
    """
    处理数据
    :param ctx:
    :return:
    """
    load_data(ctx)
    export(ctx)
    add_manual(ctx)


if __name__ == '__main__':
    _ctx = Ctx()
    run(_ctx)
