"""
整合任务, 创建计划
"""
import datetime
import os
from pathlib import Path

import requests
from src.aoi.aoi_protection import cfg

file_save_path = Path(os.path.abspath(__file__)).parent / 'file_upload'
file_save_path.mkdir(exist_ok=True, parents=True)


def get_token(basic_url):
    """
    获取token
    :param basic_url:
    :return:
    """
    req = requests.post(
        url=basic_url + "/account/login",
        data={'user_email': '<EMAIL>', 'user_password': 'pw123456'}
    )
    return req.json()['data']['access_token']


def upload_file(basic_url, filename):
    """
    上传文件
    :param basic_url:
    :param filename:
    :return:
    """
    req = requests.post(
        url=basic_url + "/compat/upload",
        files={"file": open(filename, "rb")}
    )
    url = cfg.DE_OSS_BASE_PATH + req.text
    print(f"上传路径:{filename}, 文件链接:{url}")
    return url


def create_aoi_pre_plan(basic_url, file_path, batch_id, strategy_type, src):
    """
    创建任务
    :param basic_url:
    :param file_path:
    :param batch_id:
    :param strategy_type:
    :param src:
    :return:
    """
    kwargs = {
        'file_url': upload_file(basic_url, file_path),
        'batch': batch_id,
        # 下发任务都是
        'strategy_type': cfg.MANUAL_COMMON,
        'src': src,
        'src_type': 0
    }
    print("创建计划请求参数: ", kwargs)
    req = requests.post(
        url=basic_url + "/plan/strategy/upload",
        json=kwargs,
        headers={'token': get_token(basic_url)}
    )
    return req.json()


def create_preview(basic_url, bid_list, batch_id, batch_type):
    """
    创建预览计划
    :param basic_url
    :param bid_list:
    :param batch_id:
    :param batch_type
    :return:
    """
    bid_list = list(set(bid_list))
    if len(bid_list) < 1:
        print("无任务需要下发")
        return
    current = datetime.datetime.now().strftime("%Y%m%d%H%M")
    file_name = file_save_path / f"{batch_id}_{batch_type}_{current}.txt"
    with open(file_name, 'w') as f:
        f.writelines("\n".join(bid_list))
    create_aoi_pre_plan(basic_url, file_name, batch_id, batch_type, file_name.name)


if __name__ == '__main__':
    pass
