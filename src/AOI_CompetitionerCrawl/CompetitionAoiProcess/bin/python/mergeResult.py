# encoding=gbk
"""
merge result
"""
import sys
import os


def main():
    """
    :return: void
    """
    InputFile = open(sys.argv[1])
    OutputFile1 = open(sys.argv[2], "w")
    aoi_poi_sim = dict()
    faceid_set = set()

    while True:
        lines = InputFile.readlines(1000)
        if not lines:
            break

        for line in lines:

            line = line.strip("\r\n")
            items = line.split("\t")
            if len(items) != 7:
                continue
            sim = items[0]
            score = float(items[2])
            faceId = items[3].strip()
            bid = items[4].strip()
            aoi_name = items[5]
            poi_name = items[6]
            # faceId = faceId+":"+aoi_name
            faceid_set.add(faceId)
            if sim == "same":
                if faceId not in aoi_poi_sim.keys():
                    aoi_poi_sim[faceId] = [score]

                if score == aoi_poi_sim[faceId][0]:
                    aoi_poi_sim[faceId].append(bid)  # +":"+poi_name)

                if score > aoi_poi_sim[faceId][0]:
                    aoi_poi_sim[faceId] = [score]
                    aoi_poi_sim[faceId].append(bid)  # +":"+poi_name)

    for faceId in aoi_poi_sim:
        items = aoi_poi_sim[faceId]
        score = items[0]
        line = faceId.strip() + "\t"
        if float(score) > 0.8:
            for i in range(1, len(items)):
                line += items[i] + "\t"

            print >> OutputFile1, "%s" % (line)


main()
