# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AOI 完整性例行更新
"""
import datetime
import json

import os
import sys
import uuid
import time
from pathlib import Path
from shapely import wkt

root_dir = Path(os.path.abspath(__file__)).parents[3]
print(root_dir)
sys.path.insert(0, root_dir.as_posix())
sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext

SEND_MEG_TOKEN = "d0394428b72d096f84ccb56fc1269345c"
GROUP_ID = 7749482
from src.common import common_tool
import traceback
from src.door_naming.util import set_cache, get_cache

today_str = datetime.date.today().strftime("%Y-%m-%d")

KEY_aoi_complete_run = 'cache_key_aoi_complete_run'

from src.aoi.aoi_complete import accurate_upgrade
from src.aoi.aoi_complete import agg_aoi_strategy
from src.aoi.aoi_complete import resume
from src.aoi.aoi_statistics import accurate_aoi_v2
from src.aoi.aoi_monitor import gate_semantic_discount
from src.common import pgsql
from multiprocessing import Pool, Manager

# 未匹配到大门的AOI
AOI_COMPLETE_NOT_MATCH = 0
# 普通AOI 有门
AOI_COMPLETE_NORMAL = 1
# 基础AOI 有进出大门
AOI_COMPLETE_BASIC = 2
# 精准AOI
AOI_COMPLETE_ACCURATE = 3
# 精准AOI2.0
AOI_COMPLETE_ACCURATE_V2 = 4
# 高置信大门与PV表

# 特殊场景主点tag，主点再以下列表中且为紧急门时，任务大门可进可出
SPECIAL_SCENE_TGA = ['教育培训;幼儿园', '教育培训;小学', '教育培训;中学']

# 特殊来源场景与指定tag只算普通AOI
NORMAL_AOI_SRC = ['SD']
NORMAL_AOI_TAG = ['酒店;其他', '酒店;快捷酒店', '酒店', '酒店;公寓式酒店', '酒店;民宿', '购物;商铺']
# FILTER_MEMO = ['300w 冲量村委会 ', '300w 冲量村委会', '260w C端冲量 ', '260w C端冲量']
FILTER_MEMO = []

# 精准AOI分垂类建设；仅依赖边框建设；不依赖大门建设
BUILD_TAG_DEPEND_FRAME = ['交通设施;长途汽车站', '购物;购物中心', '文化传媒;展览馆', '休闲娱乐;剧院',
                          '运动健身;体育场馆',
                          '旅游景点;风景区', '旅游景点;公园', '旅游景点;文物古迹', '旅游景点', '旅游景点;其他',
                          '旅游景点;博物馆', '旅游景点;游乐园', '旅游景点;寺庙',
                          '旅游景点;景点', '旅游景点;海滨浴场', '旅游景点;动物园', '旅游景点;水族馆', '旅游景点;教堂',
                          '旅游景点;植物园', '房地产;写字楼', '购物;市场',
                          '购物;家居建材', '购物', '购物;百货商场', '休闲娱乐;休闲广场', '休闲娱乐;度假村',
                          '休闲娱乐;剧院', '文化传媒;文化宫', '文化传媒;美术馆',
                          '酒店;星级酒店']
BUILD_TAG_DEPEND_GATE = []

# 大门通行性
GATE_TRAFFICABILITY_NO = '0'  # 紧急门=不可通行
GATE_TRAFFICABILITY_OUT = '1'
GATE_TRAFFICABILITY_IN = '2'
GATE_TRAFFICABILITY_IN_OUT = '3'

# 内部路FORM
NAV_LINK_FORM_INNER_ROAD = '52'

# 道路大门类型
NAV_GATE_TYPE_URGENT = 0  # 紧急大门

# 日志记录
IS_IMPORT_LOG = False
IMPORT_LOG_TIME = time.strftime("%Y-%m-%d", time.localtime())
# LOG_CONTENT = {}

# AOI层级
AOI_LEVEL_AGG = 1
AOI_LEVEL_BASIC = 2
AOI_LEVEL_INNER = 3

# 竞品门buffer距离
POI_GATE_INTELLIGENCE_DISTANCE = 0.00030

# blu_face_complete_data 数据临时文件
BLU_FACE_COMPLETE_DATA_FILE = "/home/<USER>/aoi-strategy/blu_face_complete_to_poi.csv"


#

def accurate_aoi_diff(ctx: AoiCompleteContext):
    """
    精准AOI diff 统计
    :return:
    """
    diff_accurate_aoi_list = ctx.pg_query_dao.get_diff_accurate_aoi_list()
    if diff_accurate_aoi_list is None or len(diff_accurate_aoi_list) == 0:
        return
    for diff_accurate_aoi in diff_accurate_aoi_list:
        data = {'reason': '', 'bid': diff_accurate_aoi[2], 'log': '', 'pre_log': '', 'date_day': IMPORT_LOG_TIME,
                'face_id': diff_accurate_aoi[0],
                'level': diff_accurate_aoi[4] if diff_accurate_aoi[4] is not None else 0,
                'pre_level': diff_accurate_aoi[1]}
        # 查询POI信息
        poi_info = ctx.pg_query_dao.get_poi_by_bid(diff_accurate_aoi[2])
        if poi_info is None or len(poi_info) == 0:
            data['reason'] = 'POI失效'
        # 查询AOI层级
        if data['reason'] == '':
            aoi_info = ctx.pg_query_dao.get_aoi_by_bid(diff_accurate_aoi[2])
            if aoi_info is None or len(aoi_info) == 0:
                data['reason'] = '主点关联变更'
            elif aoi_info[3] != 2:
                data['reason'] = '层级变更'
        if data['reason'] == '':
            data['reason'] = '关联大门变更'
        # 查询log
        accurate_log_list = ctx.pg_query_dao.get_accurate_log_by_bid(diff_accurate_aoi[2])
        if accurate_log_list is None or len(accurate_log_list) == 0:
            continue
        data['log'] = accurate_log_list[0][2]
        if len(accurate_log_list) > 1:
            data['pre_log'] = accurate_log_list[1][2]
        ctx.pg_rw_dao.insert_accurate_aoi_diff(data)
    print(f"开始清除diff 日志")
    ctx.pg_rw_dao.delete_expire_accurate_log()


def aoi_complete_run_by_bid(ctx: AoiCompleteContext, bid_list):
    """
    通过BID更新AOI等级
    :param ctx: 上下文
    :param bid_list: bids
    :return:
    """
    invalid_aoi_face_list = []
    aoi_log_list = []
    intercept_logs = []
    for bid in bid_list:
        ctx.LOG_CONTENT = {}
        aoi = ctx.pg_query_dao.get_strategy_aoi_by_bid(bid)
        poi_info = ctx.pg_query_dao.get_poi_by_bid(aoi[1])
        protect_data = None  # 是否需高通量保护
        print(f"主点tag：{poi_info}")
        if poi_info is None or len(poi_info) == 0:
            invalid_aoi_face_list.append(poi_info[0])
            continue
        aoi_complete = AOI_COMPLETE_NOT_MATCH
        # 特殊来源与指定tag赋为普通AOI，不走精准AOI判断策略
        if aoi[5] in NORMAL_AOI_SRC or poi_info[0] in NORMAL_AOI_TAG or aoi[6] in FILTER_MEMO:
            aoi_complete = AOI_COMPLETE_NORMAL
        else:
            # 仅边框策略；满足指定垂类边框质量达标即转精准
            if poi_info[0] in BUILD_TAG_DEPEND_FRAME:
                aoi_complete = AOI_COMPLETE_ACCURATE
                print(f"仅边框判断策略：{aoi_complete}")
            elif aoi[3] == AOI_LEVEL_BASIC:
                aoi_complete = aoi_complete_calculate(ctx, aoi[2], aoi[1], poi_info)
                if aoi_complete >= AOI_COMPLETE_ACCURATE:
                    # 当前是精准状态,不需要保护策略
                    protect_data = None
                else:
                    protect_data = calculate_aoi_protect_data(ctx, bid, aoi_complete)
            elif aoi[3] == AOI_LEVEL_AGG:
                aoi_complete = agg_aoi_strategy.agg_aoi_statistics(ctx, aoi[1], poi_info)
        aoi_complete = aoi[4] if aoi[4] is not None and aoi[
            4] > AOI_COMPLETE_ACCURATE and aoi_complete >= AOI_COMPLETE_ACCURATE else aoi_complete
        if aoi_complete == AOI_COMPLETE_ACCURATE and ctx.pg_query_dao.bool_gate_semantics(bid):
            aoi_complete = AOI_COMPLETE_ACCURATE_V2
        print(f"AOI层级：{aoi_complete}")
        complete_info_id = str(uuid.uuid4()).replace("-", "")
        blu_face_complete_data = {"complete_info_id": complete_info_id, "main_bid": aoi[1], "face_id": aoi[0],
                                  "aoi_complete": aoi_complete,
                                  "update_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
        print("更新数据库")
        # print(LOG_CONTENT)
        # if protect_data:
        #     protect_data['log'] = json.dumps(ctx.LOG_CONTENT)
        #     intercept_logs.append(protect_data)
        #     print(f"{bid}, 触发保护策略,不更新退化")
        #     continue
        ctx.pg_rw_dao.insert_or_update_blu_face_complete(blu_face_complete_data)
        aoi_log_list.append(
            {'bid': aoi[1], 'log_content': json.dumps(ctx.LOG_CONTENT), 'date_day': IMPORT_LOG_TIME})
    ctx.pg_rw_dao.batch_insert_accurate_aoi_log(aoi_log_list)
    ctx.pg_rw_dao.batch_insert_protect_high_throughput_intercept_log(intercept_logs)
    # filter_delete_aoi(ctx, invalid_aoi_face_list)


def filter_delete_aoi(ctx: AoiCompleteContext, invalid_bid_list):
    """
    AOI 完整性等级过滤已经被删除的AOI
    之前是根据face_id删除的,换绑主点+白名单会出现bug,这里统一按照bid处理
    :return:
    """
    ctx.reconnect()
    already_delete_aoi_list = ctx.pg_query_dao.get_diff_blu_face_complete()
    if already_delete_aoi_list is not None and len(already_delete_aoi_list) > 0:
        bid_list = [aoi[0] for aoi in already_delete_aoi_list]
        invalid_bid_list = invalid_bid_list + bid_list
    print(f"删除失效AOI完整性数量：{len(already_delete_aoi_list)}, {len(invalid_bid_list)}")
    res = ctx.pg_rw_dao.delete_blu_face_complete(invalid_bid_list)
    print("数据删除完成：{}".format(res))


def calculate_aoi_protect_data(ctx: AoiCompleteContext, bid, new_aoi_complete):
    """
    高通量数据保护策略
    旧有 aoi_complete 为 3\4 需要降级为 0\1\2 时候需要拦截
    :param ctx:
    :param bid:
    :param new_aoi_complete:
    :return is_need_protect:
    """
    origin_aoi_complete_info = ctx.pg_query_dao.get_blu_face_complete_by_bid(bid)
    if origin_aoi_complete_info is None or len(origin_aoi_complete_info) == 0:
        return None

    # 旧有 aoi_complete 需为 3\4, 其他直接返回
    if origin_aoi_complete_info[1] not in [AOI_COMPLETE_ACCURATE, AOI_COMPLETE_ACCURATE_V2]:
        return None

    # 新 aoi_complete 需为 0\1\2, 其他直接返回
    if new_aoi_complete not in [AOI_COMPLETE_NOT_MATCH, AOI_COMPLETE_NORMAL, AOI_COMPLETE_BASIC]:
        return None

    # 查看是否在保护清单
    protect_data = ctx.pg_query_dao.get_protect_high_hroughput(bid)
    if protect_data is None or len(protect_data) == 0:
        return None

    # 构建拦截记录
    data = {
        'bid': bid,
        'pv': protect_data[1],
        'std_tag': protect_data[2],
        'date_day': IMPORT_LOG_TIME,
        'pre_complete': origin_aoi_complete_info[1],
        'complete': new_aoi_complete,
        'pre_log': '',
        'log': '',
        'reason': '',
    }

    # 查询POI信息
    poi_info = ctx.pg_query_dao.get_poi_list_by_bid_list([bid])
    if poi_info is None or len(poi_info) == 0:
        data['reason'] = 'POI失效'
    # 查询AOI层级
    if data['reason'] == '':
        aoi_info = ctx.pg_query_dao.get_aoi_by_bid(bid)
        if aoi_info is None or len(aoi_info) == 0:
            data['reason'] = '主点关联变更'
        else:
            ctx.LOG_CONTENT['pre_aoi_level'] = aoi_info[3]
            ctx.LOG_CONTENT['aoi_level'] = 2
            data['reason'] = '层级变更' if aoi_info[3] != 2 else ''
    if data['reason'] == '':
        data['reason'] = '关联大门变更'

    # 查询log
    accurate_log_list = ctx.pg_query_dao.get_accurate_log_by_bid(bid)
    if accurate_log_list is not None and len(accurate_log_list) > 0:
        data['pre_log'] = accurate_log_list[0][2]

    return data


def aoi_complete_calculate(ctx: AoiCompleteContext, aoi_geom, bid, poi_info):
    """
    AOI完整等级计算
    :param aoi_geom:
    :param bid:
    :param poi_info:
    :return level:
    """
    aoi_complete_level = AOI_COMPLETE_NOT_MATCH
    aoi_node_list = ctx.pg_query_dao.get_node_by_main_bid(bid)
    print("人工作业大门+策略匹配大门：{}".format(aoi_node_list))
    ctx.LOG_CONTENT['match_node'] = [tuple(row) for row in aoi_node_list]
    if aoi_node_list is None or len(aoi_node_list) == 0:
        return aoi_complete_level
    node_id_list = [node[0] for node in aoi_node_list]
    # 存在大门则为普通AOI
    aoi_complete_level = AOI_COMPLETE_NORMAL

    # 基础AOI计算
    aoi_complete_basic_level = basic_aoi_cal(ctx, node_id_list, poi_info, bid)
    print(f"基础AOI计算结果：{aoi_complete_basic_level}")
    if aoi_complete_basic_level != AOI_COMPLETE_BASIC:
        return aoi_complete_basic_level

    # 精准AOI计算
    aoi_complete_level, _ = accurate_aoi_cal(ctx, node_id_list, poi_info, aoi_geom)
    print(f"精准AOI计算：{aoi_complete_level}")
    # 是否通过大门情报过滤精准AOI
    is_filter_by_gate_info = False
    # 查询AOI当前层级信息，如果是新增的精准AOI则需要通过情报大门过滤
    origin_aoi_complete_info = ctx.pg_query_dao.get_blu_face_complete_by_bid(bid)
    if origin_aoi_complete_info is not None and len(origin_aoi_complete_info) > 0:
        if origin_aoi_complete_info[1] < AOI_COMPLETE_ACCURATE:
            is_filter_by_gate_info = True
        else:
            aoi_complete_level = origin_aoi_complete_info[1]

    # 如果符合精准AOI的判断策略，再新增大门情报过滤, 现在不检测人工作业的情报
    return aoi_complete_level


# def filter_accurate_by_gate_info(ctx: AoiCompleteContext, bid):
#     """
#     通过大门情报过滤精准AOI，如果精准AOI的挖掘情报未下发作业或者作业未完成则认为非精准AOI
#     :param bid:
#     :return:
#     """
#     gate_info_list = ctx.pg_query_dao.get_not_push_gate_info_by_bid(bid)
#     print("大门情报查询未作业：{}".format(gate_info_list))
#     if gate_info_list is None or len(gate_info_list) == 0:
#         return True
#     return False


def basic_aoi_cal(ctx: AoiCompleteContext, node_id_list, poi_info, bid):
    """
    基础AOI计算
    :param ctx:
    :param node_id_list:
    :param poi_info:
    :param bid
    :return level:
    """
    level = AOI_COMPLETE_NORMAL
    in_flag = out_flag = False
    # 如果AOI是特殊场景且是紧急门则认为可进可出
    is_special_scene = False
    if poi_info is not None and poi_info[0] in SPECIAL_SCENE_TGA:
        is_special_scene = True
    high_gate_node_map = get_high_gate_by_node(ctx, node_id_list)
    print(f"高置信大门:{high_gate_node_map}")
    ctx.LOG_CONTENT['basic_high_gate_traffic'] = high_gate_node_map
    # 使用高置信大门判断是否存在可进入的大门，存在就是基础AOI
    for short_node_id, high_gate in high_gate_node_map.items():
        gate_traversability_list = list(set(high_gate[1].split(',')))
        # 特殊场景且是紧急门则认为可进出
        if (is_special_scene and (
                len(gate_traversability_list) == 1 and gate_traversability_list[0] == GATE_TRAFFICABILITY_NO)) \
                or GATE_TRAFFICABILITY_IN_OUT in gate_traversability_list:
            in_flag = out_flag = True
            break
        if GATE_TRAFFICABILITY_OUT in gate_traversability_list:
            out_flag = True
        if GATE_TRAFFICABILITY_IN in gate_traversability_list:
            in_flag = True
    print("高置信大门计算结果：in_flag：{}，out_flag：{}".format(in_flag, out_flag))
    if in_flag and out_flag:
        return AOI_COMPLETE_BASIC

    # 通过Node大门通行属性判断是否存在可进出的大门
    nav_gate_link_list = ctx.pg_query_dao.get_nav_gate_and_link_by_node(node_id_list)
    print(f"道路库大门:{nav_gate_link_list}")
    ctx.LOG_CONTENT['basic_road_gate_traffic'] = [tuple(row) for row in nav_gate_link_list]
    if nav_gate_link_list is None or len(nav_gate_link_list) == 0:
        return level
    for nav_gate_link in nav_gate_link_list:
        node_id = nav_gate_link[3]
        in_link_form = nav_gate_link[0]
        out_link_form = nav_gate_link[1]
        nav_gate_type = nav_gate_link[6]

        # 过滤高通量大门中已查询的Node
        if node_id in high_gate_node_map:
            continue
        # 特殊场景紧急门则可入可出
        if nav_gate_type == NAV_GATE_TYPE_URGENT:
            if is_special_scene:
                in_flag = out_flag = True
                break
            continue

        # in_link 属于内部路 说明可出；此处详细策略可参考
        # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/CgIKhBCL9KWYDi
        if NAV_LINK_FORM_INNER_ROAD in in_link_form.split(','):
            out_flag = True
        # out_link 属于内部路 说明可入
        if NAV_LINK_FORM_INNER_ROAD in out_link_form.split(','):
            in_flag = True
    print("Node大门通行属性：in_flag：{}，out_flag：{}".format(in_flag, out_flag))
    if (in_flag and not out_flag) or (not in_flag and out_flag):
        print("不满足一入一出：bid:{}".format(bid))
    if not in_flag and not out_flag:
        level = check_poi_gate_intelligence(ctx, bid)
        print(f"只关联紧急门：bid:{bid}, aoi_complete:{level}")
        return level
    if in_flag and out_flag:
        return AOI_COMPLETE_BASIC
    return level


def check_poi_gate_intelligence(ctx: AoiCompleteContext, bid: str):
    """
    竞品POI门判断；如果当前AOI属于封闭区域(关联大门都是紧急门)，且竞品所有POI门buffer30米范围内存在已关联大门则认为精准1.0
    1、当前经过评估之后仅上线单门场景
    https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-10926/show?source=copy-shortcut
    :param ctx:
    :param bid:
    :return:
    """
    # 查询竞品POI门数据
    poi_gate_intelligence_list = ctx.pg_query_dao.get_poi_intelligences_by_bids([bid])
    if poi_gate_intelligence_list is None or len(poi_gate_intelligence_list) == 0:
        return AOI_COMPLETE_NORMAL
    aoi_node_list = ctx.pg_query_dao.get_node_by_main_bid(bid)
    if aoi_node_list is None or len(aoi_node_list) == 0:
        return AOI_COMPLETE_NORMAL
    # 当前经过评估之后仅上线单门场景
    if len(poi_gate_intelligence_list) != len(aoi_node_list):
        return AOI_COMPLETE_NORMAL
    # 判断竞品POI门附近是否存在关联的Node大门
    for poi_gate_intelligence in poi_gate_intelligence_list:
        poi_gate_intelligence_shape = wkt.loads(poi_gate_intelligence[1])
        is_exists = False
        for aoi_node in aoi_node_list:
            aoi_node_shape = wkt.loads(aoi_node[1])
            if poi_gate_intelligence_shape.distance(aoi_node_shape) < POI_GATE_INTELLIGENCE_DISTANCE:
                is_exists = True
                break
        if not is_exists:
            return AOI_COMPLETE_NORMAL
    return AOI_COMPLETE_ACCURATE


def accurate_aoi_cal(ctx: AoiCompleteContext, node_id_list, poi_info, aoi_geom):
    """
    精准AOI计算
    :param ctx:
    :param node_id_list:
    :param poi_info:
    :param aoi_geom:
    :return:
    """
    level = AOI_COMPLETE_BASIC
    # 缺失的情报Node
    deletion_node_id_list = []
    # 如果AOI是特殊场景且是紧急门则认为可进可出
    is_special_scene = False
    if poi_info is not None and poi_info[0] in SPECIAL_SCENE_TGA:
        is_special_scene = True

    # 查询与AOI相交道路大门并过滤内内大门
    intersects_nav_gate_node_list = get_intersects_nav_gate_by_aoi(ctx, aoi_geom)
    print("AOI范围内大门：{}".format(intersects_nav_gate_node_list))
    ctx.LOG_CONTENT['aoi_intersection_node'] = intersects_nav_gate_node_list
    if intersects_nav_gate_node_list is None or len(intersects_nav_gate_node_list) == 0:
        return AOI_COMPLETE_ACCURATE, deletion_node_id_list

    # 人工作业过滤Node
    intersects_nav_gate_node_list = manual_work_result_filter(ctx, list(set(intersects_nav_gate_node_list)))
    print("人工作业Node过滤：{}".format(intersects_nav_gate_node_list))

    relation_node_id = node_id_list
    node_id_list = [*node_id_list, *intersects_nav_gate_node_list]
    high_gate_long_node_map = get_high_gate_by_node(ctx, node_id_list)
    nav_node_list = ctx.pg_query_dao.get_nav_gate_by_node(node_id_list)
    print(f"精准AOI高置信大门信息：{high_gate_long_node_map}, 道路通行性：{nav_node_list}")
    ctx.LOG_CONTENT['accurate_high_gate_traffic'] = high_gate_long_node_map
    ctx.LOG_CONTENT['accurate_road_gate_traffic'] = [tuple(row) for row in nav_node_list]
    filter_node_id = []

    # 常规场景需要过滤紧急门，特殊场景不过滤；
    for nav_node in nav_node_list:
        node_id = nav_node[0]
        if is_special_scene:
            filter_node_id.append(node_id)
            continue
        # 优先判断高置信门
        if node_id in high_gate_long_node_map:
            node_trafficability_list = list(set(high_gate_long_node_map[node_id][1].split(',')))
            if len(node_trafficability_list) == 1 and node_trafficability_list[0] == GATE_TRAFFICABILITY_NO:
                continue
            filter_node_id.append(node_id)
        elif nav_node[3] != NAV_GATE_TYPE_URGENT:
            filter_node_id.append(node_id)

    if len(filter_node_id) == 0:
        return level, deletion_node_id_list
    if set(relation_node_id) == set(filter_node_id):
        print("过滤后大门 = 母库已匹配大门：filter_node_id:{}".format(set(filter_node_id)))
        return AOI_COMPLETE_ACCURATE, deletion_node_id_list
    print("所有大门：{},已关联大门：{}".format(filter_node_id, relation_node_id))
    is_accureate_aoi, deletion_node_id_list = accurate_aoi_cal_by_pv(ctx, list(set(filter_node_id)),
                                                                     list(set(relation_node_id)))
    if is_accureate_aoi:
        return AOI_COMPLETE_ACCURATE, deletion_node_id_list
    return level, deletion_node_id_list


def manual_work_result_filter(ctx: AoiCompleteContext, node_id_list):
    """
    人工作业大门过滤
    :param ctx:
    :param node_id_list:
    :return:
    """
    # 获取人工作业无须关联的大门Node
    no_match_node_list = ctx.pg_query_dao.get_manual_no_match_node_by_node_id(node_id_list)
    if no_match_node_list is None or len(no_match_node_list) == 0:
        return node_id_list
    for no_match_node in no_match_node_list:
        node_id_list.remove(no_match_node[0])
    return node_id_list


def accurate_aoi_cal_by_pv(ctx: AoiCompleteContext, all_node_id_list, relation_node_id_list):
    """
    通过 PV 计算是否符合精准AOI；PV 占比90%的大门都已经关联
    :param ctx:
    :param all_node_id_list:
    :param relation_node_id_list:
    :return is_accureate_aoi: 是否精准AOI；deletion_node_id：缺失NodeID
    """
    is_accurate_aoi = False
    deletion_node_id = []
    # 根据大门PV排序，如果前90%大门都已经关联则认为是精准AOI
    gate_pv_list = ctx.pg_query_dao.get_gate_30d_pv_by_node_new('', all_node_id_list)
    print("NodePv值:{}".format(gate_pv_list))
    ctx.LOG_CONTENT['pv_node'] = [tuple(row) for row in gate_pv_list]
    if gate_pv_list is None or len(gate_pv_list) == 0:
        return True, deletion_node_id
    pv_total = 0
    for pv_node_info in gate_pv_list:
        pv_total += pv_node_info[1]
    if pv_total == 0 and len(gate_pv_list) == len(all_node_id_list):
        print("pv_total:0,pv_node_info_num == all_num")
        return True, deletion_node_id
    # 因为有一个node关联一个bid的判断,所以这个值不太对,简单校验下就行
    pv_total_90 = pv_total * 0.5
    # 计算高pv node
    high_pv_node_id = set()
    temp_pv_num = 0
    for pv_node_info in gate_pv_list:
        if temp_pv_num < pv_total_90:
            high_pv_node_id.add(pv_node_info[0])
        temp_pv_num += pv_node_info[1]

    print("高PV node_id:{}".format(high_pv_node_id))
    ctx.LOG_CONTENT['high_pv_node'] = list(high_pv_node_id)
    is_accureate_aoi = True
    for node_id in high_pv_node_id:
        if node_id not in relation_node_id_list:
            is_accureate_aoi = False
            deletion_node_id.append(node_id)
            print("高PV不在匹配列表：node_id:{}".format(node_id))
    ctx.LOG_CONTENT['lose_node_id'] = deletion_node_id
    return is_accureate_aoi, deletion_node_id


def get_high_gate_by_node(ctx: AoiCompleteContext, node_id_list):
    """
    获取高置信大门
    :param ctx:
    :param node_id_list:
    :return long_node_to_high_gate_map long_node_id=>[nodeid,traversability,car_restric]:
    """
    long_node_to_high_gate_map = {}
    # 查询短Node
    node_id_list = ctx.pg_query_dao.get_short_node_by_long_node(node_id_list)
    if node_id_list is None or len(node_id_list) == 0:
        return long_node_to_high_gate_map
    short_node_id_list = [short_node[1] for short_node in node_id_list]
    short_to_long_node_map = {short_node[1]: short_node[0] for short_node in node_id_list}
    # 查询高置信大门
    high_gate_list = ctx.pg_query_dao.get_high_gate_by_node_new('', short_node_id_list)
    if high_gate_list is None or len(high_gate_list) == 0:
        return long_node_to_high_gate_map
    for high_gate in high_gate_list:
        long_node_id = short_to_long_node_map[high_gate[0]]
        long_node_to_high_gate_map[long_node_id] = tuple(high_gate)
    return long_node_to_high_gate_map


def get_intersects_nav_gate_by_aoi(ctx: AoiCompleteContext, geom):
    """
    获取AOI范围内的道路大门，过滤内内大门
    :param ctx:
    :param geom:
    :return [node_id]:
    """
    intersects_gate_node_id = []
    # 查询相交Node
    intersects_node_list = ctx.pg_query_dao.get_intersects_node_by_geom(geom)
    if intersects_node_list is None or len(intersects_node_list) == 0:
        return intersects_gate_node_id
    intersects_node_id_list = [intersects_node[0] for intersects_node in intersects_node_list]

    # 根据Node查询道路大门
    intersects_nav_gate_list = ctx.pg_query_dao.get_nav_gate_by_node(intersects_node_id_list)
    if intersects_nav_gate_list is None or len(intersects_nav_gate_list) == 0:
        return intersects_gate_node_id

    # 通过判断nav_link form过滤相交大门的内内大门
    link_id_list = set()
    link_id_to_node_map = {}
    for intersects_nav_gate in intersects_nav_gate_list:
        link_id_list.add(intersects_nav_gate[1])
        link_id_list.add(intersects_nav_gate[2])
        link_id_to_node_map[intersects_nav_gate[1]] = intersects_nav_gate[0]
        link_id_to_node_map[intersects_nav_gate[2]] = intersects_nav_gate[0]
    nav_link_list = ctx.pg_query_dao.get_nav_link_by_link_id_form(list(link_id_list))
    if nav_link_list is None or len(nav_link_list) == 0:
        return intersects_gate_node_id

    for nav_link in nav_link_list:
        link_id = nav_link[0]
        intersects_gate_node_id.append(link_id_to_node_map[link_id])
    return intersects_gate_node_id


def sync_blu_face_complete_to_poi(ctx: AoiCompleteContext):
    """
    同步blu_face_complete到poi_online
    :return:
    """
    ctx.reconnect()
    if os.path.exists(BLU_FACE_COMPLETE_DATA_FILE):
        os.remove(BLU_FACE_COMPLETE_DATA_FILE)
    ctx.pg_rw_dao.sync_blu_face_complete_to_poi(BLU_FACE_COMPLETE_DATA_FILE)
    print("同步blu_face_complete到poi_online完成")


def after_calc():
    """
    后处理
    :return:
    """
    KEY_agg_aoi_2 = 'aoi_complete_agg_aoi_2'
    KEY_accurate_upgrade = 'aoi_complete_accurate_upgrade'
    KEY_accurate_aoi_v2 = 'aoi_complete_accurate_aoi_v2'
    KEY_gate_semantic_discount = 'aoi_complete_gate_semantic_discount'
    KEY_sync_blu_face_complete_to_poi = 'aoi_complete_sync_blu_face_complete_to_poi'
    KEY_update_invalid_restore_time = 'aoi_complete_update_invalid_restore_time'
    KEY_resume = 'aoi_complete_resume'
    # 后处理部分
    try:
        with AoiCompleteContext() as _ctx:
            filter_delete_aoi(_ctx, [])
            accurate_aoi_diff(_ctx)

            print("开始执行后处理:agg_aoi_2")
            succ_data = get_cache(KEY_agg_aoi_2)
            if succ_data and succ_data == today_str:
                print('今日已执行过，agg_aoi_2')
            else:
                agg_aoi_strategy.agg_aoi_2(_ctx)
                set_cache(KEY_agg_aoi_2, today_str)

            # 2.0例行更新策略
            print("开始执行后处理:progress")
            succ_data = get_cache(KEY_accurate_upgrade)
            if succ_data and succ_data == today_str:
                print('今日已执行过，accurate_upgrade')
            else:
                _ctx.reconnect()
                accurate_upgrade.progress(_ctx)
                set_cache(KEY_accurate_upgrade, today_str)

            # 2.0监控统计项
            print("开始执行后处理:accurate_aoi_v2")
            succ_data = get_cache(KEY_accurate_aoi_v2)
            if succ_data and succ_data == today_str:
                print('今日已执行过，accurate_aoi_v2')
            else:
                _ctx.reconnect()
                accurate_aoi_v2.accurate_aoi_v2(_ctx)
                set_cache(KEY_accurate_aoi_v2, today_str)

            # 大门折损统计
            # print("开始执行后处理:gate_semantic_discount")
            # succ_data = get_cache(KEY_gate_semantic_discount)
            # if succ_data and succ_data == today_str:
            #     print('今日已执行过，gate_semantic_discount')
            # else:
            #     _ctx.reconnect()
            #     gate_semantic_discount.run(_ctx)
            #     set_cache(KEY_gate_semantic_discount, today_str)

            # 同步blu_face_complete 到 poi_online
            succ_data = get_cache(KEY_sync_blu_face_complete_to_poi)
            if succ_data and succ_data == today_str:
                print('今日已执行过，sync_blu_face_complete_to_poi')
            else:
                _ctx.reconnect()
                sync_blu_face_complete_to_poi(_ctx)
                set_cache(KEY_sync_blu_face_complete_to_poi, today_str)

            # 失效恢复统计监控
            succ_data = get_cache(KEY_update_invalid_restore_time)
            if succ_data and succ_data == today_str:
                print('今日已执行过，update_invalid_restore_time')
            else:
                _ctx.reconnect()
                print("开始处理:update_invalid_restore_time")
                _ctx.pg_rw_dao.update_invalid_restore_time()
                set_cache(KEY_update_invalid_restore_time, today_str)

            msg = f"aoi_complete_cal.py - de16 -xxl_job-236  任务处理完成 "
            common_tool.send_hi(msg, ['chenbaojun_cd'], SEND_MEG_TOKEN, GROUP_ID)

    except Exception as e:
        print('执行后处理失败', e)
        print(traceback.format_exc())
        msg = f"aoi_complete_cal.py - de17 -xxl_job-236 后处理失败，原因:{e.args}, 请检查与恢复"
        common_tool.send_hi(msg, ['chenbaojun_cd'], SEND_MEG_TOKEN, GROUP_ID)
    finally:
        succ_data = get_cache(KEY_resume)
        if succ_data and succ_data == today_str:
            print('今日已执行过，resume')
        else:
            with AoiCompleteContext() as _ctx:
                resume.run(_ctx)
                set_cache(KEY_resume, today_str)


def before_calc_and_get_mesh_id():
    """
    预处理
    :return:
    """
    with AoiCompleteContext() as _ctx:
        _ctx.pg_rw_dao.blu_face_complete_back_up()
        sql = 'select distinct(mesh_id) from mesh_conf'
        all_data = pgsql.try_query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, try_times=10)
        all_mesh_ids = [x[0] for x in all_data]
        return all_mesh_ids


def aoi_complete_run_batch(mesh_id):
    """
    AOI完整性例行更新
    :return:
    """
    # 先执行备份
    face_id = ''
    limit_number = 5000
    current_num = 0
    invalid_aoi_face_list = []
    ctx = AoiCompleteContext()
    while True:
        # 获取母库AOI
        aoi_list = ctx.pg_query_dao.get_aoi_by_mesh_id(face_id, mesh_id, limit_number)
        if not aoi_list or len(aoi_list) == 0:
            print("计算完成")
            break
        aoi_log_list = []
        intercept_logs = []
        try:
            # 执行1000条记录， 如果执行这10000条中间有错误，可以忽略这一批
            for aoi in aoi_list:
                ctx.LOG_CONTENT = {}
                print("\n当前进度：{}, face_id：{}, mesh_id: {}, bid:{}".format(current_num, aoi[0], mesh_id, aoi[1]))
                current_num += 1
                face_id = aoi[0]
                protect_data = None
                # 查询主点信息
                bid = aoi[1]
                need_strategy = ctx.pg_query_dao.need_call_strategy(bid)
                if not need_strategy:
                    print(f"无需处理，直接跳过:{face_id}, {bid}")
                    continue
                poi_info = ctx.pg_query_dao.get_poi_by_bid(aoi[1])
                print(f"主点tag：{poi_info}")
                if poi_info is None or len(poi_info) == 0:
                    invalid_aoi_face_list.append(face_id)
                    continue

                aoi_complete = AOI_COMPLETE_NOT_MATCH
                # 特殊来源与指定tag赋为普通AOI，不走精准AOI判断策略
                if aoi[5] in NORMAL_AOI_SRC or poi_info[0] in NORMAL_AOI_TAG or aoi[6] in FILTER_MEMO:
                    aoi_complete = AOI_COMPLETE_NORMAL
                else:
                    # 仅边框策略；满足指定垂类边框质量达标即转精准
                    if poi_info[0] in BUILD_TAG_DEPEND_FRAME:
                        aoi_complete = AOI_COMPLETE_ACCURATE
                        print(f"仅边框判断策略：{aoi_complete}")
                    elif aoi[3] == AOI_LEVEL_BASIC:
                        aoi_complete = aoi_complete_calculate(ctx, aoi[2], aoi[1], poi_info)
                        if aoi_complete >= AOI_COMPLETE_ACCURATE:
                            protect_data = None
                        else:
                            protect_data = calculate_aoi_protect_data(ctx, bid, aoi_complete)
                    elif aoi[3] == AOI_LEVEL_AGG:
                        aoi_complete = agg_aoi_strategy.agg_aoi_statistics(ctx, aoi[1], poi_info)
                aoi_complete = aoi[4] if aoi[4] is not None and aoi[4] > AOI_COMPLETE_ACCURATE \
                                         and aoi_complete >= AOI_COMPLETE_ACCURATE else aoi_complete
                if aoi_complete == AOI_COMPLETE_ACCURATE and ctx.pg_query_dao.bool_gate_semantics(bid):
                    aoi_complete = AOI_COMPLETE_ACCURATE_V2
                print(f"AOI层级：{aoi_complete}")
                complete_info_id = str(uuid.uuid4()).replace("-", "")
                blu_face_complete_data = {"complete_info_id": complete_info_id, "main_bid": aoi[1], "face_id": aoi[0],
                                          "aoi_complete": aoi_complete,
                                          "update_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
                # if protect_data:
                #     protect_data['log'] = json.dumps(ctx.LOG_CONTENT)
                #     intercept_logs.append(protect_data)
                #     print(f"{bid} 触发高pv保护,不退化")
                #     continue
                ctx.pg_rw_dao.insert_or_update_blu_face_complete(blu_face_complete_data)
                aoi_log_list.append(
                    {'bid': aoi[1], 'log_content': json.dumps(ctx.LOG_CONTENT), 'date_day': IMPORT_LOG_TIME})
            # if len(aoi_log_list) > 0:
            #     ctx.pg_rw_dao.batch_insert_accurate_aoi_log(aoi_log_list)
            # if len(intercept_logs) > 0:
            #     # 插入拦截记录
            #     ctx.pg_rw_dao.batch_insert_protect_high_throughput_intercept_log(intercept_logs)
        except Exception as e:
            print(f"【ERROR】 执行任务出现异常，暂时跳过此批数据：错误原因：{e} 暂存数据 --- ")
            print(traceback.format_exc())
            try:
                # 再次尝试保存数据，如果保存不了，就放弃，保证不大面积失效
                if len(aoi_log_list) > 0:
                    ctx.pg_rw_dao.batch_insert_accurate_aoi_log(aoi_log_list)
                time.sleep(1)
            except Exception as e:
                pass


if __name__ == '__main__':
    # 获取图幅号
    import tqdm

    mesh_ids = before_calc_and_get_mesh_id()
    # 在开启多进程前一定要清理之前申明的数据库连接
    pgsql.clear_all_engine()
    pool_size = 10
    with Pool(pool_size) as p, Manager() as m:
        for _ in tqdm.tqdm(p.imap_unordered(aoi_complete_run_batch, mesh_ids), total=len(mesh_ids), desc='处理任务'):
            pass
    # 多进程后也要清理之前的engine
    pgsql.clear_all_engine()
    after_calc()
