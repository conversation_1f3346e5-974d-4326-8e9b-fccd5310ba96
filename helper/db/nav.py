# !/usr/bin/env python3
"""
poi服务端数据库模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session
from sqlalchemy.dialects.postgresql import JSONB
Base = declarative_base()


class NavLink(Base):
    __tablename__ = 'nav_link'
    link_id = Column(String(128), nullable=False, primary_key=True)
    s_nid = Column(String(128), nullable=False, default="")
    e_nid = Column(String(128), nullable=False, default="")
    kind = Column(Integer, default=0)
    geom = Column(String, nullable=False, default="")

    def __str__(self):
        return self.link_id


class NavGate(Base):
    __tablename__ = 'nav_gate'
    gate_id = Column(String(128), nullable=False, primary_key=True)
    in_linkid = Column(String(128), nullable=False, default="")
    node_id = Column(String(128), nullable=False, default="")
    out_linkid = Column(String(128), nullable=False, default="")
    type = Column(Integer, nullable=False, default=2)
    fee = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.gate_id


class NavNode(Base):
    __tablename__ = 'nav_node'
    node_id = Column(String(128), nullable=False, primary_key=True)
    kind = Column(Integer, default=1)
    geom = Column(String, nullable=False, default="")

    def __str__(self):
        return self.node_id
