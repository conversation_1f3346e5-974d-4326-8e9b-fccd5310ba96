#!/bin/bash
BASEDIR=$(readlink -f "$(dirname "${BASH_SOURCE[0]}")")
source "$BASEDIR/../../../conf/globalConf.sh"

data="$TMP_PATH/poi_not_in_aoi/data"

function ManualDistribute(){
    today=$(date +%Y-%m-%d)
    Description="${today}"

    count=$(wc -l "$data/toPlatform.$today" | awk -F " " '{print $1}')
    if [ "$count" -gt 0 ]; then
        echo "$count";

        awk -F "\t" '
        {
            if($5== "dataop.bianji_ugc_revise" ||
               $5== "dataop.bianji_ugc_revise_auto" ||
               $5== "dataop.bianji_ugc_revise_light" ||
               $5== "dataop.bianji_ugc_revise_direct" ||
               $5== "dataop.bianji_ugc_revise_important" ||
               $5== "dataop.bianji_ugc_delete"       ||
               $5== "dataop.bianji_ugc_delete_auto"  ||
               $5== "dataop.bianji_ugc_delete_light" ||
               $5== "dataop.bianji_ugc_delete_direct" ||
               $5== "dataop.bianji_ugc_del_important" ) {

                   print $0 > "'$data/ugc.toPlatform.$today'"

               } else {

                   print $0 > "'$data/normal.toPlatform.$today'"
               }
        }'  $data/toPlatform.$today


        ${PYTHONBIN} "$BASEDIR/new_AoiDistribute.py" "$data/normal.toPlatform.$today" ${Description} > "$data/log.normal.$today"
        Descrip="ugc confirmed $Description";
        echo $Descrip
        ${PYTHONBIN} "$BASEDIR/new_AoiDistribute.py" "$data/ugc.toPlatform.$today" ${Descrip} > "$data/log.ugc.$today"

        DistributeCount=$(cat "$data/toPlatform.$today" | wc -l)
        Content="POI NOT IN AOI, Total $DistributeCount, Please Conduct!"
        echo "$DistributeCount"
        sh "$LIB_PATH/sendMail.sh" "'${Content}'"
    fi


}

ManualDistribute;

