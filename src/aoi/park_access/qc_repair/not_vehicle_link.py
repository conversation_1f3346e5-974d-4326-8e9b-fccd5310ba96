# -*- coding: utf-8 -*-
"""
停车场坐标不在面内
"""
import sys
import os
import uuid
import psycopg2

from shapely import wkt
import tqdm

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi.BFQuery import BFQuery
from src.aoi import utils

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()
bf_query = BFQuery()


def main():
    """
    非车行数据提取
    Returns:

    """
    data_list = []
    bids = ['10005006492195839427', '1033862993342826233', '10589450446696180236', '10718349029122646853',
            '10731945742504800187', '11432945183313609877', '1155695978458997339', '11559642294947000863',
            '11992539919966211550', '12177182286290685560', '12228151673758350954', '12263202252621060219',
            '12369494983734964292', '12593546355033235877', '13057972185449817618', '13070038627828813441',
            '13300982606050703048', '13431626783272597540', '13467962792540527087', '13483103184124804149',
            '13485103324270926552', '13513399111018085758', '13536632594219937082', '13637463820542975269',
            '1375331753983658617', '14077888650125365197', '14090172413988568091', '14426444167605617998',
            '14498222026438414944', '14718270729956432690', '14718544754299513803', '14785989933353370997',
            '1486063289360674779', '15083293426513281735', '15184470747384230417', '15282102785295306520',
            '15742782809580002447', '15913119266607550164', '15936504165218519775', '16028848850198328664',
            '16095277475470001894', '1615529252115745702', '16292345218592450137', '16397039445587516799',
            '16399286487184004747', '16403202619802039423', '16503404500393087033', '17509553569943133025',
            '17627020084612431871', '17672903679797100543', '17675529982374555758', '17836823211916492759',
            '17895394634932042803', '17997824149082341375', '18048361934024919616', '18230010912965200854',
            '18300868356720693145', '1924441900300461759', '2156856876457694790', '2484262594269927441',
            '2753190437206317755', '2767662447445325299', '2914802969260032441', '2943578451659485431',
            '3051705234038094568', '3573562273982291002', '3641345253691496352', '4020035079989433182',
            '4108933336455213473', '4272013494688390911', '4280689747012578880', '4290855754513092772',
            '4347726410684478083', '4414055439669542437', '4595303449582811006', '4709405356226549609',
            '5052301336927276246', '5071870006674988207', '5119403921894592726', '5430199895428716384',
            '5677038183685086882', '5688258234357382731', '5926557817745543690', '6058375041587122640',
            '6855482511760690081', '7338874086321957372', '7363874687664274385', '7429005176175791426',
            '7485816547843591125', '7608768121483974394', '7645876128054354448', '7788022016531140866',
            '7877898177351110265', '7887741587659148813', '8300524632406680090', '8580680093095257889',
            '864545132133277000', '9006687190391063723', '9506704033457099728', '9515035884222376299',
            '9530392845555478911', '9688442294261636499', '9731892681996114636', '974036065729562139',
            '9768772513580740992']
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids)
    for park_info in tqdm.tqdm(park_list):
        road_relation = park_info['road_relation']
        road_relation_childrens = park_info['road_relation_childrens']
        exist_not_vehicle_data = False
        if road_relation and "link_info" in road_relation and len(road_relation['link_info']) > 0:
            for link_info in road_relation['link_info']:
                link_id = link_info['link_id']
                long_link_list = trans_dao.get_long_link_by_short_link_ids([link_id])
                if not long_link_list:
                    print(f"长短LINK转换失败")
                    continue
                long_link_id = long_link_list[0][0]
                link_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
                if not link_list or link_list[0]['kind'] != 10:
                    print(f"LINK不存在or车行:{link_list}")
                    continue
                link_sp = wkt.loads(link_list[0]['geom'])
                data_list.append({'link_id': long_link_id, 'point': link_sp.interpolate(0.5, normalized=True).wkt,
                                  'bid': park_info['bid'], 'link_geom': link_sp.buffer(0.00001).wkt,
                                  'city_name': park_info['city_name']})
                exist_not_vehicle_data = True

        if road_relation_childrens and "link_info" in road_relation_childrens and len(
                road_relation_childrens['link_info']) > 0:
            for link_info in road_relation_childrens['link_info']:
                link_id = link_info['link_id']
                long_link_list = trans_dao.get_long_link_by_short_link_ids([link_id])
                if not long_link_list:
                    print(f"长短LINK转换失败")
                    continue
                long_link_id = long_link_list[0][0]
                link_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
                if not link_list or link_list[0]['kind'] != 10:
                    print(f"LINK不存在or车行:{link_list}")
                    continue
                link_sp = wkt.loads(link_list[0]['geom'])
                data_list.append(
                    {'link_id': long_link_id, 'point': link_sp.interpolate(0.5, normalized=True).wkt,
                     'bid': park_info['bid'], 'link_geom': link_sp.buffer(0.00001).wkt,
                     'city_name': park_info['city_name']})
                exist_not_vehicle_data = True
        if not exist_not_vehicle_data:
            print(f"不存在非车行数据:{park_info['bid']}")
    # 导出到文件
    data_point_list = []
    with open("not_vehicle.txt", 'w') as file:
        for data in data_list:
            if data['point'] in data_point_list:
                continue
            file.write(f"{data['bid']},{data['point']}\n")
            data_point_list.append(data['point'])

    # 灌入非车行表
    for data in data_list:
        bd09_geom = utils.gcj_to_bd09(data['point'])
        bd09_sp = wkt.loads(bd09_geom)
        data = {
            'park_manual_task_id': data['bid'],
            'cityname': data['city_name'],
            'longitude': bd09_sp.x,
            'latitude': bd09_sp.y,
            'ext': '{"note": "停车场关联link属性错误"}',
            'uuid': uuid.uuid4().hex,
            'bid': data['bid'],
            'batch_id': 'NOT_VEHICLE_0401',
            'problem_id': data['bid']
        }
        bf_query.insert_parking_gate_push_turing(data)
        exit('结束')


if __name__ == '__main__':
    main()
