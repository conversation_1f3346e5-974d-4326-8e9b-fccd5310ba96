"""
aoi ugc任务自动下发
"""
import datetime
import requests
import tqdm

from src.common import pgsql, mysql
from src.door_naming.strategy_v2 import common

TARGET_CITY = ['北京市', '上海市']
BATCH_NAME = '北京上海_aoi_ugc上报任务核实_' + datetime.date.today().strftime("%Y%m%d")
day_before = datetime.date.today() - datetime.timedelta(days=10)

MANUAL_STATUS_HAS_C = 10
MANUAL_STATUS_CREATED = 11
MANUAL_STATUS_POI_INVALID = 12
MANUAL_STATUS_PUSHED_OLD = 13
MANUAL_STATUS_PUSHED = 1
MANUAL_STATUS_DEFAULT = 0

DESIRED_TAGS_301 = [
    "交通设施;火车站",
    "交通设施;飞机场",
    "交通设施;长途汽车站",
    "购物;购物中心",
    "医疗;综合医院",
    "医疗;专科医院",
    "教育培训;高等院校",
    "文化传媒;展览馆",
    "休闲娱乐;剧院",
    "运动健身;体育场馆",
]

DESIRED_TAGS_304 = [
    "旅游景点;风景区",
    "旅游景点;公园",
    "旅游景点;文物古迹",
    "旅游景点",
    "旅游景点;其他",
    "旅游景点;博物馆",
    "旅游景点;游乐园",
    "旅游景点;寺庙",
    "旅游景点;景点",
    "旅游景点;海滨浴场",
    "旅游景点;动物园",
    "旅游景点;水族馆",
    "旅游景点;教堂",
    "旅游景点;植物园",
]


def check_task_published(bid, task_type, created_at):
    """
    检查是否处理过,避免重复下发
    :param bid:
    :param task_type:
    :param created_at:
    :return:
    """
    # 曾经处理过,都算已经处理过了
    sql = 'select * from aoi_ugc_main_geom_change_info where bid=:bid and manual_status != :manual_status '
    exists = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, {'bid': bid, "manual_status": MANUAL_STATUS_DEFAULT})
    if exists:
        return True

    sql = 'select id from strategy_feature_list ' \
          'where bid=:bid and strategy_type=:strategy_type and create_time >:create_time'
    exists = mysql.query_one(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql,
                             {"bid": bid, "strategy_type": task_type,
                              "create_time": created_at + datetime.timedelta(days=1)})
    return exists is not None


def set_task_status(t_id, status):
    """
    设置任务状态
    :param t_id:
    :param status:
    :return:
    """
    sql = 'update aoi_ugc_main_geom_change_info set manual_status=:status where id=:id'
    mysql.execute(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql, {"status": status, "id": t_id})


def get_task_type(std_tag):
    """
    获取计划类型
    :param std_tag:
    :return:
    """
    if std_tag in DESIRED_TAGS_304:
        return 304
    if std_tag in DESIRED_TAGS_301:
        return 301
    return 302


def push(ref_qb_batch_id, main_bid, strategy_type):
    """
    推送
    :param ref_qb_batch_id:
    :param main_bid:
    :param strategy_type:
    :return:
    """
    strategy_type = str(strategy_type)
    data = {
        "src": 4,
        # "ref_qb_id": f"{main_bid}-2024091902-{index}", # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ref_qb_batch_id,  # 必填，批次号
        "main_poi_bid": main_bid,  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        # "mark_geom": "POINT(1 2)", # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        # "qb_type": 8,
        # "from_src": '停车场',
        # 302 高功新增  307 高功效质量更新
        'strategy_type': strategy_type
    }
    req = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    print(main_bid, req.json())
    if req.json()['code'] == 0:
        return True
    return False


def run():
    """
    主入口
    :return:
    """
    sql = 'select id, bid, created_at from aoi_ugc_main_geom_change_info' \
          ' where manual_status=:manual_status and created_at < :created_at order by id asc '
    ret = mysql.query_all(mysql.ENGINE_MYSQL_BEEFLOW_RW, sql,
                          {"manual_status": MANUAL_STATUS_DEFAULT, "created_at": day_before})
    if len(ret) < 1:
        print("无任务处理")
        return
    handled_bid = set()
    for item in ret:
        t_id = item.id
        bid = item.bid
        if bid in handled_bid:
            print("重复", bid)
            set_task_status(t_id, MANUAL_STATUS_PUSHED_OLD)
            continue
        created_at = item.created_at
        aoi_info = common.get_aoi_by_main_bid(bid)
        if aoi_info and aoi_info.src != 'SD':
            print("已经有c端框,过滤", bid)
            set_task_status(t_id, MANUAL_STATUS_HAS_C)
            continue
        poi_info = common.get_poi_info_by_bid(bid)
        if not poi_info:
            print("poi失效", bid)
            set_task_status(t_id, MANUAL_STATUS_POI_INVALID)
            continue
        if poi_info.city not in ['北京市', '上海市']:
            continue
        std_tag = poi_info.std_tag
        task_type = get_task_type(std_tag)
        if check_task_published(bid, task_type, created_at):
            print("已经下发过任务", bid, task_type)
            set_task_status(t_id, MANUAL_STATUS_PUSHED_OLD)
            continue
        handled_bid.add(bid)
        # 推送
        if push(BATCH_NAME, bid, task_type):
            set_task_status(t_id, MANUAL_STATUS_PUSHED)
        print("==== 需要下发人工", bid, task_type, std_tag, BATCH_NAME, created_at)


if __name__ == '__main__':
    run()
