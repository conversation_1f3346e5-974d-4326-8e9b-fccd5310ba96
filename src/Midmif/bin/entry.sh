#!/bin/bash

LocalPath=`dirname $0`
cd ${LocalPath};
source ./conf.sh;
cd -;

function ClearEnv(){
	rm -r ${CityPath};
	mkdir ${CityPath};
}

function Process(){
	for i in `cat ${MODULEPATH}/OnlineOffline/online/mid.*.online | awk '{if($NF != "")print $NF}' | sort -u`; do
		mkdir -p ${CityPath}/$i;
	done

	cat ${MODULEPATH}/OnlineOffline/online/mid.aoi.online | awk -F "\t" '{
		if($NF != "")
			print $0 > "'${CityPath}/'"$NF"/aoi.all"
	}'

	cat ${MODULEPATH}/OnlineOffline/online/mid.building.online | awk -F "\t" '{
		if($NF != "")
			print $0 > "'${CityPath}/'"$NF"/building.all"
	}'

	for i in `ls -d ${CityPath}/*`; do
		${PYTHONPATH} ${BinPath}/GenerateMidmifFile.py $i/aoi.all $i/AoiShape;
		${PYTHONPATH} ${BinPath}/GenerateMidmifFile.py $i/building.all $i/BuildingShape;
	done
}

function Main(){
	ClearEnv;
	Process;
}

Main;
