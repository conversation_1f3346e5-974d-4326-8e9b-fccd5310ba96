"""
poi not in aoi
"""
import sys
import shapely.wkt
import shapely.geometry
import os


def main():
    """
    :return: void
    """
    InputFile = open(sys.argv[1])  # input aoi data
    OutputFile1 = open(sys.argv[2], "w")  # poi not in aoi
    OutputFile2 = open(sys.argv[3], "w")  # poi in aoi

    for line in InputFile:
        try:
            line = line.strip("\r\n")
            items = line.split("\t")
            shape = shapely.wkt.loads(items[1])
            x = float(items[-2])
            y = float(items[-1])
            point = shapely.geometry.Point(x, y)

            if shape.distance(point) <= 0.0:
                print >> OutputFile2, "%s" % ("\t".join(items[:-2]))
            else:
                print >> OutputFile1, "%s" % (line)
        except Exception as e:
            continue

    InputFile.close()
    OutputFile1.close()
    OutputFile2.close()


main()
