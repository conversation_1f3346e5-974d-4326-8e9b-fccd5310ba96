# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
Export aoi with no doors or roads nearby
Authors: <AUTHORS>
Date:    2023/05/17 09:42:42
"""

import os
import sys
import traceback
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from batch_inin_access import AoiWithInnerLinkGateMatch
from batch_inout_access import AoiInOutGateMatch
from batch_virture_access import AoiVirtureGateMatch
from pg_tool import PgTool
from multiprocessing import Pool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
from common import common_tool
import shapely.wkt


def multi_process_run(num):
    obj = ExportAoiLackGateLink()
    obj.run(num)

class ExportAoiLackGateLink(PgTool):
    """
    内内外/外外内大门匹配策略类，可匹配大门扩展两条link后有内部路的大门
    """
    def __init__(self, road_dsn={}):
        """
        初始化连接数据库
        :param road_dsn: 字典:道路库的连接方式
        """
        PgTool.__init__(self, road_dsn)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def commit(self):
        """
        数据提交
        """
        PgTool.commit(self)

    def pre_process(self):
        sql = "truncate bid_gate_distribution"
        self.cursor_poi_write.execute(sql)

    def query_tag(self, bid):
        """
        query std_tag and judge bid is valid or not
        """
        sql = f"""
            select
                std_tag
            from poi
            where
                bid = '{bid}'
                and stats = 1
        """
        self.cursor_poi.execute(sql)

        return self.cursor_poi.fetchone()[0]

    def query_gate_of_bid(self, bid):
        """
        query bind gate of aoi with bid
        """
        sql = f"""
            select
                b.node_id
            from
                blu_access a
                inner join
                blu_access_gate_rel b
            on
                a.access_id = b.access_id
            where
                main_bid = '{bid}'
        """

        self.cursor_aoi.execute(sql)
        res = self.cursor_aoi.fetchall()
        node_set = set()
        for node_id, in res:
            node_set.add(node_id)

        return node_set

    def save_data(self, bid, data):
        sql = f"""
            insert into
                bid_gate_distribution(
                    bid, data
                )
            values(
                '{bid}',
                '{data}'
            )
        """
        self.cursor_poi_write.execute(sql)

    def find_node_to_process_with_bid(self, bid):
        """
        根据bid寻找附近可能关联的大门信息
        Args:
            bid: poi的bid
        Return:
            face_id: aoi的主键id
            node_id_set: set类型, 大门的nodeid集合
        """
        node_id_set = set()
        sql = '''
            select
                st_astext(geom),
                kind,
                aoi_level,
                bf.face_id
            from
                blu_face bf
                inner join
                blu_face_poi bfp
            on
                bf.face_id = bfp.face_id
            where
                bfp.poi_bid = %s '''
        self.cursor_aoi.execute(sql, [bid])
        res_aoi = self.cursor_aoi.fetchone()
        if res_aoi is None or len(res_aoi) == 0:
            print("没有关联的AOI\t" + bid)
            return "", node_id_set
        if res_aoi[1] == '52' or res_aoi[2] != 2:
            print("AOI为商圈或非基础院落\t" + bid)
            return "", node_id_set
        aoi_geom = res_aoi[0]
        face_id = res_aoi[3]
        num_gate = 0
        sql = '''
            with tmp as (
                select
                   link_id,
                   st_intersection(st_geomfromtext(%s, 4326), geom) as geom
                from nav_link
                where
                    st_crosses(st_geomfromtext(%s, 4326), geom)
            )
            select
                link_id,
                st_astext((st_dump(geom)).geom) as geom
            from tmp;'''
        self.cursor_road.execute(sql, [aoi_geom, aoi_geom])
        res_point = self.cursor_road.fetchall()
        if res_point is not None and len(res_point) > 0:
            for point_geom in res_point:
                sql = """
                    select
                        string_agg(node_id, ''',''')
                    from nav_node
                    where
                        st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)"""
                self.cursor_road.execute(sql, [point_geom[1]])
                res_node = self.cursor_road.fetchall()
                if res_node is None or len(res_node) == 0:
                    continue
                node_str = res_node[0][0]
                sql = '''
                    select
                        node_id, in_linkid, out_linkid
                    from nav_gate
                    where
                    node_id in ('%s')''' % (node_str)
                self.cursor_road.execute(sql)

                res_node = self.cursor_road.fetchall()
                if res_node is not None:
                    num_gate = num_gate + len(res_node)
                for res_node_tmp in res_node:
                    node_id_set.add(res_node_tmp[0])

        #找到AOI框内的大门
        sql = """
            select
                string_agg(node_id, ''',''')
            from nav_node
            where
                st_contains(st_geomfromtext(%s, 4326), geom)
        """
        self.cursor_road.execute(sql, [aoi_geom])
        res_node = self.cursor_road.fetchall()
        if res_node is not None and len(res_node) > 0:
            node_str = res_node[0][0]
            sql = '''
                select
                    node_id,
                    in_linkid,
                    out_linkid
                from nav_gate
                where
                node_id in ('%s')
            ''' % (node_str)
            self.cursor_road.execute(sql)
            res_node = self.cursor_road.fetchall()
            if res_node is not None:
                num_gate = num_gate + len(res_node)
            for res_node_tmp in res_node:
                node_id_set.add(res_node_tmp[0])

        return face_id, node_id_set

    def get_geom_with_bid(self, bid):
        sql = f"""
            select
                st_astext(geom)
            from blu_face a
                 inner join
                 blu_face_poi b
            on
                a.face_id = b.face_id
            where
                poi_bid = '{bid}'
        """
        self.cursor_aoi.execute(sql)
        res = self.cursor_aoi.fetchone()
        return res

    def run(self, num):
        """
        函数入口
        """
        tag_set = set()
        tag_set.add("交通设施;加油加气站")
        tag_set.add("交通设施;服务区")

        #find all bid
        sql = f"""
            select
                distinct poi_bid
            from blu_face a
                 inner join
                 blu_face_poi b
            on
                a.face_id = b.face_id
            where
                aoi_level = 2
                and kind != '52'
                and poi_bid != ''
                and poi_bid like '%{num}'
        """
        self.cursor_aoi.execute(sql)
        bid_res = self.cursor_aoi.fetchall()
        process_num = 0
        max = len(bid_res)
        for bid, in bid_res:
            process_num = process_num + 1
            print("AOI缺门数据查询进度:" + str(process_num / max))
            node_set = self.query_gate_of_bid(bid)
            if len(node_set) > 0:
                self.save_data(bid, "已关联门")
                continue
            sql = f"""
                select
                    std_tag
                from poi
                where
                    bid = '{bid}'
                    and status = 1
            """
            self.cursor_poi.execute(sql)
            res_poi = self.cursor_poi.fetchone()
            if res_poi is None:
                continue
            std_tag = res_poi[0]

            _, node_set = self.find_node_to_process_with_bid(bid)
            if len(node_set) == 0:
                if std_tag in tag_set:
                    continue
                geom = self.get_geom_with_bid(bid)
                if geom is None:
                    continue
                area = shapely.wkt.loads(geom[0]).area
                if area < 1.8e-07:
                    continue
                self.save_data(bid, "缺门")
            else:
                self.save_data(bid, "存在未关联的门")

            self.commit()

if __name__ == '__main__':
    try:
        with ExportAoiLackGateLink() as obj:
            obj.pre_process()

        with Pool(10) as p:
            p.map(multi_process_run, [i for i in range(10)])
    except Exception as e:
        #报警
        common_tool.send_hi("statics aoi gate num failed", ["lifan14"])
        traceback.print_exc()
