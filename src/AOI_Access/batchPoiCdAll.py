# -*- coding: UTF-8 -*-
################################################################################
"""
此模块根据大门成果库自动生成AOI通道并进行批处理
二期，第二步，针对更新gateid后的数据，自动匹配多个node的大门数据
Authors: <AUTHORS>
Date:    2022/05/27 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import shapely.wkt
import shapely.geometry
import uuid


"""
"args" : {
      "db_name" : "rsubmit_67cda5f27c6211edb63fb8cef6c1c8f4",
      "host" : "*************",
      "password" : "wrzqrjlg",
      "port" : 8000,
      "username" : "rsubmit_67cda5f27c6211edb63fb8cef6c1c8f4_se_rw"
   },
    "args" : {
      "db_name" : "rsubmit_8bde5cae7d2311edb380b8cef6c1bcec",
      "host" : "*************",
      "password" : "sioejwqf",
      "port" : 8004,
      "username" : "rsubmit_8bde5cae7d2311edb380b8cef6c1bcec_se_rw"
   },
   """
def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


def query_poi_info(bid, cursor):
    """
    查询poi的信息
    """
    sql = "select bid,relation_bid from poi where bid = '%s';" % bid
    cursor.execute(sql)
    return cursor.fetchone()


def aoi_add_node(cursor, face_id, gate_pos):
    """
    aoi的范围框内加上点坐标，并更新link和topo关系
    """
    sql = "select st_astext(geom), mesh_id from blu_face where face_id = %s"
    cursor.execute(sql, [face_id])
    res_aoi = cursor.fetchall()
    aoi_geom = res_aoi[0][0]
    mesh_id = res_aoi[0][1]
    gate_pos_new = [(round(gate_pos[0][0], 9), round(gate_pos[0][1], 10))]
    coor_list = list(shapely.wkt.loads(aoi_geom).boundary.coords)
    coor_list_new = add_node(coor_list, gate_pos_new)
    if len(coor_list_new) == 0:
        return ""
    aoi_geom_new = shapely.geometry.Polygon(coor_list_new).wkt
    sql = "update blu_face set geom = st_geomfromtext('{}', 4326) where face_id = '{}'".format(aoi_geom_new, face_id)
    cursor.execute(sql)
    
    node_id = str(uuid.uuid4()).replace("-", "")
    sql = "select link_id, st_astext(geom), s_nid, e_nid, kind, edit_flag, memo from blu_link \
            where link_id in (select link_id from blu_face_topo where face_id = %s)"
    cursor.execute(sql, [face_id])
    res_link = cursor.fetchall()
    for v in res_link:
        link_id = v[0]
        geom = v[1]
        s_nid = v[2]
        e_nid = v[3]
        link_kind = v[4]
        link_edit_flag = v[5]
        link_memo = v[6]
        coor_list = list(shapely.wkt.loads(geom).coords)
        geom_new = split_node(coor_list, gate_pos_new)
        if len(geom_new) == 0:
            continue
        if len(geom_new) == 2:
            link_geom_new_s = str(shapely.geometry.LineString(geom_new[0]))
            link_geom_new_e = str(shapely.geometry.LineString(geom_new[1]))
            link_id1 = str(uuid.uuid4()).replace("-", "")
            link_id2 = str(uuid.uuid4()).replace("-", "")
            sql = "select st_length(st_transform(st_geomfromtext('{}', 4326),4527)), st_length(st_transform("\
                    "st_geomfromtext('{}', 4326),4527))".format(link_geom_new_s, link_geom_new_e)
            cursor.execute(sql)
            len_link = cursor.fetchall()
            len1 = len_link[0][0]
            len2 = len_link[0][1]
            
            sql = "select count(*) from blu_face_topo where link_id = '{}'".format(link_id)
            cursor.execute(sql)
            if cursor.fetchone()[0] < 2:
                sql = "delete from blu_link where link_id = '{}'".format(link_id)
                cursor.execute(sql)
            
            sql = "insert into blu_link(link_id, s_nid, e_nid, kind, mesh_id, geom, " \
                    "length, edit_flag, memo) values ('{}', '{}', '{}', '{}', '{}', st_geomfromtext('{}', 4326), {}, {}, '{}'), ('{}', '{}', " \
                    "'{}', '{}', '{}', st_geomfromtext('{}', 4326), {}, {}, '{}')".format(link_id1, s_nid, node_id, 
                    link_kind, mesh_id, link_geom_new_s, len1, link_edit_flag, link_memo, link_id2, node_id, e_nid, 
                    link_kind, mesh_id, link_geom_new_e, len2, link_edit_flag, link_memo)
            cursor.execute(sql)

            #修改blu_face_topo表
            id1 = str(uuid.uuid4()).replace("-", "")
            id2 = str(uuid.uuid4()).replace("-", "")
            sql = "delete from blu_face_topo where face_id = '{}' and link_id = '{}'; insert into blu_face_topo (id, "\
                    "face_id, link_id, mesh_id, memo) values('{}', '{}','{}','{}',''),('{}', '{}','{}','{}','')".format(
                    face_id, link_id, id1, face_id, link_id1, mesh_id, id2, face_id, link_id2, mesh_id)
            cursor.execute(sql)
            # sql = "update blu_link set geom = st_geomfromtext('{}', 4326) where link_id = '{}'".format(link_geom_new, link_id)
            # cursor.execute(sql)
    
    
    node_geom = shapely.geometry.Point(gate_pos_new).wkt
    sql = "insert into blu_node (node_id, form, geom, mesh_id, adj_meshid, adj_nid, edit_flag, memo) values("\
            "'%s', '%s', st_geomfromtext('%s', 4326), '%s', '', '', 0, '')" % (node_id, 2, node_geom, mesh_id)
    cursor.execute(sql)
    return node_id


def split_node(coor_list, gate_pos):
    """
    将一条link根据点坐标分割成两条
    """
    for i in range(len(coor_list) - 1):
        if coor_list[i] == gate_pos[0] or coor_list[i + 1] == gate_pos[0]:
            return [coor_list]
        if shapely.geometry.LineString([coor_list[i], coor_list[i + 1]]).distance(shapely.geometry.Point(
            gate_pos)) < 2e-9 and (coor_list[i][0] - gate_pos[0][0]) * (gate_pos[0][0] - coor_list[i + 1][0]) > 0:

            
            return [coor_list[:i + 1] + gate_pos, gate_pos + coor_list[i + 1:]]
    return []


def add_node(coor_list, gate_pos):
    """
    将点坐标插入到线坐标串中
    """
    for i in range(len(coor_list) - 1):
        if coor_list[i] == gate_pos[0] or coor_list[i + 1] == gate_pos[0]:
            return coor_list
        if shapely.geometry.LineString([coor_list[i], coor_list[i + 1]]).distance(shapely.geometry.Point(
            gate_pos)) < 2e-9 and (coor_list[i][0] - gate_pos[0][0]) * (gate_pos[0][0] - coor_list[i + 1][0]) > 0:

            
            return coor_list[:i + 1] + gate_pos + coor_list[i + 1:]
    return []


def main():
    """更新大门通行属性的入口函数
    """
    #读取配置文件
    cf = ConfigParser.ConfigParser()
    cf.read('../../conf/config.ini')
    dbname = "master_back"
    _host = cf.get(dbname, 'host')
    _port = cf.get(dbname, 'port')
    _user = cf.get(dbname, 'user')
    _database = cf.get(dbname, 'database')
    _password = cf.get(dbname, 'password')
    conn = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
    cursor = conn.cursor()

    dbname = "poi_cd_online"
    _host = cf.get(dbname, 'host')
    _port = cf.get(dbname, 'port')
    _user = cf.get(dbname, 'user')
    _database = cf.get(dbname, 'database')
    _password = cf.get(dbname, 'password')
    conn_cd = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
    cursor_cd = conn_cd.cursor()

    dbname = "poi_online"
    _host = cf.get(dbname, 'host')
    _port = cf.get(dbname, 'port')
    _user = cf.get(dbname, 'user')
    _database = cf.get(dbname, 'database')
    _password = cf.get(dbname, 'password')
    conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
    cursor_poi = conn_poi.cursor()

    dbname = "aoi_resume"
    _host = cf.get(dbname, 'host')
    _port = cf.get(dbname, 'port')
    _user = cf.get(dbname, 'user')
    _database = cf.get(dbname, 'database')
    _password = cf.get(dbname, 'password')
    conn_resume = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
    cursor_resume = conn_resume.cursor()

    #获取天级更新的道路库连接信息
    pathname = cf.get('regular', 'road_pathname')
    dbinfo_url = cf.get('regular', 'pg_naming_url')
    db_info = get_db_info(pathname, dbinfo_url)
    if db_info is None:
        print("get db_info failed")
        os._exit(1)

    conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
                host = db_info['host'], port = db_info['port'])
    cursor_road = conn_road.cursor()
    # sql = "create index if not exists nav_link_snid_index on nav_link(s_nid);create index if not exists nav_link_enid_index on nav_link(e_nid)"
    # cursor_road.execute(sql)
    # conn_road.commit() 
    # sql = "create index blu_node_geom_idx on blu_node using gist(geom)"
    # cursor.execute(sql)
    # conn.commit()

    sql = "select poi_gate.mid, poi.bid, related_road_gate, related_road_node, \
            st_astext(poi.geometry),valid_obj from poi_gate inner join \
            poi on poi_gate.mid = poi.mid where poi.kind != 'BF04' and  \
            poi.name not like '%停车场%' and poi_gate.valid_obj != ''"
    cursor_cd.execute(sql)
    res = cursor_cd.fetchall()

    fw = open("invalid_bid.txt", "w")
    current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    for v in res:
        mid = v[0]
        bid = v[1]
        related_road_gate = v[2]
        related_road_node = v[3]
        point_geom = v[4]
        valid_obj = v[5]
        print("begin" + bid)
        node_set = set(related_road_node.split(','))
        if len(node_set) < 2 and valid_obj != '99':
            #本次批处理仅处理关联多个大门的场景，过滤
            continue
        #如果此poi已经存在出入口，过滤
        sql = "select count(*) from blu_access where ACCESS_POI_BID = %s"
        cursor.execute(sql, [bid])
        if cursor.fetchone()[0] > 0:
            continue

        res_poi = query_poi_info(bid, cursor_cd)
        if res_poi is None or len(res_poi) == 0:
            ##这里可能后续要做处理
            fw.write(bid + "\tbid在成果库找不到失效\n")
            continue
        relation_bid = res_poi[1]
        if relation_bid == "":
            continue
        
        #判断主点bid是否失效
        res_poi = query_poi_info(relation_bid, cursor_poi)
        if res_poi is None or len(res_poi) == 0:
            fw.write(bid + "\t主点bid失效\n")
            ##这里可能后续要做处理
            continue

        #判断bid在线上库是否失效
        res_poi = query_poi_info(bid, cursor_poi)
        if res_poi is None or len(res_poi) == 0:
            ##这里可能后续要做处理
            fw.write(bid + "\tbid在poi库找不到失效\n")
            continue

        #判断是否存在AOI
        sql = "select a.face_id, st_astext(a.geom), b.poi_bid, a.mesh_id from blu_face a inner join blu_face_poi b "\
                "on a.face_id = b.face_id where b.poi_bid = %s"
        cursor.execute(sql, [relation_bid])
        res_aoi = cursor.fetchall()
        if res_aoi is None or len(res_aoi) == 0:
            ##这里可能后续要做处理
            fw.write(bid + "\t不存在AOI\n")
            continue
        if len(res_aoi) > 1:
            fw.write(bid + "\t 匹配到了多个aoi\n")
            continue
        face_id = res_aoi[0][0]
        aoi_geom = res_aoi[0][1]
        mesh_id = res_aoi[0][3]

        #先判断node_id是否失效
        node_id_invalid = False
        for node_id_tmp in set(related_road_node.split(',')):
            sql = "select count(*) from nav_gate where node_id = %s"
            cursor_road.execute(sql, [node_id_tmp])
            if cursor_road.fetchone()[0] == 0:
                node_id_invalid = True
                #fw.write(bid + "\t 存在gate_id 失效\n")
                break
        if node_id_invalid:
            fw.write(bid + "\t 存在node_id 失效\n")
            continue

        #判断gate_id是否失效
        gate_id_invalid = False
        for gate_id_tmp in set(related_road_gate.split(',')):
            sql = "select count(*) from nav_gate where gate_id = %s"
            cursor_road.execute(sql, [gate_id_tmp])
            if cursor_road.fetchone()[0] == 0:
                gate_id_invalid = True
                #fw.write(bid + "\t 存在gate_id 失效\n")
                break
        if gate_id_invalid:
            #gateid失效，需要更新
            fw.write(bid + "\t 存在gate_id 失效\n")  
            continue

        #查找匹配的link
        gate_set = set(related_road_gate.split(','))
        link_set = {}
        for gate_tmp in gate_set:
            sql = "select in_linkid, out_linkid from nav_gate where gate_id = %s"
            cursor_road.execute(sql, [gate_tmp])
            res_link = cursor_road.fetchone()
            if res_link is not None and len(res_link) > 0:
                inlink_id_tmp = res_link[0]
                outlink_id_tmp = res_link[1]
                sql = "select st_astext(geom) from nav_link where link_id = %s"
                cursor_road.execute(sql, [inlink_id_tmp])
                link_set[inlink_id_tmp] = cursor_road.fetchone()[0]
                sql = "select st_astext(geom) from nav_link where link_id = %s"
                cursor_road.execute(sql, [outlink_id_tmp])
                link_set[outlink_id_tmp] = cursor_road.fetchone()[0]
        #过滤无link的场景    
        if len(link_set) == 0:
            continue

        #求与AOI的交点
        pointregion = shapely.wkt.loads(point_geom)
        min_dis = 1.0
        gate_pos = list()
        link_id = ""
        aoi_region = shapely.wkt.loads(aoi_geom).boundary
        for link_tmp in link_set.values():
            link_region = shapely.wkt.loads(link_tmp)
            if aoi_region.intersects(link_region):
                gate_region_tmp = link_region.intersection(aoi_region)
                if min_dis > pointregion.distance(gate_region_tmp):
                    min_dis = pointregion.distance(gate_region_tmp)
                    if gate_region_tmp.geom_type != "MultiPoint":
                        gate_pos = list(gate_region_tmp.coords)
                    else:
                        dis_tmp = 1.0
                        for i in range(len(gate_region_tmp)):
                            dis_tmp_point = pointregion.distance(gate_region_tmp[i])
                            if dis_tmp_point < dis_tmp:
                                gate_pos = list(gate_region_tmp[i].coords)
                                dis_tmp = dis_tmp_point
                    #因为不处理匹配多link的情况，暂不记录匹配到的link
                    link_id = link_tmp
        
        #如果没有交点，记录
        if len(gate_pos) == 0:
            fw.write(bid + "\tlink与aoi没有交点\n")
            continue
        #交点附近20米内存在出入口点，代表已经生成了出入口
        sql = "select count(*) from blu_node where st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0002) and form = 2"
        cursor.execute(sql, [shapely.geometry.Point(gate_pos).wkt])
        if cursor.fetchone()[0] > 0:
            continue
        #找到交点之后，aoi增加节点
        node_id = aoi_add_node(cursor, face_id, gate_pos)
        if node_id == "":
            fw.write(bid + "\t大门位置与当前node重合\n")
            continue
        
        #入履历库
        fields = "face_id,name_ch,kind,mesh_id,src,scene_id,label_flag,proj_flag,\
            memo,geom, update_time,aoi_level,aoi_level_source,access_complete"
        sql = "select %s from blu_face where face_id = '%s'" % (fields, face_id)
        cursor.execute(sql)
        aoi_info = cursor.fetchone()
        sql = "insert into blu_record (%s, bid, type, strategy,task_id,old_geom,mid) " % (fields)
        sql =  sql + "values (%s, %s, %s, %s, %s, %s, %s, %s, %s, \
                %s, %s, %s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326), %s)" 
        cursor_resume.execute(sql, [aoi_info[0], aoi_info[1], aoi_info[2], 
                aoi_info[3], aoi_info[4], aoi_info[5], aoi_info[6], aoi_info[7], 
                aoi_info[8], aoi_info[9], aoi_info[10], aoi_info[11],
                aoi_info[12], aoi_info[13], bid, '3', '大门成果库批处理转化出入口', '', aoi_geom, mid])
        

        #poi与aoi通道的属性转换 BLU_ACCESS 依赖 face_id, node_id, mesh_id, link_id
        sql = "select bid, mid,status, tag, name, relation_bid, relation from poi where bid = %s"
        cursor_poi.execute(sql, [bid])
        sub_poi_info = cursor_poi.fetchone()
        #前面已经判断过该poi在库里存在

        access_id = str(uuid.uuid4()).replace("-", "")
        access_face_id = face_id
        sql = "insert into blu_access(access_id, face_id, NODE_ID, mesh_id, STATUS, kind, name, ACCESS_POI_BID, "\
                " ACCESS_POI_MID, source, update_time) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
        cursor.execute(sql, [access_id, access_face_id, node_id, mesh_id, sub_poi_info[2], 1, sub_poi_info[4], \
                sub_poi_info[0], sub_poi_info[1], 1, current_time])
        
        #BLU_ACCESS_TRAFFIC转换
        #########################这里是否需要去掉99的筛选条件#######################
        # sql = "select valid_obj, car_restric, transit_type, fee, time_do from poi_gate inner join poi on "\
        #         " poi_gate.mid = poi.mid where poi.bid = %s and poi_gate.valid_obj != '99'"
        sql = "select valid_obj, car_restric, transit_type, fee, time_do from poi_gate inner join poi on "\
                " poi_gate.mid = poi.mid where poi.bid = %s"
        cursor_cd.execute(sql, [bid])
        res_traffic = cursor_cd.fetchone()
        if res_traffic[0] == "99":
            valid_obj_list = res_traffic[0].split(',')
            car_restric_list = ["0"]
            transit_type_list = ["0"]
            fee_list = ["0"]
        else:
            valid_obj_list = res_traffic[0].split(',')
            car_restric_list = res_traffic[1].split(',')
            transit_type_list = res_traffic[2].split(',')
            fee_list = res_traffic[3].split(',')
        time_do = res_traffic[4]
        for i in range(len(valid_obj_list)):
            sql = "insert into BLU_ACCESS_TRAFFIC (ACCESS_TRAFFIC_ID, ACCESS_ID, MESH_ID, VALID_OBJ, obj_restrict,"\
                    "TRANSIT_TYPE, fee, VALID_TIME, update_time) values(%s, %s, %s, %s, %s, %s, %s, %s, %s)"
            access_traffic_id = str(uuid.uuid4()).replace("-", "")
            if len(fee_list) == 1:
                fee_tmp = int(fee_list[0])
            else:
                fee_tmp = int(fee_list[i])
            transit_type_tmp = 0
            if len(transit_type_list) > i:
                if int(transit_type_list[i]) == 1:
                    transit_type_tmp = 2
                if int(transit_type_list[i]) == 2:
                    transit_type_tmp = 3
                if int(transit_type_list[i]) == 3:
                    transit_type_tmp = 1
            if len(car_restric_list) <= i + 1:
                car_restric_list.append("0")
            cursor.execute(sql, [access_traffic_id, access_id, mesh_id, int(valid_obj_list[i]),
                           int(car_restric_list[i]), transit_type_tmp, fee_tmp, time_do, current_time])

        
        #blu_aoi_rel 表
        #link_type = 0
        link_type_dict = dict()
        if '1' == valid_obj_list[0]:
            if len(node_set) == 1:
                #link_type = int(transit_type_list[0])
                for link_id_tmp in link_set.keys():
                    link_type_dict[link_id_tmp] = int(transit_type_list[0])
            else:
                for node_id_tmp in node_set:
                    sql = "select in_linkid, out_linkid from nav_gate where node_id = %s"
                    cursor_road.execute(sql, [node_id_tmp])
                    node_res = cursor_road.fetchall()
                    for node_res_tmp in node_res:
                        link_type_dict[node_res_tmp[0]] = 3 if len(node_res) == 2 else 0
                        link_type_dict[node_res_tmp[1]] = 3 if len(node_res) == 2 else 0
        #需要具体的开发规则
        for link_id_tmp in link_set.keys():
            access_link_rel_id = str(uuid.uuid4()).replace("-", "")
            if link_id_tmp in link_type_dict.keys():
                link_type = link_type_dict[link_id_tmp]
            else:
                link_type = 0
            sql = "insert into BLU_ACCESS_LINK_REL(ACCESS_LINK_REL_ID, ACCESS_ID, MESH_ID, link_id, \
                    LINK_TYPE, source, update_time) values \
                    (%s, %s, %s, %s, %s, %s, %s)"
            cursor.execute(sql, [access_link_rel_id, access_id, mesh_id, link_id_tmp, link_type, 1, current_time])
        
        #BLU_ACCESS_GATE_REL
        #ACCESS_GATE_REL_ID
        ##注意这里有多个node的时候，node和gate要一一对应
        for gate_id_tmp in set(related_road_gate.split(',')):
            access_gate_rel_id = str(uuid.uuid4()).replace("-", "")
            sql = "select node_id from nav_gate where gate_id = %s"
            cursor_road.execute(sql, [gate_id_tmp])
            node_id_tmp = cursor_road.fetchone()[0]
            sql = "insert into BLU_ACCESS_GATE_REL(ACCESS_GATE_REL_ID, ACCESS_ID, \
                    mesh_id, NODE_ID, gate_id, source,update_time) \
                    values(%s, %s, %s, %s, %s, %s, %s)"
            cursor.execute(sql, [access_gate_rel_id, access_id, mesh_id, node_id_tmp, gate_id_tmp, 1, current_time])

    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    #conn_cd.commit()
    #conn.commit()
    #conn_poi.commit()
    conn_road.close()
    conn_cd.close()
    conn_poi.close()
    conn.close()


if __name__ == '__main__':
    main()
