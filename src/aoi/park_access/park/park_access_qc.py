# -*- coding: utf-8 -*-
"""
出入口质检过滤策略
"""
import hashlib
import json
import uuid
import datetime

from shapely.geometry import Polygon, MultiPolygon, Point, MultiPoint
from shapely import wkt
from shapely.validation import make_valid
import numpy as np
from sklearn.cluster import DBSCAN

from common import coordinate_trans, common_tool
from aoi.dao.dest_traj import DestTraj
from aoi.dao.dest_traj_1 import DestTrajOne
from aoi.dao.dest_traj_2 import DestTrajTwo
from aoi.dao.poi_online_query import PoiOnlineQuery
from aoi.dao.road import Road
from loguru import logger
from aoi import utils
from aoi.park_access import park_utils
from common import pipeline
from aoi.park_access.park import context

dest_traj = DestTraj()
dest_traj_dao1 = DestTrajOne()
dest_traj_dao2 = DestTrajTwo()
road_dao = Road()
poi_online_query = PoiOnlineQuery()

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000
AREA_FACTOR = 10000000000
FILTER_DISTANCE_CATEGORY = ['交通设施;飞机场', '交通设施;火车站']


def park_access_filter(ctx: context.Context):
    """
    新增停车场挖掘出入口质检策略
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_repeat,
        filter_already_relation_gate_and_link,
        # filter_pushed_park_access,
        filter_other_parking_node_and_link,
        filter_pushed_intelligence_by_new_park,
        filter_by_distance,
        filter_by_high_link,
    )
    pipe_list(ctx)


def intelligence_mistake_qc(ctx: context.Context):
    """
    挖掘误关联出入口情报校验
    Args:
        ctx:

    Returns:

    """
    if len(ctx.parking_access_list) == 0:
        return
    pipe_list = pipeline.Pipeline(
        filter_pushed_intelligence,
    )
    pipe_list(ctx)


def filter_repeat(ctx: context.Context, proceed):
    """
    过滤挖掘重复的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_access_list = []
    exist_node_id = dict()
    exist_link_id = dict()
    for park_access in ctx.parking_access_list:
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id not in exist_node_id:
            exist_node_id[park_access.node_id] = 1
            park_access_list.append(park_access)
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK and park_access.link_id not in exist_link_id:
            exist_link_id[park_access.link_id] = 1
            park_access_list.append(park_access)
    ctx.parking_access_list = park_access_list
    proceed()


def filter_already_relation_gate_and_link(ctx: context.Context, proceed):
    """
    过滤当前停车场已关联的link或者Node的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    # 查询关联子点的Node
    if not ctx.parking_info.bid:
        return proceed()
    long_node_ids, long_link_ids = park_utils.get_relation_node_and_link_by_park_bid(ctx.parking_info.bid)
    # 查询关联Node信息
    node_geom_list = []
    if len(long_node_ids) > 0:
        node_geom_res = road_dao.get_node_geom(list(set(long_node_ids)))
        node_geom_list = [item[0] for item in node_geom_res] if node_geom_res else []
    # 查询关联子link
    link_geom_list = []
    if len(long_link_ids) > 0:
        link_geom_res = road_dao.get_nav_link_by_link_ids(list(set(long_link_ids)))
        link_geom_list = [item[0] for item in link_geom_res] if link_geom_res else []

    # 策略挖掘出入口的Node
    new_node_geom_list = [item.node_geom for item in ctx.parking_access_list
                          if item.type == context.PARKING_ACCESS_TYPE_NODE]
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        # link去重
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK:
            # link已关联不在关联
            if park_access.link_id in long_link_ids:
                park_access.is_valid = False
                park_access.filter_reason = f"link已关联:{long_link_ids}"
                continue

            # link附近存在出入口或者待关联Node
            link_sp = wkt.loads(park_access.link_geom)
            exist_relation_gate = [node_geom for node_geom in node_geom_list + new_node_geom_list
                                   if link_sp.distance(wkt.loads(node_geom)) < 30 / COORDINATE_FACTOR_NEW]
            if len(exist_relation_gate) > 0:
                park_access.is_valid = False
                park_access.filter_reason = f"link 20m附近存在出入口过滤:{exist_relation_gate}"
                continue

            # link 附近 20m内存在待关联的link则过滤
            tmp_link_geom_list = [item.link_geom for item in ctx.parking_access_list if
                                  item.type == context.PARKING_ACCESS_TYPE_LINK and item.is_valid
                                  and item.link_id != park_access.link_id]
            exist_relation_link = [item for item in tmp_link_geom_list + link_geom_list
                                   if link_sp.distance(wkt.loads(item)) < 30 / COORDINATE_FACTOR_NEW]
            if len(exist_relation_link) > 0:
                park_access.is_valid = False
                park_access.filter_reason = f"link 20m附近存在待关联的link过滤:{tmp_link_geom_list}"
            continue

        # node去重, node 已关联不在关联
        if park_access.node_id in long_node_ids:
            park_access.is_valid = False
            park_access.filter_reason = f"node已关联:{long_node_ids}"
            continue
        # node 附近存在已关联的Node
        node_sp = wkt.loads(park_access.node_geom)
        exist_relation_gate = [item for item in node_geom_list
                               if node_sp.distance(wkt.loads(item)) < 30 / COORDINATE_FACTOR_NEW]
        if len(exist_relation_gate) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"node 20m附近存在已关联的Node过滤:{exist_relation_gate}"
            continue
        # node 附近存在关联Link也过滤
        exist_relation_link = [item for item in link_geom_list if
                               node_sp.distance(wkt.loads(item)) < 30 / COORDINATE_FACTOR_NEW]
        if len(exist_relation_link) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"node 20m附近存在已关联的link过滤:{exist_relation_link}"
            continue
    proceed()


def filter_pushed_park_access(ctx: context.Context, proceed):
    """
    过滤已推送的出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    pushed_park_access_list = dest_traj.get_pushed_park_access([ctx.parking_info.bid])
    if not pushed_park_access_list:
        return proceed()
    pushed_node_ids = []
    pushed_lind_ids = []
    road_relation_list = [json.loads(item[0]) for item in pushed_park_access_list]
    for road_relation_list in road_relation_list:
        if "link_info" in road_relation_list and len(road_relation_list["link_info"]) != 0:
            for link_info in road_relation_list["link_info"]:
                if link_info["type"] == 1 and link_info["node_id"]:
                    pushed_node_ids.append(link_info["node_id"])
                if link_info["type"] == 2 and link_info["link_id"]:
                    pushed_lind_ids.append(link_info["link_id"])
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE and park_access.node_id in pushed_node_ids:
            park_access.is_valid = False
            park_access.filter_reason = f"node已推送:{pushed_node_ids}"
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK and park_access.link_id in pushed_lind_ids:
            park_access.is_valid = False
            park_access.filter_reason = f"link已推送:{pushed_lind_ids}"
    proceed()


def filter_other_parking_node_and_link(ctx: context.Context, proceed):
    """
    过其余停车场已关联的Node或者Link
    Args:
        ctx:
        proceed:

    Returns:

    """
    if ctx.parking_info.parent_id == "":
        return proceed()
    park_bid = ctx.parking_info.bid
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if park_access.type == context.PARKING_ACCESS_TYPE_NODE:
            node_sp = wkt.loads(park_access.node_geom)
            other_park_access_list = poi_online_query.get_park_access_list(
                intersects_geom=node_sp.buffer(1 / COORDINATE_FACTOR).wkt)
            if not other_park_access_list:
                continue
            parent_park_bids = [item[5] for item in other_park_access_list
                                if item[9] and item[5] != '' and item[5] != '0' and item[5] != park_bid]
            if not parent_park_bids:
                continue
            parent_park_list = poi_online_query.get_parking_list(bids=parent_park_bids)
            if parent_park_list:
                park_access.is_valid = False
                park_access.filter_reason = f"node已被其余停车场关联,bid:{parent_park_list[0][7]}"
                continue
        if park_access.type == context.PARKING_ACCESS_TYPE_LINK:
            link_sp = wkt.loads(park_access.link_geom)
            other_park_access_list = poi_online_query.get_park_access_list(
                intersects_geom=link_sp.buffer(1 / COORDINATE_FACTOR).wkt)
            if not other_park_access_list:
                continue
            parent_park_bids = [item[5] for item in other_park_access_list
                                if item[9] and item[5] != '' and item[5] != '0' and item[5] != park_bid]
            if not parent_park_bids:
                continue
            parent_park_list = poi_online_query.get_parking_list(bids=parent_park_bids)
            if parent_park_list:
                park_access.is_valid = False
                park_access.filter_reason = f"link已被其余停车场关联,bid:{parent_park_list[0][7]}"
                continue
    return proceed()


def filter_pushed_intelligence(ctx: context.Context, proceed):
    """
    过滤已推送的情报 buffer 30范围存在则不推送
    Args:
        ctx:
        proceed:

    Returns:

    """
    pushed_node_geom_list = []
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        park_access_sp = wkt.loads(park_access.node_geom) if park_access.type == context.PARKING_ACCESS_TYPE_NODE \
            else wkt.loads(park_access.link_geom)
        if len([item for item in pushed_node_geom_list if
                park_access_sp.distance(wkt.loads(item)) < 30 / COORDINATE_FACTOR]) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"情报去重 buffer 30范围存在"
            continue
        # 获取已推送情报
        if ctx.parking_info.bid != '':
            pushed_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom(
                park_bid=ctx.parking_info.bid)
        else:
            pushed_intelligence_list = poi_online_query.get_pushed_park_access_intelligence_by_geom(
                outside_id=ctx.park_intelligence_id)
        if not pushed_intelligence_list:
            pushed_node_geom_list.append(park_access.node_geom if park_access.type == context.PARKING_ACCESS_TYPE_NODE
                                         else park_access.link_geom)
            continue
        # 已推送情报过滤
        for pushed_intelligence in pushed_intelligence_list:
            intelligence_sp = wkt.loads(pushed_intelligence['geom'])
            if pushed_intelligence['node_id'] != '':
                node_link_ids = pushed_intelligence['node_id'].split('|')
                if len(node_link_ids) == 2 and node_link_ids[1] != '':
                    link_list = ctx.dao.road_dao.get_nav_link_by_link_ids([node_link_ids[1]])
                    intelligence_sp = wkt.loads(link_list[0][0]) if link_list else intelligence_sp
            if intelligence_sp.distance(park_access_sp) < 30 / COORDINATE_FACTOR_NEW:
                park_access.is_valid = False
                park_access.filter_reason = f"已推送的情报 buffer 30范围存在\t{pushed_intelligence['qb_id']}"
                break

    proceed()


def filter_pushed_intelligence_by_new_park(ctx: context.Context, proceed):
    """
    过滤已推送的情报 buffer 30范围存在则不推送
    Args:
        ctx:
        proceed:

    Returns:

    """
    pushed_node_geom_list = []
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        if len([item for item in pushed_node_geom_list if
                node_sp.distance(wkt.loads(item)) < 30 / COORDINATE_FACTOR]) > 0:
            park_access.is_valid = False
            park_access.filter_reason = f"情报去重 buffer 30范围存在"
            continue
        is_pushed_intelligence = poi_online_query.get_pushed_park_access_intelligence_by_geom_by_kv(
            node_sp.buffer(30 / COORDINATE_FACTOR).wkt, outside_id=ctx.park_intelligence_id, bid=ctx.parking_info.bid)
        if not is_pushed_intelligence:
            pushed_node_geom_list.append(park_access.node_geom)
            continue
        park_access.is_valid = False
        park_access.filter_reason = f"已推送的情报 buffer 30范围存在\t{is_pushed_intelligence[0]}"
    return proceed()


def filter_by_distance(ctx: context.Context, proceed):
    """
    过滤已推送的情报 buffer 30范围存在则不推送
    Args:
        ctx:
        proceed:

    Returns:

    """
    show_tags = ['地上停车场', '地下停车场', '立体停车场', '门前停车场', '停车场']
    sub_park_list = []
    if ctx.aoi_info.bid:
        sub_park_list = ctx.dao.poi_online_query.get_parking_list(show_tags=show_tags, parent_id=ctx.aoi_info.bid)
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        node_sp = wkt.loads(park_access.node_geom)
        park_area_wkt = ctx.parking_info.park_area_gcj_wkt \
            if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt).buffer(
            20 / COORDINATE_FACTOR).wkt
        park_area_sp = wkt.loads(park_area_wkt)
        parent_tag = ctx.aoi_info.std_tag
        if ((parent_tag in FILTER_DISTANCE_CATEGORY or "旅游景点" in parent_tag or len(sub_park_list) > 1)
                and node_sp.distance(park_area_sp) > 150 / 110000):
            park_access.is_valid = False
            park_access.filter_reason = f"点位距离过滤"
            continue
    proceed()


def filter_by_high_link(ctx: context.Context, proceed):
    """
    高等级道路过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            continue
        if ctx.aoi_info.wkt:
            if utils.is_over_high_road_park_access(park_access.node_geom, ctx.aoi_info.wkt):
                park_access.is_valid = False
                park_access.filter_reason = f"横跨高等级道路过滤"
                continue
        park_area_wkt = ctx.parking_info.park_area_gcj_wkt \
            if ctx.parking_info.park_area_gcj_wkt else ctx.parking_info.park_area_aoi_30m_wkt
        park_area_wkt = park_area_wkt \
            if park_area_wkt else wkt.loads(ctx.parking_info.park_wkt).buffer(10 / COORDINATE_FACTOR).wkt
        if utils.is_over_high_road_park_access(park_access.node_geom, park_area_wkt):
            park_access.is_valid = False
            park_access.filter_reason = f"横跨高等级道路过滤"
            continue

    proceed()
