# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行政区划聚合算法调研
"""
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

from common import coordinate_trans, common_tool
from shapely.geometry import Point, LineString, Polygon, MultiPolygon
from shapely import wkt
from base64 import urlsafe_b64encode, urlsafe_b64decode
import hashlib
import psycopg2
import tqdm

from aoi.dao.poi_online_query import PoiOnlineQuery
pdd_sd_dao = PoiOnlineQuery()


# 生成 MD5 哈希值
def hash_md5(data):
    """
    md5加密
    Args:
        data:

    Returns:

    """
    return hashlib.md5(data.encode()).hexdigest()


def run():
    """
    行政区划聚合算法调研
    Returns:

    """
    sql = f"""
        select st_astext(geom) from bad_admin_vil_gcj 
        where st_isempty(geom) !='t' and pro_name='湖南省' and city_name='娄底市'
    """
    pdd_sd_dao.execute(sql)
    bad_admin_list = pdd_sd_dao.fetchall()
    for bad_admin in tqdm.tqdm(bad_admin_list):
        wkt1_shapely = wkt.loads(bad_admin[0])
        if wkt1_shapely.geom_type == "MultiPolygon":
            wkt_shapely_list = wkt1_shapely.geoms
        else:
            wkt_shapely_list = [wkt1_shapely]
        for item in wkt_shapely_list:
            wkt1_coords_list = item.exterior.coords
            for coord1 in wkt1_coords_list:
                point_wkt = f"point({coord1[0]} {coord1[1]})"
                guid = hash_md5(point_wkt)
                sql = f"insert into bad_admin_switch(guid, point_wkt) values('{guid}', '{point_wkt}')"
                pdd_sd_dao.execute(sql)

def switch_to_bd09_new():
    """
    bd09 坐标转换
    Returns:

    """
    sql = "select distinct guid,point_wkt from bad_admin_switch"
    pdd_sd_dao.execute(sql)
    point_wkt_list = pdd_sd_dao.fetchall()
    for point_wkt in tqdm.tqdm(point_wkt_list):
        point_wkt_gcj = point_wkt[1]
        point_wkt_shapely = wkt.loads(point_wkt_gcj)
        point_gcj = coordinate_trans.Point(0, 0)
        origin_wkt = coordinate_trans.Point(point_wkt_shapely.x, point_wkt_shapely.y)
        coordinate_trans.coord_trans("gcj02ll", "bd09mc", origin_wkt, point_gcj)
        point_wkt_bd09 = f"point({point_gcj.x} {point_gcj.y})"
        sql = f"update bad_admin_switch set point_wkt_bd09_new='{point_wkt_bd09}' where guid='{point_wkt[0]}'"
        pdd_sd_dao.execute(sql)


def switch_to_bd09():
    """
    转换到 bd09 坐标
    Returns:

    """
    sql = f"""
           select st_astext(geom),guid,village_name,pro_name from bad_admin_vil_gcj 
           where st_isempty(geom) !='t' and pro_name='湖南省' and city_name='娄底市'
       """
    pdd_sd_dao.execute(sql)
    bad_admin_list = pdd_sd_dao.fetchall()
    for bad_admin in tqdm.tqdm(bad_admin_list):
        wkt1_shapely = wkt.loads(bad_admin[0])
        if wkt1_shapely.geom_type == "MultiPolygon":
            wkt_shapely_list = wkt1_shapely.geoms
        else:
            wkt_shapely_list = [wkt1_shapely]
        wkt_shapely_bd09_list = []
        for item in wkt_shapely_list:
            coords_bd09 = []
            wkt1_coords_list = item.exterior.coords
            for coord1 in wkt1_coords_list:
                point_wkt = f"point({coord1[0]} {coord1[1]})"
                guid = hash_md5(point_wkt)
                sql = f"select point_wkt_bd09 from bad_admin_switch where guid='{guid}' limit 1"
                pdd_sd_dao.execute(sql)
                res = pdd_sd_dao.fetchone()
                point_wkt_bd09 = wkt.loads(res[0])
                coords_bd09.append(point_wkt_bd09.coords[0])
            wkt_shapely_bd09_list.append(Polygon(coords_bd09))
        if len(wkt_shapely_bd09_list) == 1:
            wkt_shapely_bd09 = wkt_shapely_bd09_list[0]
        else:
            wkt_shapely_bd09 = MultiPolygon(wkt_shapely_bd09_list)
        sql = f"""
            insert into bad_admin_switch_achieve(guid,village_name,pro_name,geom) values('{bad_admin[1]}',
            '{bad_admin[2]}','{bad_admin[3]}','{wkt_shapely_bd09.wkt}')
        """
        pdd_sd_dao.execute(sql)


if __name__ == '__main__':
    # run()
    # switch_to_bd09()
    switch_to_bd09_new()
    # wkt1_shapely = wkt.loads(wkt1)
    # wkt2_shapely = wkt.loads(wkt2)
    # wkt1_coords = wkt1_shapely.exterior.coords
    # wkt2_coords = wkt2_shapely.exterior.coords
    # for coord1 in wkt1_coords:
    #     for coord2 in wkt2_coords:
    #         if coord1 == coord2:
    #             current_coords = f"point({coord1[0]} {coord1[1]})"
    #             print(f"hash加密数据:{hash_md5(current_coords)}")
    #             exit(1)
    #         else:
    #             continue
