"""
airflow示例文件
"""
from airflow.models.dag import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta

# constants
MY_NAME = "林斌"
MY_NUMBER = 19


def multiply_by_23(number):
    """Multiplies 交通枢纽考核清单.txt number by 23 and prints the results to Airflow logs."""
    result = number * 23
    print(result)


with DAG(
        dag_id="my_dag_queue_aoi_strategy_eks",
        start_date=datetime(2024, 1, 18),
        schedule=timedelta(hours=1),
        catchup=False,
        tags=["教程例子"],
        default_args={
            "owner": MY_NAME,
            "retries": 2,
            "queue": 'aoi_strategy_opera',
            "retry_delay": timedelta(hours=1)
        }
):
    t1 = BashOperator(
        task_id="say_my_name",
        queue='aoi_strategy_opera',
        bash_command=f"echo {MY_NAME}"
    )

    t2 = PythonOperator(
        queue='aoi_strategy_opera',
        task_id="multiply_my_number_by_23",
        python_callable=multiply_by_23,
        op_kwargs={"number": MY_NUMBER}
    )
    t1 >> t2
