#-*- coding: utf-8 -*-
"""
根据bid导出子点 相关属性
"""
import time
import psycopg2

backConf = {"db": "master_back",
            "user": "master_back_se_ro", "pwd": "mapread",
            "host": "**************", "port": 5432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

#连接poi库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
def process(filename):
    """
    process 正式处理流程
    """
    file = open(filename + '_result.txt', 'w')
    file.write("BID\t名称\t类型（飞机场/火车站/汽车站）\t城市\t子点分级（几级子点）\t子点BID\t子点名称\t子点TAG\t子点的父点BID\t子点的父点名称\t子点gcj02坐标\n")
    for bid in open("/home/<USER>/chengqiong/aoi-strategy/aoi_access/txt/" + filename + ".txt"):
        bid = bid.replace(" ", "").replace("\n", "")
        sql = "select bid,name,show_tag,city from  poi where bid = '{}'".format(bid)
        poiDao.execute(sql)
        res = poiDao.fetchone()
        if res:
            child1 = '1'
            child2 = '2'
            child3 = '3'
            where = ''
            type = ''
            if res[2] == '飞机场' or res[2] == '航站楼':
                res[2] == '飞机场'
                where = "and (tag ~ '航站楼' or tag ~ '出入口' or tag ~ '停车场')"
            elif res[2] == '火车站' or res[2] == '高铁站' or res[2] == '地铁站':
                res[2] == '火车站'
                where = "and (tag ~ '售票处' or tag ~ '出入口' \
                 or tag ~ '停车场' or tag ~ '站前广场')"
            elif res[2] == '汽车站' or res[2] == '长途汽车站':
                res[2] == '汽车站'
                where = "and (tag ~ '售票处' or tag ~ '出入口' or tag ~ '停车场')"
            else:
                print(bid)
                print(res[1])
                print(res[2])
            print(bid)
            sql = "select bid,name,tag,relation_bid,st_astext(geometry) \
                from poi where relation_bid = '{}' {} ".format(bid, where)
            poiDao.execute(sql)
            res1 = poiDao.fetchall()
            if res1:
                for res1_v in res1:
                    file.write(res[0] + "\t" + res[1] + "\t" + res[2] + "\t" + res[3] + "\t" + child1 + "\t" \
                        + res1_v[0] + "\t" + res1_v[1] + "\t" + res1_v[2] + "\t" + res1_v[3] + "\t" \
                            + res[1] + "\t" + res1_v[4] + "\n")             
                    sql = "select bid,name,std_tag,relation_bid,st_astext(geometry)\
                        from poi where relation_bid = '{}' {}".format(res1_v[0], where)
                    poiDao.execute(sql)
                    res2 = poiDao.fetchall()
                    for res2_v in res2:
                        # BID、名称、类型（飞机场/火车站/汽车站）、城市、子点分级（几级子点）、子点BID、子点名称、子点TAG、子点的父点BID、子点的父点名称
                        file.write(res[0] + "\t" + res[1] + "\t" + res[2] + "\t" + res[3] + "\t" + child2 + "\t" \
                            + res2_v[0] + "\t" + res2_v[1] + "\t" + res2_v[2] + "\t" + res2_v[3] + "\t" \
                                + res1_v[1] + "\t" + res2_v[4] + "\n")             
                        sql = "select bid,name,std_tag,relation_bid,st_astext(geometry)\
                            from poi where relation_bid = '{}' {}".format(res2_v[0], where)
                        poiDao.execute(sql)
                        res3 = poiDao.fetchall()
                        for res3_v in res3:
                            file.write(res[0] + "\t" + res[1] + "\t" + res[2] + "\t" + res[3] + "\t" + child3 + "\t" \
                                + res3_v[0] + "\t" + res3_v[1] + "\t" + res3_v[2] + "\t" + res3_v[3] + "\t" \
                                    + res2_v[1] + "\t" + res3_v[4] + "\n")             
            else:
                file.write(res[0] + "\t" + res[1] + "\t" + res[2] + "\t" + res[3] + "\n")             
    file.close()

if __name__ == "__main__":
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    process('done')
    process('undone')
    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))