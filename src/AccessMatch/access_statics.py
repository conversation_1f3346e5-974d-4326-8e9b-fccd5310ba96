# !/usr/bin/env python3
# encoding=utf-8
"""
出入口通知
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")
import datetime
from datetime import timedelta
from pg_tool import PgTool
from common import common_tool


def process():
    """
    :return: void
    """

    date = datetime.datetime.now().date()
    date_yesterday = date - timedelta(days=1)
    message = f"每日出入口上量统计：\n统计日期：{date}\n"
    add_num = get_access_batch_num_by_date(date_yesterday, date, 'add')
    message += f"批处理-增加出入口上量：{add_num}\n"
    del_num = get_access_batch_num_by_date(date_yesterday, date, 'delete')
    message += f"批处理-删除出入口上量：{del_num}\n"
    add_manu_num = get_access_manu_num_by_date(date_yesterday, date, 'add')
    message += f"人工作业-增加出入口上量：{add_manu_num}\n"
    del_manu_num = get_access_manu_num_by_date(date_yesterday, date, 'delete')
    message += f"人工作业-删除出入口上量：{del_manu_num}\n"
    num_no_imp = get_access_to_imp_by_date(date_yesterday, date)
    message += f"待入库出入口总量：{num_no_imp}\n"

    message += "----------------\n"
    
    this_month_start = datetime.datetime(date.year, date.month, 1).strftime('%Y-%m-%d')
    message += f"本月累计上量统计：\n统计日期：{this_month_start}~{date}\n"
    add_num = get_access_batch_num_by_date(this_month_start, date, 'add')
    message += f"批处理-增加出入口上量：{add_num}\n"
    del_num = get_access_batch_num_by_date(this_month_start, date, 'delete')
    message += f"批处理-删除出入口上量：{del_num}\n"
    add_manu_num = get_access_manu_num_by_date(this_month_start, date, 'add')
    message += f"人工作业-增加出入口上量：{add_manu_num}\n"
    del_manu_num = get_access_manu_num_by_date(this_month_start, date, 'delete')
    message += f"人工作业-删除出入口上量：{del_manu_num}\n"
    num_no_imp = get_access_to_imp_by_date(this_month_start, date)
    message += f"待入库出入口总量：{num_no_imp}\n"
    common_tool.send_hi_access_batch(message)


def get_access_batch_num_by_date(date_start, date_end, type):
    """
    :param date_start:
    :param date_end:
    :return:
    """
    with PgTool() as pg:
        sql = f"""
            select 
                count(distinct(bid, node_id)) 
            from gate_bid_change_history
            where
                action = '{type}'
                and create_time > '{date_start}'
                and create_time < '{date_end}'
                and resource = 6
                and imp_state = 2
        """
        pg.cursor_poi.execute(sql)
        return pg.cursor_poi.fetchone()[0]


def get_access_manu_num_by_date(date_start, date_end, type):
    """
    :param date_start:
    :param date_end:
    :return:
    """
    with PgTool() as pg:
        sql = f"""
            select 
                count(distinct(bid, node_id)) 
            from gate_bid_change_history
            where
                action = '{type}'
                and create_time > '{date_start}'
                and create_time < '{date_end}'
                and resource != 6
                and imp_state = 2
        """
        pg.cursor_poi.execute(sql)
        return pg.cursor_poi.fetchone()[0]

def get_access_to_imp_by_date(date_start, date_end):
    """
    :param date_start:
    :param date_end:
    :return:
    """
    with PgTool() as pg:
        sql = f"""
            select 
                count(distinct(bid, node_id)) 
            from gate_bid_change_history
            where
                create_time > '{date_start}'
                and create_time < '{date_end}'
                and imp_state = 0
                and (action = 'add'
                    or action = 'delete'
                )
        """
        pg.cursor_poi.execute(sql)
        return pg.cursor_poi.fetchone()[0]
    

if __name__ == "__main__":
    process()
