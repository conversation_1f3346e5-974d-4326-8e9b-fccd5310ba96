<?php
echo "初始化城市准备开始".date("Y-m-d H:i:s")."\n";
require_once('CNsHead.class.php');
$filename = $argv[1];
$str = file_get_contents($filename);
$arr = explode("\n",$str);
$output = $argv[2];
$error_output = $argv[3];
echo "初始化城市准备ok".date("Y-m-d H:i:s")."\n";
/**
 *  从redis中取得城市，转码
 *  @param $bid_arr bid数组
 *  @param $nshead  nshead数据包
 *  @return $city   需要的城市名称
 */
function get_bpoi($nshead, $bid_arr){
    $arr = array(
        '(uint32)type' => 1,
        'action' => "get",
        'bidlist' => $bid_arr
    );
    $di_req = mc_pack_array2pack($arr, PHP_MC_PACK_V2);
    $ret_arr['body_len'] = strlen($di_req);
    $ret_arr['log_id'] = 123;
    $ret_arr['provider'] = "mytest";
    $flag = rand(1,2);
    if ($flag == 1) {
        $socket = fsockopen("gzns-map-poi-redis01.gzns", 8081);
    }else{
        $socket = fsockopen("gzns-map-poi-redis02.gzns", 8081);
    }
    $res = $nshead->nshead_write($socket, $ret_arr, $di_req);
    $di_res = $nshead->nshead_read($socket);
    fclose($socket);
    $res = mc_pack_pack2array($di_res['buf']);

    $city = mb_convert_encoding($res['poilist'][0]['city_name'], "UTF-8", "GBK");
    if(empty($city))
    {
        $error = '[error] 未找到城市名称- bid:'.json_encode($bid_arr).' res:'.json_encode($res).' city:'.$city;
        echo $error."\n";
    }
    return $city;
}
try{
    $nshead = new NSHead();
    foreach($arr as $k => $v){
        if(($k%100)===0){
            echo "初始化城市准备ok.num【".$k."】条".date("Y-m-d H:i:s")."\n";
        }
        if (empty($v)) {
            echo "line:".$k."为空";
            continue;
        }
        $arr1 = explode("\t",$v);
        if ($arr1[2] != '""' && !empty($arr1[2])) {
            if($arr1[11]=='CJH' || $arr1[11]=='WQT' || $arr1[11]=='cjh' || $arr1[11]=='wqt'){
                $bid = array($arr1[2]);
                $city = get_bpoi($nshead, $bid);
                if(!empty($city)){
                    $arr1[10] = $city;
                    $data = implode("\t",$arr1);
                    file_put_contents($output,$data."\n",FILE_APPEND);
                }
            }else{
                echo "[info]：行数".$k."cp=".$arr1[11]."不处理bid=".$arr1[2]."\n";
            }
            
        }else{
            echo "[info]：行数".$k."没有bid"."\n";
        }
    }
    echo "初始化城市准备ok.num【".count($arr)."】条".date("Y-m-d H:i:s")."\n";
}catch(Exception $e){
    $err = '[error] getcity.php:'.$e->getMessage()."\n";
    file_put_contents($error_output, $err,FILE_APPEND);
    echo $err;
}