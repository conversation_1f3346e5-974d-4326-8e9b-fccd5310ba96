# -*- coding: UTF-8 -*-
################################################################################
"""
直接使用数据库里的轨迹数据
Authors: <AUTHORS>
Date:    2022/05/27 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import sys
import shapely.wkt
import shapely.geometry
import uuid
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize

def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']


cf = ConfigParser.ConfigParser()
cf.read('../../conf/config.ini')

dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online_test"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()

conn_trans = psycopg2.connect(database = "trans_id", user = "trans_id_se_rw", password = "cpmkukky", \
            host = "*************", port = 5432)
cursor_trans = conn_trans.cursor()

conn_tra = psycopg2.connect(database = "poi_tra_test", user = "poi_tra_test_se_rw", password = "ihqvibzf", \
            host = "***********", port = 5432)
cursor_tra = conn_tra.cursor()

def get_tra_data_by_bid(bid):
    """
    根据bid查询轨迹信息
    Args:
        bid: poi的bid
    Return:
        tra_data: 轨迹信息
    """
    tra_data = []
    sql = "select tra_data from bid_tra_info where bid = %s"
    cursor_tra.execute(sql, [bid])
    res = cursor_tra.fetchall() 
    if res is not None and len(res) != 0:
        for v in res:
            tra_data = tra_data + v[0].split("#")

    sql = "select tra_data from bid_tra_info_all_2 where bid = %s"
    cursor_tra.execute(sql, [bid])
    res = cursor_tra.fetchall() 
    if res is not None and len(res) != 0:
        for v in res:
            tra_data = tra_data + v[0].split("#")

    sql = "select tra_data from bid_tra_info_all_3 where bid = %s"
    cursor_tra.execute(sql, [bid])
    res = cursor_tra.fetchall() 
    if res is not None and len(res) != 0:
        for v in res:
            tra_data = tra_data + v[0].split("#")

    sql = "select tra_data from bid_tra_info_all_4 where bid = %s"
    cursor_tra.execute(sql, [bid])
    res = cursor_tra.fetchall() 
    if res is not None and len(res) != 0:
        for v in res:
            tra_data = tra_data + v[0].split("#")
    
    return tra_data

def convert_short_id(link_id):
    """linkid转短id
    Args:
        link_id: 道路的linkid
    Return:
        res: 道路的短id
    """
    sql = "select tid from image_r where sid = %s"
    cursor_trans.execute(sql, [link_id])
    res = cursor_trans.fetchone()
    if res is None:
        return ""
    return res[0]


def query_link_geom(link_id):
    """linkid的范围框查询
    Args:
        link_id: 道路的linkid
    Return:
        geom
    """
    sql = "select st_astext(geom) from nav_link where link_id = %s"
    cursor_road.execute(sql, [link_id])
    return cursor_road.fetchone()[0]


def main():
    """
    轨迹策略修改版，准确率有一定提升，但会漏召部分数据
    策略未完全验证效果，还需进一步优化
    """
    #
    bid_gate_list = []
    with open("bid_node.txt", "r") as f:
        lines = f.readlines()
        for line in lines:
            data = line.strip().split('\t')
            bid_gate_list.append([data[0], data[1]])

    fw = open("bide_node_tra_match.txt", "w")
    fw_tra = open("bide_node_tra_match_tra.txt", "w")
    for data in bid_gate_list:
        bid = data[0]
        node_id = data[1]
        sql = "select in_linkid, out_linkid from nav_gate where node_id = %s limit 1"
        cursor_road.execute(sql, [node_id])
        res_road = cursor_road.fetchone()
        if res_road is None:
            continue
        inlink_geom = query_link_geom(res_road[0])
        outlink_geom = query_link_geom(res_road[1])
        match_res = "fail"
        in_linkid = convert_short_id(res_road[0])
        out_linkid = convert_short_id(res_road[1])
        tra_info_list = get_tra_data_by_bid(bid)
        if len(tra_info_list) == 0:
            continue
        for tra_info_tmp in tra_info_list:
            pos_info = ""
            tra_data = tra_info_tmp.split("@", 1)[1].split(";")
            length = len(tra_data)
            
            
            #找出link序列
            link_list = []
            index_list = []
            pos_info = ""
            for index, point_info in enumerate(tra_data):
                point_info_tmp = point_info.split(',')
                link_tmp = point_info_tmp[3].split('|')
                pos_info = pos_info + str(point_info_tmp[0]) + "," + str(point_info_tmp[1]) + ";"
                link_list = link_list + link_tmp
                for i in range(len(link_tmp)):
                    index_list.append(index)
            
            pass_flag = False
            for i in range(len(link_list) - 1, 1, -1):
                #经过了大门
                if (link_list[i][:-1] == out_linkid and link_list[i - 1][:-1] == in_linkid) or \
                    (link_list[i][:-1] == in_linkid and link_list[i - 1][:-1] == out_linkid):
                    left_pos = index_list[i]
                    if len(tra_data[left_pos].split(',')[3].split('|')) > 2 or \
                        len(tra_data[left_pos - 1].split(',')[3].split('|')) > 2:
                        pass_flag = True
                        break
                    
                    #如果点离link距离比较远 过滤
                    sql = "select st_distance(st_geomfromtext(%s, 4326), wgs2gcj(st_geomfromtext(%s, 4326)))"
                    if link_list[i] == out_linkid:
                        link_geom =  outlink_geom
                    else:
                        link_geom =  inlink_geom
                    node_geom = "POINT(%s %s)" % (tra_data[index_list[i]].split(',')[0], 
                                                  tra_data[index_list[i]].split(',')[1])
                    cursor_road.execute(sql, [link_geom, node_geom])
                    if cursor_road.fetchone()[0] > 0.0006:
                        pass_flag = True
                        break
                    match_res = "success"

                # 如果匹配成功或者经过了大门但是轨迹不满足质量要求，则跳过
                if match_res == "success" or pass_flag:
                    break
            if match_res == "success":
                break
                    
        fw.write("%s\t%s\t%s\n" % (bid, node_id, match_res))
    fw_tra.close()
    fw.close()


if __name__ == '__main__':
    main()
