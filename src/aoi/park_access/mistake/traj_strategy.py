# -*- coding: utf-8 -*-
"""
通过轨迹挖掘错误出入口
"""
import os
import sys
from shapely.geometry import Polygon, MultiPolygon, Point, MultiPoint
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.park_access.mistake import context
from src.aoi import utils

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000
AREA_FACTOR = 10000000000


def traj_strategy(ctx: context.Context):
    """
    轨迹策略挖掘错误出入口
    Args:
        ctx:

    Returns:

    """
    logger.info("轨迹策略挖掘错误出入口")
    by_end_traj(ctx)


def by_yaw_exp_traj(ctx: context.Context):
    """
    偏航轨迹策略
    Args:
        cx:

    Returns:

    """
    logger.info("by_yaw_exp_traj")
    exp_traj_list = ctx.traj.aoi_exp_traj_list + ctx.traj.park_exp_traj_list
    if len(exp_traj_list) == 0:
        return
    for sub_park_access in ctx.sub_pois:
        park_access_sp = wkt.loads(sub_park_access.point_gcj)
        park_access_buffer_5m_sp = park_access_sp.buffer(5 / COORDINATE_FACTOR_NEW)
        for exp_traj in exp_traj_list:
            if exp_traj['num_track'] < 10 or exp_traj['yaw_rate'] < 0.8:
                continue
            exp_traj_sp = wkt.loads(exp_traj['geom'])
            exp_traj_end_sp = Point(exp_traj_sp.coords[-1])
            # 如果父点经验轨迹与出入口相交, 或者终点距离小于30m, 则认为有效
            if exp_traj_sp.intersects(park_access_buffer_5m_sp) \
                    or exp_traj_end_sp.distance(park_access_sp) < 30 / COORDINATE_FACTOR_NEW:
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.link_geom = sub_park_access.link_geom
                park_access.type = sub_park_access.type
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_YAW_EXP_TRAJ
                park_access.strategy_value = exp_traj['yaw_rate']
                park_access.remark = f"traj:{exp_traj_sp.wkt},yaw_traj:{exp_traj['yaw_rate']}"
                park_access.confidence = 80
                ctx.parking_access_list.append(park_access)
                break
    return


def by_end_traj(ctx: context.Context):
    """
    终点策略挖掘错误出入口
    Args:
        ctx:

    Returns:

    """
    logger.info("终点策略挖掘错误出入口")
    traj_rate_value = 0.3
    # 主点终点轨迹过滤
    fileter_aoi_end_traj_list = utils.filter_traj_by_area(ctx.traj.dest_traj_list, ctx.aoi_info.wkt)
    # 停车点终点轨迹过滤
    park_wkt_buffer_30m = wkt.loads(ctx.parking_info.park_wkt).buffer(30 / COORDINATE_FACTOR).wkt
    filter_park_area = ctx.aoi_info.wkt if not ctx.aoi_info.wkt else ctx.parking_info.park_area_gcj_wkt \
        if ctx.parking_info.park_area_gcj_wkt else park_wkt_buffer_30m
    fileter_park_end_traj_list = utils.filter_traj_by_area(ctx.traj.park_dest_traj_list, filter_park_area)
    logger.info(f"原始轨迹AOI:{len(ctx.traj.dest_traj_list)},停车点终点轨迹:{len(ctx.traj.park_dest_traj_list)}")

    # 计算每个出入口的轨迹占比
    aoi_traj_rate_map, park_traj_rate_map = cal_park_access_traj_rate(ctx, fileter_aoi_end_traj_list,
                                                                      fileter_park_end_traj_list)
    aoi_traj_total_num = sum(aoi_traj_rate_map.values())
    park_traj_total_num = sum(park_traj_rate_map.values())
    logger.info(f"主点终点轨迹总数:{len(fileter_aoi_end_traj_list)},停车点终点轨迹总数:{len(fileter_park_end_traj_list)}")
    # 如果仅有一个出入口,且轨迹通量小于10, 则认为无效出入口
    if len(ctx.sub_pois) == 1 and aoi_traj_total_num < 10 and park_traj_total_num < 10:
        sub_park_access = ctx.sub_pois[0]
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.link_geom = sub_park_access.link_geom
        park_access.type = sub_park_access.type
        park_access.confidence = cal_traj_confidence(1)
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE_ONE_ACCESS
        park_access.strategy_value = aoi_traj_total_num
        park_access.remark = f"单入口通过轨迹数量较少:" \
                             f"aoi_traj_num:{aoi_traj_rate_map[sub_park_access.bid]}," \
                             f"aoi_traj_total_num:{aoi_traj_total_num}," \
                             f"park_traj_total_num:{park_traj_total_num}"
        ctx.parking_access_list.append(park_access)
        return

    # 多个出入口时, 通量小于30%认为异常
    park_access_map = {item.bid: item for item in ctx.sub_pois}
    if aoi_traj_total_num >= park_traj_total_num:
        for park_access_bid, aoi_pass_traj_num in aoi_traj_rate_map.items():
            # 主点轨迹通量小于30%, 且停车场轨迹通量小于30%, 则认为无效出入口
            park_pass_traj_rate = park_traj_rate_map[park_access_bid] / park_traj_total_num \
                if park_traj_total_num else 0
            aoi_pass_traj_rate = aoi_pass_traj_num / aoi_traj_total_num if aoi_traj_total_num else 0
            traj_info = f"主点出入口轨迹通量:{park_access_bid}:" \
                        f"主点轨迹占比:{aoi_pass_traj_rate}," \
                        f"停车场轨迹占比:{park_pass_traj_rate}," \
                        f"aoi_traj_total_num:{aoi_traj_total_num}," \
                        f"aoi_pass_traj_num:{aoi_pass_traj_num}," \
                        f"park_traj_total_num:{park_traj_total_num}, " \
                        f"park_traj_num:{park_traj_rate_map[park_access_bid]}"
            logger.info(traj_info)
            if aoi_pass_traj_rate < traj_rate_value and (
                    park_traj_total_num < 10 or (park_traj_total_num >= 10 and park_pass_traj_rate < traj_rate_value)):
                sub_park_access = park_access_map[park_access_bid]
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.link_geom = sub_park_access.link_geom
                park_access.type = sub_park_access.type
                park_access.confidence = cal_traj_confidence(2, aoi_traj_total_num, park_traj_total_num,
                                                             aoi_pass_traj_rate, park_pass_traj_rate)
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE
                park_access.strategy_value = aoi_pass_traj_rate
                park_access.remark = traj_info
                ctx.parking_access_list.append(park_access)
    else:
        for park_access_bid, park_pass_traj_num in park_traj_rate_map.items():
            # 主点轨迹通量小于30%, 且停车场轨迹通量小于30%, 则认为无效出入口
            aoi_pass_traj_rate = aoi_traj_rate_map[park_access_bid] / aoi_traj_total_num if aoi_traj_total_num else 0
            park_pass_traj_rate = park_pass_traj_num / park_traj_total_num if park_traj_total_num else 0
            traj_info = f"停车场出入口轨迹通量:{park_access_bid}:" \
                        f"主点轨迹占比:{aoi_pass_traj_rate}," \
                        f"停车场轨迹占比:{park_pass_traj_rate}," \
                        f"aoi_traj_total_num:{aoi_traj_total_num}," \
                        f"aoi_pass_traj_num:{aoi_traj_rate_map[park_access_bid]}," \
                        f"park_traj_total_num:{park_traj_total_num}, " \
                        f"park_traj_num:{park_traj_rate_map[park_access_bid]}"
            logger.info(traj_info)
            if park_pass_traj_rate < traj_rate_value and (
                    aoi_traj_total_num < 10 or (aoi_traj_total_num >= 10 and aoi_pass_traj_rate < traj_rate_value)):
                sub_park_access = park_access_map[park_access_bid]
                park_access = context.ParkingAccess()
                park_access.bid = sub_park_access.bid
                park_access.node_geom = sub_park_access.point_gcj
                park_access.node_id = sub_park_access.relation_node_id
                park_access.link_id = sub_park_access.relation_link_id
                park_access.link_geom = sub_park_access.link_geom
                park_access.type = sub_park_access.type
                park_access.confidence = park_pass_traj_rate
                park_access.confidence = cal_traj_confidence(3, aoi_traj_total_num, park_traj_total_num,
                                                             aoi_pass_traj_rate, park_pass_traj_rate)
                park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_PARENT_EXP_NOT_ARRIVE
                park_access.remark = traj_info
                ctx.parking_access_list.append(park_access)
    return


def cal_traj_confidence(type, aoi_traj_total=0, park_traj_total=0, aoi_pass_rate=0, park_pass_rate=0):
    """
    计算轨迹置信度
    Args:
        type: 1 -- 单入口少量轨迹通过; 2 -- 主点轨迹; 3 -- 停车场轨迹
        aoi_traj_total:
        park_traj_total:
        aoi_pass_rate:
        park_pass_rate:

    Returns:

    """
    # 单入口少量轨迹通过, 置信度为60
    if type == 1:
        return 0
    # 主点轨迹
    if type == 2:
        if aoi_traj_total >= 20 and aoi_pass_rate < 0.1:
            return 100
        elif aoi_traj_total >= 20 and aoi_pass_rate < 0.2:
            return 80
        elif aoi_traj_total >= 20 and aoi_pass_rate < 0.3:
            return 60
        return 0
    elif type == 3:
        if park_traj_total >= 20 and park_pass_rate < 0.1:
            return 100
        elif park_traj_total >= 20 and park_pass_rate < 0.2:
            return 80
        elif park_traj_total >= 20 and park_pass_rate < 0.3:
            return 60
        return 0
    return 0


def cal_park_access_traj_rate(ctx: context.Context, aoi_end_traj_list, park_end_traj_list):
    """
    计算每个出入口的轨迹占比
    Args:
        ctx:

    Returns:

    """
    # 计算每个出入口轨迹占比
    aoi_traj_rate_map = {}
    park_traj_rate_map = {}
    for sub_park_access in ctx.sub_pois:
        park_access_sp = wkt.loads(sub_park_access.point_gcj)
        park_access_buffer_5m_sp = park_access_sp.buffer(5 / COORDINATE_FACTOR_NEW)
        # 如果出入口关联Link, 且Link小于50m, 且在AOI范围内, 范围取Link范围
        if sub_park_access.type == "LINK" and sub_park_access.link_geom != '' \
                and wkt.loads(sub_park_access.link_geom).length * COORDINATE_FACTOR_NEW < 50 \
                and ctx.aoi_info.wkt and wkt.loads(ctx.aoi_info.wkt).intersects(wkt.loads(sub_park_access.link_geom)):
            park_access_buffer_5m_sp = wkt.loads(sub_park_access.link_geom).buffer(3 / COORDINATE_FACTOR_NEW)
        # 计算AOI终点轨迹占比
        pass_traj_num = 0
        for traj_sp in aoi_end_traj_list:
            if traj_sp.intersects(park_access_buffer_5m_sp):
                pass_traj_num += 1
        aoi_traj_rate_map[sub_park_access.bid] = pass_traj_num
        # 计算停车场终点轨迹占比
        pass_traj_num = 0
        for traj_sp in park_end_traj_list:
            if traj_sp.intersects(park_access_buffer_5m_sp):
                pass_traj_num += 1
        park_traj_rate_map[sub_park_access.bid] = pass_traj_num
    return aoi_traj_rate_map, park_traj_rate_map


def cal_confidence():
    """
    计算情报置信度
    Returns:

    """
