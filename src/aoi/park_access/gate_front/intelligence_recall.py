# -*- coding: utf-8 -*-
"""
门前停车场情报补招
"""
import datetime
import multiprocessing
import sys
import os
import time
import uuid
from math import ceil

import tqdm
import requests
import json
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from loguru import logger
from shapely import wkt
from sklearn.cluster import DBSCAN

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from aoi.dao.poi_online_rw import PoiOnlineRW
from shapely import wkt
from loguru import logger
from common import pipeline
from aoi.park_access.gate_front import context
from aoi import utils
from aoi.park_access import park_utils
from aoi.park_access.gate_front import park_access_qc as qc
import intelligence_gate_front

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000

# logger.remove()
log_path = "./log/underground_intelligence.log"
logger.add(log_path, rotation="10 MB")
IS_TEST = True


def intelligence_recall(ctx: context.Context):
    """
    出入口情报扩招
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        intelligence_gate_front.get_near_street_poi_by_area,
        intelligence_gate_front.get_exp_traj_list,
        high_traj_pass_recall,
        save_park_access_by_recall,
    )
    pipe_list(ctx)


def high_traj_pass_recall(ctx: context.Context, proceed):
    """
    高通量轨迹入口扩招
    Args:
        ctx:
        proceed:
    """
    if len(ctx.traj.exp_traj_list) == 0:
        return proceed()
    exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list if item['num_track'] >= 3]
    if len(exp_traj_list) <= 5:
        exp_traj_list = [item['traj'] for item in ctx.traj.exp_traj_list]
    clusters = park_utils.get_clusters_by_traj(exp_traj_list, 15, 3)
    if not clusters:
        return proceed()
    area_sp = wkt.loads(ctx.area)
    already_cal_points_count = 0
    clusters_sort = sorted(clusters.items(), key=lambda items: len(items[1]), reverse=True)
    total_points_count = sum(len(value) for _, value in clusters.items())
    convex_hull_list = []
    for label, agg_points in clusters_sort:
        if already_cal_points_count >= total_points_count * 0.9:
            break
        points = [wkt.loads(p_wkt) for p_wkt in agg_points]
        already_cal_points_count += len(points)
        multipoint = MultiPoint(points)
        # logger.info(f"经验轨迹终点聚类: {multipoint.wkt}")
        convex_hull = multipoint.convex_hull.buffer(1 / COORDINATE_FACTOR)
        if len([item for item in convex_hull_list if convex_hull.distance(item) < 15 / COORDINATE_FACTOR_NEW]) > 0:
            continue
        convex_hull_list.append(convex_hull)
        if IS_TEST == '0':
            logger.info(f"经验轨迹终点聚类convex_hull: {convex_hull.wkt},points:{len(points)}")

        # 聚类终点附近40m已存在出入口成果则跳过
        convex_hull_buffer_30m = convex_hull.buffer(30 / COORDINATE_FACTOR_NEW)
        near_park_access_list = ctx.dao.poi_online_query.get_park_storefront_prod_access_list(
            parent_bids=[ctx.bid], geom=convex_hull_buffer_30m.wkt)
        if near_park_access_list:
            logger.info(f"经验轨迹终点聚类附近已存在出入口成果:{near_park_access_list}")
            continue
        # 如果聚类中心点附近有大门, 则取大门作为出入口
        nav_gates = ctx.dao.road_dao.get_nav_gate_by_geom(convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        if nav_gates:
            open_gates_node_ids = [item[0] for item in nav_gates]
            node_list = ctx.dao.road_dao.get_node_geom(open_gates_node_ids)
            node_info = min(node_list, key=lambda x: area_sp.distance(wkt.loads(x[0])))
            park_access = context.ParkingAccess()
            park_access.node_geom = node_info[0]
            park_access.node_id = node_info[1]
            park_access.type = context.PARKING_ACCESS_TYPE_NODE
            park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG_NODE_RECALL
            ctx.parking_access_list.append(park_access)
            continue
        # 查询Link
        link_list = ctx.dao.road_dao.get_intersects_nav_link_by_wkt_new(
            convex_hull.buffer(5 / COORDINATE_FACTOR_NEW).wkt)
        intersects_link_list = [item for item in link_list if item['kind'] == 8 and item['len'] < 30]
        if intersects_link_list:
            link_info = min(intersects_link_list, key=lambda x: convex_hull.distance(wkt.loads(x['geom'])))
            parking_access = context.ParkingAccess()
            parking_access.type = context.PARKING_ACCESS_TYPE_LINK
            parking_access.node_geom = convex_hull.representative_point().wkt
            parking_access.link_id = link_info[0]
            parking_access.link_geom = link_info[3]
            parking_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG_NODE_RECALL
            ctx.parking_access_list.append(parking_access)
            continue
        # 直接取聚类中心点
        park_access = context.ParkingAccess()
        park_access.node_geom = convex_hull.representative_point().wkt
        park_access.type = context.PARKING_ACCESS_TYPE_NODE
        park_access.strategy = context.GATE_FRONT_EXP_TRAJ_END_AGG_NODE_RECALL
        ctx.parking_access_list.append(park_access)
    return proceed()


def save_park_access_by_recall(ctx: context.Context, proceed):
    """
    出入口例行更新入库
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in sorted(ctx.parking_access_list, key=lambda x: x.is_valid):
            logger.info(f"TEST是否有效:{item.is_valid},关联类型:{item.type}, node_id位置\t{item.node_id}, 策略:{item.strategy}, "
                        f"link_id\t{item.link_id}, 过滤原因:{item.filter_reason}, node_geom\t{item.node_geom}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        ctx.guid = str(uuid.uuid4()).replace("-", "")
        source_id = f"storefront_access_add_{ctx.guid}"
        ctx.batch = f"park_access_gate_front_intelligence_add_{time.strftime('%Y%m%d', time.localtime())}"
        data = {
            'bid': ctx.bid,
            'strategy': park_access.strategy,
            'type': 'park_access_gate_front',
            'batch_number': ctx.batch,
            'geom': park_access.node_geom,
            'guid': ctx.guid,
            'source_id': source_id,
            'show_tag': '门前停车场',
            'outside_id': ctx.park_intelligence_id if ctx.park_intelligence_id else '',
            'status': 'INIT',
            'resp': '',
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': '',
            'outside_task_id': ctx.task_id,
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
        }
        ctx.new_intelligence_ids.append(ctx.dao.poi_online_rw.insert_gate_front_intelligence(data))
    proceed()


def main(bids: list):
    """
    门前停车场情报扩招
    Args:
        bids:

    Returns:

    """
    # 获取停车场信息
    bids = bids.tolist() if len(bids) > 0 else None
    status = 'READY' if bids is None else None
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_park_storefront_result(bids=bids, status=status, city='成都市')
    if not parking_list:
        logger.debug("No parking found")
        return
    logger.info(f"开始挖掘门前出入口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        try:
            parking_info = [parking['face_id'], parking['geom'], parking['bid']]
            ctx = context.Context(parking_info, task_id=parking['id'])
            ctx.option = 'recall'
            intelligence_recall(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[0]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
