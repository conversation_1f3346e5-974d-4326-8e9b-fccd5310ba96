# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
"""
例行获取新增的AOI和道路大门，进行关联关系匹配
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
todo：
    1.新增的关联关系如果加入进来（或以bid为基准统计改为以faceid为基准统计？没有关联主点的AOI对导航来讲如何应用？）
    2.当前只记录了匹配关系，未考虑是否已经转为出入口
    3.人工作业的出入口没有记录和维护，可能会影响对L1 L2级别AOI的判断，推动出入口的履历库建设

AOI的修改场景:
1. 新增AOI并关联了主点bid  是否不存在没有关联主点的场景？
2. 删除并重新画了AOI，faceid变更但是主点不变
3. 删除AOI
4. 新增AOI的关联关系
"""

import math
import time
import collections
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import shapely
import shapely.wkt
import shapely.geometry
from pg_tool import PgTool
from access_function import AoiGateMatchGeneralTool
from src.AccessMatch.BatchCtx import BatchContext


class ImproveAccurateStrategy:
    """
    内外大门匹配策略类，可匹配大门两条link一条内部路一条外部路的场景
    """

    def __init__(self, ctx: BatchContext):
        """
        初始化连接数据库
        :param ctx: 字典:道路库的连接方式
        """
        self.ctx = ctx

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        pass

    def commit(self):
        self.ctx.pg_tool.commit()

    def find_node_to_process_with_bid(self, bid):
        """
        根据bid寻找附近可能关联的大门信息
        Args:
            bid: poi的bid
        Return:
            face_id: aoi的主键id
            node_id_set: set类型, 大门的nodeid集合
        """
        node_id_set = set()
        sql = '''
            select 
                st_astext(geom),kind,aoi_level, bf.face_id 
            from blu_face bf inner join blu_face_poi bfp
            on 
                bf.face_id = bfp.face_id 
            where 
                bfp.poi_bid = %s 
                and bf.src != 'SD'
        '''
        self.ctx.pg_tool.cursor_aoi.execute(sql, [bid])
        res_aoi = self.ctx.pg_tool.cursor_aoi.fetchone()
        if res_aoi is None or len(res_aoi) == 0:
            # print("没有关联的AOI\t" + bid)
            return "", "", node_id_set
        if res_aoi[1] == '52' or res_aoi[2] != 2:
            # print("AOI为商圈或非基础院落\t" + bid)
            return "", "", node_id_set
        aoi_geom = res_aoi[0]
        face_id = res_aoi[3]
        num_gate = 0
        sql = '''
            with tmp as (
                select 
                   link_id, st_intersection(st_geomfromtext(%s, 4326), geom) as geom 
                from nav_link 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom)
            ) 
            select 
                link_id, st_astext((st_dump(geom)).geom) as geom 
            from tmp;'''
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_geom, aoi_geom])
        res_point = self.ctx.pg_tool.cursor_road.fetchall()
        if res_point is not None and len(res_point) > 0:
            for point_geom in res_point:
                sql = """
                    select 
                        string_agg(node_id, ''',''') 
                    from nav_node 
                    where 
                        st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0004)"""
                self.ctx.pg_tool.cursor_road.execute(sql, [point_geom[1]])
                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                if res_node is None or len(res_node) == 0:
                    continue
                node_str = res_node[0][0]
                sql = '''
                    select 
                        node_id, in_linkid, out_linkid 
                    from nav_gate 
                    where 
                    node_id in ('%s')''' % (node_str)
                self.ctx.pg_tool.cursor_road.execute(sql)

                res_node = self.ctx.pg_tool.cursor_road.fetchall()
                if res_node is not None:
                    num_gate = num_gate + len(res_node)
                for res_node_tmp in res_node:
                    # sql = '''
                    #     select
                    #         form
                    #     from nav_link
                    #     where
                    #         link_id = %s
                    #         or link_id = %s'''
                    # self.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                    # res_form = self.cursor_road.fetchall()
                    # if ((res_form[0][0] == "52" and res_form[1][0] != "52") or
                    #     (res_form[1][0] == "52" and res_form[0][0] != "52")):
                    #     node_id_set.add(res_node_tmp[0])
                    node_id_set.add(res_node_tmp[0])

        # 找到AOI框内的大门
        sql = """
            select 
                string_agg(node_id, ''',''') 
            from nav_node 
            where 
                st_contains(st_geomfromtext(%s, 4326), geom)"""
        self.ctx.pg_tool.cursor_road.execute(sql, [aoi_geom])
        res_node = self.ctx.pg_tool.cursor_road.fetchall()
        if res_node is not None and len(res_node) > 0:
            node_str = res_node[0][0]
            sql = '''
                select 
                    node_id, in_linkid, out_linkid 
                from nav_gate 
                where 
                node_id in ('%s')''' % (node_str)
            self.ctx.pg_tool.cursor_road.execute(sql)
            res_node = self.ctx.pg_tool.cursor_road.fetchall()
            if res_node is not None:
                num_gate = num_gate + len(res_node)
            for res_node_tmp in res_node:
                # sql = '''
                #     select
                #         form
                #     from nav_link
                #     where
                #         link_id = %s
                #         or link_id = %s'''
                # self.cursor_road.execute(sql, [res_node_tmp[1], res_node_tmp[2]])
                # res_form = self.cursor_road.fetchall()
                # if (res_form[0][0] == "52" and res_form[1][0] != "52") or (
                #     res_form[1][0] == "52" and res_form[0][0] != "52" and
                #     "," not in res_form[1][0] and "," not in res_form[0][0]):
                #     node_id_set.add(res_node_tmp[0])
                node_id_set.add(res_node_tmp[0])

        return face_id, aoi_geom, node_id_set

    def find_aoi_by_link(self, link_geom_dict, node_geom, face_id):
        """
        根据link找大门的候选AOI
        Args:
            link_geom_dict: 有序字典类型，key是linkid，value是link的几何信息
            node_id: 大门的nodeid
            face_id: aoi的主键id
        Return:
            link_dict: 字典类型，key是linkid，value是link的几何信息
        """
        face_id_dict = collections.OrderedDict()
        filter_reason = []
        node_region = shapely.wkt.loads(node_geom)

        for link_id, link_geom in link_geom_dict.items():
            link_region = shapely.wkt.loads(link_geom)
            # 查找相交的AOI
            sql = '''
                select 
                    face_id, 
                    st_astext(geom) 
                from blu_face 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom) 
                    and kind != '52' 
                    and aoi_level = 2'''
            self.ctx.pg_tool.cursor_aoi.execute(sql, [link_geom])
            res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
            for res_aoi_tmp in res_aoi:
                aoi_region = shapely.wkt.loads(res_aoi_tmp[1])
                link_inter = link_region.intersection(aoi_region)
                point_region = link_region.intersection(aoi_region.boundary)
                if point_region.geom_type == "MultiPoint":
                    continue
                if point_region.distance(node_region) > 0.0004:
                    if face_id == res_aoi_tmp[0]:
                        filter_reason.append("大门距离link和AOI的交点大于40米")
                    continue
                if link_inter.length / aoi_region.length < 0.005:
                    # link与aoi相交的部分长度小于周长的0.5%
                    sql = '''
                        select 
                            node_id, 
                            st_astext(a.geom) 
                        from nav_node a inner join nav_link b 
                        on 
                            a.node_id = b.s_nid 
                            or a.node_id = b.e_nid 
                        where 
                            b.link_id = %s'''
                    self.ctx.pg_tool.cursor_road.execute(sql, [link_id])
                    res_node = self.ctx.pg_tool.cursor_road.fetchall()
                    for res_node_tmp in res_node:
                        # 找到link两个端点的坐标，判断是否在AOI内
                        if aoi_region.contains(shapely.wkt.loads(res_node_tmp[1])):
                            # 在aoi内，判断是否连接了别的内部路
                            sql = '''
                                select 
                                    count(*) 
                                from nav_link
                                where 
                                    (s_nid = %s 
                                        or e_nid = %s) 
                                    and link_id != %s 
                                    and form like %s 
                            '''
                            self.ctx.pg_tool.cursor_road.execute(sql,
                                                                 [res_node_tmp[0], res_node_tmp[0], link_id, '%52%'])
                            if self.ctx.pg_tool.cursor_road.fetchone()[0] == 0:
                                # 没有连接其余内部路，不认为与该aoi匹配
                                # if face_id == res_aoi_tmp[0]:
                                #     此场景目前有效率低，暂不下发人工
                                #     filter_reason.append("断头路场景下发人工作业")
                                break
                            else:
                                face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]
                else:
                    face_id_dict[res_aoi_tmp[0]] = link_id, res_aoi_tmp[1]

        return face_id_dict, filter_reason

    def find_aoi_with_outlink(self, node_id):
        """
        根据node查询外部路方向的候选AOI
        """
        face_id_dict = collections.OrderedDict()
        sql = '''
            select 
                link_id, s_nid, e_nid, b.node_id, form, st_astext(geom)
            from nav_link a inner join nav_gate b
            on 
                a.link_id = b.out_linkid 
                or a.link_id = b.in_linkid
            where 
                b.node_id = %s 
                and a.form not like %s '''
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id, '%52%'])
        link_res = self.ctx.pg_tool.cursor_road.fetchone()
        node_id_extension = link_res[2] if link_res[1] == node_id else link_res[1]

        sql = '''
            select 
                link_id, st_astext(geom) 
            from nav_link
            where 
                (s_nid = %s 
                    or e_nid = %s )
                and link_id != %s 
                and form not like %s '''
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id_extension, node_id_extension, link_res[0], '%52%'])
        link_info = self.ctx.pg_tool.cursor_road.fetchall()
        if link_info is None or len(link_info) == 0:
            return face_id_dict
        for link_info_tmp in link_info:
            # 查找相交的AOI
            sql = '''
                select 
                    face_id, st_astext(geom) 
                from blu_face 
                where 
                    st_crosses(st_geomfromtext(%s, 4326), geom) 
                    and st_contains(geom, st_geomfromtext(%s, 4326)) 
                    and kind != '52'
                    and aoi_level = 2
            '''
            self.ctx.pg_tool.cursor_aoi.execute(sql, [link_info_tmp[1], link_res[5]])
            res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
            if res_aoi is None or len(res_aoi) == 0:
                continue
            for res_aoi_tmp in res_aoi:
                face_id_dict[res_aoi_tmp[0]] = link_res[0], res_aoi_tmp[1]

        return face_id_dict

    def filter_by_poi_gate(self, node_geom, bid):
        """
        若大门位置存在poi大门,且poi大门的名称与附近其他aoi关联的poi名称相近，则过滤
        param:
            node_geom: 大门坐标
            bid:
        """
        sql = f"""
            select 
                name, 
                relation_bid 
            from poi 
            where 
                st_dwithin(st_geomfromtext('{node_geom}', 4326), geometry, 0.00015) 
                and (name like '%门' 
                    or name like '%门)' 
                    or name like '%门岗' 
                    or name like '%出口'
                    or name like '%入口' 
                    or std_tag = '出入口;门'
                )
        """
        self.ctx.pg_tool.cursor_poi.execute(sql)
        res = self.ctx.pg_tool.cursor_poi.fetchall()
        if res is None or len(res) != 1:
            return False, []
        if res[0][1] == bid:
            return False, []

        aoi_name_list = []
        sql = f"""
            select 
                name_ch, 
                poi_bid 
            from 
                blu_face a 
                inner join 
                blu_face_poi b 
            on 
                a.face_id = b.face_id 
            where 
                st_dwithin(st_geomfromtext('{node_geom}', 4326), geom, 0.0004) 
                and aoi_level = 2 
                and kind != '52'
        """
        self.ctx.pg_tool.cursor_aoi.execute(sql)
        res_aoi = self.ctx.pg_tool.cursor_aoi.fetchall()
        if res_aoi is None:
            return False, []
        for v in res_aoi:
            if v[1] == bid:
                aoi_name_list_tmp = [v[0]]
                for name_poi_tmp in res:
                    name_poi = name_poi_tmp[0]
                    res_info, name_dst = self.check_name_in(name_poi, aoi_name_list_tmp)
                    if res_info:
                        return False, [name_dst]
        for v in res_aoi:
            aoi_name_list.append(v[0])
        for name_poi_tmp in res:
            name_poi = name_poi_tmp[0]
            res, name_dst = self.check_name_in(name_poi, aoi_name_list)
            if res:
                return True, [name_poi, name_dst]

        return False, []

    def check_name_in(self, name, name_diff_list):
        """
        判断一个名字是否在名字列表中（相似即可）
        """
        name = name.split('-')[0].replace(')', "").replace('(', "")
        len_origin = len(name)
        for name_diff in name_diff_list:
            name_tmp = name_diff.split('-')[0].replace(')', "").replace('(', "")
            num = 0.0
            for c in name:
                if c in name_tmp:
                    num = num + 1

            len_name = min(len_origin, len(name_tmp))
            if len_name == 0:
                continue
            if num / len_name >= 0.6:
                return True, name_diff

        return False, ""

    def check_shops(self, link_geom_dict, node_link_info, node_geom, face_id):
        """
        判断是否属于门前商铺场景，是则返回true
        """
        aoi_geom = self.query_geom_aoi(face_id)
        if aoi_geom is None:
            return False
        if "" in node_link_info:
            return False
        aoi_geom = aoi_geom[0]
        if not self.check_link_arc(link_geom_dict, node_link_info, node_geom, aoi_geom):
            sql = """
                select 
                    distinct struct_id 
                from bud_face 
                where 
                    st_contains(st_geomfromtext(%s, 4326), geom)"""
            self.ctx.pg_tool.cursor_aoi.execute(sql, [aoi_geom])
            res_bud = self.ctx.pg_tool.cursor_aoi.fetchall()
            if res_bud is not None and len(res_bud) > 2:
                return True
        return False

    def query_geom_aoi(self, face_id):
        """
        query geom by face_id
        """
        sql = f"""
            select 
                st_astext(geom)
            from blu_face 
            where 
                face_id = '{face_id}'
        """
        self.ctx.pg_tool.cursor_aoi.execute(sql)
        return self.ctx.pg_tool.cursor_aoi.fetchone()

    def check_link_arc(self, link_geom_dict, node_link_info, node_geom, aoi_geom):
        """
        检查内部路的方向与邻接路的方向夹角
        """
        inner_link = node_link_info[0]
        inner_region = shapely.wkt.loads(link_geom_dict[inner_link])
        aoi_region = shapely.wkt.loads(aoi_geom)
        # 如果内部路较长(大于25米)，直接过滤
        if inner_region.length > 0.00025 or (inner_region.length / aoi_region.length > 0.03
                                             and inner_region.length > 0.00015):
            return True
        node_region = shapely.wkt.loads(node_geom)
        node_coor = list(node_region.coords)
        inner_coor = list(inner_region.coords)
        link_end = inner_coor[0]

        if node_coor[0] == link_end:
            link_end = inner_coor[len(inner_coor) - 1]
        arc_inner = math.atan2(link_end[1] - node_coor[0][1],
                               link_end[0] - node_coor[0][0]) * 180 / math.pi
        if len(link_geom_dict) == 2:
            return True
        link_can = []
        for link_tmp, link_geom_tmp in link_geom_dict.items():
            if link_tmp in node_link_info:
                continue
            link_tmp_region = shapely.wkt.loads(link_geom_tmp)
            link_tmp_region_coor = link_tmp_region.coords
            if not link_tmp_region.intersects(aoi_region):
                continue
            link_adj_next = link_tmp_region_coor[len(link_tmp_region_coor) - 2]
            if link_tmp_region_coor[0] == link_end:
                link_adj_next = link_tmp_region_coor[1]
            arc_link = math.atan2(link_adj_next[1] - link_end[1], link_adj_next[0] - link_end[0]) * 180 / math.pi
            if abs(arc_link - arc_inner) < 60 or abs(arc_link - arc_inner) > 300:
                return True
            if link_tmp_region.length > 0.0003 and link_tmp_region.length / inner_region.length > 2.5:
                link_can.append(link_tmp)

        # 如果没有直达aoi内部的道路，在进行过滤
        for link_tmp in link_can:
            link_geom_tmp = link_geom_dict[link_tmp]
            sql = """
                select 
                    link_id, 
                    s_nid, 
                    e_nid 
                from nav_link 
                where 
                    st_intersects(geom, st_geomfromtext(%s, 4326))"""
            self.ctx.pg_tool.cursor_road.execute(sql, [link_geom_tmp])
            res_link = self.ctx.pg_tool.cursor_road.fetchall()
            candidate_nodes = ""
            for res_link_tmp in res_link:
                if res_link_tmp[0] in link_geom_dict:
                    continue
                candidate_nodes = candidate_nodes + "','" + res_link_tmp[1] + "','" + res_link_tmp[2]
            if candidate_nodes == "":
                continue
            sql = """
                select 
                    a.node_id 
                from 
                    nav_gate a 
                    inner join 
                    nav_node b 
                on 
                    a.node_id = b.node_id
                where 
                    a.node_id in ('%s')
            """ % (candidate_nodes.strip("','"))
            self.ctx.pg_tool.cursor_road.execute(sql)
            res_gate = self.ctx.pg_tool.cursor_road.fetchall()
            if res_gate is None or len(res_gate) == 0:
                continue
            return False

        return True

    def record_err_data(self, bid, face_id, node_id_del_reason):
        """
        save data
        """
        for node_id, reasons in node_id_del_reason.items():
            sql = '''
                insert into 
                    gate_bid_match_tmp_res(
                        node_id, 
                        bid, 
                        face_id, 
                        action, 
                        src, 
                        process_type
                    ) 
                values(
                    %s, 
                    %s, 
                    %s, 
                    'delete', 
                    %s, 
                    'check-necessary'
                )
            '''
            self.ctx.pg_tool.cursor_poi_write.execute(sql, [node_id, bid, face_id, reasons])
            self.ctx.pg_tool.conn_poi_write.commit()

    def get_matched_node_by_bid(self, bid):
        """
        returns: set()
        """
        node_set = set()
        sql = f"""
            select 
                b.node_id 
            from 
                blu_access a 
                inner join 
                blu_access_gate_rel b 
            on 
                a.access_id = b.access_id
            where main_bid = '{bid}'
        """
        self.ctx.pg_tool.cursor_aoi.execute(sql)
        for node_id, in self.ctx.pg_tool.cursor_aoi.fetchall():
            node_set.add(node_id)

        sql = '''
            select 
                node_id, 
                action
            from gate_bid_change_history
            where 
                bid = %s
                and imp_state = 0 
            order by 
                id 
        '''
        self.ctx.pg_tool.cursor_poi.execute(sql, [bid])
        res = self.ctx.pg_tool.cursor_poi.fetchall()
        for v in res:
            if v[1] == "add":
                node_set.add(v[0])
            if v[1] == "delete":
                node_set.discard(v[0])
        return node_set

    def judge_with_distance_from_node(self, node_id, geom):
        """
        判断大门与AOI之间是否满足距离的要求
        """
        region = shapely.wkt.loads(geom)
        sql = """
            select 
                distinct st_astext(b.geom), 
                st_astext(c.geom), 
                c.link_id, 
                c.s_nid, 
                c.e_nid 
            from nav_gate a inner join nav_node b 
            on 
                a.node_id = b.node_id 
            inner join nav_link c 
            on 
                (a.in_linkid = c.link_id 
                or a.out_linkid = c.link_id) 
            where 
                a.node_id = %s
                and c.form like %s
        """
        self.ctx.pg_tool.cursor_road.execute(sql, [node_id, '%52%'])
        res = self.ctx.pg_tool.cursor_road.fetchall()
        if res is None or len(res) == 0:
            return True
        if len(res) > 1:
            return True
        node_region = shapely.wkt.loads(res[0][0])
        link_region = shapely.wkt.loads(res[0][1])
        if region.contains(node_region):
            return True
        dis = 0.0
        if link_region.intersects(region.boundary):
            dis = link_region.intersection(region.boundary).distance(node_region)
            return dis / region.boundary.length <= 0.15
        else:
            dis = link_region.length
            sql = """
                select 
                    st_astext(geom) 
                from nav_link 
                where 
                    st_intersects(geom, st_geomfromtext(%s, 4326)) 
                    and link_id != %s 
                    and form like %s
            """
            self.ctx.pg_tool.cursor_road.execute(sql, [res[0][1], res[0][2], '%52%'])
            res_link = self.ctx.pg_tool.cursor_road.fetchall()
            flag = True
            for v in res_link:
                link_tmp_region = shapely.wkt.loads(v[0])
                if link_tmp_region.intersects(region.boundary):
                    flag = False
                    if (dis - link_tmp_region.intersection(region).length
                        + link_tmp_region.length) / region.boundary.length <= 0.15:
                        return True
            return flag

    def near_multi_aoi(self, aoi_geom, node_geom, bid):
        """
        判断大门是否邻近多个边框
        """
        aoi_region = shapely.wkt.loads(aoi_geom)
        node_region = shapely.wkt.loads(node_geom)
        if aoi_region.contains(node_region):
            return False

        bid_set = self.find_nearby_base_yard_by_node(node_geom, bid)
        if len(bid_set) > 0:
            return True
        else:
            return False

    def find_nearby_base_yard_by_node(self, node_geom, bid):
        """
        大门附近未跨越高等级道路的基础AOI
        """
        bid_set = set()
        sql = f"""
            select 
                coalesce(poi_bid, ''),
                st_astext(st_shortestline(st_boundary(geom), st_geomfromtext('{node_geom}', 4326)))
            from blu_face 
                left join
                blu_face_poi 
            on 
                blu_face.face_id = blu_face_poi.face_id
            where st_dwithin(geom, st_geomfromtext('{node_geom}', 4326), 0.0003)
            and aoi_level = 2
            and kind != '52'
            and poi_bid != '{bid}'
        """
        self.ctx.pg_tool.cursor_aoi.execute(sql)
        for bid, line_geom, in self.ctx.pg_tool.cursor_aoi.fetchall():
            sql = f"""
                select 
                    count(*) 
                from nav_link
                where 
                    st_intersects(geom, st_geomfromtext('{line_geom}', 4326))
                    and kind <= 7
            """
            self.ctx.pg_tool.cursor_road.execute(sql)
            if self.ctx.pg_tool.cursor_road.fetchone()[0] == 0:
                bid_set.add(bid)
        return bid_set

    def cross_high_link(self, node_geom, aoi_geom):
        sql = f"""
            select
                count(*) 
            from nav_link
            where 
                st_intersects(geom, st_shortestline(st_geomfromtext('{node_geom}', 4326), st_geomfromtext('{aoi_geom}', 4326)))
                and kind <= 7
        """
        self.ctx.pg_tool.cursor_road.execute(sql)
        return self.ctx.pg_tool.cursor_road.fetchone()[0] > 0

    def can_filter(self, bid, node_id):
        """
        判断纵向大门能否过滤
        线上关联或者人工判断有效后, 可过滤, 返回 True, 否则不可过滤, 返回 False
        """
        sql = f"""
            select 1 
            from blu_access ba 
                inner join blu_access_gate_rel bagr on ba.access_id=bagr.access_id 
            where ba.main_bid='{bid}' and bagr.node_id='{node_id}'
            limit 1
        """
        self.ctx.pg_tool.cursor_aoi.execute(sql)
        res = self.ctx.pg_tool.cursor_aoi.fetchone()
        if res:
            """
            说明线上关联
            """
            return True
        sql = f"""
            select * 
            from gate_aoi_match_check_data 
            where bid='{bid}' and node_id='{node_id}' and conclusion = 'Y'
            limit 1 
        """
        self.ctx.pg_tool.cursor_poi.execute(sql)
        res = self.ctx.pg_tool.cursor_poi.fetchone()
        if res:
            """
            说明人工判断有效
            """
            return True
        return False

    def run(self, num):
        """更新大门通行属性的入口函数
        """
        # print("内外大门批处理策略开始：" +
        #       time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))

        # 寻找待匹配的大门和AOI的关联关系的bid列表
        aoi_gate_match_general_obj = AoiGateMatchGeneralTool(self.ctx.pg_tool)
        if self.ctx.bid:
            face_bid_dict = {self.ctx.bid: ""}
        else:
            face_bid_dict = aoi_gate_match_general_obj.find_bid_to_match(num)
        # num_all = len(face_bid_dict)
        process_num = 0.0
        for bid, face_id_dst in face_bid_dict.items():
            process_num = process_num + 1
            # print("内外大门匹配策略进度:" + str(process_num / num_all))
            face_id_dst_tmp, aoi_geom, node_set = self.find_node_to_process_with_bid(bid)
            if face_id_dst == "":
                face_id_dst = face_id_dst_tmp
            if face_id_dst_tmp == "":
                continue
            if face_id_dst_tmp != face_id_dst:
                # aoi_gate_match_general_obj.set_bid_intel_fail(bid, "关联了两个AOI")
                continue

            aoi_region = shapely.wkt.loads(aoi_geom)

            node_id_del_reason = dict()
            in_in_gate_dict = dict()
            in_out_gate_dict = dict()
            for node_id in node_set:
                # 需要重新匹配的场景
                node_geom = aoi_gate_match_general_obj.get_node_geom(node_id)
                if node_geom is None:
                    continue
                link_geom_dict, node_link_info = aoi_gate_match_general_obj.find_link_by_node(node_id)
                if node_link_info[1] == "":
                    in_in_gate_dict[node_id] = shapely.wkt.loads(node_geom)
                if node_link_info[0] != "" and node_link_info[1] != "":
                    in_out_gate_dict[node_id] = shapely.wkt.loads(node_geom)

                res, name_list = self.filter_by_poi_gate(node_geom, bid)
                if res:
                    node_id_del_reason[node_id] = "poi大门命名策略过滤"
                    continue

                # 如果内部路和内部路的连接路没有在50度之内的，就过滤，减少一部分底商场景的badcase
                if len(name_list) == 0 and self.check_shops(link_geom_dict, node_link_info, node_geom, face_id_dst_tmp):
                    node_id_del_reason[node_id] = "门前商铺策略过滤高置信度"
                    continue
                if (aoi_region.area <= 5e-7
                        and not self.judge_with_distance_from_node(node_id, aoi_geom)):
                    node_id_del_reason[node_id] = "小AOI场景下距离策略过滤"

                if self.near_multi_aoi(aoi_geom, node_geom, bid):
                    node_id_del_reason[node_id] = "临近多边框策略过滤"
                    continue

            dis = max(0.0004, aoi_region.length / 16.0)
            for node_id1, node_region1 in in_in_gate_dict.items():
                if node_id1 in node_id_del_reason:
                    continue
                for node_id2, node_region2 in in_out_gate_dict.items():
                    if node_region1.distance(node_region2) < dis and not self.cross_high_link(node_region2.wkt,
                                                                                              aoi_geom):
                        if self.can_filter(bid, node_id2):
                            continue

                        node_id_del_reason[node_id1] = "纵向大门策略过滤"
                        break

            self.record_err_data(bid, face_id_dst, node_id_del_reason)
            self.commit()
            aoi_gate_match_general_obj.commit()


if __name__ == '__main__':
    pass
    # obj = ImproveAccurateStrategy()
    # obj.run(1)
