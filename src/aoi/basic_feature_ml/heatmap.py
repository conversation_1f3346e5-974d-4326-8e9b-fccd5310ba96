# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热力图转换
"""
import cv2
import glob


def switch_binary_image(heat_img, binary_img):
    """
    热力图转换二值图
    :param heat_img:
    :param binary_img:
    :return:
    """
    threshold = 0.5
    image = cv2.imread(heat_img, cv2.IMREAD_GRAYSCALE)
    threshold = int(threshold * 256)
    image[image >= threshold] = 255
    image[image < threshold] = 0
    cv2.imwrite(binary_img, image)

if __name__ == '__main__':
    heat_img_dir = "/home/<USER>/gengyong/test/tmp/green/online_work/green_online_1_0601/discern_img"
    save_dir = "/home/<USER>/gengyong/test/tmp/green/online_work/green_online_1_0601/binary_img_dir"
    files = glob.glob("{}/*.jpg".format(heat_img_dir))
    for key, file in enumerate(files):
        filename = file.rsplit("/", 1)[1]
        print(filename)
        print(key)
        switch_binary_image(file, save_dir + "/" + filename)
