# -*- coding: utf-8 -*-
"""
图灵作业参考资料
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/SWyXWRxQ4s5LK8
"""
import sys
import os
from shapely.geometry import Point, Polygon, mapping, shape, LineString, MultiLineString

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from shapely import wkt, make_valid
from shapely.ops import unary_union
from loguru import logger
from aoi import utils
import tqdm
from aoi.park_access import park_utils
import cv2
import numpy as np
from shapely.geometry import LineString

COORDINATE_FACTOR = 100000
CLIP_BUFFER_DISTANCE = 10 / 100000  # 轨迹裁剪范围
DRAFT_DIST_LIMIT = 30 / 100000  # 两点允许的漂移距离限制


def end_traj_ref(bids: list, area: str, is_clear_high_road_traj: bool):
    """
    终点轨迹参考资料
    Args:
        bids:
        area:

    Returns:

    """
    if not bids:
        return
    area_sp = wkt.loads(area)
    area_buffer_50m_sp = area_sp.buffer(20 / COORDINATE_FACTOR)
    valid_lines = []
    logger.info(f"获取终点轨迹参考资料")
    for bid in tqdm.tqdm(bids):
        current_end_traj_list = utils.get_traj_list_by_bid_and_geom(bid, area_buffer_50m_sp.wkt)
        if not current_end_traj_list:
            # logger.warning(f"{bid} 没有轨迹数据")
            continue
        end_traj_list = [wkt.loads(item[0]) for item in current_end_traj_list]
        for end_traj_sp in end_traj_list:
            line_list = [end_traj_sp]
            if end_traj_sp.geom_type == 'MultiLineString':
                line_list = end_traj_sp.geoms
            for line_sp in line_list:
                # 轨迹终点不在门前面范围内，过滤
                if not area_buffer_50m_sp.contains(Point(line_sp.coords[-1])):
                    # logger.warning(f"{bid} 轨迹终点不在门前面范围内")
                    continue
                # 裁剪轨迹
                clipped_line = clip_line_to_buffer(line_sp, area_buffer_50m_sp)
                if clipped_line is None:
                    logger.warning(f"{bid} 轨迹裁剪失败")
                    continue
                # 过滤低质轨迹
                traj_sp = filter_drift_traj(clipped_line)
                if not traj_sp:
                    continue
                # 交割点裁切
                # logger.debug(f"原始轨迹:{traj_sp}")
                if is_clear_high_road_traj:
                    traj_sp = split_by_intersects_points(traj_sp, area)
                # traj_sp = split_by_intersects_points(traj_sp, area)
                # logger.debug(f"裁切后轨迹:{traj_sp}")
                if traj_sp:
                    valid_lines.append(traj_sp)
    return valid_lines


def draw_end_traj():
    """
    绘制终点轨迹
    Returns:

    """
    # 给定的bounds
    bounds = (116.313770019712, 39.757375199279096, 116.31851997, 39.7556384944683)

    # 创建一个白色背景的图像
    width, height = 800, 600  # 图像的宽度和高度
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景

    # 将地理坐标转换为图像坐标
    def geo_to_pixel(lon, lat):
        x = int((lon - bounds[0]) / (bounds[2] - bounds[0]) * width)
        y = int((bounds[1] - lat) / (bounds[1] - bounds[3]) * height)
        return x, y

    # 定义LineString的坐标点
    line_coords = [(116.314, 39.757), (116.315, 39.756), (116.317, 39.7555)]
    line_pixels = [geo_to_pixel(lon, lat) for lon, lat in line_coords]

    # 使用OpenCV绘制LineString
    for i in range(len(line_pixels) - 1):
        cv2.line(image, line_pixels[i], line_pixels[i + 1], color=(0, 0, 255), thickness=2)  # 红色线段

    # 保存图像
    cv2.imwrite('./log/img/test.png', image)


def split_by_intersects_points(traj_sp, park_area_geom):
    """
    交割点裁切轨迹
    Args:
        traj_sp:
        park_area_geom:

    Returns:

    """
    traj_sp_list = [traj_sp]
    if traj_sp.geom_type == 'MultiLineString':
        traj_sp_list = traj_sp.geoms
    intersects_line_list = []
    for traj_sp in traj_sp_list:
        # 获取轨迹与面交割点
        park_area_sp = wkt.loads(park_area_geom)
        park_area_buffer_5m_sp = park_area_sp.buffer(5 / COORDINATE_FACTOR)
        intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(park_area_sp.wkt, [traj_sp])
        if not intersect_point_list:
            intersect_point_list = park_utils.calcul_intersect_points_by_traj_and_area(park_area_buffer_5m_sp.wkt,
                                                                                       [traj_sp])
        if not intersect_point_list:
            return None
        # 获取交割点集合距离起点最近的点
        start_sp = Point(traj_sp.coords[0])
        nearest_intersect_point = min(intersect_point_list, key=lambda pt: wkt.loads(pt).distance(start_sp))
        intersect_point_sp = wkt.loads(nearest_intersect_point)
        # 交割点buffer10m获取相交line
        intersect_point_buffer_10m_sp = intersect_point_sp.buffer(20 / COORDINATE_FACTOR)
        intersects_line = intersect_point_buffer_10m_sp.intersection(traj_sp)
        intersects_line_list.append(intersects_line)
    return unary_union(intersects_line_list)


def filter_self_intersecting_traj(traj_sp):
    """
    过滤自相交较多的轨迹
    """
    self_intersects_count = count_self_intersections(traj_sp)
    if self_intersects_count > 0:
        logger.debug(f"轨迹自相交次数:{self_intersects_count}, traj_sp:{traj_sp.wkt}")
    if self_intersects_count >= 5:
        return False
    else:
        return True


def count_self_intersections(geom):
    """计算几何对象的自相交次数"""
    if not geom.is_valid:
        geom = make_valid(geom)  # 修复无效的几何对象

    if isinstance(geom, LineString):
        # 如果是 LineString，计算自相交次数
        intersection = geom.intersection(geom)
        if intersection.is_empty:
            return 0
        elif intersection.geom_type == "Point":
            return 1
        elif intersection.geom_type == "MultiPoint":
            return len(intersection.geoms)
        else:
            # 其他情况（如 LineString 或 GeometryCollection）
            return 0
    elif isinstance(geom, MultiLineString):
        # 如果是 MultiLineString，分别计算每条 LineString 的自相交次数
        total_intersections = 0
        for line in geom.geoms:
            total_intersections += count_self_intersections(line)
        return total_intersections
    else:
        # 其他类型的几何对象（如 GeometryCollection），返回 0
        return 0


def clip_line_to_buffer(line, polygon):
    """
    裁剪轨迹
    只保留轨迹在门前面边界buffer范围内的部分
    """
    polygon_buffer = polygon.buffer(CLIP_BUFFER_DISTANCE)
    clipped = line.intersection(polygon_buffer)
    if clipped.is_empty:
        return None
    return clipped


def filter_drift_traj(line):
    """
    过滤低质轨迹
    """
    if line.geom_type == "MultiLineString":
        valid_lines = []
        for part in line.geoms:
            coords = list(part.coords)
            if len(coords) < 2:
                continue

            for i in range(len(coords) - 1):
                point1 = Point(coords[i])
                point2 = Point(coords[i + 1])
                if point1.distance(point2) > DRAFT_DIST_LIMIT:
                    break
            else:
                valid_lines.append(part)
        if len(valid_lines) > 0:
            return MultiLineString(valid_lines)
        return None
    else:
        coords = list(line.coords)
        if len(coords) < 2:
            return line

        for i in range(len(coords) - 1):
            point1 = Point(coords[i])
            point2 = Point(coords[i + 1])
            if point1.distance(point2) > DRAFT_DIST_LIMIT:
                return None  # 轨迹不合格，丢弃
        return line  # 轨迹合格，保留


if __name__ == '__main__':
    bid = sys.argv[1]
    area = ""
    end_traj_ref([bid], area)
