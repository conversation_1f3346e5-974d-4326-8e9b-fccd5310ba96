"""
情报任务处理
"""
import json

from src.common import pgsql
import dataclasses
import shapely.wkt
from src.door_naming.strategy_v2 import common
import datetime

FROM_TYPE_UGC = 'ugc'
FROM_TYPE_INTELLIGENCE = '竞品情报'

# 无效情报
QB_TYPE_NOTHING = "not_handle"
# 大门名称错误
QB_TYPE_ACCESS_NAME = "access_name"
# 大门缺失
QB_TYPE_DOOR_MISS = "door_miss"
# 关联关系缺失
QB_TYPE_DOOR_RELATION = "door_relation"

# door_miss_dis 30米
QB_DOOR_MISS_DIS = 30 * 1.1 * 1e-5

AOI_BUFFER_SIZE = 40 * 1.1 * 1e-5

# 工作状态
WORK_STATUS_NOT_HANDLE = 0
WORK_STATUS_WAIT_CREATE = 1  # 等待创建
WORK_STATUS_CREATED = 2  # 已经创建完成

HIGH_CITY_4 = ['北京市', '上海市', '广州市', '深圳市']
HIGH_CITY_10 = []


@dataclasses.dataclass
class InQbAccess:
    """
    已经存在的情报数据
    """
    geom: str
    type: str
    uuid: str = ''


@dataclasses.dataclass
class SpecialUgcPoi:
    """
    ugc poi
    """
    bid: str
    parent_bid: str
    geom: str
    name: str
    std_tag: str
    click_pv: str


@dataclasses.dataclass
class SpecialIntelligence:
    """
    竞品情报
    """
    name: str
    tag: str
    geom: str


@dataclasses.dataclass
class Access:
    """
    附近的出入口
    """
    access_id: str
    geom: str
    name: str
    access_poi_bid: str


@dataclasses.dataclass
class NearbyGate:
    """
    附近的大门
    """
    gate_id: str
    node_id: str
    node_geom: str


def get_exist_qb_by_main_bid(main_bid):
    """
    加载已有情报数据
    :param main_bid:
    :return:
    """
    in_qb_list = []
    sql = 'select main_bid,  uuid, type, st_astext(geom) as geom from access_intelligence_new where main_bid=:d1'
    ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": main_bid})
    if ret:
        for item in ret:
            in_qb_list.append(InQbAccess(uuid=item.uuid, type=item.type, geom=item.geom))
    return in_qb_list


def add_qb_access_name(main_bid, access_id, geom, jp_name, batch_id, work_status=WORK_STATUS_WAIT_CREATE):
    """
    添加需要核实名称的情报
    :param main_bid:
    :param access_id:
    :param geom:
    :param jp_name:
    :param batch_id:
    :param work_status:
    :return:
    """
    sql = 'select * from access_intelligence_new where main_bid=:d1 and type=:type and uuid=:uuid'
    ret = pgsql.query_one(pgsql.ENGINE_POI_ONLINE, sql,
                          {'d1': main_bid, 'type': QB_TYPE_ACCESS_NAME, 'uuid': access_id})
    if ret:
        print(f"{main_bid} 已存在 {access_id} 情报, 不再能添加")
    else:
        sql = 'insert into access_intelligence_new ' \
              ' (main_bid, uuid, geom, type, batch_id, work_info, work_status) ' \
              ' values (:d1, :uuid, st_geomfromtext(:geom,4326), :type, :batch_id, :work_info, :work_status)'
        pgsql.execute(pgsql.ENGINE_POI_ONLINE, sql,
                      {'d1': main_bid, 'uuid': access_id, 'type': QB_TYPE_ACCESS_NAME,
                       'geom': geom,
                       'batch_id': batch_id,
                       'work_info': json.dumps({"gd_access_name": jp_name}, ensure_ascii=False),
                       'work_status': work_status})


def door_name_same_main_name(aoi_name, node_name):
    """
    大门名称与主点名称是否相似
    :param aoi_name: aoi主点名称
    :param node_name: node绑定的poi名称
    :return: False 不相似, True 相似
    """
    aoi_name = aoi_name.split('-')[0].replace(')', "").replace('(', "")
    if '-' in node_name:
        node_name = node_name.strip().split('-')[0].replace(')', "").replace('(', "")
    elif '(' in node_name:
        node_name = node_name.strip().split('(')[0].replace(')', "")
    else:
        node_name = node_name.replace(')', "").replace('(', "")

    num = 0
    for c in aoi_name:
        if c in node_name:
            num = num + 1
    len_name = min(len(aoi_name), len(node_name))
    if len_name > 0 and num / len_name > 0.5:
        # 以策略匹配,但是与主点名称相似度较高,认为策略是正确的
        return True
    return False


def load_intelligence(main_bid, main_name, special_name=True) -> list[SpecialIntelligence]:
    """
    加载竞品数据
    :param main_bid:
    :param main_name:
    :param special_name: 是否只加载特殊名称
    :return:
    """
    date_before = datetime.datetime.now() - datetime.timedelta(days=60)
    special_list = []
    if special_name:
        sql = 'select name, tag, x, y from apartment_intelligence' \
              ' where aoi_bid=:d1 and name like :d2 and updated_at > :date'
        ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": main_bid, 'd2': '%号门%', 'date': date_before})
    else:
        sql = 'select name, tag, x, y from apartment_intelligence' \
              ' where aoi_bid=:d1 and tag like :d2 and updated_at > :date'
        ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": main_bid, "d2": "%临街院门%", 'date': date_before})
    for item in ret:
        if not door_name_same_main_name(main_name, item.name) or '停车' in item.name:
            print(f"{main_name}-{item.name} 竞品 过滤")
            continue
        jp = SpecialIntelligence(
            name=item.name,
            tag=item.tag,
            geom=f"POINT({item.x} {item.y})"
        )
        special_list.append(jp)
    return special_list


def load_ugc_poi(main_geom, main_name, special_name=True) -> list[SpecialUgcPoi]:
    """
    加载ugc数据
    :param main_geom:
    :param main_name:
    :param special_name: 是否是特殊名称
    :return:
    """
    ugc_list = []
    aoi_buffer = shapely.wkt.loads(main_geom).buffer(30 * 1.1 * 1e-5).wkt
    if special_name:
        sql = 'with tmp as (select st_geomfromtext(:d1, 4326) as geom) ' \
              'select bid, name, st_astext(geometry) as geom, relation_bid, std_tag, click_pv from poi, tmp where ' \
              ' st_intersects(tmp.geom, poi.geometry) and name like :name'
        ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {'name': "%号门%", "d1": aoi_buffer})
    else:
        sql = 'with tmp as (select st_geomfromtext(:d1, 4326) as geom) select bid, name, st_astext(geometry) as geom,' \
              f' relation_bid, std_tag, click_pv from poi, tmp where st_intersects(tmp.geom, poi.geometry) ' \
              f' and relation_bid=0 ' \
              f" and name like '%门' or name like '%门岗' " \
              f" or name like '%号口' or name like '%出口' " \
              f" or name like '%入口' or name like '%入口)' " \
              f") and std_tag like '出入口%' " \
              f" and name not like '%停车%' and name not like '%检票%'"
        ret = pgsql.query_all(pgsql.ENGINE_POI_ONLINE, sql, {"d1": aoi_buffer})

    for item in ret:
        if not door_name_same_main_name(main_name, item.name) or '停车' in item.name:
            print(f"{main_name}-{item.name}  ugc 过滤")
            continue
        sub_poi = SpecialUgcPoi(
            bid=item.bid,
            name=item.name,
            parent_bid=item.relation_bid,
            geom=item.geom,
            std_tag=item.std_tag,
            click_pv=item.click_pv,
        )
        ugc_list.append(sub_poi)
    return ugc_list


def load_access(main_bid) -> list[Access]:
    """
    加载出入口数据
    :param main_bid:
    :return:
    """
    access_list = []
    sql = 'select access_id, name , st_astext(geom) as geom, access_poi_bid' \
          ' from blu_access where main_bid=:d1'
    ret = pgsql.query_all(pgsql.ENGINE_MASTERBACK_PROXY, sql, {"d1": main_bid})
    for item in ret:
        acc = Access(access_id=item.access_id, name=item.name, geom=item.geom, access_poi_bid=item.access_poi_bid)
        access_list.append(acc)
    return access_list


def load_covered_gate(aoi_geom) -> list[NearbyGate]:
    """
    加载附近的道路大门
    :param aoi_geom:
    :return:
    """
    gate_list = []
    geom_buffer = shapely.wkt.loads(aoi_geom).buffer(AOI_BUFFER_SIZE).wkt
    gate_info = common.get_covered_gate_by_geom(geom_buffer)
    if gate_info:
        for g in gate_info:
            gate_list.append(NearbyGate(gate_id=g['gate_id'], node_id=g['node_id'], node_geom=g['geom']))
    return gate_list
