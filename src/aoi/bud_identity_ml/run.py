# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建筑物自动化
"""
import argparse
import json
import random

import sys
import os
import time
import multiprocessing

import redis

from loguru import logger

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)
from aoi.bud_identity_ml import context
from aoi.bud_identity_ml import bud
from aoi import BFQuery
from common import common_tool
from back.admin.locker import lock_guardian_with_retry, RedisLocker

# 预览计划处理状态
PRE_PLAN_STATUS_IDENTIFIED = "IDENTIFYED"
PRE_PLAN_STATUS_VECTORED = "VECTORED"
PRE_PLAN_STATUS_AFTER_HANDING = "AFTER_HANDING"
PRE_PLAN_STATUS_AFTER_HANDLED = "AFTER_HANDLED"
PRE_PLAN_STATUS_FAILED = "FAILED"
PRE_PLAN_STATUS_END = "END"

# 背景计划类型
PLAN_TYPE_BUD = 47

log_path = "./auto_bud.log"
logger.add(log_path, rotation="10 MB")


def run(pre_plan):
    """
    建筑物自动化策略
    :param ctx:
    :return:
    """
    BF_QUERY = BFQuery.BFQuery()
    logger.info(f"查询预览计划：{pre_plan[0]}")
    batch = pre_plan[0]
    try:
        if pre_plan[2] == "":
            raise Exception(f"街区信息为空：{pre_plan[0]}")
        # BF_QUERY.update_basic_pre_plan_status_by_id(pre_plan[0], PRE_PLAN_STATUS_AFTER_HANDING)
        ctx = context.Context()
        ctx.batch = str(batch)
        ctx.step = context.Step
        ctx.filter_values = context.FilterValue
        # 街区信息初始化
        street_into_list = json.loads(pre_plan[2])
        street_map = {}
        for street_info in street_into_list:
            street_map[street_info["uuid"]] = street_info["poly_geom"]
        ctx.street_info = street_map
        PIPE_LIST = bud.pipes()
        PIPE_LIST(ctx)
        BF_QUERY.update_basic_pre_plan_status_by_id(pre_plan[0], PRE_PLAN_STATUS_AFTER_HANDLED)
        logger.info(f"{ctx.batch}自动化完成")
    except Exception as e:
        logger.error(f"{batch}自动化失败:{e}")
        # common_tool.send_hi(f"建筑物后处理失败：{batch}, {e.args}", ['gengyong_cd'])
        BF_QUERY.update_pre_plan_info_by_id(batch, PRE_PLAN_STATUS_FAILED, f"建筑物后处理失败:{str(e)}")


def get_plans_with_db_lock(limit: int) -> list:
    """
    获取任务，并且把任务改成中间状态（加锁）
    一次只获取部分数据，给其他 worker 留点
    Returns:
    任务列表
    """
    dao = BFQuery.BFQuery()
    plans = dao.get_basic_pre_plan_list_by_status(PRE_PLAN_STATUS_VECTORED, PLAN_TYPE_BUD, limit=limit)

    for _plan in plans:
        dao.update_basic_pre_plan_status_by_id(_plan[0], PRE_PLAN_STATUS_AFTER_HANDING)
    return plans


def get_plans_with_lock(limit: int) -> list:
    """
    加锁获取任务，获取的任务需要更改为中间状态，更改操作需要锁的保护
    Returns:
    任务列表
    """
    locker = RedisLocker(
        rds=redis.Redis(host='*************', port=9000, db=0),
        key='bud:after_handle:fetcher:plans',
        ex=60
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    return lock_guardian_with_retry(
        locker=locker,
        times=10,
        sleep_time=sleep_time,
        fun=get_plans_with_db_lock,
        limit=limit,
    )


def main():
    """
    主函数
    """
    # 获取待处理的后处理任务
    pre_plan_list = get_plans_with_lock(int(ARGS.limit))
    if not pre_plan_list:
        exit("没有需要处理的后处理任务")

    size = min(int(ARGS.multi), len(pre_plan_list))
    logger.info(f"待处理的后处理任务数量size：{size}")
    with multiprocessing.Pool(size) as pool:
        pool.map(run, pre_plan_list)
    logger.info(f"所有后处理任务处理完毕")
    

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='建筑物后处理')
    parser.add_argument('--multi', type=int, default=3)
    parser.add_argument('--limit', type=int, default=100)

    ARGS = parser.parse_args()
    logger.info(f"参数信息：{ARGS}")

    main()
