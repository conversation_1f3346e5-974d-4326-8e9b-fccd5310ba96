# !/usr/bin/env python3
# encoding=utf-8
"""
bid回捞重新执行关联关系批处理
"""
from common import common_tool
from pg_tool import PgTool
import sys
import os
import traceback

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")


def process():
    """
    :return: void
    """
    bids = get_salvaged_bids()
    add_bids_to_intel_src(bids)

    message = f"【周级例行-重新触发关联关系】fail_reason=关联了两个AOI，触发数量：{len(bids)}"
    print(message)
    common_tool.send_hi_access_batch(message, ['lifan14'])


def get_salvaged_bids():
    """
    获取需回捞的bids
    """
    bids_result = []
    fail_reason_bids = get_bids_by_fail_reason()
    for item in fail_reason_bids:
        bids_result.append(item[0])
    return bids_result


def get_bids_by_fail_reason():
    """
    获取因关联多个AOI导致策略失败的bids
    """
    last_time = get_last_process_update_time()
    with PgTool() as pg:
        sql = f"""
                select
                    distinct bid
                from gate_match_intel_src
                where
                    fail_reason = '关联了两个AOI'
                and status = 3
                    and update_time > '{last_time}'
            """
        pg.cursor_poi.execute(sql)
        return pg.cursor_poi.fetchall()


def get_last_process_update_time():
    """
    获取上次处理的时间
    """
    with PgTool() as pg:
        sql = f"""
                select
                    update_time
                from gate_match_intel_src
                where
                    src = '周级-关联两个AOI重新触发'
                order by
                    update_time desc
             """
        pg.cursor_poi.execute(sql)
        return pg.cursor_poi.fetchone()[0]


def add_bids_to_intel_src(bids):
    """
    将回捞的bids插入到策略情报表中
    """
    with PgTool() as pg:
        for bid in bids:
            sql = f"""
                    insert into
                        gate_match_intel_src (
                            bid,
                            status,
                            src
                        )
                    values (
                        '{bid}',
                        0,
                        '周级-关联两个AOI重新触发'
                    )
                    """
            pg.cursor_poi_write.execute(sql)


if __name__ == "__main__":
    try:
        process()
    except Exception as e:
        common_tool.send_hi("周级回捞关联关系失败", ["lifan14"])
        traceback.print_exc()
