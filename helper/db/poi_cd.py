# !/usr/bin/env python3
"""
poi服务端数据库模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session

Base = declarative_base()


class PoiLimitation(Base):
    """
    限行信息
    """

    __tablename__ = 'poi_limitation'
    mid = Column(String(100), primary_key=True)
    mesh_id = Column(String(6), nullable=False, default='')
    limit_type = Column(String(254), nullable=False, default='')
    limit_time = Column(Integer, nullable=False, default=0)

    def __str__(self):
        return self.mid


class PoiAggragated(Base):
    """
    聚合信息
    """

    __tablename__ = 'poi_aggragated'
    mid = Column(String(100), primary_key=True)
    mesh_id = Column(String(6), nullable=False, default='')
    status = Column(Integer, nullable=False, default=0)
    source = Column(Integer, nullable=False, default=0)
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    def __str__(self):
        return self.mid


class Poi(Base):

    __tablename__ = 'poi'

    bid = Column(String(64), nullable=False, default="")
    mid = Column(String(64), nullable=False, primary_key=True)
    mesh_id = Column(String(6), nullable=False, default="")
    name = Column(String(254), nullable=False, default="")
    alias = Column(String(1024), nullable=False, default="")
    address = Column(String(512), nullable=False, default="")
    district = Column(String(10), nullable=False, default="")
    telephone = Column(String(200), nullable=False, default="")
    kind = Column(String(64), nullable=False, default="")
    brand_code = Column(String(12), nullable=False, default="")
    food_type = Column(String(128), nullable=False, default="")
    status = Column(Integer, nullable=False, default=0)
    relation = Column(String(64), nullable=False, default="")
    relation_bid = Column(String(64), nullable=False, default="")
    relation_type = Column(Integer, nullable=False, default=-1)
    admin_code = Column(String(8), nullable=False, default="")
    city = Column(String(64), nullable=False, default="")
    display_x = Column(Float, nullable=False)
    display_y = Column(Float, nullable=False)
    arrive_x = Column(Float, nullable=False)
    arrive_y = Column(Float, nullable=False)
    link_id = Column(String(64), nullable=False, default="")
    side = Column(String(2), nullable=False, default="")
    mark = Column(String(4), nullable=False, default="")
    std_tag = Column(String(128), nullable=False, default="")
    index_tag = Column(String(128), nullable=False, default="")
    show_tag = Column(String(128), nullable=False, default="")
    geometry = Column(String)

    def __str__(self):
        return self.mid


class PoiNaviStd(Base):
    __tablename__ = 'poi_navi_std'

    uid = Column(String(64), primary_key=True)
    mid = Column(String(64), nullable=False, default="")
    bid = Column(String(64), nullable=False, default="")
    navi_x = Column(Float, nullable=False, default=0)
    navi_y = Column(Float, nullable=False, default=0)
    arrive_obj = Column(String(128), nullable=False, default="")
    link_id = Column(String(64), nullable=False, default="")
    src = Column(String(64), nullable=False, default="")
    batch = Column(String(64), nullable=False, default="")
    is_pushed = Column(Integer, nullable=False, default=0)
    update_time = Column(DateTime, nullable=False, default=func.now())

    def __str__(self):
        return self.uid
