#-*- coding: utf-8 -*-
"""
17w导航数据维护脚本
"""
import time
import psycopg2
import csv
from itertools import islice


backConf = {"db": "master_back",
            "user": "master_back_se_ro", "pwd": "mapread",
            "host": "**************", "port": 5432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

#连接poi库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

    # 初始化状态
    sql = "update bid_gate_todo_lists set aoi_level = 0, level = 0"
    poiDao.execute(sql)
    poiDbConn.commit()
    print("init bid_gate_todo_lists success")

    # 更新aoi类型
    sql = "select poi_bid,aoi_level  from  blu_face_poi a inner join blu_face b on a.face_id = b.face_id"
    SQL_for_file_output = "COPY ({0}) TO STDOUT WITH CSV HEADER".format(sql)
    with open('/home/<USER>/chengqiong/guiji_bid_aoi_level_old.csv', 'w') as f_output:
        backgroundDao.copy_expert(SQL_for_file_output, f_output)
        backDbConn.commit()

    sql ="DROP TABLE IF EXISTS guiji_bid_aoi_level_20230509"
    poiDao.execute(sql)
    poiDbConn.commit()
    sql = "CREATE TABLE guiji_bid_aoi_level_20230509( \
    bid VARCHAR(128) not null default '', \
    aoi_level integer not null default 0 \
    )"
    poiDao.execute(sql)
    poiDbConn.commit()
    # txt文本单次删除第一行
    with open('/home/<USER>/chengqiong/guiji_bid_aoi_level_old.csv', mode='r') as f:
        line = f.readlines()  # 读取文件
        try:
            line = line[1:]  # 只读取第一行之后的内容
            f = open('/home/<USER>/chengqiong/guiji_bid_aoi_level.csv', mode='w')  # 以写入的形式打开txt文件
            f.writelines(line)    # 将修改后的文本内容写入
            f.close()             # 关闭文件
        except:
            pass
    with open('/home/<USER>/chengqiong/guiji_bid_aoi_level.csv') as f:
        poiDao.copy_from(f, "guiji_bid_aoi_level_20230509", sep=',')
        poiDbConn.commit()
    
    sql = "update bid_gate_todo_lists l set aoi_level \
         = s.aoi_level from guiji_bid_aoi_level_20230509 s where l.bid = s.bid"
    poiDao.execute(sql)
    poiDbConn.commit()
    print("aoi_level update success")

    # 更新精准aoi level
    sql = " select main_bid , aoi_complete from blu_face_complete"
    SQL_for_file_output = "COPY ({0}) TO STDOUT WITH CSV HEADER".format(sql)
    with open('/home/<USER>/chengqiong/guiji_bid_level_old.csv', 'w') as f_output:
        backgroundDao.copy_expert(SQL_for_file_output, f_output)
        backDbConn.commit()
 
    sql ="DROP TABLE IF EXISTS guiji_bid_level_20230509"
    poiDao.execute(sql)
    poiDbConn.commit()
    sql = "CREATE TABLE guiji_bid_level_20230509( \
    bid VARCHAR(128) not null default '', \
    level integer not null default 0 \
    )"
    poiDao.execute(sql)
    poiDbConn.commit()
    # txt文本单次删除第一行
    with open('/home/<USER>/chengqiong/guiji_bid_level_old.csv', mode='r') as f:
        line = f.readlines()  # 读取文件
        try:
            line = line[1:]  # 只读取第一行之后的内容
            f = open('/home/<USER>/chengqiong/guiji_bid_level.csv', mode='w')  # 以写入的形式打开txt文件
            f.writelines(line)    # 将修改后的文本内容写入
            f.close()             # 关闭文件
        except:
            pass
    with open('/home/<USER>/chengqiong/guiji_bid_level.csv') as f:
        poiDao.copy_from(f, "guiji_bid_level_20230509", sep=',')
        poiDbConn.commit()
    
    sql = "update bid_gate_todo_lists l set level = s.level from guiji_bid_level_20230509 s where l.bid = s.bid"
    poiDao.execute(sql)
    poiDbConn.commit()
    print("level update success")

    # 更新作业状态标记
    sql = "WITH work_set AS \
(SELECT bid,count(*) sum, \
sum(case when conclusion != '' then 1 else 0 end) done, \
sum(case when conclusion = '' then 1 else 0 end) undone \
FROM gate_aoi_match_check_data \
group by bid \
) \
update bid_gate_todo_lists l set is_send_work = 1 from work_set s where l.bid = s.bid"
    poiDao.execute(sql)
    poiDbConn.commit()
    print("is_send_work update success")
    # 更新作业结果
    sql = "WITH work_set AS \
(SELECT bid,count(*) sum, \
sum(case when conclusion != '' then 1 else 0 end) done, \
sum(case when conclusion = '' then 1 else 0 end) undone \
FROM gate_aoi_match_check_data \
group by bid \
) \
update bid_gate_todo_lists l set is_worked = 1 from work_set s where l.bid = s.bid and sum > 0 and undone = 0"
    poiDao.execute(sql)
    poiDbConn.commit()
    print("is_worked update success")
if __name__ == "__main__":
    process()
    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
