#-*- coding: utf-8 -*-
"""
精准aoi level字段维护脚本
"""
import time
import psycopg2
import requests
import json


curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}
 
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "**************", "port": 9432}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}

#连接poi库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()


def updateAoiGateByBid(bid, level):
    """
    更新 aoi level
    """
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = "update aoi_gate_match_batch_result \
        set level=%s, status=1, update_time='%s' where bid='%s'" % (level, update_time, bid)
    print("sql：" + sql)
    poiDao.execute(sql)
    poiDbConn.commit()


def checkAoiIsL2(face_id, node_id_str):
    """
    判断是否是 L2 AOI level 
    """
    node_id_list = node_id_str.split(",")
    # 选择AOI范围内大门
    print("开始查询AOI")
    # 查询AOI信息
    sql = "select st_astext(geom) from blu_face where face_id = '{}'".format(face_id)
    backgroundDao.execute(sql)
    aoi_res = backgroundDao.fetchone()
    if aoi_res is None or len(aoi_res) == 0:
        print("aoi不存在{}".format(face_id))
        return False

    print("开始查询道路大门")
    # 查询AOI对应道路大门
    sql = "select node_id from nav_node nn where st_intersects(st_geomfromtext('{}',4326), nn.geom)".format(aoi_res[0])
    roadDao.execute(sql)
    all_node_id_list_res = roadDao.fetchall()
    if all_node_id_list_res is None or len(all_node_id_list_res) == 0:
        print("node大门查询失败{}".format(face_id))
        return False

    valid_node_id = set()
    for node_info in all_node_id_list_res:
        valid_node_id.add(node_info[0])

    print("开始查询gate link")
    # 查询对应gate
    sql = "select node_id,in_linkid,out_linkid \
        from nav_gate where node_id in('{}') and type in (1,2)".format("','".join(valid_node_id))
    roadDao.execute(sql)
    nav_gate_list_res = roadDao.fetchall()
    if nav_gate_list_res is None or len(nav_gate_list_res) == 0:
        print("gate大门查询为空：{}".format(face_id))
        return False
    link_node_map = {}
    link_list = set()
    for nav_gate_info in nav_gate_list_res:
        link_list.add(nav_gate_info[1])
        link_list.add(nav_gate_info[2])
        link_node_map[nav_gate_info[1]] = nav_gate_info[0]
        link_node_map[nav_gate_info[2]] = nav_gate_info[0]

    #查询nav_link
    sql = "select link_id from nav_link where kind != 52 and link_id in('{}')".format("','".join(link_list))
    roadDao.execute(sql)
    link_list_res = roadDao.fetchall()
    if link_list_res is None or len(link_list_res) == 0:
        print("nav_link查询为空：{}".format(face_id))
        return False
    gate_node_id = set()
    for link_info in link_list_res:
        gate_node_id.add(link_node_map[link_info[0]])
    for node_id in node_id_list:
        gate_node_id.add(node_id)

    print("开始查询pv")
    #查询node pv
    sql = "select distinct node_id,all_pv_30d from ft_nav_gate_20230108 \
            where all_pv_30d != 0 and node_id in('{}') order by all_pv_30d desc".format("','".join(gate_node_id))
    pvDao.execute(sql)
    pv_node_id_list_res = pvDao.fetchall()
    if pv_node_id_list_res is None or len(pv_node_id_list_res) == 0:
        print("pv查询失败{}".format(pv_node_id_list_res))
        return False
    pv_total = 0
    for pv_node_info in pv_node_id_list_res:
        pv_total += pv_node_info[1]
    pv_total_90 = pv_total * 0.9

    print("开始计算pv")
    #计算高pv node
    high_pv_node_id = set()
    temp_pv_num = 0
    for pv_node_info in pv_node_id_list_res:
        if temp_pv_num < pv_total_90:
            high_pv_node_id.add(pv_node_info[0])
        temp_pv_num += pv_node_info[1]

    is_accureate_aoi = True
    for node_id in high_pv_node_id:
        if node_id not in node_id_list:
            is_accureate_aoi = False
            print("高PV不在匹配列表：node_id:{}".format(node_id))
            break
    return is_accureate_aoi


def checkAoiIsL1(face_id, node_id_str):
    """
    判断是否是 L1 AOI level 
    """
    node_id_str = node_id_str.replace(",", "','")
    sql = "select l1.form as in_form,l2.form as out_form,gate_id,node_id,in_linkid,out_linkid \
            from nav_gate g \
            left join nav_link l1 on g.in_linkid = l1.link_id \
            left join nav_link l2 on g.out_linkid = l2.link_id \
            where g.node_id in ('{}')".format(node_id_str)
    print("checkAoiIsL1 sql:" + sql)
    roadDao.execute(sql)
    gate_lists = roadDao.fetchall()
    if gate_lists is None:
        return False
    in_flag = out_flag = 0
    for gate in gate_lists:
        if in_flag == 0:
            in_lists = gate[0].split(',')
            if '52' in in_lists:
                in_flag = 1
        if out_flag == 0:
            out_lists = gate[1].split(',')
            if '52' in out_lists:
                out_flag = 1
    if in_flag == 1 and out_flag == 1:
        return True
    return False


def getAoiLevel(face_id, node_id_str):
    """
    getAoiLevel 获取AOI level
    """
    level = 1
    if checkAoiIsL1(face_id, node_id_str):
        print("start checkAoiIsL1:" + face_id)
        level = 2
    if level == 2:
        if checkAoiIsL2(face_id, node_id_str):
            print("start checkAoiIsL2" + face_id)
            level = 3
    print("level：" + str(level))        
    return level


def process():
    """
    process 正式处理流程
    """
    print("start")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    #获取待处理的数据列表
    index = 0
    while True:
        aoi_gate_list_sql = "select bid,face_id,success_node_id \
            from aoi_gate_match_batch_result where status = 0 limit 10000"
        # aoi_gate_list_sql = "select bid,face_id,success_node_id from aoi_gate_match_batch_result where bid in ('10000009795415658129','10000050805904863321','10000053154131216308')"
        poiDao.execute(aoi_gate_list_sql)
        aoi_gate_list = poiDao.fetchall()
        if aoi_gate_list:
            index = index + 1
            for v in aoi_gate_list:
                bid = v[0]
                face_id = v[1]
                node_id_str = v[2]
                print("bid：" + bid)
                print("face_id：" + face_id)
                print("node_id_str：" + node_id_str)
                node_id_str = node_id_str.strip(',')
                # 0未匹配到, 1匹配到, 2 L1 AOI, 3 L2 AOI
                level = 0
                if len(node_id_str): 
                    try:
                        level = getAoiLevel(face_id, node_id_str)
                    except:
                        print('try_error...bid=%s' % (bid))
                updateAoiGateByBid(bid, level)
        else:
            break 
        if index > 1000:
            break    
        time.sleep(3)
    print("end")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

if __name__ == "__main__":
    process()