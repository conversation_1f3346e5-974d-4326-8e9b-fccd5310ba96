"""
公共函数
"""
from distutils.log import info
import sys
import os
import time
import hashlib 
import tqdm
import requests
import math
import json
import copy 
import heapq
from datetime import datetime
import numpy as np
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from shapely import wkt
from loguru import logger


def get_near_node(ctx, access_qb):
    """
    根据坐标选3m内最近的node
    """
    node_buffer_wkt = wkt.loads(access_qb.node_geom).buffer(3 / 110000).wkt
    node_list = ctx.dao.road_dao.get_nav_node_by_geom(node_buffer_wkt)
    now_node_id = ''
    if node_list and len(node_list) > 0:
        if len(node_list) == 1:
            return node_list[0][0]
        node_dis = 0
        for node in node_list:
            # 计算距离 保留和原始 access_qb.node_geom 最近的node
            distance = wkt.loads(node[1]).distance(wkt.loads(access_qb.node_geom))
            if now_node_id == '':
                now_node_id = node[0]
                node_dis = distance
            elif distance < node_dis:
                now_node_id = node[0]
                node_dis = distance
    return now_node_id 


def get_near_link(ctx, access_qb):
    """
    link就近绑路
    """
    node_wkt = access_qb.node_geom
    node_buffer_wkt = wkt.loads(node_wkt).buffer(5 / 110000).wkt
    link_list = ctx.dao.road_dao.get_nav_link_by_geom(geom=node_buffer_wkt, form='52', kind=8)
    if not link_list:
        link_list = ctx.dao.road_dao.get_nav_link_by_geom(geom=node_buffer_wkt, form=None, kind=8)
    if not link_list:
        link_list = ctx.dao.road_dao.get_nav_link_by_geom(geom=node_buffer_wkt, form=None, kind=7)
    now_link_id = ''
    link_dis = 0
    if link_list and len(link_list) > 0:
        if len(link_list) == 1:
            return link_list[0][0]
        for link in link_list:
            # 计算距离 保留和原始 access_qb.node_geom 最近的link
            distance = wkt.loads(link[1]).distance(wkt.loads(access_qb.node_geom))
            if now_link_id == '':
                now_link_id = link[0]
                link_dis = distance
            elif distance < link_dis:
                now_link_id = link[0]
                link_dis = distance
    return now_link_id 


def get_nav_gate_semantic(ctx, long_node_id):
    """
    获取大门通行性命名
    """
    invalid_door, in_door, in_out_door, out_door = "不可入", "入口", "出入口", "出口"
    semantic_res = ctx.dao.dest_traj_dao.get_nav_gate_traffic([long_node_id])
    if len(semantic_res) == 0:
        return False
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == '3' and type_arr[1] == '3':
            return in_out_door
        elif '2' in type_arr:
            return in_door
        elif '1' in type_arr:
            return out_door
        return invalid_door
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]
    if type == "0":
        return invalid_door  # 紧急门
    else:
        if "入口" in nnww_tag:
            return in_door  # 入口
        elif "出口" in nnww_tag:
            return out_door  # 出口门
        else:
            if type == "2":
                return in_door  # 入口
            elif type == "3":
                return in_out_door  # 出入口
            elif type in ["1"]:
                return out_door  # 出口门
    return False