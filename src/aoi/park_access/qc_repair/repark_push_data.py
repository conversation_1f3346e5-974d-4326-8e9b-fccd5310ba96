# -*- coding: utf-8 -*-
"""
停车场关联高通量Link 点不在 Link 上
"""
import datetime
import sys
import os
import tqdm
import requests
import pymysql
import psycopg2
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

expimpMysqlConf = {"databases": "bee_flow", "user": "bee_flow_r", "pwd": "bee_flow_r_2021",
                   "host": "*************", "port": 5730}

# 连接MySQL
expimpDbConn = pymysql.connect(host=expimpMysqlConf['host'],
                               port=expimpMysqlConf['port'],
                               user=expimpMysqlConf['user'],
                               password=expimpMysqlConf['pwd'],
                               database=expimpMysqlConf['databases'],
                               charset='utf8')
aoiDao = expimpDbConn.cursor()


poiConf = {"db": "poi_online",
            "user": "poi_aoi_r", "pwd": "poi_aoi_r",
            "host": "**************", "port": 8532}
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDbConn.autocommit = True
poiDao = poiDbConn.cursor()

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_relatedness.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_relatedness.txt"


def main():
    """
    停车场关联高通量Link 点不在 Link 上
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    sql = f"""
        select bid from parking_push_data where source = 'STRATEGY_HIGH_LINK_POINT_REPAIR' and create_time > '2025-01-17'
    """
    aoiDao.execute(sql)
    park_list = aoiDao.fetchall()
    for park in tqdm.tqdm(park_list):
        bid = park[0]
        # 查询BID是否被更新过
        sql = f"select * from park_change_log where bid = '{bid}' " \
              f"and param='road_relation' and create_time > '2025-01-15' and create_time < '2025-01-17 15:00:00' " \
              f"and source != 'STRATEGY_HIGH_LINK_POINT_REPAIR' " \
              f"and source != 'STRATEGY_ACCESS_ROAD_RELATION_NODE_UPDATE' " \
              f"and source != 'STRATEGY_PROCESS' order by create_time desc limit 1"
        aoiDao.execute(sql)
        res = aoiDao.fetchone()
        if res:
            print(f"{bid}\t两天内更新过:{res}")
            release_road_relation(bid, res[1])
            continue
        #重新推送road_relation
        sql = f"select road_relation from park_online_data where bid='{bid}'"
        poiDao.execute(sql)
        poi_info = poiDao.fetchone()
        if not poi_info:
            print(f"{bid}信息未查询到")
            continue
        road_relation = poi_info[0]
        release_road_relation(bid, road_relation)



def release_road_relation(bid, road_relation):
    """
    更新road_relation
    Args:
        bid:
        road_relation:

    Returns:

    """
    # 写入成果数据
    with open("./repair_bid_road_relation_0117.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{road_relation}\n")
        f.flush()

    logger.info(f"{bid} 开始推送:{road_relation}")
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_ROAD_RELATION_REPAIR",
        "source_id": f"STRATEGY_ROAD_RELATION_REPAIR_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_ROAD_RELATION_REPAIR_0107",
        "road_relation": road_relation
    }
    logger.info(f"road_relation修复推送:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in f:
            data_list = line.strip().split("\t")
            if data_list[8] != '【R6008】关联高通量link 的停车的point不在link上':
                continue
            bids.append(data_list[2])
    return bids


if __name__ == '__main__':
    main()
