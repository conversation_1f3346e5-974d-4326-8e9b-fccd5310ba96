# -*- coding: utf-8 -*-
"""
导航轨迹保存
"""
import datetime
import json
import multiprocessing
import sys
import os
import tqdm
import numpy as np
from shapely import wkt
from loguru import logger
import traceback

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.traj_navigate import TrajNavigate as TrajNavigateDao
from src.aoi.dao.road import Road
from src.aoi import utils
from src.aoi.park_access.overground import traj_navigate
from src.aoi.park_access.overground.traj_navigate import TrajNavigate
from src.common import pipeline
from src.aoi.park_access import park_utils

# 定义 QPS 限制
QPS = 10  # 轨迹接口每秒最多 10 次请求
ONE_SECOND = 1

COORDINATE_FACTOR_NEW = 100000

master_back_dao = MasterBackDao()
poi_online_query = PoiOnlineQuery()
poi_online_rw = PoiOnlineRW()
road_dao = Road()
traj_navigate_dao = TrajNavigateDao()

# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

BUILD_GATE_CATEGORY = ['医疗;综合医院', '医疗;专科医院', '教育培训;高等院校', '房地产;住宅区', '房地产;其他', '房地产', '公司企业;公司', '公司企业;园区', '公司企业',
                       '公司企业;厂矿', '公司企业;农林园艺', '教育培训;中学', '教育培训;小学', '教育培训;幼儿园', '教育培训;培训机构', '教育培训;其他', '教育培训;科研机构',
                       '教育培训;科技馆', '教育培训;成人教育', '教育培训;特殊教育学校', '教育培训', '政府机构;各级政府', '政府机构;行政单位', '政府机构;公检法机构',
                       '政府机构;政治教育机构', '政府机构;中央机构', '政府机构;涉外机构', '汽车服务;汽车销售', '汽车服务;汽车配件', '汽车服务;汽车维修', '汽车服务;汽车检测场',
                       '文化传媒;广播电视', '绿地;高尔夫球场', '医疗;疗养院', '医疗;其他', '医疗', '生活服务;物流公司']
NOT_BUILD_CATEGORY = ['交通设施;加油加气站', '交通设施;飞机场', '交通设施;火车站', '购物;购物中心']
AREA_FIRST_CATEGORY = ['交通设施;飞机场', '交通设施;火车站', '旅游景点;风景区', '旅游景点;公园', '旅游景点;人文景观']
IS_TEST = True


class Context:
    """
    轨迹处理上下文
    """

    def __init__(self, parking: dict):
        self.aoi_bid = parking['parent_id'] if parking['parent_id'] and parking['parent_id'] not in ('0', '1') else ''
        self.aoi_wkt = ''
        self.park_bid = parking['bid']
        self.park_area = parking['area']
        self.park_gcj_area = ''
        self.park_wkt = parking['gcj_geom']
        self.traj_link_ids = []
        self.traj_list = []


class ParseTrajContext:
    """
    轨迹解析处理上下文
    """

    def __init__(self, bid, origin_traj_list):
        self.bid = bid
        self.origin_traj_list = origin_traj_list
        self.park_area = ''
        self.park_wkt = ''
        self.aoi_wkt = ''
        self.out_traj_list = []


def save_origin_link_traj(ctx: Context):
    """
    导航轨迹获取
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        get_blu_access_link,
        get_aoi_intersects_link,
        get_park_area_intersects_link,
        get_link_traj,
        save_origin_traj
    )
    pipe_list(ctx)


def parse_link_traj(ctx: ParseTrajContext):
    """
    解析原始轨迹
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        parse_origin_link_traj,
        traj_intercept,
        save_out_link_traj,
    )
    pipe_list(ctx)


def get_blu_access_link(ctx: Context, proceed):
    """
    获取关联关系LINK
    Args:

    Returns:

    """
    if not ctx.aoi_bid:
        return proceed()
    blu_access_list = master_back_dao.get_blu_access_list_by_main_bid(ctx.aoi_bid)
    lone_link_ids = []
    for blu_access in blu_access_list:
        nav_gate_list = road_dao.get_nav_gate_by_node_id(blu_access[0])
        if not nav_gate_list:
            continue
        lone_link_ids += [x['in_linkid'] for x in nav_gate_list]
    ctx.traj_link_ids += lone_link_ids
    return proceed()


def get_aoi_intersects_link(ctx: Context, proceed):
    """
    获取AOI边框相交的内部路LINK
    Args:
        aoi_wkt:
        park_wkt:

    Returns:

    """
    if not ctx.aoi_bid:
        return proceed()
    aoi_res = master_back_dao.get_aoi_info_by_bid(ctx.aoi_bid)
    if not aoi_res:
        return proceed()
    ctx.aoi_wkt = aoi_res[1]
    link_list = road_dao.get_intersects_nav_link_by_wkt_new(ctx.aoi_wkt)
    if not link_list:
        return proceed()
    aoi_intersects_link_ids = [item['link_id'] for item in link_list if item['kind'] != 10 and item['kind'] > 7]
    ctx.traj_link_ids += aoi_intersects_link_ids
    return proceed()


def get_park_area_intersects_link(ctx: Context, proceed):
    """
    获取AOI边框相交的内部路LINK
    Args:
        aoi_wkt:
        park_wkt:

    Returns:

    """
    if not ctx.park_area:
        return proceed()
    ctx.park_gcj_area = utils.mc_to_gcj(ctx.park_area)
    link_list = road_dao.get_intersects_nav_link_by_wkt_new(ctx.park_gcj_area)
    if not link_list:
        return proceed()
    aoi_intersects_link_ids = [item['link_id'] for item in link_list if item['kind'] != 10 and item['kind'] > 7]
    ctx.traj_link_ids += aoi_intersects_link_ids
    return proceed()


def get_link_traj(ctx: Context, proceed):
    """
    获取LINK轨迹
    Args:
        ctx:

    Returns:

    """
    # 根据范围获取LINK轨迹
    link_traj_list = []
    logger.info(f"开始获取LINK轨迹:{set(ctx.traj_link_ids)}")
    for link_id in tqdm.tqdm(list(set(ctx.traj_link_ids))):
        traj_info = traj_navigate.get_origin_link_traj(link_id)
        if not traj_info:
            continue
        link_traj = {'link_id': link_id, 'traj_list': traj_info}
        link_traj_list.append(link_traj)
    ctx.traj_list = link_traj_list
    return proceed()


def save_origin_traj(ctx: Context, proceed):
    """
    轨迹入库
    Args:
        traj_list:

    Returns:

    """
    data_list = []
    for traj in ctx.traj_list:
        data_list.append((ctx.park_bid, json.dumps(traj['traj_list'])))
    traj_navigate_dao.insert_traj_navigate_list(data_list)
    return proceed()


def parse_origin_link_traj(ctx: ParseTrajContext, proceed):
    """
    解析原始轨迹
    Args:
        ctx:

    Returns:

    """
    if not ctx.origin_traj_list:
        return proceed()
    traj_list = []
    logger.info(f"开始解析原始轨迹:{len(ctx.origin_traj_list)}")
    for origin_traj_list in tqdm.tqdm(ctx.origin_traj_list):
        for origin_traj in json.loads(origin_traj_list):
            traj_obj = TrajNavigate()
            traj_obj.aoi_wkt = ctx.aoi_wkt if ctx.aoi_wkt else ctx.park_area
            traj_obj.park_area_wkt = ctx.park_area
            # traj_obj.parse_link_trace(json.loads(json.loads(origin_traj)))
            traj_obj.parse_link_trace(json.loads(origin_traj))
            traj_obj.filter_noise_trace_by_angle_and_intersects()
            # traj_obj.filter_out_aoi_traj()
            traj_list += traj_obj.traj_list
    ctx.out_traj_list = traj_list
    return proceed()


#
# def parse_origin_link_traj(ctx: ParseTrajContext, proceed):
#     """
#     解析原始轨迹
#     Args:
#         ctx:
#
#     Returns:
#
#     """
#     if not ctx.origin_traj_list:
#         return proceed()
#     traj_list = []
#     logger.info(f"开始解析原始轨迹:{len(ctx.origin_traj_list)}")
#     for origin_traj in tqdm.tqdm(ctx.origin_traj_list):
#         traj_obj = TrajNavigate()
#         traj_obj.aoi_wkt = ctx.aoi_wkt if ctx.aoi_wkt else ctx.park_area
#         traj_obj.park_area_wkt = ctx.park_area
#         # traj_obj.parse_link_trace(json.loads(json.loads(origin_traj)))
#         traj_obj.parse_link_trace(json.loads(json.loads(origin_traj)))
#         traj_obj.filter_noise_trace_by_angle_and_intersects()
#         # traj_obj.filter_out_aoi_traj()
#         traj_list += traj_obj.traj_list
#     ctx.out_traj_list = traj_list
#     return proceed()


def traj_intercept(ctx: ParseTrajContext, proceed):
    """
    轨迹
    Args:
        ctx:

    Returns:

    """
    # 获取范围内已入库轨迹
    old_traj_list = poi_online_query.get_navigate_traj(bids=[ctx.bid])
    old_traj_ids = [item['traj_id'] for item in old_traj_list] if old_traj_list else []
    filter_traj_list = []
    aoi_sp = wkt.loads(ctx.aoi_wkt) if ctx.aoi_wkt else None
    park_sp = wkt.loads(ctx.park_area) if ctx.park_area else None
    for link_traj in ctx.out_traj_list:
        if link_traj.traj_id in old_traj_ids:
            continue
        # 轨迹截取入库, 如果停车场范围在AOI内部,直接根据AOI范围buffer50截取, 停车场面在AOI外部,aoi buffer 200m 入库
        if (aoi_sp and park_sp and aoi_sp.contains(park_sp)) \
                or (aoi_sp and not park_sp and aoi_sp.contains(wkt.loads(ctx.park_wkt))):
            link_traj.geom = aoi_sp.buffer(50 / COORDINATE_FACTOR_NEW).intersection(link_traj.geom)
            link_traj.bid = ctx.bid
            filter_traj_list.append(link_traj)
            old_traj_ids.append(link_traj.traj_id)
            continue
        # AOI存在直接AOIbuffer200m入库
        if aoi_sp:
            link_traj.geom = aoi_sp.buffer(200 / COORDINATE_FACTOR_NEW).intersection(link_traj.geom)
            link_traj.bid = ctx.bid
            filter_traj_list.append(link_traj)
            old_traj_ids.append(link_traj.traj_id)
            continue
        if park_sp:
            link_traj.geom = park_sp.buffer(50 / COORDINATE_FACTOR_NEW).intersection(link_traj.geom)
            link_traj.bid = ctx.bid
            filter_traj_list.append(link_traj)
            old_traj_ids.append(link_traj.traj_id)
            continue
        link_traj.geom = wkt.loads(ctx.park_wkt).buffer(50 / COORDINATE_FACTOR_NEW).intersection(
            link_traj.geom)
        link_traj.bid = ctx.bid
        filter_traj_list.append(link_traj)
        old_traj_ids.append(link_traj.traj_id)
    ctx.out_traj_list = filter_traj_list
    return proceed()


def save_out_link_traj(ctx: ParseTrajContext, proceed):
    """
    轨迹入库
    Args:
        traj_list:

    Returns:

    """
    data_list = []
    for traj in ctx.out_traj_list:
        if traj.geom.is_empty or traj.geom.geom_type != "LineString":
            continue
        data_list.append((traj.long_link_id, traj.short_link_id, traj.geom.wkt, traj.traj_id, traj.gps_time, traj.dir,
                          traj.weight, traj.prediction, traj.cuid, traj.ori_traj_id, traj.confidence, traj.encode_cuid,
                          traj.bid))
    poi_online_rw.insert_traj_navigate_list(data_list)
    # 更新轨迹状态
    traj_navigate_dao.update_traj_navigate_status(ctx.bid)
    return proceed()


def main(bid_list: list):
    """
    link 轨迹入库
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    if len(bid_list) == 0:
        raise ValueError('没有输入BID!')
    show_tags = ['地上停车场', '停车场', '立体停车场', '地下停车场']
    parking_list = poi_online_query.get_parking_list_by_kv(bids=bid_list.tolist(), show_tags=show_tags)
    parking_list = sorted(parking_list, key=lambda x: x['bid'])
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"\n{parking['bid']} 开始获取轨迹")
            # 判断BID是否已存在
            traj_info = traj_navigate_dao.get_traj_navigate(bids=[parking["bid"]])
            if traj_info:
                logger.info("轨迹已存在")
                continue
            ctx = Context(parking)
            save_origin_link_traj(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking['bid']}\n{traceback.format_exc()}")


def parse_link_traj_list(bid_list: list):
    """
    link 解析轨迹入库
    Args:
    Returns:

    """
    # 获取轨迹信息
    for bid in tqdm.tqdm(bid_list):
        try:
            logger.info(f"\n{bid} 开始解析轨迹")
            traj_list = traj_navigate_dao.get_traj_navigate(bids=[bid], status='INIT')
            if not traj_list:
                logger.info("没有需要解析数据")
                continue
            trajs = [item['traj_info'] for item in traj_list]
            parking_list = poi_online_query.get_parking_list_by_kv(bids=[bid])
            if not parking_list:
                logger.info("停车场信息未查到")
                continue
            ctx = ParseTrajContext(bid, trajs)
            ctx.park_area = utils.mc_to_gcj(parking_list[0]['area']) if parking_list[0]['area'] else ''
            # 查询AOI信息
            if parking_list[0]['parent_id']:
                aoi_res = master_back_dao.get_aoi_info_by_bid(parking_list[0]['parent_id'])
                ctx.aoi_wkt = aoi_res[1] if aoi_res else ''
                ctx.park_wkt = parking_list[0]['geom']
            parse_link_traj(ctx)
        except Exception as e:
            print(f"开始解析轨迹失败:{e}{bid}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    # 解析轨迹入库
    if IS_TEST == '1':
        parse_link_traj_list(bid_list)
        exit()
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
