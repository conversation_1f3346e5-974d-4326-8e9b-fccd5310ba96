# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场出入口上下文信息
"""
import sys
import os
from math import ceil

from loguru import logger
from pathlib import Path
from dataclasses import dataclass, field
from aoi import utils
from typing import List
from shapely.geometry import Point, Polygon, LineString, MultiLineString, MultiPoint
from shapely import wkt
from aoi.dao.poi_online_query import PoiOnlineQuery
from aoi.dao.poi_online_rw import PoiOnlineRW
from aoi.dao.dest_traj import DestTraj
from aoi.dao.master_back import MasterBackDao
from aoi.dao.aoi_resume_query import AoiResumeQuery
from aoi.BFQuery import BFQuery
from aoi.dao.road import Road
from aoi.dao.trans import Trans
from aoi.dao.traj_feature import TrajFeatureDao

log_path = "./log/underground.log"
logger.add(log_path, rotation="10 MB")

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")


@dataclass
class Dao:
    """
    数据库访问对象
    """
    road_dao = Road()
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    aoi_resume_query = AoiResumeQuery()
    trans_dao = Trans()
    beeflow_dao = BFQuery()
    master_back_dao = MasterBackDao()
    dest_traj_dao = DestTraj()
    traj_feature_dao = TrajFeatureDao()


@dataclass
class ParkingAccess:
    """
    停车场出入口挖掘成果
    """
    type: str = field(default="", init=False)  # 类型, 关联Node还是link
    node_id: str = field(default="", init=False)
    short_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    node_geom_mc: str = field(default="", init=False)
    node_passage: str = field(default="", init=False)
    link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    link_geom_mc: str = field(default="", init=False)
    strategy: str = field(default="", init=False)  # 挖掘策略
    is_valid: bool = field(default=True, init=False)  # 关联是否合法
    filter_reason: str = field(default="", init=False)  # 过滤原因
    road_relation: str = field(default="", init=False)
    name: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)


@dataclass
class ParkingInfo:
    """"
    停车场信息
    """
    bid: str = field(default="", init=False)
    name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    park_wkt: str = field(default="", init=False)
    park_area_wkt: str = field(default="", init=False)
    park_area_aoi_30m_wkt: str = field(default="", init=False)
    park_area_gcj_wkt: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    traj_dbname: str = field(default="", init=False)

@dataclass
class AOIInfo:
    """
    停车场父AOI信息
    """
    bid: str = field(default="", init=False)
    wkt: str = field(default="", init=False)
    area: str = field(default="", init=False)
    std_tag: str = field(default="", init=False)
    aoi_complete: int = field(default=0, init=False)
    aoi_level: int = field(default=0, init=False)
    park_distance: float = field(default=0, init=False)
    blu_access_list: list = field(default_factory=list, init=False)


COORDINATE_FACTOR = 110000

# 出入口关联类型
PARKING_ACCESS_TYPE_NODE = "NODE"
PARKING_ACCESS_TYPE_LINK = "LINK"
PARKING_ACCESS_TYPE_POINT = "POINT"

# 出入口关联策略
GATE_FRONT_EXP_TRAJ_END_AGG = "GATE_FRONT_EXP_TRAJ_END_AGG"
GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_EXP_TRAJ_END_GATE = "GATE_FRONT_EXP_TRAJ_END_GATE"
GATE_FRONT_EXP_TRAJ_END_LINK = "GATE_FRONT_EXP_TRAJ_END_LINK"
GATE_FRONT_AREA_NEAR_LINK = "GATE_FRONT_AREA_NEAR_LINK"


class Context:
    """
    建筑物后处理上下文
    """

    def __init__(self, parking: dict):
        self.origin_parking_list = parking
        self.park_intelligence_id = parking['id']
        self.parking_info = ParkingInfo()
        self.dao = Dao()
        self.parking_access_list = list()
        self.virtual_park_info = []
        self.sub_pois = []
        self.aoi_info = AOIInfo()
        self.init_parking_info()
        self.init_aoi_info()

    def init_parking_info(self):
        """
        初始化上下文
        Args:
            ctx:
            parking:

        Returns:

        """
        paring_info = ParkingInfo()
        parking = self.origin_parking_list
        paring_info.name = parking["name"]
        paring_info.show_tag = parking["show_tag"]
        paring_info.parent_id = parking["parent_bid"]
        paring_info.bid = parking["bid"]
        paring_info.park_wkt = parking["geom"]
        self.parking_info = paring_info

    def init_aoi_info(self):
        """
        初始化AOI信息
        Returns:

        """
        if self.parking_info.parent_id == "" or self.parking_info.parent_id == "0":
            return
        aoi_res = self.dao.master_back_dao.get_aoi_info_by_bid(self.parking_info.parent_id)
        if not aoi_res:
            return
        aoi_info = AOIInfo()
        aoi_info.bid = aoi_res[2]
        aoi_info.area = aoi_res[0]
        aoi_info.wkt = aoi_res[1]
        aoi_info.aoi_complete = aoi_res[4]
        aoi_info.aoi_level = aoi_res[3]
        self.aoi_info = aoi_info
        # 计算停车场与AOI距离
        aoi_sp = wkt.loads(aoi_info.wkt)
        park_sp = wkt.loads(self.parking_info.park_wkt)
        self.aoi_info.park_distance = aoi_sp.distance(park_sp)
        # 查询AOI垂类
        poi_info = self.dao.poi_online_query.get_poi_by_bids([aoi_res[2]])
        if not poi_info:
            logger.info(f"{self.parking_info.bid}POI失效")
            return
        self.aoi_info.std_tag = poi_info[0]['std_tag']
