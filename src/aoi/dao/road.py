# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
道路库
"""
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class Road(PgDao):
    """
    道路库
    """

    def __init__(self, shared_pool=True, pool=None):
        self.cursor = None
        conf = CommTool.get_config("pg")
        road_conf = {}
        if CommTool.is_debug():
            if 'road' in conf:
                road_conf = conf['road']
            else:
                raise Exception("road conf not found")
        else:
            road_info = CommTool.get_road_db_from_dcmpf()
            if 'db' in road_info:
                road_conf = road_info
                road_conf['pwd'] = road_info['passwd']
            road_conf['source'] = 'road'
            # road_conf = CommTool.get_config("pg").get("road")
        super().__init__(pg_conf=road_conf, pool=pool, use_shared_pool=shared_pool)

    def get_inner_road(self, wkt):
        """
        获取内部路 kind
        :param wkt:
        :return:
        """
        sql = """
            select lane_n, st_astext(geom), link_id from nav_link
            where st_intersects(geom, st_buffer(st_geomfromtext(%s,4326), 0.00006)) 
                and (kind = 8 or form like '%52%')
        """
        return self.queryall(sql, [wkt])

    def get_intersects_nav_link_by_wkt(self, wkt: str):
        """
        获取相交的nav_link
        :param wkt:
        :return:
        """
        sql = f"""
            select link_id, form, kind, st_astext(geom),s_nid,e_nid,len,app from nav_link 
            where st_intersects(geom, st_geomfromtext(%s,4326))
        """
        return self.queryall(sql, [wkt])

    def get_nav_link_by_link_id_form(self, link_id_list: list):
        """
        获取nav_link
        :param link_id_list:
        :return:
        """
        sql = f"""
            select link_id, form, kind, st_astext(geom),s_nid,e_nid from nav_link 
            where form != '52' and link_id = ANY(%s)
        """
        return self.queryall(sql, [link_id_list])

    def get_not_ld_out_road_and_other_road(self, wkt, distance):
        """
        获取非LD道路外部路,包含其他道路
        :param wkt:
        :return:
        """
        sql = """ 
            select link_id, kind, form, dir, lane_l, lane_r, st_asText(geom) from nav_link 
            where kind >= 1 and kind <= 8 and form !~ '52' 
                and st_intersects(geom, st_buffer(st_geomfromtext(%s,4326), %s))
        """
        return self.queryall(sql, [wkt, distance])

    def get_ld_road_by_wkt(self, wkt, distance):
        """
        获取LD道路面，WKT buff 0.5米
        :param wkt:
        :return:
        """
        sql = """
            select distinct lane_group_id from nav_lane_marking_pl 
                where st_intersects(geom, st_buffer(st_geomfromtext(%s,4326), %s)) 
                and (longitudinal_pl_type ~ '4')
        """
        lane_group_list = self.queryall(sql, [wkt, distance])
        if not lane_group_list:
            return None, None
        group_id_list = []
        for lan_group in lane_group_list:
            group_id_list.append(lan_group[0])
        sql = "select boundary_list from nav_lane_group where lane_group_id = ANY(%s) and boundary_num > 0"
        boundary_str_list = self.queryall(sql, [group_id_list])
        # boundary_list 第一个与最后一个就是边界线
        ld_wkt_list = []
        boundary_list = []
        for boundary_str in boundary_str_list:
            marking_pl_id_list = boundary_str[0].split(",")
            boundary_list.append("{}|{}".format(marking_pl_id_list[0], marking_pl_id_list[len(marking_pl_id_list) - 1]))
            sql = "select st_astext(geom) from nav_lane_marking_pl where marking_pl_id in(%s,%s)"
            current_ld_wkt_list = self.queryall(sql, [marking_pl_id_list[0],
                                                      marking_pl_id_list[len(marking_pl_id_list) - 1]])
            if current_ld_wkt_list is None or len(current_ld_wkt_list) < 2:
                continue
            merge_ld_road_shapely = CommTool.get_ld_road_wkt_by_boundary_link(current_ld_wkt_list[0][0],
                                                                              current_ld_wkt_list[1][0])
            ld_wkt_list.append(merge_ld_road_shapely.wkt)
        return ld_wkt_list, boundary_list

    def get_nav_gate_by_geom(self, geom):
        """
        获取指定范围内的道路大门L
        :param geom:
        :return:
        """
        sql = f"select node_id from nav_node where st_intersects(geom,st_geomfromtext(%s, 4326))"
        node_res = self.queryall(sql, [geom])
        if not node_res:
            return None
        node_ids = [node[0] for node in node_res]
        sql = "select node_id,type from nav_gate where node_id = ANY(%s)"
        return self.queryall(sql, [node_ids])

    def get_nav_gate_by_node_id(self, node_ids):
        """
        获取指定范围内的道路大门L
        :param geom:
        :return:
        """
        sql = "select node_id,type from nav_gate where node_id = ANY(%s)"
        return self.queryall(sql, [node_ids])

    def get_nav_node_by_geom(self, geom):
        """
        获取指定范围内的道路大门L
        :param geom:
        :return:
        """
        sql = f"select node_id,st_astext(geom) from nav_node where st_intersects(geom,st_geomfromtext(%s, 4326))"
        node_res = self.queryall(sql, [geom])
        if not node_res:
            return None
        return node_res

    def get_nav_link_by_geom(self, geom, form='52', kind=7):
        """
        获取指定范围内的道路大门L
        :param geom:
        :return:
        dir 1 双向，2顺，3逆
        """
        args = [geom]
        sql = f"""select link_id, s_nid, e_nid, dir, st_astext(geom), kind
            from nav_link where st_intersects(geom,st_geomfromtext(%s, 4326))"""
        if form:
            sql += " and form ~ %s"
            args.append(form)
        if kind:
            sql += " and kind >= %s"
            args.append(kind)
        nav_links = self.queryall(sql, args)
        return nav_links

    def get_high_nav_link_by_geom(self, geom, form, kind=8):
        """
        获取指定范围内的高等级道路link 刨除高架隧道之类的
        """
        args = [geom]
        sql = f"""select link_id, form, s_nid, e_nid, dir, st_astext(geom) 
            from nav_link where st_intersects(geom,st_geomfromtext(%s, 4326))"""
        if kind:
            sql += " and kind < %s"
            args.append(kind)
        if form:
            placeholders = ', '.join(['%s'] * len(form)) if form else ''  # 构建占位符字符串
            args.extend(form)  # 使用extend而不是append来添加form列表中的所有元素
            sql += f" and form not in ({placeholders})"
        nav_links = self.queryall(sql, args)
        return nav_links

    def get_all_ld_road_by_wkt(self, wkt, distance):
        """
        获取LD道路/同向车道面，WKT buff 0.5米
        :param wkt:
        :return:
        """
        sql = """
            select distinct lane_group_id from nav_lane_marking_pl 
                where st_intersects(geom, st_buffer(st_geomfromtext(%s,4326), %s)) 
                and (longitudinal_pl_type ~ '4' or longitudinal_pl_type ~ '2')
        """
        lane_group_list = self.queryall(sql, [wkt, distance])
        if not lane_group_list:
            return None, None
        group_id_list = []
        for lan_group in lane_group_list:
            group_id_list.append(lan_group[0])
        sql = "select boundary_list from nav_lane_group where lane_group_id = ANY(%s) and boundary_num > 0"
        boundary_str_list = self.queryall(sql, [group_id_list])
        # boundary_list 第一个与最后一个就是边界线
        ld_wkt_list = []
        boundary_list = []
        for boundary_str in boundary_str_list:
            marking_pl_id_list = boundary_str[0].split(",")
            boundary_list.append("{}|{}".format(marking_pl_id_list[0], marking_pl_id_list[len(marking_pl_id_list) - 1]))
            sql = "select st_astext(geom) from nav_lane_marking_pl where marking_pl_id in(%s,%s)"
            current_ld_wkt_list = self.queryall(sql, [marking_pl_id_list[0],
                                                      marking_pl_id_list[len(marking_pl_id_list) - 1]])
            if current_ld_wkt_list is None or len(current_ld_wkt_list) < 2:
                continue
            merge_ld_road_shapely = CommTool.get_ld_road_wkt_by_boundary_link(current_ld_wkt_list[0][0],
                                                                              current_ld_wkt_list[1][0])
            ld_wkt_list.append(merge_ld_road_shapely.wkt)
        return ld_wkt_list, boundary_list

    def get_node_geom(self, node_ids: list):
        """
        获取Node的几何信息
        Args:
            node_ids:

        Returns:

        """
        sql = "select st_astext(geom),node_id from nav_node where node_id=ANY(%s)"
        return self.queryall(sql, [node_ids])

    def get_nav_link_by_link_ids(self, link_id_list: list):
        """
        获取nav_link
        :param link_id_list:
        :return:
        """
        sql = f"select st_astext(geom),s_nid,e_nid,link_id,kind,len,dir from nav_link where link_id=ANY(%s)"
        return self.queryall(sql, [link_id_list])

    def is_over_high_road_link(self, geom: str):
        """
        判断link是否横跨高等级道路
        :param geom:
        :return:
        """
        sql = f"""
            select link_id,s_nid,e_nid,st_astext(geom) as geom from nav_link where st_intersects(geom,st_geomfromtext(%s, 4326)) and kind <= 7
        """
        return self.queryall_by_kv_cursor(sql, [geom])

    def get_union_geom_nav_link_by_node(self, node_ids: list):
        """
        获取nav_link
        :param node_ids:
        :return:
        """
        sql = "select st_astext(st_union(geom)) from nav_link where s_nid=ANY(%s) or e_nid=ANY(%s)"
        return self.queryone(sql, [node_ids, node_ids])

    def get_nav_link_by_sn_node(self, s_nodes: None, e_nodes: None):
        """
        获取nav_link
        Args:
            s_nodes:
            e_nodes:

        Returns:

        """
        if not s_nodes or not e_nodes:
            return None
        sql = "select link_id, st_astext(geom) from nav_link where 1 = 1"
        args = []
        if s_nodes:
            args.append(s_nodes)
            sql += "and s_nid=ANY(%s)"
        if e_nodes:
            args.append(e_nodes)
            sql += "and e_nid=ANY(%s)"
        return self.queryall(sql, args)

    def get_nav_link_by_s_node(self, s_nodes):
        """
        获取nav_link
        Args:
            s_nodes:
            e_nodes:

        Returns:

        """
        if not s_nodes:
            return None
        sql = "select link_id, st_astext(geom), dir from nav_link where 1 = 1 "
        args = []
        if s_nodes:
            args.append(s_nodes)
            sql += "and s_nid=ANY(%s)"
        return self.queryall(sql, args)

    def get_nearest_node(self, area_wkt, point_wkt):
        """
        指定范围内获取距离某个点最近的node
        Args:
            area_wkt:
            point_wkt:

        Returns:

        """
        sql = f"""
              select node_id, st_astext(geom) from nav_node 
              where st_intersects(geom, ST_GeomFromText(%s, 4326))
              ORDER BY geom <-> ST_GeomFromText(%s, 4326) LIMIT 1;
           """
        return self.queryone(sql, [area_wkt, point_wkt])

    def get_nav_link_by_node(self, node_ids: list, form='52', link_ids=None, exclude_link_id=None):
        """
        获取nav_link
        :param node_ids:
        :return:
        """
        sql = f"""
            select link_id,s_nid,e_nid,len,form,dir,st_astext(geom) from nav_link 
            where (s_nid =ANY(%s) or e_nid = ANY(%s))
        """
        args = [node_ids, node_ids]
        if form:
            sql += "and form ~ %s"
            args.append(form)
        if link_ids:
            args.append(link_ids)
            sql += "and link_id=ANY(%s)"
        if exclude_link_id:
            sql += "and link_id != %s"
            args.append(exclude_link_id)
        return self.queryall(sql, args)

    def get_adjoin_nid(self, node_id_arr):
        """
        获取邻接节点, 否则跨图幅有bug
        """
        sql = "select adjoin_nid from nav_node where node_id=ANY(%s) and  adjoin_nid !='' "
        ret = self.queryall(sql, [node_id_arr])
        if not ret:
            return []
        return [item[0] for item in ret]

    def get_contains_node_by_geom(self, geom):
        """
        获取范围内的Node
        Args:
            geom:

        Returns:

        """
        sql1 = f"""
           select node_id from nav_node
           where st_contains(st_geomfromtext(%s, 4326), geom)
        """
        return self.queryall(sql1, [geom])

    def get_near_link_by_node_geom(self, geom, kind=7):
        """
        根据坐标找附近需要关联的Link
        Args:
            geom:

        Returns:

        """
        sql = f"""
            select link_id,s_nid,e_nid,st_astext(geom) from nav_link 
            where st_intersects(geom, st_geomfromtext(%s, 4326)) 
            and kind > %s and kind != 10
            order by st_distance(geom, st_geomfromtext(%s, 4326)) limit 1
        """
        return self.queryone(sql, [geom, kind, geom])

    def get_union_geom_nav_link_by_kind(self, s_nid: str):
        """
        根据s_nid获取非高等级道路
        Args:
            s_nid:

        Returns:

        """
        sql = f"select st_astext(st_union(geom)) from nav_link where s_nid = %s and kind > 7"
        return self.queryone(sql, [s_nid])

    def get_intersects_nav_link_by_wkt_new(self, wkt: str):
        """
        获取相交的nav_link
        :param wkt:
        :return:
        """
        sql = f"""
            select link_id, form, kind, st_astext(geom) as geom,s_nid,e_nid,len,app,dir from nav_link 
            where st_intersects(geom, st_geomfromtext(%s,4326))
        """
        return self.queryall_by_kv_cursor(sql, [wkt])

    def get_nav_link_by_node_new(self, node_ids: list, form='52'):
        """
        获取nav_link
        :param node_ids:
        :return:
        """
        sql = f"""
            select link_id,s_nid,e_nid,len,form,kind from nav_link 
            where (s_nid =ANY(%s) or e_nid = ANY(%s))
        """
        args = [node_ids, node_ids]
        if form:
            sql += " and form = %s"
            args.append(form)
        return self.queryall_by_kv_cursor(sql, args)

    def get_nav_link_by_link_ids_new(self, link_id_list: list):
        """
        获取nav_link
        :param link_id_list:
        :return:
        """
        sql = f"select st_astext(geom) geom,s_nid,e_nid,link_id,kind,len,dir,form from nav_link where link_id=ANY(%s)"
        return self.queryall_by_kv_cursor(sql, [link_id_list])

    def get_nav_gate_by_node_id_new(self, node_id, out_link_id=None):
        """
        获取进入大门8级路的Link
        Args:
            out_link_id:

        Returns:

        """
        args = [node_id]
        sql = f"""
            select in_linkid,gate_id,out_linkid from nav_gate where node_id = %s
        """
        if out_link_id:
            sql += "and out_linkid = %s"
            args.append(out_link_id)
        return self.queryall_by_kv_cursor(sql, args)

    def get_link_scene(self, link_ids):
        """
        获取Link专题
        :param link_ids:
        :return:
        """
        sql = f"""
            select link_id, theme from NAV_LINK_SCENE where link_id=ANY(%s)
        """
        return self.queryall_by_kv_cursor(sql, [link_ids])

    def get_near_link_by_node_geom_new(self, geom, center_geom, kind=7):
        """
        根据坐标找附近需要关联的Link
        Args:
            geom:

        Returns:

        """
        sql = f"""
            select link_id,s_nid,e_nid,st_astext(geom) as geom from nav_link 
            where st_intersects(geom, st_geomfromtext(%s, 4326)) 
            and kind > %s and kind != 10
            order by st_distance(geom, st_geomfromtext(%s, 4326)) limit 1
        """
        return self.queryone_by_kv_cursor(sql, [geom, kind, center_geom])

    def get_node_list(self, node_ids=None):
        """
        获取Node的几何信息
        Args:
            node_ids:

        Returns:

        """
        sql = f"""
          select st_astext(geom) as geom,node_id from nav_node where 1=1
        """
        args = []
        if node_ids:
            sql += ' and node_id=ANY(%s)'
            args.append(node_ids)
        return self.queryall_by_kv_cursor(sql, args)

    def get_nav_link(self, link_ids=None, s_nodes=None, e_nodes=None, kind=None, form=None, geom=None):
        """
        获取nav_link
        Args:
            link_ids:
            s_nodes:
            e_nodes:

        Returns:

        """
        args = []
        sql = "select link_id, st_astext(geom) as geom, s_nid, e_nid, kind, form, dir,viad from nav_link where 1 = 1"
        if link_ids:
            args.append(link_ids)
            sql += "and link_id=ANY(%s)"
        if s_nodes:
            args.append(s_nodes)
            sql += "and s_nid=ANY(%s)"
        if e_nodes:
            args.append(e_nodes)
            sql += "and e_nid=ANY(%s)"
        if kind:
            args.append(kind)
            sql += "and kind=%s"
        if form:
            args.append(form)
            sql += "and form=%s"
        if geom:
            args.append(geom)
            sql += "and st_intersects(st_geomfromtext(%s, 4326), geom)"
        if len(args) == 0:
            return []
        return self.queryall_by_kv_cursor(sql, args)

    def cross_level_links(self, gcj_line_wkt=None, level=7):
        """
        判断一条线是否跨越了高等级道路
        Args:
            gcj_line_wkt
            level

        Returns:
        """
        sql = """
            select link_id from nav_link 
            where st_intersects(geom, st_geomfromtext(%s, 4326)) 
            and kind <= %s
        """
        rows = self.queryall_by_kv_cursor(sql, [gcj_line_wkt, level])
        return rows