"""
从unify_work_history 解析任务
"""
import json
from typing import List
from src.aoi.aoi_protection import context
from src.door_naming.DaoBeeflow import DaoBee<PERSON>
from src.aoi.aoi_protection import cfg


def load_from_unify(task_id, wpf_id) -> List[context.HumanHandleData]:
    """
    从履历查询
    :param task_id: 任务id
    :param wpf_id: wpf_id
    :return:
    """
    return_data = []
    with DaoBeeflow() as dao:
        sql = 'select work_resource, work_result, wpf_id from unify_work_history where wpf_id=%s'
        dao.cursor_beeflow.execute(sql, [wpf_id])
        ret = dao.cursor_beeflow.fetchall()
        for (work_resource_str, work_result_str, wpf_id) in ret:
            work_resource = json.loads(work_resource_str)
            work_result = json.loads(work_result_str)
            bid = work_resource['bd_poi']['bid']
            work_conclusion = work_result['work_conclusion']
            if work_conclusion and work_conclusion == 1:
                human_ret = cfg.CAN_OFFLINE
            else:
                human_ret = cfg.CANNOT_OFFLINE
            d = context.HumanHandleData(wpf_task_id=task_id, bid=bid, wpf_human_status=human_ret)
            return_data.append(d)
    return return_data
