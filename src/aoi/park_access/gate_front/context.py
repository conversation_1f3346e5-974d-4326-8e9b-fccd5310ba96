# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场出入口上下文信息
"""
import sys
import os
from loguru import logger
from dataclasses import dataclass, field

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW
from src.aoi.dao.dest_traj import DestTraj
from src.aoi.dao.master_back import MasterBackDao
from src.aoi.dao.aoi_resume_query import AoiResumeQuery
from src.aoi.BFQuery import BFQuery
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi.dao.traj_feature import TrajFeatureDao

log_path = "./log/underground.log"
logger.add(log_path, rotation="10 MB")


@dataclass
class Traj:
    """
    轨迹信息
    """
    dest_traj_list: list = field(default_factory=list)
    exp_traj_list: list = field(default_factory=list)


@dataclass
class Dao:
    """
    数据访问对象
    """
    road_dao = Road()
    poi_online_query = PoiOnlineQuery()
    poi_online_rw = PoiOnlineRW()
    aoi_resume_query = AoiResumeQuery()
    trans_dao = Trans()
    beeflow_dao = BFQuery()
    master_back_dao = MasterBackDao()
    dest_traj_dao = DestTraj()
    traj_feature_dao = TrajFeatureDao()


@dataclass
class ParkingAccess:
    """
    停车场出入口挖掘成果
    """
    type: str = field(default="", init=False)  # 类型, 关联Node还是link
    node_id: str = field(default="", init=False)
    short_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    node_geom_mc: str = field(default="", init=False)
    node_passage: str = field(default="", init=False)
    link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    link_geom_mc: str = field(default="", init=False)
    strategy: str = field(default="", init=False)  # 挖掘策略
    strategy_value: str = field(default="", init=False)  # 挖掘策略
    is_valid: bool = field(default=True, init=False)  # 关联是否合法
    filter_reason: str = field(default="", init=False)  # 过滤原因
    road_relation: str = field(default="", init=False)
    name: str = field(default="", init=False)
    parent_id: str = field(default="", init=False)
    park_access_bid: str = field(default="", init=False)
    confidence: int = field(default=0, init=False)


@dataclass
class ParkingSubPOI:
    """
    停车场子POI信息
    """
    bid: str = field(default="", init=False)
    name: str = field(default="", init=False)
    point_gcj: str = field(default="", init=False)
    relation_node_id: str = field(default="", init=False)
    node_geom: str = field(default="", init=False)
    relation_link_id: str = field(default="", init=False)
    link_geom: str = field(default="", init=False)
    road_relation: str = field(default="", init=False)
    type: str = field(default="", init=False)


COORDINATE_FACTOR = 110000

# 出入口关联类型
PARKING_ACCESS_TYPE_NODE = "NODE"
PARKING_ACCESS_TYPE_LINK = "LINK"
PARKING_ACCESS_TYPE_POINT = "POINT"

# 出入口关联策略
GATE_FRONT_EXP_TRAJ_END_AGG = "GATE_FRONT_EXP_TRAJ_END_AGG"
GATE_FRONT_EXP_TRAJ_END_AGG_LINK = "GATE_FRONT_EXP_TRAJ_END_AGG_LINK"
GATE_FRONT_EXP_TRAJ_END_AGG_NODE = "GATE_FRONT_EXP_TRAJ_END_AGG_NODE"
GATE_FRONT_EXP_TRAJ_END_GATE_HIGH_CONFIDENCE = "GATE_FRONT_EXP_TRAJ_END_GATE_HIGH_CONFIDENCE"
GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_END_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_END_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_END_TRAJ_AREA_INTERSECT_NODE = "GATE_FRONT_END_TRAJ_AREA_INTERSECT_NODE"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE_NEW = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_GATE_NEW"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK_NEW = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_LINK_NEW"
GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_NODE = "GATE_FRONT_EXP_TRAJ_AREA_INTERSECT_NODE"
GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_NODE = "GATE_FRONT_PARKING_TRAJ_AREA_INTERSECT_NODE"
GATE_FRONT_EXP_TRAJ_END_GATE = "GATE_FRONT_EXP_TRAJ_END_GATE"
GATE_FRONT_EXP_TRAJ_END_LINK = "GATE_FRONT_EXP_TRAJ_END_LINK"
GATE_FRONT_AREA_NEAR_LINK = "GATE_FRONT_AREA_NEAR_LINK"
MANUAL_WORK_ACHIEVE_RELATION_GATE = "MANUAL_WORK_ACHIEVE_RELATION_GATE"
MANUAL_WORK_ACHIEVE_RELATION_LINK = "MANUAL_WORK_ACHIEVE_RELATION_LINK"
AREA_NEAR_GATE_AND_EXP_TRAJ = "AREA_NEAR_GATE_AND_EXP_TRAJ"
AREA_NEAR_OPEN_GATE = "AREA_NEAR_OPEN_GATE"
GATE_FRONT_EXP_TRAJ_END_AGG_NODE_RECALL = "GATE_FRONT_EXP_TRAJ_END_AGG_NODE_RECALL"
AREA_INTERSECTS_LINK_WITH_SHORT_EDGE = "AREA_INTERSECTS_LINK_WITH_SHORT_EDGE"
AREA_INTERSECTS_LINK_WITH_SHORT_EDGE_V2 = "AREA_INTERSECTS_LINK_WITH_SHORT_EDGE_V2"
GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_GATE = "GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_GATE"
GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_LINK = "GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_LINK"
GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_NODE = "GATE_FRONT_QUARTER_EXP_TRAJ_AREA_INTERSECT_NODE"

# 出入口自动关联策略
GATE_FRONT_AREA_NEAR_LINK_ADN_EXP_TRAJ_END_AGG = 'GATE_FRONT_AREA_NEAR_LINK_ADN_EXP_TRAJ_END_AGG'
GATE_FRONT_AREA_NEAR_LINK_ADN_HIGH_CONFIDENCE_EXP_TRAJ = 'GATE_FRONT_AREA_NEAR_LINK_ADN_HIGH_CONFIDENCE_EXP_TRAJ'

# 出入口错误挖掘策略
PARK_ACCESS_MISTAKE_BY_DISTANCE = "PARK_ACCESS_MISTAKE_BY_DISTANCE"
PARK_ACCESS_MISTAKE_BY_URGENT_GATE = "PARK_ACCESS_MISTAKE_BY_URGENT_GATE"
PARK_ACCESS_RELATION_OTHER_PARK_ACCESS = "PARK_ACCESS_RELATION_OTHER_PARK_ACCESS"
PARK_ACCESS_EXP_TRAJ_NOT_ARRIVED = "PARK_ACCESS_EXP_TRAJ_NOT_ARRIVED"

# 缺路情报挖掘
MIS_GATE_EXP_TRAJ_END_AGG = "MIS_GATE_EXP_TRAJ_END_AGG"
MIS_GATE_HIGH_EXP_TRAJ = "MIS_GATE_HIGH_EXP_TRAJ"

# 情报置信度
INTELLIGENCE_CONFIDENCE_HIGH = 100
INTELLIGENCE_CONFIDENCE_MID = 80
INTELLIGENCE_CONFIDENCE_LOW = 60
INTELLIGENCE_CONFIDENCE_NO = 0


class Context:
    """
    建筑物后处理上下文
    """

    def __init__(self, parking: map, origin_intelligence_ids=None, task_id=0):
        self.park_intelligence_id = parking[0]
        self.area = parking[1]
        self.geom = ''
        self.bid = parking[2]
        self.parent_id = ''
        self.park_type = 'new'
        self.guid = ''
        self.req = ''
        self.resp = ''
        self.batch = ''
        self.task_id = task_id
        self.origin_intelligence_ids = origin_intelligence_ids
        self.option = 'new'
        self.new_intelligence_ids = list()
        self.show_tag = '门前停车场'
        self.near_street_poi_list = list()
        self.parking_access_list = list()
        self.sub_pois = list()
        self.dao = Dao()
        self.traj = Traj()
