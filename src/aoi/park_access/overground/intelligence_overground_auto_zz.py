# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""
import datetime
import multiprocessing
import re
import sys
import os
import time
import uuid
from math import ceil

import tqdm
import requests
import json
import math
import numpy as np
import traceback
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiPolygon
from loguru import logger
from shapely import wkt
from sklearn.cluster import DBSCAN

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../../..")
sys.path.append("../..")
from aoi.dao.poi_online_query import PoiOnlineQuery
from shapely import wkt
from loguru import logger
from common import pipeline
from context import Context
import context
from aoi.park_access import park_utils
from aoi import utils
from aoi.park_access import park_access_qc as qc

COORDINATE_FACTOR = 110000
COORDINATE_FACTOR_NEW = 100000

# logger.remove()
log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

BUILD_GATE_CATEGORY = ['医疗;综合医院', '医疗;专科医院', '教育培训;高等院校', '房地产;住宅区', '房地产;其他', '房地产', '公司企业;公司', '公司企业;园区', '公司企业',
                       '公司企业;厂矿', '公司企业;农林园艺', '教育培训;中学', '教育培训;小学', '教育培训;幼儿园', '教育培训;培训机构', '教育培训;其他', '教育培训;科研机构',
                       '教育培训;科技馆', '教育培训;成人教育', '教育培训;特殊教育学校', '教育培训', '政府机构;各级政府', '政府机构;行政单位', '政府机构;公检法机构',
                       '政府机构;政治教育机构', '政府机构;中央机构', '政府机构;涉外机构', '汽车服务;汽车销售', '汽车服务;汽车配件', '汽车服务;汽车维修', '汽车服务;汽车检测场',
                       '文化传媒;广播电视', '绿地;高尔夫球场', '医疗;疗养院', '医疗;其他', '医疗', '生活服务;物流公司']
NOT_BUILD_CATEGORY = ['交通设施;加油加气站', '交通设施;飞机场', '交通设施;火车站', '购物;购物中心']
IS_TEST = True


def create_intelligence(ctx: Context):
    """
    出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        # 根据aoi及终点轨迹和经验轨迹
        add_access_by_aoi_node_dest_and_exp_traj,
        park_access_filter,
    )
    pipe_list(ctx)


def park_access_filter(ctx: Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_qc(ctx)
    return proceed()


def add_access_by_aoi_node_dest_and_exp_traj(ctx: context, proceed):
    """
    根据aoi大门及轨迹判断是否有出入口
    """
    # 名称过滤疑似路侧
    park_name = ctx.parking_info.name
    if '东侧' in park_name or '西侧' in park_name or '南侧' in park_name or '北侧' in park_name:
        logger.info(f"filter_warning: {park_name}, 名称疑似路侧，跳过")
        return proceed()
    
    # 距离过滤疑似路侧 停车场到面的边沿最小距离
    # print(wkt.loads(ctx.parking_info.park_wkt), wkt.loads(ctx.aoi_info.wkt))
    if ctx.aoi_info.wkt:
        aoi_area_sp = wkt.loads(ctx.aoi_info.wkt)
        park_point_sp = wkt.loads(ctx.parking_info.park_wkt)
        park_aoi_dis = park_point_sp.distance(aoi_area_sp)
        if park_aoi_dis > 0:
            logger.info(f"filter_warning: {park_name}, 停车场在aoi外，距离是 {park_aoi_dis:.2f} m，跳过")
            return proceed()
        else:
            park_aoi_min_dis = min_distance_to_polygon(park_point_sp, aoi_area_sp)
            if park_aoi_min_dis < 13:
                logger.info(f"filter_warning: {park_name}, 停车场到aoi最小距离是 {park_aoi_min_dis:.2f} m，小于10m，跳过")
                return proceed()

    competitor_access = ctx.dao.poi_online_query.get_competitor_access_nav_by_park_bids_and_access_type(
                                                                    [ctx.parking_info.bid], '入口')
    logger.info(f"竞品坐标：{competitor_access}")

    root_access_infos = get_access_info_by_root_bid(ctx, ctx.parking_info.root_bid)
    #循环 aoi大门    
    for aoi_node in ctx.aoi_info.blu_access_list:
        logger.info(f"aoi_node: {aoi_node}")
        node_sp = wkt.loads(aoi_node.node_geom)
        if not aoi_node.is_enter:
            logger.info(f"filter_warning: {aoi_node}, 大门不开放，跳过")
            continue
        if not aoi_node.node_geom:
            logger.info(f"filter_warning: {aoi_node}, 大门无坐标，跳过")
            continue

        passage = utils.is_close_gate(aoi_node.node_id)
        if passage:
            logger.info(f"filter_warning: {aoi_node}, 大门通行性不开放，跳过")
            continue

        # 当前停车场是否大门已存在出入口
        if ctx.sub_pois:
            access_min_dis = min(node_sp.distance(wkt.loads(access_info.point_gcj)) \
                for access_info in ctx.sub_pois) * 110000
            if access_min_dis < 30:
                logger.info(f"filter_warning: {aoi_node},当前停车场此出入口已存在，跳过")
                continue

        # 当前主点下是否已有此输入口
        if root_access_infos:
            # for root_access_info in root_access_infos:
            #     print(root_access_info[0])
            root_access_min_dis = min(node_sp.distance(wkt.loads(root_access_info[0])) \
                for root_access_info in root_access_infos) * 110000 
            if root_access_min_dis < 10:
                logger.info(f"filter_warning: {aoi_node},当前主点下此出入口已存在，跳过,root_bid = \
                    {ctx.parking_info.root_bid}")
                continue
        
        # 过滤有地下入口 先用30M范围粗略过滤 我们自己地下停车场的口
        if ctx.dao.poi_online_query.check_node_has_access_near_by(node_sp.buffer(35 / COORDINATE_FACTOR).wkt, \
                show_tag=["地下停车场", '立体停车场']):
            logger.info(f"filter_warning: {aoi_node}, 附近30m已有地下入口，跳过")
            continue

        if is_competitor_underground_here(ctx, node_sp):
            logger.info(f"filter_warning: {aoi_node}, 附近30m已有竞品地下入口，跳过")
            continue
        
        res, _, _ = utils.is_dead_link_road(node_sp.buffer(5 / 110000).wkt, 50, form=None)
        print(f"断头路：{res}")
        if res:
            logger.info(f"filter_warning: {aoi_node}, 断头路过滤")
            continue

        # 父点经验轨迹和大门的最近距离
        park_exp_min_dis = 10000
        # if ctx.traj.park_exp_traj_list_more:
        if ctx.traj.aoi_exp_traj_list:
            # 只取前面的
            exp_traj_list = ctx.traj.aoi_exp_traj_list
            if len(ctx.traj.aoi_exp_traj_list) > 4:
                exp_traj_list = ctx.traj.aoi_exp_traj_list[0:4]
            # for traj in exp_traj_list:
            #     print(traj)
            park_exp_min_dis = min(node_sp.distance(Point(traj.coords[-1])) for traj in exp_traj_list) * 110000

        # 大门附近竞品附近
        is_competitor_near = False 
        competitor_min_dis = 10000 
        if competitor_access:
            competitor_min_dis = min(node_sp.distance(wkt.loads(competitor['geom'])) \
                for competitor in competitor_access) * 110000
            # print(competitor_min_dis)
            if competitor_min_dis < 50:
                is_competitor_near = True
                logger.info(f"{aoi_node}, 附近竞品存在,距离是{competitor_min_dis}")

        # 经验轨迹和竞品有一个就可以
        if park_exp_min_dis > 60 and not is_competitor_near:
            logger.info(f"filter_warning: {aoi_node}, 经验轨迹最小距离超过60m且附近没有竞品，跳过 {park_exp_min_dis:.3f} m, \
                is_competitor_near:{is_competitor_near}")
            continue

        # 通过大门buffer 5m 的轨迹量
            # 获取轨迹
        end_trajs = ctx.traj.dest_traj_list
        # end_trajs_num = len(end_trajs)
        link_geom_sp = None
        link_infos = ctx.dao.road_dao.get_nav_link_by_node(node_ids=[aoi_node.node_id])
        if link_infos:
            link_geom_sp = wkt.loads(link_infos[0][6])
        # 终点轨迹轨迹数量经过node的数量
        pass_node_dest_traj_num = get_dest_traj_pass_node_num(node_sp.buffer(20 / COORDINATE_FACTOR), \
            link_geom_sp, end_trajs)
        # pass_node_dest_traj_num = aoi_node.dest_traj_pass_num
        if pass_node_dest_traj_num < 4:
            logger.info(f"filter_warning: {aoi_node}, 通过大门的轨迹量小于3,是{pass_node_dest_traj_num}，跳过")
            continue
        print(f"\ntarget\t{ctx.parking_info.bid}\t{aoi_node.node_id}\t{aoi_node.node_geom}\t\
            {park_exp_min_dis}\t{pass_node_dest_traj_num}\t{competitor_min_dis}\n")
    proceed()


def get_access_info_by_root_bid(ctx, root_bid):
    """
    根据主点召回出入口信息
    """
    return ctx.dao.poi_online_query.get_access_info_by_root_bid(root_bid)


def is_competitor_underground_here(ctx, node_sp):
    """
    竞品是否有这个地下口
    """
    node_buffer_sp = node_sp.buffer(30 / COORDINATE_FACTOR)
    park_bid = ctx.parking_info.bid
    access_infos = ctx.dao.poi_online_query.get_competitor_access_info_by_park_bid(park_bid, ['地下停车场', '立体停车场'])
    if access_infos:
        for access_info in access_infos:
            if wkt.loads(access_info[1]).intersection(node_buffer_sp):
                return True
    
    root_bid = ctx.parking_info.root_bid
    if root_bid == '':
        return False
    access_infos = ctx.dao.poi_online_query.get_competitor_access_info_by_main_bid(root_bid, ['地下停车场', '立体停车场'])
    if access_infos:
        for access_info in access_infos:
            if wkt.loads(access_info[1]).intersection(node_buffer_sp):
                return True
    return False


def calculate_linestring_angle(line1, line2):
    """
    计算两条线路的夹角
    """
    # 确保输入是有效的 LineString
    if not isinstance(line1, LineString) or not isinstance(line2, LineString):
        logger.info("输入必须是 LineString 对象")
        return 90, 90
    # 提取方向向量
    x1, y1 = line1.coords[0]
    x2, y2 = line1.coords[-1]
    x3, y3 = line2.coords[0]
    x4, y4 = line2.coords[-1]
    # 计算方向向量
    u = (x2 - x1, y2 - y1)
    v = (x4 - x3, y4 - y3)
    # 计算点积
    dot_product = u[0] * v[0] + u[1] * v[1]
    # 计算向量的模长
    norm_u = math.sqrt(u[0] ** 2 + u[1] ** 2)
    norm_v = math.sqrt(v[0] ** 2 + v[1] ** 2)
    # 防止除以零的情况
    if norm_u == 0 or norm_v == 0:
        logger.info("线段的长度不能为零")
        return 90, 90
    # 计算夹角的余弦值
    cos_theta = dot_product / (norm_u * norm_v)
    # 将余弦值限定在 [-1, 1] 范围内，避免数值误差
    cos_theta = max(-1, min(1, cos_theta))
    # 计算夹角（弧度制）
    angle_rad = math.acos(cos_theta)
    # 如果需要角度制，转换为角度
    angle_deg = math.degrees(angle_rad)
    return angle_rad, angle_deg


def is_cross_node(traj, node_buffer, link_geom):
    """
    判断轨迹是否穿过 node
    """
    intersection = traj.intersection(node_buffer)
    if isinstance(intersection, LineString) and intersection.length > 0:
        angle_rad, angle_deg = calculate_linestring_angle(intersection, link_geom)
        return angle_deg <= 70 or angle_deg >= 110
    return False


def get_dest_traj_pass_node_num(node_buffer_sp, link_geom_sp, dest_traj_list):
    """
    获取轨迹通过节点的数量
    """
    traj_pass_node_num = 0
    if not dest_traj_list:
        return 0 
    for sp in dest_traj_list:
        if link_geom_sp:
            # 有link
            res = is_cross_node(sp, node_buffer_sp, link_geom_sp)
            if res:
                traj_pass_node_num += 1
        else:
            # 无link
            if node_buffer_sp.intersection(sp):
                traj_pass_node_num += 1
    return traj_pass_node_num


def min_distance_to_polygon(point, polygon):
    """
    点到面的最小距离
    """
    # 创建 Point 对象
    # 检查点是否在多边形内部
    if polygon.contains(point):
        # 如果点在多边形内部，计算点到每条边的最短距离
        min_distance = float('inf')
        exterior_coords = polygon.exterior.coords
        # 遍历多边形的每一条边
        for i in range(len(exterior_coords) - 1):
            # 获取边的两个顶点
            p1 = exterior_coords[i]
            p2 = exterior_coords[i + 1]
            # 创建边的 LineString 对象
            line = LineString([p1, p2])
            # 计算点到边的最短距离
            distance = point.distance(line) * 110000
            # 更新最小距离
            if distance < min_distance:
                min_distance = distance
        return min_distance
    else:
        # 如果点在多边形外部，直接计算点到多边形边界的最短距离
        return point.distance(polygon.exterior) * 110000


def main(bid_list: list):
    """
    地上停车场情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场', '停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    for parking in tqdm.tqdm(parking_list):
        try:
            logger.info(f"开始挖掘地上停车场自动化情报:{parking[0]},{parking[7]}")
            ctx = Context(parking)
            ctx.intelligence_type = "park_access_overground_add"
            ctx.init_context()
            ctx.get_park_sub_pois()
            # AOI 场景
            if ctx.aoi_info.bid:
                # 获取出入口信息
                ctx.get_aoi_blu_access_list()
                # 轨迹过滤AOI范围外的
                ctx.filter_traj_by_aoi()
                # 计算AOI出入口轨迹通量
                ctx.cal_dest_traj_pass_blu_access()
            ctx.traj.filter_aoi_dest_traj_list = ctx.traj.dest_traj_list
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking[7]}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    # 取四大垂类停车场BID
    if bid_file == '0' and bids == '0':
        poi_online_query = PoiOnlineQuery()
        category = ['住宅区']
        limit = 1500
        park_categories_list = poi_online_query.get_park_categories_overground_lists(category, limit)
        bid_list = list(set([item['bid'] for item in park_categories_list]))
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)

#  aq_py3 intelligence_overground_auto_zz.py 0  17998856295590709660  0  1 0