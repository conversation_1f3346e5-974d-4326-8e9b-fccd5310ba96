#!/bin/bash
#directory

CURR_PATH=$(readlink -f `dirname $BASH_SOURCE[0]`)
source "$CURR_PATH"/../../../../conf/globalConf.sh
WORKPATH=$(cd $CURR_PATH/..;pwd);
BINPATH="${WORKPATH}/bin"
PythonScript="${WORKPATH}/bin/python"
PhpScript="${WORKPATH}/bin/php"
DATAPATH="$TMP_PATH/gaode_data"
OUTPUTPATH="${DATAPATH}/output"
SPLITPATH="${DATAPATH}/split"
TMPPATH="${DATAPATH}/tmp"
CITYPATH="${DATAPATH}/city"
mkdir -p "$OUTPUTPATH" "$SPLITPATH" "$TMPPATH" "$CITYPATH"

#tool path
#PYTHONBIN="python"
#PHPBIN="/home/<USER>/odp/php/bin/php"
AS_HASH="/home/<USER>/map/mappoi/address/tools/As_Hash"



#hadoop path
HadoopInputPath="/app/lbs/lbs-poi/spider/gaode"
HadoopOutputPath="/app/lbs/lbs-poi/chentong/upload/Gaode_AOI"
HadoopJobName="wenxingwang.parseGaodeData"
HadoopMapCapacity=1000
HadoopRedCapacity=180
HadoopMapTask=1000
HadoopRedTask=2000
HadoopQueueName="map-poi"
HadoopPriority="NORMAL"
HadoopQueueName="map-poi"

#relay data path
POIDATAPATH="/home/<USER>/accur-common-data/data"
#AOIPATH="ftp://gzhxy-mapse-sug-bs201.gzhxy/home/<USER>/aoi/"
AOIPATH="ftp://sh01-map-poi-dc01.sh01.baidu.com/home/<USER>/safe_zone/aoi/"
# TEXT_CLASSIFICATION="/home/<USER>/chentong/tools/text_classification"
GDAOIDATA="${DATAPATH}/gaode.aoi"
# InFilePath="ftp://map:<EMAIL>/home/<USER>/chentong/CompetitionAoiProcess/tmp/AOI_PC.input"
#InFilePath="ftp://map:<EMAIL>/home/<USER>/wenxingwang/aoi_strategy/AOI_CompetitionerCrawl/CompetitionAoiProcess/tmp/AOI_PC.input"
TJ_AOI="ftp://gzns-ns-map-guoke16.gzns.baidu.com/home/<USER>/sh_script/blu_face_aoi_export/blu_face_aoi.csv"
TC_AOI="ftp://gzns-map-poi-spider00.gzns.baidu.com/home/<USER>/workspace/spider-info/data/publish/tencent/infos_base"
