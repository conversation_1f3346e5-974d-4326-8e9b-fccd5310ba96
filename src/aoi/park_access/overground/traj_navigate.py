# -*- coding: utf-8 -*-
"""
地上停车场情报挖掘
"""

import json
import os
import sys
import time

import requests
from mapio.utils import bid2uid, uid2bid, bns
from shapely.geometry import Point, LineString
from shapely import wkt
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import numpy as np
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi import utils
from src.aoi.dao.road import Road
from src.aoi.dao.trans import Trans
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.poi_online_rw import PoiOnlineRW

log_path = "./log/overground_intelligence.log"
logger.add(log_path, rotation="10 MB")

x_pi = 3.14159265358979324 * 3000.0 / 180.0
pi = 3.1415926535897932384626  # π
a = 6378245.0  # 长半轴
ee = 0.00669342162296594323  # 偏心率平方
trace_split_distance = 0.00120
trace_split_time_second = 480

TRAJ_TYPE_WKT = 'WKT'
TRAJ_TYPE_LINK = 'LINK'

road_dao = Road()
trans_dao = Trans()
poi_online_rw_dao = PoiOnlineRW()
poi_online_dao = PoiOnlineQuery()

COORDINATE_FACTOR_NEW = 100000


def get_navigation_traj_by_wkt(geom):
    """
    根据范围获取导航数据
    Returns:

    """
    traj_navigate = TrajNavigate()
    traj_navigate.aoi_wkt = geom
    traj_navigate.get_traj_by_wkt()
    traj_navigate.filter_out_aoi_traj()
    traj_navigate.filter_noise_trace_by_angle_and_intersects()
    return traj_navigate.traj_list


def get_navigation_traj_by_link(long_link_id, aoi_wkt, from_db=True):
    """
    根据LINK获取轨迹数据
    Args:
        long_link_id:

    Returns:

    """
    traj_list = get_link_traj_by_db(aoi_wkt)
    if not traj_list and not from_db:
        link_traj_list = get_link_traj_by_request(long_link_id, aoi_wkt)
        traj_list = list(set([item.geom for item in link_traj_list]))
    return traj_list


def get_link_traj_by_db(aoi_wkt):
    """
    LINK轨迹数据库获取
    Args:
        long_link_id:

    Returns:

    """
    aoi_sp = wkt.loads(aoi_wkt)
    aoi_buffer_20m = aoi_sp.buffer(20 / COORDINATE_FACTOR_NEW)
    traj_list = poi_online_dao.get_navigate_traj(geom=aoi_buffer_20m.wkt)
    if not traj_list:
        return []
    return [wkt.loads(traj['geom']) for traj in traj_list]


def get_link_traj_by_request(long_link_id, aoi_wkt, park_area_wkt=None):
    """
    接口请求获取轨迹数据
    Args:
        long_link_id:

    Returns:

    """
    long_link_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
    if not long_link_list:
        return []
    short_link_list = trans_dao.get_short_link_by_long_link_ids([long_link_id])
    if not short_link_list:
        return []
    short_link_id = short_link_list[0][0]
    traj_navigate = TrajNavigate()
    traj_navigate.long_link_id = long_link_id
    traj_navigate.aoi_wkt = aoi_wkt
    traj_navigate.park_area_wkt = park_area_wkt
    traj_navigate.short_link_id = short_link_list[0][0]
    # 判断是否双向LINK
    traj_dir_value = [0]
    if long_link_list[0]['dir'] == 1:
        traj_dir_value.append(1)
    for traj_dir in traj_dir_value:
        traj_navigate.dir_link_id = f"{short_link_id}{traj_dir}"
        traj_list = traj_navigate.get_traj_by_link()
        if traj_list:
            traj_navigate.traj_list += traj_list
    traj_navigate.filter_out_aoi_traj()
    traj_navigate.filter_noise_trace_by_angle_and_intersects()
    return traj_navigate.traj_list


def get_origin_link_traj(long_link_id):
    """
    获取原始LINK轨迹信息
    Args:
        long_link_id:

    Returns:

    """
    long_link_list = road_dao.get_nav_link_by_link_ids_new([long_link_id])
    if not long_link_list:
        return []
    short_link_list = trans_dao.get_short_link_by_long_link_ids([long_link_id])
    if not short_link_list:
        return []
    short_link_id = short_link_list[0][0]
    traj_navigate = TrajNavigate()
    traj_navigate.long_link_id = long_link_id
    traj_navigate.short_link_id = short_link_list[0][0]
    # 判断是否双向LINK
    traj_dir_value = [0]
    if long_link_list[0]['dir'] == 1:
        traj_dir_value.append(1)
    traj_list = []
    for traj_dir in traj_dir_value:
        traj_navigate.dir_link_id = f"{short_link_id}{traj_dir}"
        traj_info = traj_navigate.get_link_traj_request()
        traj_list.append(traj_info)
    return traj_list


@dataclass
class TrajLine:
    """"
    轨迹线
    """
    bid: str = field(default="", init=False)
    long_link_id: str = field(default="", init=False)
    short_link_id: str = field(default="", init=False)
    geom: str = field(default="", init=False)
    traj_id: str = field(default="", init=False)
    gps_time: int = field(default=0, init=False)
    dir: int = field(default=0, init=False)
    weight: int = field(default=0, init=False)
    prediction: float = field(default=0, init=False)
    cuid: str = field(default='', init=False)
    ori_traj_id: str = field(default='', init=False)
    confidence: float = field(default=0, init=False)
    encode_cuid: str = field(default='', init=False)


class TrajNavigate(object):
    """
    导航轨迹
    """

    def __init__(self):
        self.wkt = ''
        self.aoi_wkt = ''
        self.park_area_wkt = ''
        self.long_link_id = ''
        self.short_link_id = ''
        self.dir_link_id = ''
        self.traj_list = []

    def get_traj_by_wkt(self):
        """
        根据范围获取轨迹
        Returns:

        """
        # 拉取轨迹数据
        origin_traj = self.get_wkt_traj_request()
        # 解析轨迹数据
        self.parse_wkt_trace(origin_traj)
        return self.traj_list

    def get_traj_by_link(self):
        """
        根据LINK获取轨迹
        Returns:

        Returns:

        """
        # 拉取轨迹数据
        origin_traj = self.get_link_traj_request()
        # 解析轨迹数据
        self.parse_link_trace(json.loads(origin_traj))
        return self.traj_list

    def get_wkt_traj_request(self):
        """
        获取轨迹请求
        Returns:

        """
        geom_sp = wkt.loads(utils.gcj_to_wgc(self.aoi_wkt))
        bounds = geom_sp.bounds
        # 获取当前时间
        now = datetime.now()
        # 计算15天前的时间
        days_ago_15 = now - timedelta(days=15)
        days_ago_1 = now - timedelta(days=1)
        # ip = bns.get_host_by_bns('traj-as-sz.MAP.gzdt')  # 试生产
        ip = bns.get_host_by_bns('group.opera-Online-NaviTrajAdvancedSearch-AdvancedSearch-000-gz.map-navi.all')  # 生产
        url = f"http://{ip}/AsService/as_grid_service"
        payload = json.dumps({
            "sort": True,
            "filter": {
                "prediction": 0.6
            },
            "opt": {
                "src": 3,
                "with_data": True,
                "time": {
                    "time_s": int(days_ago_15.timestamp()),
                    "time_e": int(days_ago_1.timestamp()),
                }
            },
            "gps_info": [
                {
                    "x": bounds[0],
                    "y": bounds[1],
                },
                {
                    "x": bounds[2],
                    "y": bounds[3],
                }
            ]
        })
        logger.info(f"轨迹请求:{payload}")
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        logger.info(f"轨迹响应")
        time.sleep(0.05)
        return response.json()

    def get_link_traj_request(self):
        """
        LINK轨迹
        Args:

        Returns:

        """
        # ip = bns.get_host_by_bns('traj-as-sz.MAP.gzdt')  # 试生产
        ip = bns.get_host_by_bns('group.opera-Online-NaviTrajAdvancedSearch-AdvancedSearch-000-gz.map-navi.all')  # 生产
        url = f"http://{ip}/AsService/as_link_service"
        # 获取当前时间
        now = datetime.now()
        # 计算15天前的时间
        days_ago_15 = now - timedelta(days=15)
        days_ago_1 = now - timedelta(days=1)
        payload = json.dumps({
            "sort": False,
            "opt": {
                "src": 3,
                "sub_src": 1,
                "with_data": True,
                "time": {
                    "time_s": int(days_ago_15.timestamp()),
                    "time_e": int(days_ago_1.timestamp()),
                }
            },
            "ui_link_req": [
                {
                    "link_id": self.dir_link_id,
                }
            ]
        })
        headers = {
            'Content-Type': 'application/json'
        }
        # logger.info(f"轨迹请求req:{payload}")
        response = requests.request("POST", url, headers=headers, data=payload)
        # logger.info(f"轨迹请求end:{url}")
        time.sleep(0.1)
        return response.text

    def parse_wkt_trace(self, origin_traj):
        """
        解析滴滴轨迹
        Args:
            trace_str:
        Returns:
        """
        if "geo_value" not in origin_traj or len(origin_traj['geo_value']) == 0:
            # print(f"没有轨迹:{self.link_id}")
            return
        for traj_info in origin_traj['geo_value']:
            if traj_info is None:
                continue
            traj_line = TrajLine()
            traj_line.long_link_id = self.long_link_id
            traj_line.short_link_id = self.short_link_id
            traj_line.traj_id = traj_info['traj_id']
            traj_line.gps_time = traj_info['gps_time']
            traj_line.prediction = traj_info['prediction']
            traj_line.cuid = traj_info['cuid']
            traj_line.confidence = traj_info['confidence']
            traj_line.encode_cuid = traj_info['encode_cuid']
            current_point_list = []
            pre_point = []
            for point_info in traj_info['traj_data']['traj_data']:
                if point_info is None:
                    continue
                current_point = Point(point_info["x"], point_info["y"])
                if len(pre_point) == 0:
                    pre_point = point_info
                    current_point_list.append(current_point)
                    continue
                # 计算两点之间距离跟时间
                pre_point_geom = Point(pre_point["x"], pre_point["y"])
                distance = pre_point_geom.distance(current_point)
                interval_time = point_info['t'] - pre_point['t']
                if distance > trace_split_distance or interval_time > trace_split_time_second:
                    # print(f"distance:{distance},interval_time:{interval_time},length:{len(current_point_list)}")
                    if len(current_point_list) > 1:
                        traj_line.geom = wkt.loads(utils.wgs_to_gcj(LineString(current_point_list).wkt))
                        self.traj_list.append(traj_line)
                    current_point_list = [current_point]
                    pre_point = point_info
                    continue
                current_point_list.append(Point(point_info["x"], point_info["y"]))
                pre_point = point_info
            if len(current_point_list) <= 1:
                continue
            traj_line.geom = wkt.loads(utils.wgs_to_gcj(LineString(current_point_list).wkt))
            self.traj_list.append(traj_line)

    def parse_link_trace(self, origin_traj):
        """
        解析滴滴轨迹
        Args:
            trace_str:
        Returns:
        """
        if "traj_info" not in origin_traj or len(origin_traj['traj_info']) == 0:
            return
        for traj_info in origin_traj['traj_info']:
            if traj_info is None:
                continue
            traj_line = TrajLine()
            traj_line.long_link_id = self.long_link_id
            traj_line.short_link_id = self.short_link_id
            traj_line.traj_id = traj_info['traj_id']
            traj_line.gps_time = traj_info['gps_time']
            traj_line.dir = traj_info['dir']
            traj_line.weight = traj_info['weight']
            traj_line.prediction = traj_info['prediction']
            traj_line.cuid = traj_info['cuid']
            traj_line.confidence = traj_info['confidence']
            traj_line.encode_cuid = traj_info['encode_cuid']
            current_point_list = []
            pre_point = []
            for point_info in traj_info['traj_data']['traj_data']:
                if point_info is None:
                    continue
                current_point = Point(point_info["x"], point_info["y"])
                if len(pre_point) == 0:
                    pre_point = point_info
                    current_point_list.append(current_point)
                    continue
                # 计算两点之间距离跟时间
                pre_point_geom = Point(pre_point["x"], pre_point["y"])
                distance = pre_point_geom.distance(current_point)
                interval_time = point_info['t'] - pre_point['t']
                if distance > trace_split_distance or interval_time > trace_split_time_second:
                    # print(f"distance:{distance},interval_time:{interval_time},length:{len(current_point_list)}")
                    if len(current_point_list) > 1:
                        traj_line.geom = wkt.loads(utils.wgs_to_gcj(LineString(current_point_list).wkt))
                        self.traj_list.append(traj_line)
                    current_point_list = [current_point]
                    pre_point = point_info
                    continue
                current_point_list.append(Point(point_info["x"], point_info["y"]))
                pre_point = point_info
            if len(current_point_list) <= 1:
                continue
            traj_line.geom = wkt.loads(utils.wgs_to_gcj(LineString(current_point_list).wkt))
            self.traj_list.append(traj_line)

    def filter_out_aoi_traj(self):
        """
        过滤出AOI的轨迹
        Args:
            ctx:
            proceed:
        """
        aoi_wkt = self.aoi_wkt
        aoi_sp = wkt.loads(aoi_wkt)
        aoi_reduce_10m_sp = aoi_sp.buffer(-10 / COORDINATE_FACTOR_NEW)
        aoi_buffer_10m_sp = aoi_sp.buffer(10 / COORDINATE_FACTOR_NEW)
        aoi_buffer_20m_sp = aoi_sp.buffer(20 / COORDINATE_FACTOR_NEW)
        aoi_buffer_50m_sp = aoi_sp.buffer(50 / COORDINATE_FACTOR_NEW)
        park_area_sp = wkt.loads(self.park_area_wkt) if self.park_area_wkt else None
        filter_traj_list = []
        for traj_info in self.traj_list:
            traj = traj_info.geom
            if not aoi_buffer_20m_sp.intersects(traj.buffer(1 / COORDINATE_FACTOR_NEW)):
                continue
            traj = aoi_buffer_50m_sp.intersection(traj)
            if traj.geom_type != 'LineString' or traj.is_empty:
                continue
            start_point = traj.coords[0]
            end_point = traj.coords[-1]
            if aoi_buffer_10m_sp.contains(Point(start_point)) and not aoi_reduce_10m_sp.contains(Point(end_point)):
                filter_traj_list.append(traj_info)
            elif park_area_sp and park_area_sp.contains(Point(start_point)) and not park_area_sp.contains(
                    Point(end_point)):
                filter_traj_list.append(traj_info)
        self.traj_list = filter_traj_list

    def filter_noise_trace_by_angle_and_intersects(self):
        """
        过滤轨迹角度异常数据
        Returns:
        """
        if len(self.traj_list) == 0:
            return
        filter_trace_list = []
        for trace_info in self.traj_list:
            coords_list = trace_info.geom.coords
            angle_abnormal_trace_list = []
            point_x_list = []
            point_y_list = []
            for key, coords in enumerate(coords_list):
                point_x_list.append(coords[0])
                point_y_list.append(coords[1])
                if key > len(coords_list) - 2 or key == 0:
                    continue
                current_coords = coords
                next_coords = coords_list[key + 1]
                pre_coords = coords_list[key - 1]
                line_a = np.array([current_coords[0] - pre_coords[0], current_coords[1] - pre_coords[1]])
                line_b = np.array([current_coords[0] - next_coords[0], current_coords[1] - next_coords[1]])
                angele = self.angle_cal(line_a, line_b)
                if angele < 40:
                    angle_abnormal_trace_list.append(coords)
                    continue
            # 计算自相交个数
            self_intersects_trace_num = self.cal_interset_num(point_x_list, point_y_list)
            # print(f"自相交次数:{self_intersects_trace_num},锐角次数:{angle_abnormal_trace_list}")
            if len(angle_abnormal_trace_list) > 10 or self_intersects_trace_num > 10:
                # print(f"轨迹异常:{trace_info.geom}")
                continue
            filter_trace_list.append(trace_info)
        self.traj_list = filter_trace_list

    def cal_interset_num(self, x, y):
        """
        计算轨迹自相交的个数
        """
        # print x
        # print y
        intersectNum = 0
        for i in range(len(x) - 2):
            res = 0
            for k in range(i + 2, len(x) - 1):
                x1 = x[i]
                y1 = y[i]
                x2 = x[i + 1]
                y2 = y[i + 1]
                x3 = x[k]
                y3 = y[k]
                x4 = x[k + 1]
                y4 = y[k + 1]
                res = self.cal_intersect(x1, y1, x2, y2, x3, y3, x4, y4)
                if res == 1:
                    intersectNum = intersectNum + 1
        return intersectNum

    def cal_intersect(self, x1, y1, x2, y2, x3, y3, x4, y4):
        """
        calculate intersect number
        :param x1:
        :param y1:
        :param x2:
        :param y2:
        :param x3:
        :param y3:
        :param x4:
        :param y4:
        :return:
        """
        res = 0
        num1 = (x4 - x1) * (y2 - y1) - (y4 - y1) * (x2 - x1)
        num2 = (x3 - x1) * (y2 - y1) - (y3 - y1) * (x2 - x1)
        num3 = (x1 - x3) * (y4 - y3) - (y1 - y3) * (x4 - x3)
        num4 = (x2 - x3) * (y4 - y3) - (y2 - y3) * (x4 - x3)
        if num1 * num2 < 0 and num3 * num4 < 0:
            res = 1
        if (x1 == x3 and y1 == y3 and x2 == x4 and y2 == y4) or (
                x1 == x4 and y1 == y4 and x2 == x3 and y2 == y3):
            res = 0
        if num1 == 0:
            if (x4 > x1 and x4 > x2) or (x4 < x2 and x4 < x1):
                res = 0
        if num2 == 0:
            if (x3 >= x1 and x3 >= x2) or (x3 <= x2 and x3 <= x1):
                res = 0
        if num3 == 0:
            if (x1 >= x3 and x1 >= x4) or (x1 <= x3 and x1 <= x4):
                res = 0
        if num4 == 0:
            if (x2 >= x3 and x2 >= x4) or (x2 <= x3 and x2 <= x4):
                res = 0
        return res

    def angle_cal(self, a, b):
        """
        计算夹角
        Args:
            a:
            b:

        Returns:

        """
        a_norm = np.sqrt(np.sum(a * a))
        b_norm = np.sqrt(np.sum(b * b))
        if a_norm == 0 or b_norm == 0:
            return 180
        cos_value = np.dot(a, b) / (a_norm * b_norm)
        arc_value = np.arccos(cos_value)
        angle_value = arc_value * 180 / np.pi
        return angle_value


def filter_out_traj_by_wkt(range_wkt, traj_list):
    """
    过滤出AOI的轨迹
    Args:
        ctx:
        proceed:
    """
    aoi_sp = wkt.loads(range_wkt)
    aoi_buffer_20m_sp = aoi_sp.buffer(20 / COORDINATE_FACTOR_NEW)
    aoi_buffer_50m_sp = aoi_sp.buffer(50 / COORDINATE_FACTOR_NEW)

    filter_traj_list = []
    for traj in traj_list:
        if not aoi_buffer_20m_sp.intersects(traj.buffer(1 / COORDINATE_FACTOR_NEW)):
            continue
        traj = aoi_buffer_50m_sp.intersection(traj)
        if traj.geom_type != 'LineString':
            continue
        start_point = traj.coords[0]
        end_point = traj.coords[-1]
        if aoi_sp.contains(Point(start_point)) and not aoi_sp.contains(Point(end_point)):
            filter_traj_list.append(traj)
    return filter_traj_list
