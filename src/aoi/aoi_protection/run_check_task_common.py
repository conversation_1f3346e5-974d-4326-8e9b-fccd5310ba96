"""
任何核实,常规
"""
import datetime
import json
import os
import sys
import tqdm
from pathlib import Path
from typing import List

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())
import dataclasses
from src.aoi.aoi_protection import cfg
from src.door_naming.DaoBeeflow import DaoBee<PERSON>

from src.aoi.aoi_protection.context import Data
from src.aoi.aoi_protection import strategy_auto
from src.door_naming.DaoAoiPoi import DaoAoiPoi

# 暂定60天
month_ago = datetime.date.today() - datetime.timedelta(days=60)


@dataclasses.dataclass
class Ctx:
    """
    context
    """
    data_list: List[Data] = dataclasses.field(default_factory=list)


def load_data(ctx: Ctx):
    """
    数据加载
    :param ctx:
    :return:
    """
    with DaoBeeflow() as dao:
        sql = 'select id, bid, src, pv, created_at, updated_at, std_tag ' \
              'from aoi_protect_information where handle_status=%s and src not in %s limit 3000'
        print(dao.cursor_beeflow.mogrify(sql, [cfg.HANDLE_STATUS_DEFAULT, cfg.HIGH_LEVEL_TAG]))
        dao.cursor_beeflow.execute(sql, [cfg.HANDLE_STATUS_DEFAULT, cfg.HIGH_LEVEL_TAG])
        ret = dao.cursor_beeflow.fetchall()
        for item in tqdm.tqdm(ret, desc='数据加载'):
            _id, bid, src, pv, created_at, updated_at, std_tag = item
            data = Data(
                id=_id,
                bid=bid,
                src=src,
                pv=pv,
                created_at=created_at,
                updated_at=updated_at,
                std_tag=std_tag,
            )
            ctx.data_list.append(data)


def check_bid_repeat(dao: DaoBeeflow, bid: str):
    """
    设置是否重复下发
    规则:
       1. 近两个月内重复出现,并且人工已经核实过,或者在核实
       2 近两天内重复出现, 并且被策略处理过或者下发人工
    :param dao:
    :param bid:
    :return:
    """
    dao.conn_beeflow.ping(reconnect=True)
    # 近两个月内有人工作业记录,无论是什么状态,都拦截
    sql = 'select id from aoi_protect_information where bid=%s and updated_at > %s  and work_type in %s' \
          ' and handle_status in %s '
    work_type = [cfg.WORK_TYPE_MANUAL]
    handle_status = [cfg.HANDLE_STATUS_WAIT_PREVIEW, cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE, cfg.HANDLE_STATUS_SUCCESS]
    dao.cursor_beeflow.execute(sql, [bid, month_ago.strftime("%Y-%m-%d"), work_type, handle_status])
    ret = dao.cursor_beeflow.fetchone()
    if ret:
        return ret[0]
    # 近两天内处理过. 无论是策略还是人工,都是重复,
    yesterday = datetime.date.today() - datetime.timedelta(days=1)
    work_type = [cfg.WORK_TYPE_MANUAL, cfg.WORK_TYPE_STRATEGY]
    handle_status = [cfg.HANDLE_STATUS_SUCCESS,
                     cfg.HANDLE_STATUS_WAIT_PREVIEW,
                     cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE,
                     cfg.HANDLE_STATUS_STRATEGY,
                     cfg.HANDLE_STATUS_FAIL
                     ]
    dao.cursor_beeflow.execute(sql, [bid, yesterday.strftime("%Y-%m-%d"), work_type, handle_status])
    ret = dao.cursor_beeflow.fetchone()
    if ret:
        return ret[0]
    return 0


def set_repeat(dao: DaoBeeflow, task_id: int, repeat_id: int):
    """
    设置重复
    :param dao:
    :param task_id:
    :param repeat_id:
    :return:
    """
    memo = {
        "msg": '数据重复',
        "data": {
            "repeat_id": repeat_id
        },
    }
    sql = 'update aoi_protect_information set work_type=%s, handle_status=%s, memo=%s where id=%s limit 1'
    dao.cursor_beeflow.execute(sql, [
        cfg.WORK_TYPE_REPEAT,
        cfg.HANDLE_STATUS_SUCCESS,
        json.dumps(memo, ensure_ascii=False), task_id])
    dao.conn_beeflow.commit()


def set_strategy(dao: DaoBeeflow, data: Data, can_offline):
    """
    设置为策略自动化处理
    :param dao:
    :param data:
    :param can_offline:
    :return:
    """
    dao.conn_beeflow.ping(reconnect=True)
    if can_offline:
        can_offline_ret = 1
    else:
        can_offline_ret = 2
    sql = 'update aoi_protect_information set work_type=%s, handle_status=%s, memo=%s, can_offline=%s where id=%s '
    dao.cursor_beeflow.execute(sql, [cfg.WORK_TYPE_STRATEGY, cfg.HANDLE_STATUS_STRATEGY,
                                     json.dumps(data.memo, ensure_ascii=False), can_offline_ret, data.id])
    dao.conn_beeflow.commit()


def set_need_manual(dao: DaoBeeflow, data: Data, batch_id):
    """
    设置人工处理
    :param dao:
    :param data:
    :param batch_id:
    :return:
    """
    dao.conn_beeflow.ping(reconnect=True)
    sql = 'update aoi_protect_information set work_type=%s, handle_status=%s , batch_id=%s, batch_type = %s' \
          ' where id=%s'
    dao.cursor_beeflow.execute(sql, [cfg.WORK_TYPE_MANUAL, cfg.HANDLE_STATUS_WAIT_PREVIEW, batch_id, cfg.MANUAL_COMMON,
                                     data.id])
    dao.conn_beeflow.commit()


def do_strategy(ctx: Ctx):
    """
    策略处理
    :param ctx:
    :return:
    """
    batch_id = "aoi_protection_" + datetime.datetime.now().strftime("%Y%m%d%H")
    with DaoBeeflow(rw=True) as dao_bee_flow:
        for item in tqdm.tqdm(ctx.data_list, desc='数据处理'):
            item: Data
            bid = item.bid
            # 重复
            repeat_id = check_bid_repeat(dao_bee_flow, bid)
            if repeat_id:
                print(f"存在重复: {item.id} - {item.bid} - 重复项:{repeat_id}")
                set_repeat(dao_bee_flow, item.id, repeat_id)
                continue
            # 策略判断是否可以处理
            with DaoAoiPoi(poi_cfg_name=DaoAoiPoi.POI_CFG_SLAVE) as dao:
                # 是否策略可处理
                is_auto, can_offline = strategy_auto.run(dao, item)
                if is_auto:
                    print(f"可策略解: {item.id} - {item.bid} - {item.memo}")
                    set_strategy(dao_bee_flow, item, can_offline)
                    continue
                # 策略自动处理
                set_need_manual(dao_bee_flow, item, batch_id)
                print(f"不可策略解:{item.id} - {item.bid} - 等待创建人工:{batch_id}")


if __name__ == '__main__':
    _ctx = Ctx()
    load_data(_ctx)
    do_strategy(_ctx)
