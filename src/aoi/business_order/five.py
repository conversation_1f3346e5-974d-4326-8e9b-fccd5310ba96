import psycopg2
import csv

# business_order
buConf = {"db": "business_order", "user": "business_order_se_rw", "pwd": "kpigzear",
          "host": "**************",
          "port": 7432}

# 连接business_order
buDbConn = psycopg2.connect(database=buConf["db"],
                            user=buConf["user"],
                            password=buConf["pwd"],
                            host=buConf["host"],
                            port=buConf["port"])
bus_cursor = buDbConn.cursor()

def get_fourth_face_data():
    sql = "SELECT ST_AsText(ST_Union(geom)) FROM bad_admin_vil_gcj WHERE town_name = '粤海街道';"
    bus_cursor.execute(sql)
    results = bus_cursor.fetchall()

    # 写入 CSV 文件，使用 utf-8 编码
    with open('fourth_face_data.csv', mode='w', encoding='utf-8', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['union_geom'])  # 写入表头
        for row in results:
            writer.writerow(row)
    if bus_cursor:
        bus_cursor.close()
    if buDbConn:
        buDbConn.close()

print("开始导出数据：四级区划粤海街道的坐标信息")
get_fourth_face_data()
print("导出完毕")