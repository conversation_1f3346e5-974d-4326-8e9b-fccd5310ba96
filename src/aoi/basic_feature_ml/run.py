# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿地自动化
"""

from pathlib import Path

import sys
import os

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")
from aoi.basic_feature_ml import context
from aoi.basic_feature_ml import green_land
from aoi import BFQuery
from common import common_tool


# 绿地基础目录
GREEN_BASI_DIR = Path("/home/<USER>/aoi-strategy/data/green")

# 预览计划处理状态
PRE_PLAN_STATUS_IDENTIFIED = "IDENTIFIED"
PRE_PLAN_STATUS_AFTER_HANDING = "AFTER_HANDING"
PRE_PLAN_STATUS_AFTER_HANDLED = "AFTER_HANDLED"

AFS_SHELL_CMD = "/home/<USER>/afs-api/bin/afsshell --username=map_data_aoi --password=DKGjrtAiXi2r"
DISCERN_RESULT_AFS_DIR = "afs://fenghuang.afs:9902/user/map_data_aoi/green/discern_result"

PIPE_LIST = green_land.pipes()
BF_QUERY = BFQuery.BFQuery()

# 背景计划类型
PLAN_TYPE_GREEN = 45


def run(ctx: context.Context):
    """
    绿地自动化策略
    :param ctx:
    :return:
    """
    PIPE_LIST(ctx)
    print(f"{ctx.batch}自动化完成")


if __name__ == '__main__':
    # 获取待处理的后处理任务
    pre_plan = BF_QUERY.get_basic_pre_plan_by_status(PRE_PLAN_STATUS_IDENTIFIED, PLAN_TYPE_GREEN)
    if pre_plan is None or len(pre_plan) == 0:
        sys.exit(0)

    BF_QUERY.update_basic_pre_plan_status_by_id(pre_plan[0], PRE_PLAN_STATUS_AFTER_HANDING)
    batch = pre_plan[0]
    path_info = context.PathInfo(base_dir=GREEN_BASI_DIR, batch=str(pre_plan[0]))
    path_info.ensure_dirs()
    ctx = context.Context()
    ctx.batch = str(batch)
    ctx.path_info = path_info
    ctx.afs_shell_cmd = AFS_SHELL_CMD
    ctx.afs_discern_result_filepath = f"{DISCERN_RESULT_AFS_DIR}/{batch}_output.zip"
    ctx.local_discern_result_filepath = f"{ctx.path_info.headmap_dir}/{batch}_output.zip"
    ctx.filter_min_area = 0.0000000050  # 识别面积过滤
    ctx.simplify_distance = 0.00003  # 抽稀距离
    ctx.merge_distance = 0.00002  # 绿地面合并距离
    try:
        run(ctx)
        BF_QUERY.update_basic_pre_plan_status_by_id(pre_plan[0], PRE_PLAN_STATUS_AFTER_HANDLED)
    except Exception as e:
        BF_QUERY.update_basic_pre_plan_status_by_id(batch, PRE_PLAN_STATUS_IDENTIFIED)
        common_tool.send_hi(f"绿地后处理失败：{batch}", ['gengyong_cd'])
        raise e
