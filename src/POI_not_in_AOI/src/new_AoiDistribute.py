# bin/usr/python
# encoding=utf8
"""
 author <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com on 20171220
 input: bid
 output: post_result_flag + bid + description
 post_result_flag: 0 for success, 1 for bid info failure, 2 for post process failure, 3 for other failure
"""

import sys
import traceback
import httplib
import json
import hashlib

# gzhxy-mapse-sug-bs104.gzhxy.baidu.com:8969
# fty.poi.baidu.com:80
# http://cp01-map-poidata-plat-wangchao39.epc.baidu.com:8080@陈通
GlobalParameter = {"server_name": "bee.baidu.com",
                   "server_api": "/aoi/process/api/apientry?project_id={}&bid={}&input_id={}&problem_priority={}"
                                 "&problem_type={}&sign={}",
                   "port": "80", "token": "a6baaf3d7efe8041b33fc859e5085034", "project_id": "36", "input_id": "test",
                   "problem_priority": "8", "problem_type": "1"}


def GetSign(RequestArr):
    """
    :param RequestArr:
    :return:
    """
    KeyArr = []
    ResultString = ""
    for key in RequestArr.keys():
        KeyArr.append(key)
    KeyArr.sort()
    for i in KeyArr:
        ResultString += (i + "=" + RequestArr[i])
    md5 = hashlib.md5()
    md5.update(ResultString + GlobalParameter["token"])
    return md5.hexdigest()


def PostRequest(bid):
    """
    :param bid:
    :return:
    """
    server_name = GlobalParameter["server_name"]
    RequestArr = {}
    port = GlobalParameter["port"]
    RequestArr["project_id"] = GlobalParameter["project_id"]
    RequestArr["bid"] = bid
    RequestArr["input_id"] = GlobalParameter["input_id"]
    RequestArr["problem_priority"] = GlobalParameter["problem_priority"]
    RequestArr['problem_type'] = GlobalParameter["problem_type"]
    params = ''
    headers = {}
    timeout = 30
    server_api = GlobalParameter["server_api"].format(GlobalParameter["project_id"], bid, GlobalParameter["input_id"], \
                                                      GlobalParameter["problem_priority"],
                                                      GlobalParameter["problem_type"], GetSign(RequestArr))

    http_client = httplib.HTTPConnection(server_name, port, timeout)
    http_client.request('POST', server_api, params, headers)
    response = http_client.getresponse().read()
    http_client.close()
    json_resp = json.loads(response)
    info = bid

    if 'data' in json_resp and 'status' in json_resp and 'errorMsg' in json_resp:
        if json_resp['status'] == 0:
            print '0' + '\t' + info
        else:
            print '1' + '\t' + info + '\t' + json_resp['errorMsg'].encode('gbk')
    else:
        print '2' + '\t' + info + '\t' + 'Post Request Failed!'


if __name__ == '__main__':
    print "input: bid"
    print "output: post_result_flag + bid + description"
    print "post_result_flag: 0 for success, 1 for bid info failure, 2 for post process failure, 3 for other failure"

    infile = open(sys.argv[1])
    batch = "POI NOT IN AOI " + str(sys.argv[2])
    GlobalParameter["input_id"] = batch
    while True:
        line = infile.readline()
        if line == "" or line is None:
            break
        items = line.strip('\n\r').split('\t')
        bid = str(items[0])
        PostRequest(bid)
        """
        try:
            PostRequest(bid)
        except Exception as e:
            print '3' + '\t' + bid + '\t' + str(e)
        """
