#!/bin/bash

CUR_DIR=$(readlink -f `dirname $BASH_SOURCE[0]`)
source $CUR_DIR"/conf.sh"

function PreProcess(){
	if [ ! -d ${RawDataPath} ]; then
		mkdir -p ${RawDataPath};
	fi

	if [ ! -d ${OnlinePath} ]; then
		mkdir -p ${OnlinePath};
	fi

	if [ ! -d ${SplitPath} ]; then
		mkdir -p ${SplitPath};
	fi
}

function GenMid(){
	local INFILE=$1;
	while read line; do
	    guid=`uuidgen -t`
	    mid="A"`echo $guid | ${BinPath}/guid2uid`
	    echo -e "${line}\t${mid}" >> ${INFILE}.out
	done<${INFILE}	
}

function GetNewData(){
####获取所有的aoi形状数据
	cat $DATA_PATH/jichudiwu/data_from_image/out/*/*/blu_face.csv | awk -F "\t" '{
		faceid = $1;
		shape = $2;
		name = $7;
		if(faceid == "face_id")
			next;
		print faceid "\t" shape "\t" name;
	}' > ${RawDataPath}/aoi.shape.raw;

####获取所有的aoi与bid映射关系数据
	cat $DATA_PATH/jichudiwu/data_from_image/out/*/*/blu_face_poi.csv | awk -F "\t" '
	ARGIND==1{
		not_release_bid = $1;
		map[not_release_bid];
	}
	ARGIND==2{
		faceid = $2;
		bid = $5;

		if(bid in map)
			next

		if(faceid == "face_id")
			next;

		print faceid "\t" bid;		
	}' "$DATA_PATH/bid2releasebid" - > ${RawDataPath}/aoi2bid.raw;

####整理数据，构建faceid name shape bid address city mid
	awk -F "\t" '
	ARGIND==1{
		faceid = $1;
		bid = $2;
		map[faceid] = bid;
		info[bid];
	}
	ARGIND==2{
		bid = $1;
		address = $4;
		city = $19;
		if(bid in info)
			result[bid] = address "\t" city;
	}
	ARGIND==3{
		faceid = $1;

		if(faceid in map){
			bid = map[faceid];
			if(bid in result)
				print $0 "\t" bid "\t" result[bid];
		}
	}' ${RawDataPath}/aoi2bid.raw "$DATA_PATH/poi/poi_res.utf8" ${RawDataPath}/aoi.shape.raw > ${RawDataPath}/aoi.std.raw;

	${PYTHONBIN} ${BinPath}/Gcj022Bd09mkt.py ${RawDataPath}/aoi.std.raw ${RawDataPath}/aoi.std.new.tmp;

####生成新的mid
	rm ${SplitPath}/*;

  split_line_cnt=` wc -l < ${RawDataPath}/aoi.std.new.tmp | awk '{print sprintf("%.0f", ($1 / 20))}' `
  split -l ${split_line_cnt} ${RawDataPath}/aoi.std.new.tmp ${SplitPath}/part.input.

  for ONE_PART in `ls ${SplitPath}/part.input.a?`; do
          GenMid ${ONE_PART} &
  done
  wait

  cat ${SplitPath}/*out > ${RawDataPath}/aoi.std.new;
}

function DiffData(){
####产出AOI需要上线的数据
##faceid继承
##楼块bid去重
	local time_stamp=`date +"%s"`;
	awk -F "\t" -v time=${time_stamp} '
	ARGIND==1{
		faceid = $5;
        bid = $2;
		mid = $1;
		create_time = $8;
		map[faceid] = mid "\t" create_time;

        if(bid > 1)
            bid_map[bid] = mid "\t" create_time;
	}
	ARGIND==2{
		faceid = $1;
		bid = $4;

		name = $3;
		address = $5;
		faceid = $1;
		shape = $2;
		type = 1;
		update_time = time;
		city = $6;

		if(faceid in map){
			split(map[faceid], list, "\t");
			mid = list[1];
			create_time = list[2];
		}

        else if(bid in bid_map){
            split(bid_map[bid], list, "\t");
            mid = list[1];
            create_time = list[2];
        }

		else{
			mid = $7;
			create_time = time;
		}

		print mid "\t" bid "\t" name "\t" address "\t" faceid "\t" shape "\t" type "\t" create_time "\t" update_time "\t" city;
	}' $DATA_PATH"/jichudiwu/data.aoi" ${RawDataPath}/aoi.std.new > ${OnlinePath}/mid.aoi.online;
	
####产出AOI需要下线的数据
	awk -F "\t" '
	ARGIND==1{
		mid = $1;
		map[mid];
	}
	ARGIND==2{
		mid = $1;
		if(mid in map)
			next;

		mid = $1;
		bid = $2;

		print mid "\t" bid;
	}' ${OnlinePath}/mid.aoi.online $DATA_PATH"/jichudiwu/data.aoi" > ${OnlinePath}/mid.aoi.offline;
}

function Main(){
	PreProcess;
	GetNewData;
	DiffData;
}

Main;
