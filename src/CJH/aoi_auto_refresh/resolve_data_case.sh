#!/usr/bin/env bash
DATE=`date +%Y%m%d`
END_NAME=`date +%m%d`
#shell执行路径
WORKPATH=`pwd`

#--------------------正式环境
#SCRIPT_PATH="~/odp_fty/app/process/script/aoi-bid2releasebid"
SCRIPT_PATH="/home/<USER>/liming06/ronghe/CJH/aoi_auto_refresh/relyscript"
PSQL="psql -h ************  -p5432 -Umap -d postgres "
PYTHON="/home/<USER>/liming06/aoi-pds-online/python27/bin/python"
PHP="/home/<USER>/odp_fty/php/bin/php"
POI_DATAPATH="/home/<USER>/liming06/ronghe/CJH/aoi_auto_refresh"
#--------------------测试环境

#testing  是否具备执行条件    基础环境检查
if [ -d "${SCRIPT_PATH}" ]; then
    echo "[ok] SCRIPT_PATH 存在"
else
    echo "[WARNING] SCRIPT_PATH 不存在"
    exit 1
fi
which ${PHP}
if [ $? -ne 0 ]; then
    echo "[WARNING] PHP 不存在"
    exit 1
else
    echo "[ok] PHP 存在"
fi
which ${PYTHON}
if [ $? -ne 0 ]; then
    echo "[WARNING] PYTHON 不存在"
    exit 1
else
    echo "[ok] PYTHON 存在"
fi

#清理进程方法
function killprocess(){
    echo "[INFO] 开始清理${1}进程"
    PROCESS=`ps -ef|grep $1|grep -v grep|grep -v PPID|awk '{print $2}'`
    for i in $PROCESS
    do
        echo "[INFO] 清理${1}进程 [${i}]"
        kill -9 $i
    done
    echo "[INFO] 清理${1}进程结束"
}

#测压盖生成比率，并输出aoi_rate.txt
function aoirate(){
    #先备份
    if [ -f "${WORKPATH}/${DATE}/aoi_rate.txt" ]; then
        mv "${WORKPATH}/${DATE}/aoi_rate.txt" "${WORKPATH}/${DATE}/aoi_rate_${END_NAME}.txt"
    fi
    if [ -d "${WORKPATH}/data/" ]; then
        echo "[INFO] 数据缓存文件夹存在"
        rm ${WORKPATH}/data/*
        echo "[INFO] 数据缓存清理完成"
    else
        mkdir ${WORKPATH}/data/
    fi
    cd ${WORKPATH}/data/
    #分解
    split -l 100 ${WORKPATH}/${DATE}/aoi_db.txt -d -a 5 split_
    echo "[INFO] 分割文件完成"
    #kill已存在check_aoi.sh进程
    killprocess check_aoi.sh
    #执行测压盖生成aoi_rate.txt
    echo "[INFO] 生成压盖文件开始 ${WORKPATH}/${DATE}/aoi_rate.txt"
    for file in `ls ${WORKPATH}/data/split_*`
    do
        newfile=${file##*/}
        nohup sh ${WORKPATH}/check_aoi.sh ${WORKPATH}/data/$newfile $WORKPATH $PHP ${WORKPATH}/${DATE}/aoi_rate.txt &
    done
    wait
    echo "[INFO] 生成压盖文件完成"
    mv ${WORKPATH}/data/nohup.out ${WORKPATH}/${DATE}/
    if [ -f "${WORKPATH}/${DATE}/aoi_rate.txt" ]; then
        echo "[INFO] 生成压盖文件：${WORKPATH}/${DATE}/aoi_rate.txt"
    else
        echo "[INFO] 不存在压盖情况"
        touch ${WORKPATH}/${DATE}/aoi_rate.txt
    fi 
    echo "[INFO] 生成aoi_rate完成"
    cd ${WORKPATH}/${DATE}/
    #判断压盖逻辑数据没有错误
    if [ -s "${WORKPATH}/data/aoi_rate_error.info" ]; then
        echo "[error] 测试压盖比例数据错误 查看：${WORKPATH}/data/aoi_rate_error.info"
        exit 1
    fi
}

#跑压盖逻辑
function updatecover(){
    #生成offline
    awk -F "\t" 'NR==FNR{a[$2]}NR!=FNR{if($3 in a)print $0}' ${WORKPATH}/${DATE}/aoi_rate.txt ${WORKPATH}/${DATE}/aoi.csv > ${WORKPATH}/${DATE}/offline 
    #更新AOI库中名称
    while read LINE
    do
        bid=`echo -e "$LINE"|awk -F"\t" '{print $2}'`
        update_sql="update blu_face_from_se set note='CJH overlap',status='0' where bid ='${bid}';"
        ${PSQL} -c "${update_sql}"
        sleep 0.05
    done < "${WORKPATH}/${DATE}/aoi_rate.txt"
    wait
    echo "[INFO] 压盖结束"
}

#数据库准备操作
function prepare(){ 
    ${PSQL} -c "delete from aoi_test;"
    wait
    if [ $? -ne 0 ]; then
        echo "[error] 清空aoi_test数据失败"
        exit 1
    else
        echo "[INFO] 清空aoi_test数据完成"
    fi
    #将aoi_city导入aoi_test
    ${PSQL} -c "\copy aoi_test from '${WORKPATH}/${DATE}/aoi_city'"
    wait
    if [ $? -ne 0 ]; then
        echo "[error] copy aoi_test命令执行失败"
        exit 1
    else
        echo "[INFO] copy命令执行成功"
    fi
    #去掉bid为空和bid重复的数据
    ${PSQL} -c "delete from aoi_test where bid='\"\"';"
    if [ $? -ne 0 ]; then
        echo "[error] 删除aoi_test为空数据失败"
        exit 1
    else
        echo "[INFO] 删除aoi_test为空数据成功"
    fi
    #消除bid重复数据成功
    ${PSQL} -c "\copy (select bid from aoi_test where bid<>'' group by bid having count(*)>1) to '${WORKPATH}/${DATE}/repeat_bid'"
    if [ $? -ne 0 ]; then
        echo "[error] 获取bid重复数据失败"
        exit 1
    fi
    while read LINE
    do
        delete_sql="delete from aoi_test where bid='${LINE}';"
        ${PSQL} -c "${delete_sql}"
        if [ $? -ne 0 ]; then
            echo "[error] 删除bid重复数据失败bid:${LINE}"
            exit 1
        fi
    done < "${WORKPATH}/${DATE}/repeat_bid"
    echo "[INFO] aoi_test数据准备完成"
}

#全量场景化源替换数据
function changeresource(){
    echo "[INFO] 场景化开始"

    #备份bid重复数据
    ${PSQL} -c "\copy (select * from blu_face_from_se where bid in (select bid from aoi_test)) to '${WORKPATH}/${DATE}/db_notcjh'"
    wait
    if [ $? -ne 0 ]; then
        echo "[WARNING] 非场景化数据备份操作错误，请手动回滚"
        exit 1
    fi
    #非场bid重复数据
    ${PSQL} -c "delete from blu_face_from_se where bid in (select bid from aoi_test);"
    wait
    if [ $? -ne 0 ]; then
        echo "[WARNING] 非场景化删除错误，程序中断"
        exit 1
    fi
    #aoi_test导出到aoi_db.txt
    ${PSQL} -c "\copy aoi_test to '${WORKPATH}/${DATE}/aoi_db.txt';"
    wait
    if [ $? -ne 0 ]; then
        echo "[WARNING] 场景化数据库aoi_db导出操作错误，请手动回滚"
        exit 1
    fi
    echo "[INFO] 场景化结束"
}

#生成csv文件
function makecsv(){
    echo "[INFO] 开始生成aoi.csv"
    ${PSQL} -c "\copy (select face_id,geom,bid,name_ch,kind,area,scene_id,display_flag,proj_flag,mesh_id,city_name,cp,update_date,memo from blu_face_from_se where poi_status in ('1') and bid !~'[^0-9]' and name_ch<>'' and ST_IsValid(geom)='t') to '${WORKPATH}/${DATE}/aoi.csv'"
    wait
    if [ $? -ne 0 ]; then
        echo "[WARNING] 生成csv文件错误，请手动回滚"
        exit 1
    fi
    echo "[INFO] 生成aoi.csv结束"
}

################################################################################################################################
#                                           main function                                                                      #
################################################################################################################################



if [ -d "${WORKPATH}/${DATE}/" ]; then
    echo "[INFO] 日期文件夹已经存在，当日文件可能已经备份过"
else
    # 生成目录结构
    mkdir ${WORKPATH}/${DATE}
    mkdir ${WORKPATH}/${DATE}/log
    touch ${WORKPATH}/${DATE}/log/aoi-to-pds.log
    if [ -d "${WORKPATH}/${DATE}/" ]; then
        echo "[INFO] 创建目录：${WORKPATH}/${DATE}/ 成功。"
    else
        echo "[error] 创建目录：${WORKPATH}/${DATE}/ 失败"
        exit 1
    fi
    cd ${WORKPATH}/${DATE}/
    
    #做全局备份
    ${PSQL} -c "\copy (select * from blu_face_from_se) to '${WORKPATH}/${DATE}/blu_face'"
    if [ $? -ne 0 ]; then
        echo "[WARNING] 备份blu_face_from_se  =>  blu_face失败，请手动回滚"
        exit 1
    fi
    echo "[INFO] 备份blu_face_from_se成功:${WORKPATH}/${DATE}/blu_face"
    
    #输出远程目录内容到index.html
    wget --ftp-user=map --ftp-password=bkGgNd48VaXaiAiU4ZoQ "ftp://gzns-map-poi-dcheck02.gzns.baidu.com/home/<USER>/map_data_release/Back_admin/" -O "./index.html"
    wait
    DIR_NAME=`sed -n '/.*\/<\/a>/p' index.html|awk -F"\">" '{print $2}'|awk -F"/" '{print $1}'|sort -r|head -1`
    echo "[INFO] 最新版本号为${DIR_NAME}"

    #下载文件到本地
#    wget -c -q --ftp-user=map --ftp-password=bkGgNd48VaXaiAiU4ZoQ "ftp://gzns-map-poi-dcheck02.gzns.baidu.com/home/<USER>/map_data_release/Back_admin/${DIR_NAME}/county/aoi_poi_center.csv" -O "./aoi_poi_center.csv"
    cp ../k1 ./aoi_poi_center.csv
    wait
    if [ -f "${WORKPATH}/${DATE}/aoi_poi_center.csv" ]; then
        # 显示第一行
        echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++     表头     ++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
        sed -n '1p' ${WORKPATH}/${DATE}/aoi_poi_center.csv
        echo "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
        #去掉表头
        sed '1d' ${WORKPATH}/${DATE}/aoi_poi_center.csv > ${WORKPATH}/${DATE}/aoi.txt

        echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   去重    +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
        # 去重，只加入增量
        # step1 查bid2releasebid 表
        # step2 去重 和现在数据库中的数据对比，只留下bid不在里面的数据
        rm ${POI_DATAPATH}/bid2releasebid
        wget ftp://gzns-map-poi-export.gzns/home/<USER>/poi-data-export/data_inner/bid2releasebid -O ${POI_DATAPATH}/bid2releasebid

        awk -F "\t" 'ARGIND==1{bid=$3; map[bid] } 
                     ARGIND==2{bid=$1; release_bid = $2; if(bid in map){map[bid]=release_bid} } 
                     ARGIND==3{bid=$3; 
                               if(map[bid] != ""){
                                                  temp=$1 "\t" $2 "\t" map[bid]; 
                                                  for(i=4;i<=NF;i++){
                                                                     temp = temp "\t" $i
                                                                    } 
                                                  print temp
                                                }    
                                else{  print $0   }
                             } ' ${WORKPATH}/${DATE}/aoi.txt ${POI_DATAPATH}/bid2releasebid ${WORKPATH}/${DATE}/aoi.txt > ${WORKPATH}/${DATE}/aoi.txt.release_bid


        awk -F "\t" 'ARGIND==1{bid=$3; shape=$2; map[bid]=shape} 
                     ARGIND==2{bid=$3; shape=$2; 
                               if(bid ~"\""){
                                   next
                               }

                               if(bid in map){
                                    print $0
                                } else {
                                    print $0
                                }

                        }' ${WORKPATH}/${DATE}/blu_face ${WORKPATH}/${DATE}/aoi.txt.release_bid > ${WORKPATH}/${DATE}/aoi.txt.to_replace

        rm ${WORKPATH}/${DATE}/aoi.txt;
        mv ${WORKPATH}/${DATE}/aoi.txt.to_replace ${WORKPATH}/${DATE}/aoi.txt;
        echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   去重完成   +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"

        #TODO  test 保留10行  测试用
        #sed -i '100,$d' aoi.txt
        #wait
        #echo "[INFO] 去表头成功"
        #rm ${WORKPATH}/${DATE}/aoi_poi_center.csv
        #给aoi.txt添加城市 并进行数据库备份及相应删除
        ${PHP} ${WORKPATH}/getcity.php ${WORKPATH}/${DATE}/aoi.txt ${WORKPATH}/${DATE}/aoi_city ${WORKPATH}/${DATE}/aoi_city_error
        #判断是否有错误数据没有获取到城市信息
        if [ -s "${WORKPATH}/${DATE}/aoi_city_error" ]; then
            echo "[error] 获取aoi城市信息失败 查看：${WORKPATH}/${DATE}/aoi_city_error"
            exit 1
        fi
        
        wait
        #数据库准备 去空去重 生成 aoi_test表
        echo "[INFO] 数据准备工作开始。。"
        prepare
        echo "[INFO] 数据准备工作结束。。"
        #跑场景化sql备份数据 删除 blu_face_from_se表 一些数据 生成 aoi_db.txt
        echo "[INFO] 跑场景化数据备份及删除。。"
        changeresource
        #测压盖生成aoi_rate.txt
        echo "[INFO] 跑场压盖。。"
        aoirate
        #生成aoi.csv
        echo "[INFO] 生成下线数据文件。。"
        makecsv
        #更新压盖
        updatecover
        echo "[INFO] 开始写入库blu_face_from_se"
        #入库
        ${PSQL} -c "insert into blu_face_from_se select * from aoi_test;"
        wait
        echo "[INFO] 准备上线"
        #转换数据格式
        
        ${PYTHON} ${SCRIPT_PATH}/script/geo_clockwise.py ${WORKPATH}/${DATE}/aoi_db.txt ${WORKPATH}/${DATE}/online
        wait
        ${PYTHON} ${SCRIPT_PATH}/script/geo_clockwise.py ${WORKPATH}/${DATE}/offline ${WORKPATH}/${DATE}/downline
        wait
        #测试。 先执行到这里
        #exit 1
        #备份上线生成的日志
        if [ -f "${SCRIPT_PATH}/nohup.out" ]; then
            mv ${SCRIPT_PATH}/nohup.out ${SCRIPT_PATH}/CJH_${END_NAME}
        fi
        killprocess aoi_online.sh
        echo "[INFO] 上线数据：${WORKPATH}/${DATE}/online  行数："
        wc -l ${WORKPATH}/${DATE}/online
        echo "[INFO] 下线数据：${WORKPATH}/${DATE}/downline  行数："
        wc -l ${WORKPATH}/${DATE}/downline
#        sh ${SCRIPT_PATH}/aoi_online.sh ${WORKPATH}/${DATE}/online > ${WORKPATH}/${DATE}/online.log 
        wait
        echo "[INFO] 上线完成"
#        sh ${SCRIPT_PATH}/aoi_downline.sh ${WORKPATH}/${DATE}/downline > ${WORKPATH}/${DATE}/downline.log 
        wait
        echo "[INFO] 下线完成"
    else
        echo "[WARNING] 下载文件失败,不能操作数据库"
    fi
fi
