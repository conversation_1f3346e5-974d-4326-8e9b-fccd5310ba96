"""
整合需要创建的任务
"""
import dataclasses
import datetime
import json
import os
import sys
import time
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[3]
print('当前根目录 ==== ', root_path)
sys.path.insert(0, root_path.as_posix())

from src.door_naming.DaoBeeflow import DaoBeeflow
from src.aoi.aoi_protection import cfg, batch_create_preview
from typing import List


@dataclasses.dataclass
class Data:
    """
    data
    """
    id: int
    bid: int
    batch_id: int
    batch_type: int
    preview_task_id: int = 0


@dataclasses.dataclass
class SameBatchData:
    """
    同一批次任务处理
    """
    data_list: List[Data] = dataclasses.field(default_factory=list)


def sync_batch_data(dao_beeflow: DaoBeeflow, batch_data: SameBatchData):
    """
    查询是否生效
    :param dao_beeflow:
    :param batch_data:
    :return:
    """
    dao_beeflow.conn_beeflow.ping(reconnect=True)
    for data in batch_data.data_list:
        if data.preview_task_id > 0:
            continue
        sql = 'select id from strategy_feature_list where batch_id=%s and bid=%s'
        dao_beeflow.cursor_beeflow.execute(sql, [data.batch_id, data.bid])
        ret = dao_beeflow.cursor_beeflow.fetchone()
        if ret:
            data.preview_task_id = ret[0]
            print(f"{data.batch_id}={data.bid} 已经下发 {data.preview_task_id}")
            # 修改状态
            sql = 'update aoi_protect_information set handle_status= %s, qb_id=%s  where id=%s limit 1'
            dao_beeflow.cursor_beeflow.execute(sql,
                                               [cfg.HANDLE_STATUS_WAIT_HUMAN_HANDLE, data.preview_task_id, data.id])
            dao_beeflow.conn_beeflow.commit()
        else:
            print(f"{data.batch_id}={data.bid} 未检测到下发记录 {data.preview_task_id}")


def create_by_batch_id(batch_id, batch_type):
    """
    按照批次进行下发, 这个脚本不能并行处理,否则容易出现重复下发
    :param batch_id:
    :param batch_type:
    :return:
    """
    same_batch_data = SameBatchData()
    with DaoBeeflow(rw=True) as dao_beeflow:
        sql = 'select id, bid, batch_id, qb_id ' \
              'from aoi_protect_information ' \
              'where work_type=%s and handle_status=%s and batch_id=%s and batch_type=%s '
        dao_beeflow.cursor_beeflow.execute(sql, [
            cfg.WORK_TYPE_MANUAL, cfg.HANDLE_STATUS_WAIT_PREVIEW, batch_id, batch_type])
        ret = dao_beeflow.cursor_beeflow.fetchall()
        for item in ret:
            _id, bid, batch_id, qb_id = item
            if qb_id == '':
                qb_id = 0
            else:
                qb_id = int(qb_id)
            same_batch_data.data_list.append(
                Data(id=_id, bid=bid, batch_id=batch_id, batch_type=batch_type, preview_task_id=qb_id)
            )
        # 检查是否任务已经下发
        sync_batch_data(dao_beeflow, same_batch_data)
        # 未下发的下发任务
        wait_create_bid = [x.bid for x in same_batch_data.data_list if x.preview_task_id < 1]
        if len(wait_create_bid) > 0:
            batch_create_preview.create_preview(
                batch_create_preview.cfg.BASE_API_PATH,
                wait_create_bid, batch_id,
                batch_type)
        # time.sleep(3)
        # sync_batch_data(dao_beeflow, same_batch_data)


def run(batch_type=cfg.MANUAL_COMMON):
    """
    找出需要下发的任务批次
    :param batch_type:
    :return:
    """
    with DaoBeeflow(rw=True) as dao_beeflow:
        sql = 'select distinct(batch_id) from aoi_protect_information where work_type = %s and ' \
              ' handle_status = %s and batch_type=%s limit 30 '
        dao_beeflow.cursor_beeflow.execute(sql, [cfg.WORK_TYPE_MANUAL, cfg.HANDLE_STATUS_WAIT_PREVIEW, batch_type])
        ret = dao_beeflow.cursor_beeflow.fetchall()
        print(f"{batch_type} 需要处理任务数量: {len(ret)}")
        for batch_item in ret:
            batch_id = batch_item[0]
            create_by_batch_id(batch_id, batch_type)


if __name__ == '__main__':
    if len(sys.argv) >= 2:
        run_type = sys.argv[1]
        if run_type in ['common', 'high']:
            run_type_int = cfg.MANUAL_HIGH if run_type == 'high' else cfg.MANUAL_COMMON
            run(batch_type=run_type_int)
        else:
            raise Exception('无效类型')
    else:
        print("\n用法: py39 run_batch_prepre_preview.py <type> \n"
              "     <type>  common = 常规任务 high = 高优任务 ")
