# -*- coding: UTF-8 -*-
################################################################################
"""
取消输出到导航的文件，多进程运行
输出文件以创建内业计划，并分析过滤的场景
Authors: <AUTHORS>
Date:    2023/01/19 09:42:42
"""

import psycopg2
import time
import ConfigParser
import os
import urllib2
import json
import sys
import os
import redis
#import shapely.wkt
#import shapely.geometry
import uuid
from multiprocessing import Process
#from shapely.geometry import Polygon, LineString
#from shapely.ops import linemerge, unary_union, polygonize


def get_db_info(pathname, requrl):
    """根据传入的pathname获取数据库的连接信息
    Args:
        pathname: 数据库的节点信息
    Return:
        res: list类型，数据库的连接信息
    """
    content = {"pathname": pathname, "action":"query", "type":"dbinfo"}
    send_data = json.dumps(content)
    headers = {'Content-Type': 'application/json'}
    request = urllib2.Request(url = requrl, headers = headers, data = send_data)
    response = urllib2.urlopen(request)
    res = json.loads(response.read())
    if res['status'] != 0:
        return
    return res['data']

cf = ConfigParser.ConfigParser()
cf.read('../../../conf/config.ini')

#获取天级更新的道路库连接信息
pathname = cf.get('regular', 'road_pathname')
dbinfo_url = cf.get('regular', 'pg_naming_url')
db_info = get_db_info(pathname, dbinfo_url)
if db_info is None:
    print("get db_info failed")
    os._exit(1)

conn_road = psycopg2.connect(database = db_info['db'], user = db_info['user'], password = db_info['passwd'], \
            host = db_info['host'], port = db_info['port'])
cursor_road = conn_road.cursor()

dbname = "master_back"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_aoi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_aoi = conn_aoi.cursor()

dbname = "poi_online"
_host = cf.get(dbname, 'host')
_port = cf.get(dbname, 'port')
_user = cf.get(dbname, 'user')
_database = cf.get(dbname, 'database')
_password = cf.get(dbname, 'password')
conn_poi = psycopg2.connect(database = _database, user = _user, password = _password, host = _host, port = _port)
cursor_poi = conn_poi.cursor()

conn_trans = psycopg2.connect(database = "trans_id", user = "trans_id_se_rw", password = "cpmkukky", \
            host = "*************", port = 5432)
cursor_trans = conn_trans.cursor()

def convert_short_id(link_id):
    """linkid转短id
    Args:
        link_id: 道路的linkid
    Return:
        res: 道路的短id
    """
    sql = "select tid from image_r where sid = %s"
    cursor_trans.execute(sql, [link_id])
    res = cursor_trans.fetchone()
    if res is None:
        return ""
    return res[0]


def main():
    """
    轨迹数据上传至数据库，方便后续查询
    """
    node_bid = dict()
    with open("bid_node.txt", "r") as f:
        lines = f.readlines()
        for line in lines:
            node_id = line.strip().split('\t')[1]
            bid = line.strip().split('\t')[0]
            node_bid[node_id] = bid
    f = os.popen("ls ../beijing_20230204.txt").read()
    fw = open("gate_bid_tra_match.txt", "w")
    fw_tra = open("gate_bid_tra_match_with_tra.txt", "w")
    bid = ""
    file_name_list = f.strip().split('\n')
    node_tra_info = dict()
    for file_name in file_name_list:
        with open(file_name, "r") as f:
            lines = f.readlines()
            for line in lines:
                if line[0] != "{":
                    continue
                node_tra_info_tmp = json.loads(line.strip())
                if node_tra_info_tmp["node_id"] in node_tra_info:
                    node_tra_info[node_tra_info_tmp["node_id"]] = (node_tra_info[node_tra_info_tmp["node_id"]] 
                                                                   + node_tra_info_tmp["traj_ids"])
                else:
                    node_tra_info[node_tra_info_tmp["node_id"]] = node_tra_info_tmp["traj_ids"]
    
    r = redis.Redis(host='*************', port=2003, db=11)
    for s_node_id, traj_id_list in node_tra_info.items():
        sql = "select sid from image_n where tid = '%s'" % (s_node_id)
        cursor_trans.execute(sql)
        node_id = cursor_trans.fetchone()[0]
        sql = "select in_linkid, out_linkid from nav_gate where node_id = %s limit 1"
        cursor_road.execute(sql, [node_id])
        res_road = cursor_road.fetchone()
        if res_road is None:
            continue
        match_res = "fail"
        in_linkid = convert_short_id(res_road[0])
        out_linkid = convert_short_id(res_road[1])

        for traj_id in traj_id_list:
            tra_info_str = r.get(traj_id)
            tra_info = json.loads(tra_info_str)
            point_list = tra_info["points"]
            link_list = []
            index_list = []
            pos_info = ""
            for index, point_info in enumerate(point_list):
                point_info_dict = point_info
                link_tmp = point_info_dict["LinkSeq"]
                pos_info = pos_info + point_info_dict["Lng"] + "," + point_info_dict["Lat"] + ";"
                link_list = link_list + link_tmp
                for i in range(len(link_tmp)):
                    index_list.append(index)
            fw_tra.write("%s\t%s\t%s\n" % (node_bid[node_id], node_id, pos_info.strip(';')))
            for i in range(len(link_list) - 1, 1, -1):
                
                if (str(link_list[i])[:-1] == out_linkid and str(link_list[i - 1])[:-1] == in_linkid) or \
                    (str(link_list[i])[:-1] == in_linkid and str(link_list[i - 1])[:-1] == out_linkid):
                    
                    # fw_tra_all.write("%s\t%s\t%s\n" % (bid, node_id, tra_data))
                    
                    #判断是不是在AOI内部
                    
                    
                    for j in range(index_list[i], min(len(point_list), index_list[i] + 10)):
                        tmp_point = point_list[index_list[j]]
                        geom_point = "point(%s %s)" % (tmp_point["Lng"], tmp_point["Lat"])
                        
                        sql = "select poi_bid, a.face_id from blu_Face a inner join blu_Face_poi b \
                                on a.face_id = b.face_id where st_contains(geom, wgs2gcj(\
                                st_geomfromtext(%s, 4326))) and kind != '52' and aoi_level = 2"
                        cursor_aoi.execute(sql, [geom_point])
                        res_aoi = cursor_aoi.fetchone()
                        if res_aoi is None:
                            continue
                        bid = res_aoi[0]
                        if bid == node_bid[node_id]:
                            match_res = "success"
                            fw_tra.write("%s\t%s\t%s\n" % (bid, node_id, pos_info.strip(';')))
                            break

        fw.write("%s\t%s\t%s\n" % (bid, node_id, match_res))
        

if __name__ == "__main__":
    main()