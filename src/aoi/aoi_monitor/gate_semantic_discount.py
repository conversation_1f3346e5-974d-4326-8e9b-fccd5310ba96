# -*- coding: utf-8 -*-
# !/usr/bin/env python3

"""
大门语义化折损统计
"""
from datetime import datetime
import sys
import os
import hashlib

import tqdm

sys.path.insert(0, os.path.abspath(os.path.dirname(os.getcwd())))
sys.path.append("../..")
sys.path.append("..")

from src.aoi.aoi_complete.AoiCompleteContext import AoiCompleteContext

# 精准AOI1.0
ACCURATE_AOI_COMPLETE_V1 = 3

# 大门语义化
PLAN_TYPE_GATE_SEMANTIC = 63

# 作业状态
WORK_SATTUS_COMPLETED = 3


def run(ctx: AoiCompleteContext):
    """
    大门语义化折损统计与大门语义化工作量统计
    :return:
    """
    semantic_work_progress_statistics(ctx)
    semantic_discount_statistics(ctx)


def semantic_work_progress_statistics(ctx: AoiCompleteContext):
    """
    大门语义化作业进度统计
    :return:
    """
    ctx.reconnect()
    id = 0
    limit_number = 1000
    # 大门语义化作业分类 {src, category, work_total, work_complete_total}
    gate_semantic_result = {}
    total = 0
    while True:
        total += limit_number
        # 预览计划情报查询
        feature_list = ctx.mysql_bf.get_feature_list_by_strategy_type_and_id(PLAN_TYPE_GATE_SEMANTIC, id, limit_number)
        if feature_list is None or len(feature_list) == 0:
            break
        id = feature_list[len(feature_list) - 1][2]
        bid_to_src_map = {feature[0]: feature[1] for feature in feature_list}
        bid_list = ctx.pg_query_dao.get_poi_list_by_bid_list(bid_to_src_map.keys())
        if bid_list is None or len(bid_list) == 0:
            continue
        bid_to_tag_map = {bid_info[0]: bid_info[1] for bid_info in bid_list}
        for feature in feature_list:
            bid = feature[0]
            src = feature[1]
            work_status = feature[3]
            src_type = get_type_by_strategy_src(src)
            if bid not in bid_to_tag_map:
                continue
            category = get_category_by_tag(bid_to_tag_map[bid])
            gate_semantic_key = src_type + category
            if gate_semantic_key in gate_semantic_result:
                gate_semantic_result[gate_semantic_key]['work_total'] += 1
            else:
                gate_semantic_result[gate_semantic_key] = {
                    "src": src_type,
                    "category": category,
                    "work_total": 1,
                    "work_complete_total": 0
                }
            if work_status == WORK_SATTUS_COMPLETED:
                gate_semantic_result[gate_semantic_key]['work_complete_total'] += 1
    print(f"折损统计完成, 成果量：{len(gate_semantic_result)}")
    update_gate_semantic_work_statistics(ctx, gate_semantic_result)


def semantic_discount_statistics(ctx: AoiCompleteContext):
    """
    大门折损统计
    :return:
    """
    limit_number = 10000
    bid = ""
    number = 0
    # 大门语义化成果{key=>{tag,city,city_name,complete,number}}
    gate_semantic_result = {}
    ctx.reconnect()
    while True:
        accurate_aoi_list = ctx.pg_query_dao.get_aoi_by_complete_and_face_id(ACCURATE_AOI_COMPLETE_V1, bid,
                                                                             limit_number)
        if accurate_aoi_list is None or len(accurate_aoi_list) == 0:
            break
        for accurate_aoi in tqdm.tqdm(accurate_aoi_list, desc="大门语义化折损统计", leave=False):
            number += 1
            bid = accurate_aoi[0]
            # if number % 10000 == 0:
            #     print(f"当前进度:{number}, bid:{bid}, 成果量：{len(gate_semantic_result)}")
            # print(f"当前进度:{number}, bid:{bid}, 成果量：{len(gate_semantic_result)}")
            gate_semantic_key = str(accurate_aoi[1]) + accurate_aoi[2]
            # 查询主点信息
            poi_info = ctx.pg_query_dao.get_poi_by_bid(bid)
            category = "其它"
            if poi_info is not None and len(poi_info) > 0:
                category = get_category_by_tag(poi_info[0])
            gate_semantic_key = gate_semantic_key + category

            gate_semantic_key_md5 = encrypt_md5(gate_semantic_key)
            if gate_semantic_key_md5 in gate_semantic_result:
                gate_semantic_result[gate_semantic_key_md5]["number"] += 1
            else:
                gate_semantic_result[gate_semantic_key_md5] = {
                    "category": category,
                    "city": accurate_aoi[2],
                    "city_group": get_city_group_by_city(accurate_aoi[2]),
                    "complete": accurate_aoi[1],
                    "number": 1
                }
    update_gate_semantic(ctx, gate_semantic_result)
    ctx.pg_rw_dao.delete_semantic_discount_data()
    print(f"大门折损更新完成")


def update_gate_semantic(ctx: AoiCompleteContext, gate_semantic_map):
    """
    更新大门语义化成果统计
    :param gate_semantic_map:{key=>{category, city, city_group, complete, number}}
    :return:
    """
    for gate_semantic_key, gate_semantic_result in gate_semantic_map.items():
        ctx.pg_rw_dao.update_gate_semantic_discount(gate_semantic_result, datetime.now())
    return


def update_gate_semantic_work_statistics(ctx: AoiCompleteContext, gate_semantic_map):
    """
    更新大门语义化作业量统计
    :param gate_semantic_map:{key=>{src, category, work_total, work_complete_total}}
    :return:
    """
    for gate_semantic_key, gate_semantic_result in gate_semantic_map.items():
        ctx.pg_rw_dao.update_gate_semantic_work_statistics(gate_semantic_result, datetime.now())
    return


def encrypt_md5(str):
    """
    加密md5
    :param str:
    :return:
    """
    # 创建md5对象
    new_md5 = hashlib.md5()
    new_md5.update(str.encode(encoding='utf-8'))
    return new_md5.hexdigest()


def get_category_by_tag(tag):
    """
    根据tag获取对应的分类
    :param tag:
    :return:
    """
    # tag全等
    category_to_tag = {
        "高校": ["教育培训;高等院校"],
        "医院": ["医疗;综合医院", "医疗;专科医院"],
        "购物中心": ["购物;购物中心"],
        "展览馆&剧院": ["文化传媒;展览", "休闲娱乐;剧院"],
        "体育场馆": ["运动健身;体育场馆"],
        "住宅区": ["房地产;住宅区"],
        "公司": ["公司企业", "公司企业;厂矿", "公司企业;公司", "公司企业;园区"],
        "政府": ["政府机构", "政府机构;各级政府", "政府机构;公检法机构", "政府机构;其他"],
        "机场火车站": ["交通设施;飞机场", "交通设施;火车站"],
        "长途汽车站": ["交通设施;长途汽车站"]
    }
    # tag包含
    category_to_contains_tag = {
        "景区": ["旅游景点"],
    }
    for category, tag_list in category_to_tag.items():
        if tag in tag_list:
            return category
    for category, tag_list in category_to_contains_tag.items():
        for val in tag_list:
            if val in tag:
                return category
    return "其它"


def get_city_group_by_city(city_name):
    """
    根据城市名获取城市群
    :param city_name:
    :return: 城市群
    """
    city_group_map = {
        "长三角": [
            "上海市""," "杭州市""," "宁波市""," "温州市""," "苏州市""," "嘉兴市""," "绍兴市""," "湖州市""," "金华市""," "台州市"],
        "京津冀": ["北京市", "天津市", "廊坊市", "保定市", "唐山市", "张家口市", "沧州市"],
        "珠三角": ["广州市", "深圳市", "东莞市", "佛山市", "惠州市", "清远市", "中山市"],
        "成渝": ["重庆市", "成都市", "德阳市", "眉山市", "广安市", "南充市"],
        "山东半岛": ["青岛市", "济南市", "烟台市", "潍坊市", "德州市", "泰安市", "临沂市", "淄博市"],
        "合南": ["南京市", "徐州市", "合肥市", "六安市", "滁州市", "宿州市"],
        "海峡西岸": ["福州市", "泉州市", "厦门市", "漳州市"],
        "武汉周边": ["武汉市", "黄冈市", "孝感市", "鄂州市"],
        "关中": ["西安市", "咸阳市", "渭南市"],
        "中原": ["郑州市", "开封市", "新乡市", "周口市", "许昌市", "洛阳市"]
    }
    for city_group, city_list in city_group_map.items():
        if city_name in city_list:
            return city_group
    return "其它"


def get_type_by_strategy_src(strategy_src):
    """
    根据情报来源获取大门语义化作业类型
    :param strategy_src:
    """
    strategy_src_to_type_map = {
        "语义化_方位错误": "方位核实",
        "语义化_冗余合并": "关联合并",
        "语义化_出入口附近存在实体门": "实地名称核实",
        "语义化_无通行属性数据": "通车属性核实",
    }
    for part_strategy_src, type in strategy_src_to_type_map.items():
        if part_strategy_src in strategy_src:
            return type
    return "其它"


if __name__ == '__main__':
    with AoiCompleteContext() as _ctx:
        run(_ctx)
