# -*- coding: utf-8 -*-
"""
停车场关联高通量Link 点不在 Link 上
"""
import datetime
import multiprocessing
import sys
import os
import tqdm
import requests
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.aoi.dao.trans import Trans
from src.aoi.dao.road import Road
from src.aoi import utils

poi_online_query = PoiOnlineQuery()
trans_dao = Trans()
road_dao = Road()

log_path = "./log/point_not_on_link.log"
logger.add(log_path, rotation="10 MB")

AFS_SHELL = "/home/<USER>/afs-api/bin/afsshell --username=poi-engine --password=poi-engine-online"
AFS_PARH = "afs://kunpeng.afs.baidu.com:9902/user/poi-engine/offline_quality_control/result/"
CURRENT_DATE = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
HIGH_FILE_NAME = "/home/<USER>/aoi-strategy/2024-04-22/src/aoi/park_access/qc_repair/parking_part_result_relatedness.txt"
HIGH_LINK_AFS_PATH = f"{CURRENT_DATE}/parking_statistic/parking_part_result_relatedness.txt"


def main(bids: list):
    """
    停车场关联高通量Link 点不在 Link 上
    Args:
        task_ids:

    Returns:

    """
    # 获取停车场信息
    park_list = poi_online_query.get_parking_list_by_kv(bids=bids.tolist())
    for park in tqdm.tqdm(park_list):
        try:
            logger.info(f"\n{park['bid']} 开始处理")
            road_relation_json = park['road_relation']
            if not road_relation_json or road_relation_json['link_info'][0]['type'] != 3:
                continue
            short_link_id = road_relation_json['link_info'][0]['link_id']
            point_mc_xy = road_relation_json['link_info'][0]['point'].split(",")
            long_link_list = trans_dao.get_long_link_by_short_link_ids([short_link_id])
            if not long_link_list:
                logger.error(f"{park['bid']} 没有对应的长Link")
                continue
            long_link_ids = [item[0] for item in long_link_list]
            link_info_list = road_dao.get_nav_link_by_link_ids(long_link_ids)
            if not link_info_list:
                logger.error(f"{park['bid']} LinkID无效:{long_link_ids}")
                continue
            link_sp = wkt.loads(link_info_list[0][0])
            point_gcj_xy = utils.mc_to_gcj(f"point({point_mc_xy[0]} {point_mc_xy[1]})")
            point_sp = wkt.loads(point_gcj_xy)
            # 计算点到直线距离,超过0.01m则重新计算
            if point_sp.distance(link_sp) < 0.0000001:
                logger.info(f"点距离Link较近, 不处理:{point_sp.distance(link_sp) * 100000}")
                continue
            # 计算垂足
            foot_point_point = utils.get_foot_point(point_sp, link_sp)
            # 垂足计算失败, 直接取Link上最近的一个点
            if not foot_point_point:
                logger.error(f"{park['bid']} 计算垂足失败:{point_sp}, {link_sp}")
                node_ids = [link_info_list[0][1], link_info_list[0][2]]
                node_list = road_dao.get_node_geom(node_ids)
                if not node_list:
                    logger.error(f"{park['bid']} 没有对应的节点:{node_list}")
                    continue
                foot_point_point = min([wkt.loads(item[0]) for item in node_list], key=lambda x: x.distance(point_sp))

            logger.info(f"{park['bid']} 计算垂足成功:origin:{point_sp}, link:{link_sp}, foot:{foot_point_point}")
            # 推送上线
            foot_point_point_mc_sp = wkt.loads(utils.gcj_to_mc(foot_point_point.wkt))
            road_relation_json['link_info'][0][
                'point'] = f"{str(foot_point_point_mc_sp.x)},{str(foot_point_point_mc_sp.y)}"
            release_road_relation(park["bid"], road_relation_json)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"{park['bid']} 异常:{e}")


def release_road_relation(bid, road_relation):
    """
    更新road_relation
    Args:
        bid:
        road_relation:

    Returns:

    """
    # 写入成果数据
    with open("./point_not_on_link.txt", "a+", encoding='utf-8') as f:
        f.write(f"{bid}\t{road_relation}\n")
        f.flush()
    # return

    logger.info(f"{bid} 开始推送:{road_relation}")
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    params = {
        "source": "STRATEGY_HIGH_POINT_NOT_IN_LINK_REPAIR",
        "source_id": f"STRATEGY_HIGH_POINT_NOT_IN_LINK_REPAIR_{bid}",
        "bid": bid,
        "batch_id": "STRATEGY_HIGH_POINT_NOT_IN_LINK_REPAIR_1214",
        "road_relation": road_relation
    }
    logger.info(f"road_relation修复推送:{params}")
    res = requests.post(url, json=params, headers={"Content-Type": "application/json"})
    logger.info(f"{bid} 推送结果:{res.text}")


def get_need_deal_bids_by_afs():
    """
    通过AFS获取需要处理的BID
    Returns:

    """
    bids = []
    cmd = f"{AFS_SHELL} get {AFS_PARH}{HIGH_LINK_AFS_PATH} {HIGH_FILE_NAME}"
    logger.info(f"开始拉取文件:{cmd}")
    ret = os.system(cmd)
    if ret != 0:
        logger.error(f"{cmd} 执行失败")
        return bids
    with open(HIGH_FILE_NAME, "r") as f:
        for line in f:
            data_list = line.strip().split("\t")
            if data_list[5] != '【R6008】关联高通量link 的停车的point不在link上':
                continue
            bids.append(data_list[2])
    return bids


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    batch = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    if len(bid_list) == 0:
        bid_list = get_need_deal_bids_by_afs()
    if len(bid_list) == 0:
        exit(f"没有可执行BID")
    bid_lists = np.array_split(np.array(bid_list), progress)
    with multiprocessing.Pool(progress) as pool:
        res = pool.map(main, bid_lists)
