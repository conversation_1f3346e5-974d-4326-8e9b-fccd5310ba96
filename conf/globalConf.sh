#!/bin/bash
ROOT=$(readlink -f "`dirname $BASH_SOURCE[0]`/../")

NUTS_TOOLS=${NUTS_TOOLS:=/home/<USER>/tools}
export PATH=${NUTS_TOOLS}/bin:$PATH
export LD_LIBRARY_PATH=${NUTS_TOOLS}/lib:${NUTS_TOOLS}/lib/postgresql:$LD_LIBRARY_PATH
# 统一使用utf8编码
export LANG='en_US.utf-8'

PYTHONBIN="/home/<USER>/tools/bin/python"
PYTHON3="/home/<USER>/.jumbo/bin/python3"
PHPBIN="/home/<USER>/odp/php/bin/php"
AS_HASH="/home/<USER>/map/mappoi/address/tools/As_Hash"
HADOOP="/home/<USER>/hadoop-client/hadoop/bin/hadoop"
FTP_PATH="/home/<USER>/ftppath"
AFS_SHELL="/home/<USER>/afs-api/bin/afsshell"
AFS_CD_APOI_COMMAND="$AFS_SHELL --username=map_data_cd_apoi --password=Ksn7tvt5V6f7"
#POI大数据中心自己维护的建筑物形状表路径
BUILDING_PATH=" --ftp-user=map --ftp-password=AC6Eb8jUM1mCF1HlcDjm ftp://gzns-map-poi-indoor0.gzns.baidu.com/home/<USER>/diwu-service/script/dump_data/today/mpoi.all"
DIWU_POI_ALL_PATH="--ftp-user=map --ftp-password=AC6Eb8jUM1mCF1HlcDjm ftp://gzns-map-poi-indoor0.gzns.baidu.com/home/<USER>/diwu-service/script/dump_data/today/poi.all"
INFOFLOW_ROBOT_TOKEN=""
LIB_PATH=$ROOT"/lib"
SRC_PATH=$ROOT"/src"
# 定义数据路径
DATA_PATH=$(readlink -f "$ROOT/../data")
TMP_PATH=$DATA_PATH"/tmp"
NFS_PATH=$ROOT"/NFS_data/map_data_image/"
# 定义日志路径
LOG_PATH=$(readlink -f "$ROOT/../log")
mkdir -p "$TMP_PATH" "$LOG_PATH" "$NFS_PATH"



PG_HOST=************
PG_PORT=5432
PG_USER=map
# psql -h************ -p5432 -Umap -d postgres
PG_CONN="psql -h${PG_HOST} -p${PG_PORT} -U${PG_USER}"

PG_MASTER_BACK_HOST=**************
PG_MATER_BACK_PORT=5432
PG_MATER_BACK_USER=master_back_se_rw
PG_MATER_BACK_PASSWD=cwduumoj
PG_MASTER_BACK_CONN="psql -h${PG_MASTER_BACK_HOST} -p${PG_MATER_BACK_PORT} -U${PG_MATER_BACK_USER} -W${PG_MATER_BACK_PASSWD}"


MAIL_RECEIVE="<EMAIL>"

#邮件通知
function MailWarning(){
    content=$1;
    title=$2;
    echo "$content" | mail -s "$title" "$MAIL_RECEIVE";
}
