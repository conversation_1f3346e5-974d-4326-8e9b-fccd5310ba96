# !/usr/bin/env python3
"""
客户端数据库模型
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, create_engine, func, SmallInteger, BigInteger, Float
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session

Base = declarative_base()


class Work(Base):
    """
    客户端work表
    """

    __tablename__ = 'work'
    work_id = Column(String(64), nullable=False, primary_key=True)
    name = Column(String(254), nullable=False, default="")
    work_resource = Column(Text, nullable=False, default="")
    work_result = Column(Text, nullable=False, default="")
    work_memo = Column(Text, nullable=False, default="")

    def __str__(self):
        return self.work_id


class PoiClient(Base):
    """
    客户端POI表
    """

    __tablename__ = 'poi'

    bid = Column(String(64), nullable=False)
    mid = Column(String(64), nullable=False, default="", primary_key=True)
    mesh_id = Column(String(6), nullable=False, default="")
    name = Column(String(254), nullable=False, default="")
    eng_name = Column(String(254), nullable=False, default="")
    alias = Column(Text, nullable=False, default="")
    address = Column(String(1024), nullable=False, default="")
    district = Column(String(10), nullable=False, default="")
    telephone = Column(String(200), nullable=False, default="")
    kind = Column(String(64), nullable=False, default="")
    tag = Column(String(254), nullable=False, default="")
    std_tag = Column(String(254), nullable=False, default="")
    index_tag = Column(String(254), nullable=False, default="")
    show_tag = Column(String(254), nullable=False, default="")
    brand_code = Column(String(12), nullable=False, default="")
    food_type = Column(String(128), nullable=False, default="")
    status = Column(Integer, default=1)
    relation = Column(String(64), nullable=False, default="")
    relation_bid = Column(String(64), nullable=False, default="")
    relation_type = Column(Integer, default=-1)
    admin_code = Column(String(64), nullable=False, default="")
    display_x = Column(Float, nullable=False, default=0)
    display_y = Column(Float, nullable=False, default=0)
    arrive_x = Column(Float, nullable=False, default=0)
    arrive_y = Column(Float, nullable=False, default=0)
    link_id = Column(String(60), nullable=False, default="")
    side = Column(String(2), nullable=False, default="")
    mark = Column(String(4), nullable=False, default="")
    memo = Column(String(254), nullable=False, default="")
    city = Column(String(254), nullable=False, default="")
    importance = Column(Integer, default=6)
    secret = Column(String(1), nullable=False, default="0")
    geom = Column(String)

    def __str__(self):
        return self.mid


class PoiNavi(Base):
    __tablename__ = 'poi_navi'

    uid = Column(String(64), primary_key=True)
    mid = Column(String(64), nullable=False, default="")
    bid = Column(String(64), nullable=False, default="")
    navi_x = Column(Float, nullable=False, default=0)
    navi_y = Column(Float, nullable=False, default=0)
    arrive_obj = Column(String(128), nullable=False, default="")
    link_id = Column(String(64), nullable=False, default="")
    src = Column(String(64), nullable=False, default="")

    def __str__(self):
        return self.uid
