# -*- coding: utf-8 -*-
"""
地下停车场情报挖掘
"""
import multiprocessing
import sys
import os
import time
import uuid
import tqdm
import requests
import json
import numpy as np
import traceback
from shapely import wkt
from loguru import logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))
from src.aoi.dao.poi_online_query import PoiOnlineQuery
from src.common import pipeline
from src.aoi.park_access.mistake import context
from src.aoi import utils
from src.aoi.park_access.mistake import park_access_qc as qc
from src.aoi.park_access.mistake import traj_strategy

COORDINATE_FACTOR = 110000

# logger.remove()
log_path = "./log/underground_intelligence.log"
logger.add(log_path, rotation="10 MB")

IS_TEST = True


def create_intelligence(ctx: context.Context):
    """
    门前出入口挖掘
    Args:
        ctx:

    Returns:

    """
    pipe_list = pipeline.Pipeline(
        get_park_access_list,
        # check_by_distance,
        # check_relation_link_exist_close_gate,
        # is_manual_work,
        check_by_traj,
        park_access_filter,
        save_park_to_db,
    )
    pipe_list(ctx)


def get_park_access_list(ctx: context.Context, proceed):
    """
    获取停车场出入口
    Returns:

    """
    sub_pois = ctx.dao.poi_online_query.get_sub_pois(ctx.parking_info.bid, open_limit_new_not='1')
    if not sub_pois:
        logger.error(f"{ctx.parking_info.bid}没有子poi")
        return
    for sub_poi in sub_pois:
        if not sub_poi[3] or sub_poi[4] != 1:
            continue
        park_sub_poi = context.ParkingSubPOI()
        park_sub_poi.bid = sub_poi[0]
        park_sub_poi.point_gcj = sub_poi[2]
        road_relation = sub_poi[3]
        park_sub_poi.road_relation = road_relation
        if road_relation is not None and "link_info" in road_relation:
            # 关联node
            link_info_list = road_relation["link_info"]
            if len(link_info_list) == 0:
                continue
            link_info = link_info_list[0]
            if 'type' in link_info and link_info["type"] == 1:
                short_node_ids = link_info['node_id']
                long_node_list = ctx.dao.trans_dao.get_long_node_by_short_node_ids([short_node_ids])
                long_node_ids = [item[0] for item in long_node_list if long_node_list]
                if len(long_node_ids) == 0:
                    continue
                park_sub_poi.relation_node_id = long_node_ids[0]
                park_sub_poi.type = 'NODE'
                node_list = ctx.dao.road_dao.get_node_geom([long_node_ids[0]])
                if node_list:
                    park_sub_poi.node_geom = node_list[0][0]

            # 关联link
            if 'type' in link_info and link_info["type"] == 2:
                short_link_ids = link_info['link_id']
                long_link_list = ctx.dao.trans_dao.get_long_link_by_short_link_ids([short_link_ids])
                long_link_ids = [item[0] for item in long_link_list if long_link_list]
                if len(long_link_ids) == 0:
                    continue
                park_sub_poi.relation_link_id = long_link_ids[0]
                park_sub_poi.type = 'LINK'
                link_list = ctx.dao.road_dao.get_nav_link_by_link_ids([long_link_ids[0]])
                if link_list:
                    park_sub_poi.link_geom = link_list[0][0]
            ctx.sub_pois.append(park_sub_poi)
    if len(ctx.sub_pois) == 0:
        logger.error(f"{ctx.parking_info.bid}没有有效子poi")
        return
    return proceed()


def check_by_distance(ctx: context.Context, proceed):
    """
    根据距离判断出入口准确性, 距离停车场超出150认为异常
    Args:
        ctx:
        proceed:

    Returns:

    """
    park_area_sp = wkt.loads(ctx.parking_info.park_area_gcj_wkt) \
        if ctx.parking_info.park_area_gcj_wkt else wkt.loads(ctx.parking_info.park_wkt)
    for sub_park_access in ctx.sub_pois:
        sub_park_access_sp = wkt.loads(sub_park_access.point_gcj)
        # 出入口距离停车场面距离
        park_access_distance_park = sub_park_access_sp.distance(park_area_sp) * COORDINATE_FACTOR
        if park_access_distance_park < 150:
            continue
        park_access_distance_aoi = 0
        if ctx.aoi_info and ctx.aoi_info.bid \
                and ('旅游景点' not in ctx.aoi_info.std_tag or '地下停车场' in ctx.parking_info.show_tag):
            aoi_sp = wkt.loads(ctx.aoi_info.wkt)
            park_access_distance_aoi = sub_park_access_sp.distance(aoi_sp) * COORDINATE_FACTOR
            if sub_park_access_sp.distance(aoi_sp) < 50 / COORDINATE_FACTOR:
                continue
        elif ctx.aoi_info.bid == '' and park_access_distance_park < 500:
            continue
        logger.info(f"{sub_park_access.bid}距离停车场过远:{park_access_distance_park}")
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.node_id = sub_park_access.relation_node_id
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_DISTANCE
        park_access.strategy_value = f"park_access_distance_park:{park_access_distance_park}," \
                                     f"park_access_distance_aoi:{park_access_distance_aoi}"
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_relation_link_exist_close_gate(ctx: context.Context, proceed):
    """
    判断关联link 且存在封闭大门
    Args:
        ctx:
        proceed:

    Returns:

    """
    for sub_park_access in ctx.sub_pois:
        if sub_park_access.type != 'LINK':
            continue
        link_id = sub_park_access.relation_link_id
        link_list = ctx.dao.road_dao.get_nav_link_by_link_ids([link_id])
        if not link_list:
            logger.error(f"{link_id}\t信息不存在")
            continue
        link_sp = wkt.loads(link_list[0][0])
        nav_gate_list = ctx.dao.road_dao.get_nav_gate_by_geom(link_sp.buffer(3 / COORDINATE_FACTOR).wkt)
        if not nav_gate_list:
            logger.error(f"{link_id}\t附近没有关联大门")
            continue
        node_ids = [item[0] for item in nav_gate_list]
        close_gate_list = [node_id for node_id in node_ids if utils.is_close_gate(node_id)]
        if len(close_gate_list) == 0:
            continue
        park_access = context.ParkingAccess()
        park_access.bid = sub_park_access.bid
        park_access.node_geom = sub_park_access.point_gcj
        park_access.link_id = sub_park_access.relation_link_id
        park_access.type = sub_park_access.type
        park_access.strategy = context.PARK_ACCESS_MISTAKE_BY_LINK_NEAR_CLOSE_GATE
        park_access.strategy_value = close_gate_list[0]
        ctx.parking_access_list.append(park_access)
    return proceed()


def check_by_traj(ctx: context.Context, proceed):
    """
    通过轨迹挖掘错误出入口
    Args:
        ctx:
        proceed:

    Returns:

    """
    ctx.init_traj()
    traj_strategy.traj_strategy(ctx)
    # traj_strategy.by_yaw_exp_traj(ctx)
    return proceed()


def is_manual_work(ctx: context.Context, proceed):
    """
    判断是否人工干预
    Args:
        ctx:
        proceed:

    Returns:

    """
    for park_access in ctx.parking_access_list:
        # 判断出入口是否人工干预
        manual_work_id = ctx.dao.beeflow_dao.is_manual_work(park_access.bid)
        if not manual_work_id:
            continue
        park_access.is_valid = False
        park_access.filter_reason = f"人工干预:{manual_work_id}"
    return proceed()


def park_access_filter(ctx: context.Context, proceed):
    """
    出入口情报合法性过滤
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin park_access_filter")
    qc.park_access_qc(ctx)
    return proceed()


def save_park_to_db(ctx: context.Context, proceed):
    """
    保存停车场情报
    Args:
        ctx:
        proceed:

    Returns:

    """
    logger.info(f"begin save_park_to_db:{len(ctx.parking_access_list)}")
    if IS_TEST == '0':
        for item in ctx.parking_access_list:
            logger.info(f"TEST关联类型:{item.type}, park_access_bid\t{item.bid}, node_id位置\t{item.node_id}, "
                        f"策略:{item.strategy}, link_id\t{item.link_id}, 是否有效:{item.is_valid},"
                        f"过滤原因:{item.filter_reason}, strategy_value:\t{item.strategy_value},remark:\t{item.remark}")
        return proceed()
    for park_access in ctx.parking_access_list:
        if not park_access.is_valid:
            logger.info(f"关联类型:{park_access.type}, node_id位置\t{park_access.node_id}, 策略:{park_access.strategy}, "
                        f"link_id\t{park_access.link_id}, 是否有效:{park_access.is_valid},过滤原因:{park_access.filter_reason}")
            continue
        jdq_batch = 'JQDCWP2024101810'
        resp = ''
        data = {
            'bid': ctx.parking_info.bid,
            'parent_bid': ctx.parking_info.parent_id,
            'strategy': park_access.strategy,
            'strategy_value': park_access.strategy_value,
            'type': 'park_access_mistake',
            'batch_number': f"park_access_mistake_intelligence_{time.strftime('%Y%m%d', time.localtime())}",
            'geom': park_access.node_geom,
            'guid': str(uuid.uuid4()).replace("-", ""),
            'source_id': '',
            'show_tag': ctx.parking_info.show_tag,
            'outside_id': '',
            'status': 'INIT',
            'resp': resp,
            'node_id': park_access.node_id,
            'link_id': park_access.link_id,
            'jdq_batch': jdq_batch,
            'road_relation': json.dumps(
                {'node_id': park_access.node_id, 'link_id': park_access.link_id, 'type': park_access.type}),
            'park_access_bid': park_access.bid,
            'confidence': park_access.confidence,
            'remark': park_access.remark,
        }
        ctx.dao.poi_online_rw.insert_mistake_intelligence(data)
    proceed()


def push(point_access_geom, source_id, ref_qb_batch_id, park_bid, park_access_id):
    """
    一体化下发
    Args:
        point_xy:
        source_id:

    Returns:

    """
    data = {
        "src": 10,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": source_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ref_qb_batch_id,  # 必填，批次号
        "main_poi_bid": park_bid,  # 停车场bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "JQD",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "核实灌入出入口是否是工单停车场的出入口，是则不处理，不是则删除",
                "gates": [{'wkt': '', 'desc': '', 'gate_bid': park_access_id}]
            }
        }
    }
    logger.info(f"推送数据:{data}")
    req = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    logger.info(f"推送结果:{req.text}")
    return req.text


def main(bid_list: list):
    """
    门前停车场情报产出
    Args:
        bid_list:

    Returns:

    """
    # 获取停车场信息
    poi_online_query = PoiOnlineQuery()
    parking_list = poi_online_query.get_parking_list(bids=bid_list.tolist(), show_tags=['地上停车场', '地下停车场', '立体停车场'])
    if not parking_list:
        logger.debug("No parking found")
        return
    park_bids = [item[7] for item in parking_list]
    park_pv_list = poi_online_query.get_poi_by_bids(park_bids)
    park_pv_dict = {item['bid']: item['click_pv'] for item in park_pv_list}
    logger.info(f"开始挖掘关联错误出入口，共{len(parking_list)}")
    for parking in tqdm.tqdm(parking_list):
        try:
            if park_pv_dict[parking[7]] <= 8:
                logger.info(f"{parking[7]}PV较低，跳过:{park_pv_dict[parking[7]]}")
                continue
            ctx = context.Context(parking)
            ctx.debug = True if IS_TEST == '0' else False
            create_intelligence(ctx)
        except Exception as e:
            print(f"情报挖掘失败:{e}{parking}\n{traceback.format_exc()}")


if __name__ == '__main__':
    bid_file = sys.argv[1]
    bids = sys.argv[2]
    achievement_file = sys.argv[3]
    progress = int(sys.argv[4])
    IS_TEST = sys.argv[5]
    bid_list = []
    if bid_file != "0":
        with open(bid_file, "r") as f:
            bid_list = f.read().replace("\n", ",").strip(",").split(",")
    if bids != "0":
        bid_list = bids.split(",")
    # 取四大垂类停车场BID
    if bid_file == '0' and bids == '0':
        poi_online_query = PoiOnlineQuery()
        park_categories_list = poi_online_query.get_4categories_park_bids()
        bid_list = list(set([item['bid'] for item in park_categories_list]))
    bid_lists = np.array_split(np.array(bid_list), progress)
    main(np.array(bid_list))
    # with multiprocessing.Pool(progress) as pool:
        # res = pool.map(main, bid_lists)