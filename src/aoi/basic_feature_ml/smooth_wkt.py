# !/usr/bin/env python3
"""
TODO: 接入 AOI 边框修形后处理
平滑坐标串上的角。
"""
import math

import numpy as np
from osgeo import ogr

METER = 1e-5
# 圆滑半径：表示从角的顶点起，被圆弧替换的（两侧）线段的最大长度。若角所在的边小于该长度，则不会被圆弧化。
SMOOTH_MAX_RADIUS = 5 * METER
# 弧片段长度：将圆弧表示为坐标串时，需要用等长的短线段代替曲线，该长度便是短线段的长度。
SMOOTH_ARC_SEGMENT_LENGTH = 0.5 * METER
# 平滑角度限制：为最大可圆滑角度的余弦值。接近 pi 的角不必圆滑，否则圆滑后的点可能过多，设为 -1 相当于禁用此规则。
SMOOTH_ANGLE_LIMIT = math.cos(0.95 * math.pi)


def smooth_line_string(line_string: str) -> str:
    geom = ogr.CreateGeometryFromWkt(line_string)
    assert geom.GetGeometryType() == ogr.wkbLineString

    points = geom.GetPoints()
    smoothed_points = smooth_line(points)
    return f'LINESTRING({wkt_points_string(smoothed_points)})'


def smooth_polygon(wkt: str) -> str:
    geom = ogr.CreateGeometryFromWkt(wkt)
    assert geom.GetGeometryType() == ogr.wkbPolygon

    smoothed_points = [smooth_ring(ring.GetPoints()) for ring in geom]
    return f'POLYGON({", ".join([f"({wkt_points_string(points)})" for points in smoothed_points])})'


def smooth_multi_polygon(wkt: str) -> str:
    geom = ogr.CreateGeometryFromWkt(wkt)
    assert geom.GetGeometryType() == ogr.wkbMultiPolygon

    smoothed_points = [[smooth_ring(ring.GetPoints()) for ring in polygon] for polygon in geom]
    multi_polygon_str = ", ".join(
        [f'({", ".join([f"({wkt_points_string(points)})" for points in polygon])})'
         for polygon in smoothed_points]
    )
    return f'MULTIPOLYGON({multi_polygon_str})'


def smooth_line(points):
    """
    平滑首尾不相连的点集。
    """
    smoothed_points = get_smoothed_points(points)
    smoothed_points = [points[0], *smoothed_points, points[-1]]
    return smoothed_points


def smooth_ring(points):
    """
    平滑首尾相连的闭合点集。
    """
    points.append(points[1])
    smoothed_points = get_smoothed_points(points)
    smoothed_points.append(smoothed_points[0])
    return smoothed_points


def get_smoothed_points(points):
    """
    获取平滑后的坐标串，仅返回受到平滑作用的点集。
    注意：由于坐标串头尾两点不在平滑处理范围内，故返回的点集中不包含原始点集的头尾两点。
    """
    if len(points) < 3:
        return points

    # 每相邻的3个点构成一个待圆滑的角。
    angle_groups = [(points[i], points[i + 1], points[i + 2]) for i in range(len(points) - 2)]
    arc_3points_groups = [calculate_arc_3points(*group) for group in angle_groups]
    smoothed_segments = [generate_arc_points_by_3points(*group) if group else None for group in arc_3points_groups]

    # 展平圆弧点集，若某顶点不符合圆弧化条件，则保留原顶点。
    smoothed_points = [smooth_segment if smooth_segment else [group[1]]
                       for group, smooth_segment in zip(angle_groups, smoothed_segments)]
    smoothed_points = [p for segment in smoothed_points for p in segment]
    return smoothed_points


def calculate_arc_3points(p_edge1, p_vertex, p_edge2):
    """
    计算用于作弧的3点。
    @param p_edge1: 角边上的一点
    @param p_vertex: 角的顶点
    @param p_edge2: 角边上的另一点
    @return: 返回一个3元组：(圆弧与角边的切点, 圆弧的圆心, 圆弧与另一条角边的切点)
    """
    v1 = vec(p_vertex, p_edge1)
    v2 = vec(p_vertex, p_edge2)
    v1_length = np.linalg.norm(v1)
    v2_length = np.linalg.norm(v2)

    # NOTE: 当前选择不检查边长，若边长大于弧段长度就可以进行圆滑。
    # if v1_length < SMOOTH_MAX_RADIUS or v2_length < SMOOTH_MAX_RADIUS:
    #     return None

    # 边长不能小于圆滑半径的2倍，且圆滑后的剩余边长不能小于弧片段长，否则圆滑后的点会交叉，造成自相交。
    smooth_radius = min(
        SMOOTH_MAX_RADIUS,
        (v1_length - SMOOTH_ARC_SEGMENT_LENGTH) / 2,
        (v2_length - SMOOTH_ARC_SEGMENT_LENGTH) / 2
    )
    if smooth_radius <= 0:
        return None

    v1 = smooth_radius * normalize(v1)
    v2 = smooth_radius * normalize(v2)
    # 若向量共线或夹角过大，则不需要圆滑。
    if np.cross(v1, v2) == 0 or cos_vec(v1, v2) < SMOOTH_ANGLE_LIMIT:
        return None

    a = np.array([v1, v2])
    smooth_radius_squared = smooth_radius ** 2
    b = np.array([smooth_radius_squared, smooth_radius_squared])
    smooth_circle_center = np.linalg.solve(a, b)
    return v1 + p_vertex, smooth_circle_center + p_vertex, v2 + p_vertex


def generate_arc_points_by_3points(p_arc1, p_center, p_arc2):
    """
    根据给定的3点作弧。
    @param p_arc1: 圆弧与角边的切点
    @param p_center: 圆弧的圆心
    @param p_arc2: 圆弧与另一条角边的切点
    @return: 圆弧上的点集
    """
    v1 = vec(p_center, p_arc1)
    v2 = vec(p_center, p_arc2)

    # 计算分段弧长的点数
    radius = np.linalg.norm(v1)
    cos_value = cos_vec(v1, v2)
    # 对于原图形出现自相交或平角的特殊情况，由于浮点数精度，可能使得 cos 值超过其函数的值域 [-1, 1]。
    if cos_value <= -1 or cos_value >= 1:
        return None

    arc_angle = math.acos(cos_value)
    n_point = math.floor((arc_angle * radius) / SMOOTH_ARC_SEGMENT_LENGTH)

    if n_point == 0:
        return None

    arc_segment_angle = arc_angle / n_point
    # 判断 v1, v2 的相对位置，不妨设：逆时针为正方向，表示 v2 可由 v1 逆时针旋转小于 pi 的角度得到。
    is_clockwise = np.cross(v1, v2) < 0
    arc_segment_angle = -arc_segment_angle if is_clockwise else arc_segment_angle
    rotate_matrix = np.array([
        [math.cos(arc_segment_angle), -math.sin(arc_segment_angle)],
        [math.sin(arc_segment_angle), math.cos(arc_segment_angle)]
    ])
    arc_points = select(v1, n_point, lambda v: np.matmul(rotate_matrix, v))
    arc_points = [p + p_center for p in arc_points]
    return [p_arc1, *arc_points, p_arc2]


def wkt_points_string(points):
    return ', '.join(f'{x} {y}' for x, y in points)


def select(seed, n, func):
    for _ in range(n):
        seed = func(seed)
        yield seed


def vec(point1, point2):
    x1, y1 = point1
    x2, y2 = point2
    return np.array([x2 - x1, y2 - y1])


def normalize(vector) -> np.ndarray:
    return vector / np.linalg.norm(vector)


def cos_vec(v1, v2):
    return np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
