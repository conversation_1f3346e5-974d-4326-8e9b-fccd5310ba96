# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
长短Node/link转换库
"""
import os
import sys

from typing import List

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from src.aoi import CommTool
from src.aoi.dao.pg_dao import PgDao


class Trans(PgDao):
    """
    长短Node/link转换库
    """

    def __init__(self):
        self.cursor = None
        conf = CommTool.get_config("pg")
        if "trans" in conf:
            poi_online_r_conf = conf['trans']
            super().__init__(poi_online_r_conf)
        else:
            raise Exception("trans 配置不存在")

    def get_short_node_by_long_node_ids(self, long_node_ids: list):
        """
        短Node/link转换库
        Args:
            long_node_ids: 

        Returns:

        """""
        sql = f"select tid,sid from image_n where sid = ANY(%s)"
        return self.queryall(sql, [long_node_ids])

    def get_long_node_by_short_node_ids(self, short_node_ids: list):
        """
        短Node/link转换库
        Args:
            short_node_ids: 

        Returns:

        """""
        sql = f"select sid from image_n where tid = ANY(%s)"
        return self.queryall(sql, [short_node_ids])

    def get_short_link_by_long_link_ids(self, long_link_ids: list):
        """
        短Node/link转换库
        Args:
            long_link_ids: 

        Returns:

        """""
        sql = f"select tid,sid from image_r where sid = ANY(%s)"
        return self.queryall(sql, [long_link_ids])

    def get_long_link_by_short_link_ids(self, short_link_ids: list):
        """
        短Node/link转换库
        Args:
            short_link_ids: 

        Returns:

        """""
        sql = f"select sid from image_r where tid = ANY(%s)"
        return self.queryall(sql, [short_link_ids])
