#-*- coding: utf-8 -*-
"""
超过 4 门的 aoi 生成任务
"""
from cgitb import reset
import time
import psycopg2
import requests
import json
import shapely.wkt
import shapely.geometry
import uuid
from multiprocessing import Process
from shapely.geometry import Polygon, LineString
from shapely.ops import linemerge, unary_union, polygonize



curl_data = '{"pathname":"/muku/road/master_road_ml", "action":"query","type":"dbinfo"}'
url = "http://rc.guoke.map.baidu.com:8000/rcmanage/naming"
headers = {"content-type": "application/json"}
res = requests.post(url=url, data=curl_data, headers=headers)
# {"status": 0, "message": "ok", "data": {"host": "*************", "get_info": 0, "user": "mukucache_a0968f8a9eda11ed9183002590f91981_se_rw", "domain":
#  "mukucache", "passwd": "osuvhyva", "model": "rw", "drop_info": 0, "db": "mukucache_a0968f8a9eda11ed9183002590f91981", "port": 5532}}
if res.status_code == 200:
    curl_text = json.loads(res.text)
    if curl_text['message'] == 'ok':
        roadConf = {"db": curl_text['data']['db'],
            "user": curl_text['data']['user'], "pwd": curl_text['data']['passwd'],
            "host": curl_text['data']['host'], "port": curl_text['data']['port']}
    else:
        print("roadConf curl_text error")
        exit()
else:
    print("roadConf connect error")
    exit()
# psql -Umukucache_a0968f8a9eda11ed9183002590f91981_se_rw -Wosuvhyva -p 5532 -h *************  -dmukucache_a0968f8a9eda11ed9183002590f91981

backConf = {"db": "master_back",
            "user": "master_back_se_rw", "pwd": "cwduumoj",
            "host": "**************", "port": 5432}

# poi测试
# poiConf = {"db": "poi_online",
#             "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
#             "host": "**************", "port": 9432}
# poi正式
poiConf = {"db": "poi_online",
            "user": "poi_aoi_rw", "pwd": "poi_aoi_rw",
            "host": "gzbh-ns-map-de16.gzbh.baidu.com", "port": 8532}

pvConf = {"db": "eq_guarantee_db",
            "user": "eq_guarantee_db_se_rw", "pwd": "mxkeazfk",
            "host": "************", "port": 6432}

transConf = {"db": "trans_id",
            "user": "trans_id_se_rw", "pwd": "cpmkukky",
            "host": "*************", "port": 5432}

#连接poi正式库
poiDbConn = psycopg2.connect(database=poiConf["db"],
                              user=poiConf["user"],
                              password=poiConf["pwd"],
                              host=poiConf["host"],
                              port=poiConf["port"])
poiDao = poiDbConn.cursor()

# 连接背景母库
backDbConn = psycopg2.connect(database=backConf["db"],
                              user=backConf["user"],
                              password=backConf["pwd"],
                              host=backConf["host"],
                              port=backConf["port"])
backgroundDao = backDbConn.cursor()
 
# 连接道路库
roadDbConn = psycopg2.connect(database=roadConf["db"],
                              user=roadConf["user"],
                              password=roadConf["pwd"],
                              host=roadConf["host"],
                              port=roadConf["port"])
roadDao = roadDbConn.cursor()

# 连接pv库
pvDbConn = psycopg2.connect(database=pvConf["db"],
                              user=pvConf["user"],
                              password=pvConf["pwd"],
                              host=pvConf["host"],
                              port=pvConf["port"])
pvDao = pvDbConn.cursor()

# 连接trans库
transDbConn = psycopg2.connect(database=transConf["db"],
                              user=transConf["user"],
                              password=transConf["pwd"],
                              host=transConf["host"],
                              port=transConf["port"])
transDao = transDbConn.cursor()


def process():
    """
    process 正式处理流程
    """
    # 初始化数据 
    #获取待处理的数据列表
    beijing = guangzhou = shenzhen = other = no_poi= 0
    
    batch_l1 = 'cq_20230221_l1_4gates'
    # batch_l2 = 'cq_20230221_l2_4gates'
    f_creatplan = open(batch_l1, "w")
    # f_creatplan = open(batch_l2, "w")
    sql = "select id, success_node_id, bid from aoi_gate_match_batch_result where level = 2 and \
         valid = 1 and  (length(success_node_id) - length(replace(success_node_id,',',''))) >= 3"
    fw = open('level2_4gate_lists.txt', 'w')
    poiDao.execute(sql)
    aoi_gate_list = poiDao.fetchall()
    create_plan_bid_list = []
    if aoi_gate_list:
        for v in aoi_gate_list:
            id = v[0]
            print("开始核实id" + str(id))
            bid = v[2]
            node_id_str = v[1]
            node_id_str = node_id_str.strip(',')
            node_id_list = node_id_str.split(",")
            item = {"bid":bid, "aoi_src_data_extra": []}

            for node_id in node_id_list:
                sql_man = "select 1 from blu_access_gate_rel where node_id = '{}'".format(node_id)
                backgroundDao.execute(sql_man)
                res2 = backgroundDao.fetchone()
                if res2:
                    continue
                gate_sql = "select st_astext(geom) as mark_wkt from nav_node where node_id = '{}'".format(node_id)
                roadDao.execute(gate_sql)
                gate_res = roadDao.fetchall()
                if gate_res:
                    print("有下发的大门任务的：\t" + str(id) + "\t" + node_id)
                    item['aoi_src_data_extra'].append({"trans_id": node_id + "_" + \
                        time.strftime('%Y%m%d', time.localtime(time.time())), "mark_wkt":gate_res[0]})
            if len(item['aoi_src_data_extra']) > 0:
                create_plan_bid_list.append(item)

    json.dump(create_plan_bid_list, f_creatplan)
    f_creatplan.close()
    fw.close()

if __name__ == "__main__":
    process()